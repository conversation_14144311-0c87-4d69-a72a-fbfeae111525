﻿using Azure.Storage.Blobs.Models;
using NuGet.Protocol;
using PharmaLex.SmartTRACE.Web;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models;

namespace PharmaLex.SmartTRACE.WebTests.Extensions
{
    public class LeafModelExtensionsTests
    {
        readonly BlobItem sut;
        public LeafModelExtensionsTests()
        {
            sut = BlobsModelFactory.BlobItem("testurl.test.net/test");
        }

        [Fact]
        public void Check_ToLeafModel()
        {
            var expected = new LeafModel
            {
                text = "test",
                href = "testurl.test.net/test",
                Submission = "testsub",
                op = "New"
            };
            var actual = sut.ToLeafModel("testsub");
            Assert.Equal(expected.ToJson(), actual.ToJson());
        }
    }
}
