﻿CREATE TRIGGER [dbo].[UserClaim_Insert] ON [dbo].[UserClaim]
FOR INSERT AS
INSERT INTO [Audit].[UserClaim_Audit]
SELECT 'I', [UserId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[UserClaim_Update] ON [dbo].[UserClaim]
FOR UPDATE AS
INSERT INTO [Audit].[UserClaim_Audit]
SELECT 'U', [UserId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[UserClaim_Delete] ON [dbo].[UserClaim]
FOR DELETE AS
INSERT INTO [Audit].[UserClaim_Audit]
SELECT 'D', [UserId], [ClaimId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO

INSERT INTO [dbo].[UserClaim] SELECT 1, 1, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[UserClaim] SELECT 2, 1, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[UserClaim] SELECT 3, 1, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[UserClaim] SELECT 4, 1, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[UserClaim] SELECT 5, 1, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[UserClaim] SELECT 11, 3, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[UserClaim] SELECT 12, 3, GETDATE(), 'update script', GETDATE(), 'update script'