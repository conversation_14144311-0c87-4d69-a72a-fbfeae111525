﻿@model List<string>
@using Newtonsoft.Json
@{
    ViewData["Title"] = "Manage Sequences";
}
<div id="submissions" class="manage-container" v-cloak>
    <header class="manage-header">
        <h2>Manage Sequences</h2>
    </header>
    <div v-if="uploading" class="page-loading">
        <div class="page-loader"></div>
    </div>
    <form>
        @Html.AntiForgeryToken()
        <div class="form-col form-col-third">
            <h5>Upload dossier (*.zip)</h5>
            <upload v-on:uploading="fileUploading($event)" v-on:file-added="fileAdded($event)" :config="uploadConfig"></upload>
        </div>
        <div v-if="list && list.length" class="form-col form-col-third">
            <h5>Recently viewed</h5>
            <div class="folderview-container" style="cursor: pointer;">
                <div class="node" v-for="s in list" v-on:click.stop="onSequenceSelected(s)">
                    <div class="node-row">
                        <i class="icon-file"></i>
                        <div><span>{{s}}</span></div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@section Scripts {
    <script src="@Cdn.GetUrl("js/plx.js")"></script>
    <script src="@Cdn.GetUrl("js/toast.js")"></script>
    <script src="@Cdn.GetUrl("lib/uppy/uppy.min.js")"></script>
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        var pageConfig = {
            appElement: '#submissions',
            data() {
                return {
                    uploading: false,
                    list: @Html.Raw(JsonConvert.SerializeObject(Model)),
                    uploadConfig: {
                        height: '400px',
                        uploadLink: '/submissions/link/{fileName}',
                        unpackLink: '/submissions/unpack/{fileName}',
                        unpacked: false,
                    }
                }
            },
            methods: {
                onSequenceSelected(sequence) {
                    window.location.href = 'submissions/' + sequence;
                },
                fileUploading(value) {
                    this.uploading = value;
                },
                fileAdded(value) {
                    this.list.push(value);
                    this.list = [...new Set(this.list)];
                    this.uploading = false;
                    plx.toast.show(`Sequence uploaded successfully`, 2, 'confirm', null, 2000);
                }
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/Upload" />
}