﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class ExternalUserCreationModelTests
    {
        [Fact]
        public void ExternalUserCreationModel_Get_SetValue()
        {
            //Arrange
            var model = new ExternalUserCreationModel();
            model.FullName="test";
            model.Url = "/test";
            model.OriginalUrl = "/test";
            model.ContactMail = "<EMAIL>";
            model.ExpirationDays = "-1";
            //Assert
            Assert.NotNull(model);
        }
    }
}
