﻿using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class IEnumerableExtensionsTests
    {
        [Fact]
        public void ExternalUserCreationModel_Get_SetValue()
        {
            //Arrange
            IEnumerable<CtdNodeModel> models = new List<CtdNodeModel>()
           {
               new CtdNodeModel{ChildNodes=new List<CtdNodeModel>(){ new CtdNodeModel { Id=1,Name="test" } } }
           };
            //Act
            var result = models.Flatten<CtdNodeModel>(x => x.ChildNodes);
            //Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);
        }
    }
}
