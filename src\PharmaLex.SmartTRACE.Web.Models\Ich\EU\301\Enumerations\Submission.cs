﻿using System;
using System.ComponentModel;
using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.Eu301
{
    [Serializable]
    public enum procedureType
    {
        [Description("Centralised")]
        centralised,
        [Description("National Procedure")]
        national,
        [Description("Mutual Recognition Procedure (MRP)")]
        [XmlEnum("mutual-recognition")]
        mutualrecognition,
        [Description("Decentralised Procedure (DCP)")]
        decentralised,
    }

    [Serializable]
    public enum submissionMode
    {
        [Description("None")]
        none,
        [Description("Single")]
        single,
        [Description("Grouping")]
        grouping,
        [Description("Work Sharing")]
        worksharing,
    }

    [Serializable]
    public enum submissionunitType
    {
        [Description("Initial")]
        initial,
        [XmlEnum("validation-response")]
        [Description("Validation response")]
        validationresponse,
        [Description("Response")]
        response,
        [XmlEnum("additional-info")]
        [Description("AdditionalInfo")]
        additionalinfo,
        [Description("Closing")]
        closing,
        [Description("Consolidating")]
        consolidating,
        [Description("Corrigendum")]
        corrigendum,
        [Description("Reformatting of an existing submission application")]
        reformat,
    }

    [Serializable]
    public enum submissionType
    {
        [XmlEnum("initial-maa")]
        [Description("Initial Marketing Authorisation")]
        initialmaa,
        [Description("Marketing Authorisation")]
        maa,
        [XmlEnum("var-type1a")]
        [Description("Variation Type IA")]
        vartype1a,
        [XmlEnum("var-type1ain")]
        [Description("Variation Type IAin")]
        vartype1ain,
        [XmlEnum("var-type1b")]
        [Description("Variation Type IB")]
        vartype1b,
        [XmlEnum("var-type2")]
        [Description("Variation Type II")]
        vartype2,
        [XmlEnum("var-nat")]
        [Description("National Variation")]
        varnat,
        [Description("Extension")]
        extension,
        [Description("Repeat Use Procedure")]
        rup,
        [Description("Periodic Safety Update Report")]
        psur,
        [Description("PSUR single assessment procedure")]
        psusa,
        [Description("Risk Management Plan (outside any procedure)")]
        rmp,
        [Description("Renewal")]
        renewal,
        [Description("Specific obligation related to a post-authorisation measure")]
        [XmlEnum("pam-sob")]
        pamsob,
        [Description("Annex II condition related to a post-authorisation measure")]
        [XmlEnum("pam-anx")]
        pamanx,
        [Description("Additional pharmacovigilance activity in the risk-management plan")]
        [XmlEnum("pam-mea")]
        pammea,
        [Description("Legally binding measure related to a post-authorisation measure")]
        [XmlEnum("pam-leg")]
        pamleg,
        [Description("Cumulative review following a request originating from a PSUR or a signal evaluation")]
        [XmlEnum("pam-sda")]
        pamsda,
        [Description("Corrective Action/Preventive Action")]
        [XmlEnum("pam-capa")]
        pamcapa,
        [Description("Paediatric submissions related to a post-authorisation measure (Par 45)")]
        [XmlEnum("pam-p45")]
        pamp45,
        [Description("Paediatric submissions related to a post-authorisation measure (Par 46)")]
        [XmlEnum("pam-p46")]
        pamp46,
        [Description("Submission of a post authorisation efficacy study")]
        [XmlEnum("pam-paes")]
        pampaes,
        [Description("Recommendation related to a post-authorisation measure")]
        [XmlEnum("pam-rec")]
        pamrec,
        [Description("Submission of a post authorisation safety study protocol")]
        pass107n,
        [Description("Submission of a post authorisation safety study report")]
        pass107q,
        [Description("Active Substance Master File")]
        asmf,
        [Description("Plasma Master File")]
        pmf,
        [Description("Referral under Article 20")]
        [XmlEnum("referral-20")]
        referral20,
        [Description("Referral under Article 29(4)")]
        [XmlEnum("referral-294")]
        referral294,
        [Description("Referral under Article 29 paediatric")]
        [XmlEnum("referral-29p")]
        referral29p,
        [Description("Referral under Article 30")]
        [XmlEnum("referral-30")]
        referral30,
        [Description("Referral under Article 31")]
        [XmlEnum("referral-31")]
        referral31,
        [Description("Referral under Article 35")]
        [XmlEnum("referral-35")]
        referral35,
        [Description("Referral under Article 5(3)")]
        [XmlEnum("referral-5-3")]
        referral53,
        [Description("Referral under Article 107i")]
        [XmlEnum("referral-107i")]
        referral107i,
        [Description("Referral under Article 16c(1c)")]
        [XmlEnum("referral-16c1c")]
        referral16c1c,
        [Description("Referral under Article 16c(4)")]
        [XmlEnum("referral-16c4")]
        referral16c4,
        [Description("Annual Reassessment")]
        [XmlEnum("annual-reassessment")]
        annualreassessment,
        [Description("Urgent Safety Restriction")]
        usr,
        [Description("Clinical data for publication  Redacted Proposal")]
        [XmlEnum("clin-data-pub-rp")]
        clindatapubrp,
        [Description("Clinical data for publication  Final Version")]
        [XmlEnum("clin-data-pub-fv")]
        clindatapubfv,
        [Description("Paediatric Submission, Article 7, 8 or 30 of the Regulation")]
        [XmlEnum("paed-7-8-30")]
        paed7830,
        [Description("Paediatric Submission, Article 29")]
        [XmlEnum("paed-29")]
        paed29,
        [Description("Paediatric Submission, Article 45")]
        [XmlEnum("paed-45")]
        paed45,
        [Description("Paediatric Submission, Article 46")]
        [XmlEnum("paed-46")]
        paed46,
        [Description("Article 58")]
        [XmlEnum("article-58")]
        article58,
        [Description("Notification 61(3)")]
        [XmlEnum("notification-61-3")]
        notification613,
        [Description("Transfer of Marketing Authorisation")]
        [XmlEnum("transfer-ma")]
        transferma,
        [Description("Lifting of a Suspension")]
        [XmlEnum("lifting-suspension")]
        liftingsuspension,
        [Description("Withdrawal during Assessment or Withdrawal of MA")]
        withdrawal,
        [Description("CEP application")]
        cep,
        [Description("The submission is not a regulatory activity")]
        none,
        [Description("Supplemental info")]
        [XmlEnum("supplemental-info")]
        supplementalinfo
    }
}
