﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Storage;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class DocumentsControllerTests
    {
        private readonly ISubmissionBlobContainer _azureBlobContainer;
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly INotificationService _notificationService;
        readonly DocumentsController controller;
        #region Constructor 
        public DocumentsControllerTests()
        {
            _azureBlobContainer = Substitute.For<ISubmissionBlobContainer>();
            var azblobCoClient = Substitute.For<BlobContainerClient>();
            var azBlobServiceClient = Substitute.For<BlobServiceClient>();
            azBlobServiceClient.Uri.Returns(new Uri("https://www.test.com/help"));
            azBlobServiceClient.GetUserDelegationKey(Arg.Any<DateTimeOffset?>(), Arg.Any<DateTimeOffset>(), Arg.Any<CancellationToken>())
                .Returns(
                Azure.Response.FromValue(BlobsModelFactory.UserDelegationKey(
                    Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), signedService: Guid.NewGuid().ToString(), signedVersion:"1",value:"test"), Substitute.For<Azure.Response>()));
            
            azblobCoClient.GetParentBlobServiceClient().ReturnsForAnyArgs(azBlobServiceClient);
            var blobClient = Substitute.For<BlobClient>();
            blobClient.GetParentBlobContainerClient().ReturnsForAnyArgs(azblobCoClient);
            blobClient.Name.Returns("https://www.test.com/help");
            blobClient.Uri.Returns(new Uri("https://www.test.com/help"));
            blobClient.CanGenerateSasUri.Returns(true);
            blobClient.BlobContainerName.Returns("testc");
            blobClient.AccountName.Returns("test");
            _azureBlobContainer.GetBlobClientAsync("example.txt").Returns(blobClient);
            _cache = Substitute.For<IDistributedCacheServiceFactory>();
            _notificationService = Substitute.For<INotificationService>();
            controller = new DocumentsController(_azureBlobContainer, _cache, _notificationService);
        }
        #endregion

        [Fact]
        public async Task Download_Returns_No_Result()
        { // Arrange
            var filename = "example.txt";
            try
            {
                var result = await controller.Download(filename) as Exception;
            }
            catch (Exception ex)
            {
                if (ex.Message != null)
                {
                    Assert.Equal("Object reference not set to an instance of an object.", ex.Message);
                }
            }
        }
        [Fact]
        public async Task Download_Returns_InvalidObject()
        { // Arrange
            var filename = "example.txt";
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            //Act
            var result = await controller.Download(filename) as BadRequestObjectResult;

            //Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }

        [Fact]
        public async Task Notify_Returns_Ok_Result()
        { // Arrange
            List<SubmissionPublisher> submissionPublishers = new List<SubmissionPublisher>()
             {
                 new SubmissionPublisher
                 {
                     Id=1,
                     SubmissionResourceId=1,
                     SubmissionResource=new SubmissionResource()
                     {
                         Id=1,InitialSentToEmail="<EMAIL>"
                     },
                     Publisher=new User()
                     {
                         Email="<EMAIL>",
                         GivenName="Test",
                         FamilyName="Test",
                     }
                 }
             };
            UserModel usermodel = new UserModel() { Id = 1, DisplayFullName = "abc", Email = "<EMAIL>" };

            SubmissionModel submodel = new SubmissionModel() { Id = 1, ApplicationId = 1, CountriesIds = [1, 2, 3] };
            var subCache = Substitute.For<IMappedEntityCacheServiceProxy<Submission, SubmissionModel>>();
            _cache.CreateMappedEntity<Submission, SubmissionModel>()
                    .Configure(Arg.Any<Func<IIncludable<Submission>, IIncludable<Submission>>>()).Returns(subCache);
            subCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submodel);

            SubmissionResourceModel submissioresourcenmodel = new SubmissionResourceModel(){Id = 1,Comments = "Test",SubmissionId = 1};

            var _submissionResourcecache = Substitute.For<IMappedEntityCacheServiceProxy<SubmissionResource, SubmissionResourceModel>>();
            _cache.CreateMappedEntity<SubmissionResource, SubmissionResourceModel>().Returns(_submissionResourcecache);
            _submissionResourcecache.FirstOrDefaultAsync(Arg.Any<Expression<Func<SubmissionResource, bool>>>()).Returns(submissioresourcenmodel);


            var _PublishersCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionPublisher>>();
            _cache.CreateEntity<SubmissionPublisher>().Configure(Arg.Any<Func<IIncludable<SubmissionPublisher>, IIncludable<SubmissionPublisher>>>()).Returns(_PublishersCache);
            _PublishersCache.WhereAsync(Arg.Any<Expression<Func<SubmissionPublisher, bool>>>()).Returns(submissionPublishers);


            var userdCache = Substitute.For<IMappedEntityCacheServiceProxy<User, UserModel>>();
            _cache.CreateMappedEntity<User, UserModel>().Returns(userdCache);
            userdCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<User, bool>>>()).Returns(usermodel);

            DocumentModel document = new DocumentModel();

            var result = await controller.NotifyPublishers(document) as StatusCodeResult;
            Assert.Equal(200, result?.StatusCode);

        }
        [Fact]
        public async Task Notify_Returns_Invalid()
        { 
            // Arrange
            DocumentModel document = new DocumentModel();
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            //Act
            var result = await controller.NotifyPublishers(document) as BadRequestObjectResult;

            //Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);

        }
    }
}