﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities.Audit;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class SmartTRACEContext
    {
        public virtual DbSet<ClaimAudit> ClaimAudit { get; set; }
        public virtual DbSet<UserAudit> UserAudit { get; set; }
        public virtual DbSet<UserClaimAudit> UserClaimAudit { get; set; }
        public virtual DbSet<ActiveSubstanceAudit> ActiveSubstanceAudit { get; set; }
        public virtual DbSet<ApplicationAudit> ApplicationAudit { get; set; }
        public virtual DbSet<ClientAudit> ClientAudit { get; set; }
        public virtual DbSet<CountryAudit> CountryAudit { get; set; }
        public virtual DbSet<PicklistDataAudit> PicklistDataAudit { get; set; }
        public virtual DbSet<ProductAudit> ProductAudit { get; set; }
        public virtual DbSet<ActiveSubstanceProductAudit> ActiveSubstanceProductAudit { get; set; }
        public virtual DbSet<RegionAudit> RegionAudit { get; set; }
        public virtual DbSet<RegulatoryAuthorityAudit> RegulatoryAuthorityAudit { get; set; }
        public virtual DbSet<RegulatoryAuthorityCountryAudit> RegulatoryAuthorityCountryAudit { get; set; }
        public virtual DbSet<ProjectAudit> ProjectAudit { get; set; }
        public virtual DbSet<SubmissionAudit> SubmissionAudit { get; set; }
        public virtual DbSet<SubmissionResourceAudit> SubmissionResourceAudit { get; set; }
        public virtual DbSet<SubmissionPublisherAudit> SubmissionPublisherAudit { get; set; }
        public virtual DbSet<ApplicationCountryAudit> ApplicationCountryAudit { get; set; }
        public virtual DbSet<SubmissionCountryAudit> SubmissionCountryAudit { get; set; }
        public virtual DbSet<ApplicationProductAudit> ApplicationProductAudit { get; set; }
        public virtual DbSet<PicklistDataCountryAudit> PicklistDataCountryAudit { get; set; }
        public virtual DbSet<UserClientAudit> UserClientAudit { get; set; }
        public virtual DbSet<DocumentAudit> DocumentAudit { get; set; }
        public virtual DbSet<DocshifterDocumentFileAudit> DocshifterDocumentFileAudit { get; set; }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder)
        {
            modelBuilder.AuditEntityBase<ClaimAudit>(entity =>
            {
                entity.Property(e => e.ClaimType).HasMaxLength(32);

                entity.Property(e => e.Name).HasMaxLength(1024);
            });

            modelBuilder.AuditEntityBase<UserAudit>(entity =>
            {
                entity.Property(e => e.Email).HasMaxLength(512);

                entity.Property(e => e.FamilyName).HasMaxLength(1024);

                entity.Property(e => e.GivenName).HasMaxLength(1024);

                entity.Property(e => e.LastLoginDate).HasColumnType("datetime");
            });

            modelBuilder.AuditEntityBase<UserClaimAudit>();

            modelBuilder.AuditEntityBase<ActiveSubstanceAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);
            });

            modelBuilder.AuditEntityBase<ApplicationAudit>(entity =>
            {
                entity.Property(e => e.ApplicationNumber).HasMaxLength(32);

                entity.Property(e => e.Comments).HasMaxLength(250);
            });

            modelBuilder.AuditEntityBase<ClientAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(128);
            });

            modelBuilder.AuditEntityBase<CountryAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(128);
            });

            modelBuilder.AuditEntityBase<PicklistDataAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);
            });

            modelBuilder.AuditEntityBase<ActiveSubstanceProductAudit>();

            modelBuilder.AuditEntityBase<ProductAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);

                entity.Property(e => e.Strength).HasMaxLength(64);
            });

            modelBuilder.AuditEntityBase<RegionAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(128);

                entity.Property(e => e.Abbreviation).HasMaxLength(8);
            });

            modelBuilder.AuditEntityBase<RegulatoryAuthorityAudit>();

            modelBuilder.AuditEntityBase<RegulatoryAuthorityCountryAudit>();

            modelBuilder.AuditEntityBase<ProjectAudit>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);

                entity.Property(e => e.Code).HasMaxLength(32);

                entity.Property(e => e.OpportunityNumber).HasMaxLength(32);
            });

            modelBuilder.AuditEntityBase<SubmissionAudit>(entity =>
            {
                entity.Property(e => e.UniqueId).HasMaxLength(32);

                entity.Property(e => e.SequenceNumber).HasMaxLength(10);

                entity.Property(e => e.RelatedSequenceNumber).HasMaxLength(10);

                entity.Property(e => e.SerialNumber).HasMaxLength(10);

                entity.Property(e => e.ReferenceNumber).HasMaxLength(50);

                entity.Property(e => e.CespNumber).HasMaxLength(128);

                entity.Property(e => e.DocubridgeVersionId).HasMaxLength(35);

                entity.Property(e => e.HealthAuthorityDueDate).HasColumnType("datetime");

                entity.Property(e => e.AuthoringDeadline).HasColumnType("datetime");

                entity.Property(e => e.PlannedDispatchDate).HasColumnType("datetime");

                entity.Property(e => e.ActualDispatchDate).HasColumnType("datetime");

                entity.Property(e => e.PlannedSubmissionDate).HasColumnType("datetime");

                entity.Property(e => e.ActualSubmissionDate).HasColumnType("datetime");

                entity.Property(e => e.WithdrawalDate).HasColumnType("datetime");
            });

            modelBuilder.AuditEntityBase<SubmissionResourceAudit>(entity =>
            {
                entity.Property(e => e.RegulatoryLead).HasMaxLength(256);

                entity.Property(e => e.PublishingLead).HasMaxLength(256);

                entity.Property(e => e.InitialSentToEmail).HasMaxLength(256);

                entity.Property(e => e.EstimatedHours).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.AuditEntityBase<SubmissionPublisherAudit>();

            modelBuilder.AuditEntityBase<ApplicationCountryAudit>();

            modelBuilder.AuditEntityBase<SubmissionCountryAudit>();

            modelBuilder.AuditEntityBase<ApplicationProductAudit>();

            modelBuilder.AuditEntityBase<PicklistDataCountryAudit>();

            modelBuilder.AuditEntityBase<UserClientAudit>();

            modelBuilder.AuditEntityBase<DocumentAudit>();

            modelBuilder.AuditEntityBase<DocshifterDocumentFileAudit>();
        }
    }
}
