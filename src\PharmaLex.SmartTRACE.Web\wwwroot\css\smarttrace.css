﻿:root {
    --smarttrace-logo-color: #009aa8;
    --background-secondary: #3B3B3B;
    --blue-900: #062239;
    --background-image: url('../images/Smartphlex_background_green.png');
}

.login-content {
    display: inline-flex;
    justify-content: space-between;
    align-items: stretch;
    padding: 0;
}

.login-provider {
    width: calc(50% - 1px);
    background-color: rgba(255, 255, 255, 0.9);
    padding: 2rem;
}

.custom-container {
    width: 100%;
    max-width: 1260px;
    max-height: calc(100% - 100px);
    margin: 0 auto;
    padding: 20px 20px 0 20px;
    overflow-x: hidden;
    overflow-y: auto;
    text-align: left;
    height: inherit;
    display:flex;
    flex-wrap: wrap;
}

.custom-header {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.custom-header h2 {
    flex-grow: 1;
}

.custom-header a {
    height: 2rem;
    white-space: nowrap;
}

.custom-header a:first-of-type {
    margin-left: auto;
}

.pane-container {
    display: grid;
    grid-template-columns: 20% 48% 30%;
    grid-column-gap: 1%;
    width: 100%;
    height: calc(100% - 60px);
    position: relative;
    margin-top: var(--spaceM);
}

.manage-container.viewer {
    height: 100%;
}

section.collapsible {
    border: 1px solid var(--grey-100);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.collapsible header {
    border: 1px solid var(--background-secondary);
    background-color: var(--background-secondary);
    color: #fff;
    font-weight: bold;
    padding: 5px;
    white-space: nowrap;
}

.collapsible-container {
    overflow-x: visible;
    overflow-y: auto;
    display: flex;
    padding: 5px;
    flex: 1;
}

.submission-item {
    display: flex;
    flex-wrap: nowrap;
    align-items: baseline;
    margin-top: 5px;
    padding: 5px;
}

.submission-item label {
    margin: 5px 0 0 5px;
}

@media screen and (max-width: 1950px) {
    .page-header-content .logo {
        margin-left: 20%;
    }
}

@media screen and (max-width: 1950px) {
    .page-footer .logo, .page-footer .p {
        margin-left: 20%;
    }
}

.copyright {
    margin-right: 20%;
}

.icon-button-view:before {
    content: '\e813'
}

/*CTD Viewer*/
.treeview {
    overflow: visible;
    display: flex;
    flex-direction: column;
    padding: 5px;
    flex: 1;
}

.node {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    margin-top: 5px;
}

.node .node-children {
    margin-left: 20px;
    flex-basis: 100%;
    padding-left: 6.5px;
}
.node-row {
    display: flex;
    flex-wrap: nowrap;
    align-items: baseline;
    line-height: 1.5;
}

.node-row > * {
    padding: 3px;
}
.node-row.selected{
    background: #aaa;
}
.node-row.replace,
.node-row.changed {
    color: var(--main-theme-color);
}
.node-row.delete {
    color: #7a8b98;
}
.node-row:hover {
    background: #eeeeee;
}
.node-row .document{
    cursor: pointer;
}
.node-row.delete .document {
    cursor: default;
}

.submission-item:hover i.menu-icon,
.node-row:hover i.menu-icon {
    animation: icon-fade-in .5s;
    opacity: 1;
}

@keyframes icon-fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
/*Context Menu*/
.menu-container {
    position: relative;
}
.menu-container .menu {
    display: flex;
    flex-flow: row-reverse;
}
.menu-container i.menu-icon {
    opacity: 0;
}
.menu-container i.menu-icon:hover {
    color: var(--main-theme-color);
}
.menu-container .context {
    position: absolute;
    left: -60px;
    border: 1px solid var(--main-theme-color);
    background-color: white;
    z-index: 10000;
    margin: 0;
    opacity: .8;
    direction: rtl;
    color: #333;
}
.menu-container .context > * {
    white-space: nowrap;
    margin: 5px;
    padding: 3px 10px 2px 10px;
    cursor: pointer;
}
.menu-container .context > *:hover {
    background: #eeeeee;
}

/*EndContext Menu*/
.pdf-viewer {
    width: 100%;
    height: 100%;
}

.modal-dialog-container {
    overflow-y: hidden;
}
.modal-dialog-content {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}
.dialog-content > div {
    height: calc(100% - 20px);
    padding: 5px 0;
    overflow-y: auto;
    flex: 1;
}

.envelope-header{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}
.envelope-detail {
    text-align: left;
    display: grid;
    grid-template-columns: 15% 85%;
    grid-template-rows: max-content;
    grid-gap: 5px;
}
.envelope-detail.us {
    grid-template-columns: 30% 70%;
    margin-top: 5px;
}

.envelope-item {
    text-align: left;
    display: grid;
    grid-template-columns: 1fr 3fr;
    grid-template-rows: max-content;
    grid-column-gap: 20px;
    grid-row-gap: 10px;
    border: 2px solid var(--main-theme-color);
    margin: 5px 0;
    padding: 5px;
    line-height: 1.5;
    align-items: center;
}
.envelope-item >:nth-child(2n+1){
    background-color: #eeeeee;
}
.us >:nth-child(2n+1) {
    font-weight: 600;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 5px;
}

.envelope-item h4{
    margin-top: .5rem;
    white-space: nowrap;
    grid-column: auto / span 2;
}

.file-history {
    padding: 5px;
}
.file-history h6{
    text-decoration: underline;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.file-history .title{
    display:flex;
    justify-content: center;
}

.file-history-item {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 5px 5px 0 5px;
    margin-top: 5px;
}

.file-history-item.selected{
    font-size: 1rem;
    font-weight: 600;
}
.file-history-item.selected > div {
    border: 2px solid var(--main-theme-color);
}

.file-history-item > div{
    border: 1px dashed var(--main-theme-color);
    padding: 10px 20px;
    width: 100%;
}

.file-history-item .icons {
    display:flex;
    flex-flow: row-reverse;
}
.file-history-item .icons > * {
    margin: 10px 10px 0 10px;
    cursor: pointer;
}

.icon-down-large:before {
    content: '\f175';
    font-size: 2rem;
    position: relative;
}

.treeview .node-row > :first-child{
    min-width: 26px;
}
.treeview .node-row > :nth-child(2){
    margin: 0 5px;
}

.treeview .node-row > :nth-child(3) {
    display: flex;
    line-height: 1.5;
    align-content: center;
    padding-right: 10px;
}

.loader {
    position: absolute;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: start;
    justify-content: center;
    opacity: .8;
    z-index: 10000;
    background-color: #fff;
}

/* Home */
.home-container {
    text-align: left;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 0 50px 50px 0;
}


.welcome-message span {
    font-weight: normal;
}

.company-tile {
    width: 250px;
    height: 250px;
    border: 1px solid #515c65;
    margin: 50px 0 0 50px;
    cursor: pointer;
}

.company-tile header {
    background-color: #233c4c;
    color: #fff;
    font-weight: bold;
    padding: 5px;
}

.company-tile .tile-content {
    padding: 5px;
}

.company-tile .description-list-column-grid {
    margin: 0;
}

/* Company */
.countries-select {
    height: 500px;
    margin: 1rem 0 0.5rem 0;
    background-color: #c7d8e5;
    font-size: 0.9rem;
}

.company-countries-select {
    height: 500px;
    margin: 1rem 0 0.5rem 0;
    background-color: #c7d8e5;
    font-size: 0.9rem;
    display: none;
}

.company-countries-map {
    height: 500px;
    margin: 1rem 0 0.5rem 0;
    position: relative;
    overflow: hidden;
}

.app-content {
    margin-top: 3rem;
    font-size: 1.5rem;
    line-height: 2rem;  
}

    .app-content > ul {
        margin-left: 5rem;
        margin-top: 2rem;
    }

    .app-content > div {
        margin-top: 3rem;
    }

.home-page {
    margin-top: 4rem;
}

.users-content {
    font-size: 1.5rem;
}

.users-logo-content {
    margin-top: 5rem;
}

.dashboard-users-display {
    display: flex;
    align-items: center;
}

.dashboard-logo-info {
    display: flex;
    align-items: center;
    margin-top: 3rem;
}

    .dashboard-logo-info span {
        margin-left: 0.5rem;
        font-size: 2rem;
        color: var(--smarttrace-logo-color);
    }


/*Team*/
.team-table th:nth-child(4), .team-table td:nth-child(4), .team-table td:nth-child(5), .team-edit-table td:nth-child(3) {
    display: none;
}

.team-edit-table th:nth-child(4), .team-edit-table td:nth-child(4), .team-edit-table td:nth-child(5) {
    display: table-cell;
}

/*Documents*/
.documents-list {
    display: inline-grid;
}

    .documents-list dt {
        width: 20px;
        margin-right: 0;
    }

    .documents-list dd {
        margin-left: 0;
    }

.documents-table {
    margin-top: 0;
}

    .documents-table th {
        background-color: #fff;
        text-transform: none;
        color: #233c4c;
        border-bottom: 1px solid #233c4c;
        padding: 0;
        font-size: 1.2rem;
    }

        .documents-table th:last-child, .documents-table td:last-child {
            width: 100px;
            text-align: center;
        }

    .documents-table td:last-child {
        color: #ddd;
    }

    .documents-table td.pinned {
        color: #f18d39;
    }

    .documents-table td:first-child {
        width: 35px;
    }

    .documents-table td:nth-child(n+2) {
        padding-left: 0;
    }

    .documents-table td.selected {
        font-weight: bold;
    }

.documents-tools-col {
    background-color: #fff;
}

.company-documents-drop-zone .uppy-DragDrop-container {
    padding-top: 70px;
}

.document-details-grid {
    margin: 0 0 0.5rem 0;
}

/*Contracts*/
.contract-countries-checkbox-list {
    margin-top: 0.5rem;
    column-count: 3;
}

    .contract-countries-checkbox-list.disabled label {
        color: #ccc;
    }

    .contract-countries-checkbox-list .uppy-DragDrop-container {
        padding: 150px 50px 0 50px;
    }

.comments-area {
    height: 150px;
}

/*Autocomplete*/
.autocomplete-table th {
    background-color: transparent;
    color: #233c4c;
    font-size: 0.8rem;
    padding: 0;
}

    .autocomplete-table th:nth-child(2) {
        text-align: center;
    }

.autocomplete-table td {
    border: none;
    padding: 4px 4px 4px 0;
    overflow: hidden;
}

    .autocomplete-table td:first-child {
        padding: 4px 0;
        text-align: center;
        width: 7%;
    }

    .autocomplete-table td:nth-child(2) {
        width: auto;
        overflow: hidden;
        text-align: left;
    }

    .autocomplete-table td:nth-child(3) {
        width: 20%;
        text-align: right;
    }

    .autocomplete-table td:last-child {
        width: 10%;
        text-align: right;
        color: #009aa8;
    }

        .autocomplete-table td:last-child .icon-cancel-circled:before {
            font-size: 1.1rem;
            cursor: pointer;
        }

    .autocomplete-table td:first-child {
        text-align: left;
        padding: 4px 0.5rem;
    }

.autocomplete-table tr:not(:first-child):hover {
    background-color: aliceblue;
}

.report-table {
    table-layout: auto;
    width: 100%;
}

    .report-table tr:hover {
        background-color: aliceblue;
    }

    .report-table tr.selected {
        background-color: #72b2e5;
    }

.buddy-input {
    display: flex;
    flex-direction: row;
}

    .buddy-input :first-child {
        width: 30%;
        margin-right: 5px;
    }

    .buddy-input select {
        width: 70%;
        background-color: #c7d8e5
    }

.dialog-content select {
    background-color: #c7d8e5
}

.action-icons {
    display: flex;
    flex-direction: row;
}

.table-cell-input {
    text-indent: unset;
    resize: none;
    width: 140px;
    font-size: 0.8em;
}

    .table-cell-input.narrow {
        width: 100px;
    }

.table-cell.narrow {
    width: 60px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table-cell-input:disabled {
    background-color: inherit;
}

.flex-container {
    display: flex;
    height: 100%;
    width: 100%;
}

.flex-row-justified {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

label.info {
    margin-right: 1rem;
    font-weight: 600;
}

.autocomplete {
    display: block;
    position: relative;
}

.autocomplete-items {
    position: absolute;
    padding: 0;
    margin: 0;
    border: 1px solid #eeeeee;
    overflow: hidden;
    width: 100%;
    background-color: white;
}

.autocomplete-item {
    list-style: none;
    text-align: left;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: initial;
    padding: 4px;
    text-transform: capitalize;
}

    .autocomplete-item.is-active,
    .autocomplete-item:hover {
        background-color: #72b2e5;
        color: white;
    }

table.fixed {
    table-layout: fixed;
    width: 100%;
}

th div.composite-header {
    width: 100%;
}

.composite-header .short {
    width: 15%;
    display: inline-block;
}

.composite-header .long {
    width: 84%;
    display: inline-block;
    text-align: right;
}

/*Filtered table - autoresize filter box*/
.table-filter-items {
    max-height: 100%;
}

/*Tabs*/
.tab-container {
    display: flex;
    font-size: 1.8rem;
    justify-content: stretch;
}

    .tab-container > div {
        color: #aaa;
        border-bottom: 3px;
        padding: 5px 10px 5px 0;
        margin: 25px 10px;
        cursor: pointer;
        white-space: nowrap;
        text-wrap: none;
        text-overflow: ellipsis;
    }

        .tab-container > div.active {
            color: #000;
            border-bottom: 3px solid #009aa8;
            cursor: default;
        }

.tabs {
    display: flex;
    width: 100%;
    margin-bottom: 10px;
    justify-content: center;
    font-size: 1.8rem;
}

    .tabs .tab:first-child {
        border-radius: 10px 0 0 10px;
    }

    .tabs .tab:last-child {
        border-radius: 0 10px 10px 0;
    }

.tab {
    color: #aaa;
    background-color: #ddd;
    padding: 10px 5px;
    cursor: pointer;
}

    .tab.active {
        background-color: #F18C3A;
        color: #fff;
        cursor: default;
    }

.tabcontent {
    display: none;
    padding: 6px 12px;
}

ul.api {
    list-style-type: none;
    list-style-image: none;
    margin-top: 10px;
}

    ul.api li {
        padding: 10px 20px;
        background-color: #ddd;
        color: #aaa;
        cursor: pointer;
    }

        ul.api li.current,
        ul.api li.current a {
            background-color: #009aa8;
            color: #fff;
            cursor: default;
            transition: 0.1s;
        }

/*Territory tabs*/
.api-tab {
    overflow: hidden;
    background-color: #f1f1f1;
    margin: 0;
    color: #aaa;
}

    .api-tab button {
        background-color: inherit;
        float: left;
        border: none;
        outline: none;
        cursor: pointer;
        padding: 3px 5px;
        transition: 0.1s;
        width: 30%;
        color: #aaa;
    }

        .api-tab button:hover {
            background-color: #ddd;
        }

        .api-tab button.active {
            background-color: #009aa8;
            color: #fff;
        }

.tipified {
    padding: 3px 5px;
    border-radius: 5px;
}

    .tipified:hover {
        background-color: #fff;
        color: #6da7d5
    }

.tippy-content ul {
    list-style: none;
    margin: 0;
    max-height: 200px;
    overflow-y: overlay;
}

.tippy-content li {
    text-align: left;
}

/*Layout logos*/
.logo-image {
    content: url('../images/SmartTrace_logo.png');
}

/*Active substance page*/
.active-substance-input {
    display: block;
}

.validation-error {
    border: 1px solid #f00;
}

input.validation-error {
    border: 1px solid #f00;
}

.data-nav-item {
    width: 90px;
}

.manage-nav-item {
    width: 120px;
}

.field-validation-error, .required-field {
    color: red;
}

.warning-field {
    color: darkorange;
}

.product-substance-label {
    display: flex;
}

.disable-select-wrapper {
    pointer-events: none;
}

/*Workflow*/
.workflow-progress {
    display: flex;
    margin: 1rem 0 3rem 0;
    position: relative;
}

    .workflow-progress:before {
        width: 100%;
        position: absolute;
        top: calc(50% - 1px);
        height: 1px;
        background-color: #ccc;
        content: '';
        z-index: 0;
    }

    .workflow-progress li {
        list-style: none;
        text-align: center;
        z-index: 1;
        display: flex;
        align-items: center;
    }

        .workflow-progress li:first-child {
            margin-left: 2rem;
        }

        .workflow-progress li:last-child {
            margin-right: 2rem;
        }

    .workflow-progress + h3 {
        margin-top: 2rem;
    }

.workflow-step {
    margin: 0;
    border: 1px dashed #ccc;
    border-radius: 1rem;
    padding: 1rem;
    flex-grow: 1;
    color: #ccc;
    background-color: #fff;
}

.workflow-step-ready {
    border: 1px solid #ccc;
    background-color: #ccc;
    color: #fff;
}

.workflow-step-done {
    border: 1px solid #233c4c;
    background-color: #233c4c;
    color: #fff;
}

.workflow-step-active {
    border: 1px solid var(--main-theme-color);
    background-color: var(--main-theme-color);
    color: #fff;
    font-weight: bold;
}

.workflow-arrow {
    background-image: url('/images/bullet_grey_large.png');
    background-position: center center;
    background-repeat: no-repeat;
    width: 4rem;
    background-size: 50%;
    margin: 0 1rem;
}

.workflow-step-done + .workflow-arrow {
    background-image: url('/images/bullet_navy_large.png');
}

.hide-button {
    display: none;
}

.field-duplication-error {
    color: red;
    display: flex;
}

.submission-country {
    overflow-y: auto;
    max-height: 90px;
}

i.remove-icon {
    position: absolute;
    left: 95%;
    cursor: pointer;
    opacity: 0;
}

.country-option:hover i.remove-icon {
    opacity: 1;
}

.country-option.selectable:hover {
    cursor: pointer;
    background-color: #c8d0d0;
}

ul.no-bullets {
    list-style-type: none;
    list-style-image: none;
    margin-left: 0.3rem;
}

/*Application/submission view*/
.application-view, .submission-view {
    display: grid;
    grid-template-rows: max-content;
    grid-gap: 20px 0px;
    margin-top: 20px;
    line-height: 1.5;
}

.application-view {
    grid-template-columns: 2.5fr 5fr;
}

.submission-view {
    grid-template-columns: 1.5fr 6fr;
}

    .application-view > span:nth-child(2n), .submission-view > span:nth-child(2n) {
        background-color: #eeeeee;
        padding-left: 5px;
    }

    .application-view > span:nth-child(2n+1), .submission-view > span:nth-child(2n+1) {
        font-weight: bold;
    }

    .request-email{
        padding-top: 10px;
    }

    .concerned-member-state-checkbox-label {
        display: block;
    }

.profile-sub-menu {
    padding-top: 0.5rem;
}

.form-col:first-child {
    padding-left: 0;
}

.form-col > h5 > span {
    opacity: 0;
}

.submission-content, .document-list {
    margin-top: 1rem;
}

.submission-additional-content {
    margin-top: 2rem;
}

.notification-email {
    position: relative;
    width: fit-content;
}

    .notification-email .tooltiptext {
        visibility: hidden;
        width: 490px;
        background-color: #eeeeee;
        text-align: center;
        border-radius: 6px;
        padding: 5px 0;
        position: absolute;
        left: 110%;
    }

    .notification-email .tooltiptext::after {
        content: "";
        position: absolute;
        top: 30%;
        left: -1%;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-right: 6px solid #eeeeee;
    }


    .notification-email:hover .tooltiptext {
        visibility: visible;
    }

.clients-col {
    column-count: 2;
    margin-top: 1.2rem;    
}

.client-item {
    overflow: hidden;
}

.claims-col {
    margin-top: 1.5rem;
}

.document-version-row {
    display: block;
}

    .document-version-row > div {
        margin-right: 10px;
    }

        .document-version-row > div > select {
            vertical-align: unset;
        }

        .document-version-row > div > div {
            margin: 0 0 1rem 1rem;
        }

.document-list > .select-wrapper {
    width: 121px;
}

.document-list > div {
    margin-bottom: 1rem;
}

    .document-list > div > div {
        margin: 1rem 0 1rem 1rem;
    }

    .document-list > div > select {
        vertical-align: unset;
    }

.document-list-title {
    display: inline-block;
    min-width: 12rem;
}

.version-container {
    display: flex;
}

.version-child {
    flex: 0.5;
}

.version-view-child {
    width: 5rem;
}

.buttons-child {
    flex: 2;
}

.expand-document {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    cursor: pointer;
    word-break: break-word;
}

.document-view-list {
    display: flex;
    justify-content: space-around;
    background-color: #eeeeee;
    margin-top: 1rem;
}

.document-view-name {
    margin-left: 1rem;
    cursor: pointer;
}

.upload-info {
    display: block;
    justify-content: space-around
}

    .upload-info > div > span {
        font-weight: bold;
    }

.version-info{
    display: flex;
    align-items:center;
}

    .version-info > div {
        font-weight: bold;
    }

.text-medium-helper {
    font-size: 1.1rem;
}

.submission-export-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    margin-top: 1rem;
    text-align: left;
}

    .submission-export-container > div:nth-child(1){
        flex: 2;                                         
    }

    .submission-export-container > div:nth-child(2) {
        flex: 1.8;
    }

    .submission-export-container > div:nth-child(3) {
        flex: 2.5;
    }

    .submission-export-container > div:nth-child(4) {
        flex: 2.4;
    }

    .submission-export-container > div:nth-child(5) {
        flex: 2.2;
    }

.submission-export-column {
    display: flex;
    flex-direction: column;
    flex-basis: 100%;
}

.submission-export-checkbox {
    display: flex;
    flex-direction: column;
    flex-basis: 100%;
    flex: 1;
}

.submission-column-title {
    font-weight: bold;
    margin-bottom: 1rem;
}

.submission-export-title {
    font-size: large;
    font-weight: bold;
    text-align: center;
}

.submission-export-buttons {
    margin-top: 40px;
}

.common-state-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 1rem 0 1rem;
    border-radius: 25px;
    height: 2rem;
}

    .common-state-container[stateId="1"] {
        background-color: var(--main-theme-color);
    }

    .common-state-container[stateId="2"] {
        background-color: #09442A;
    }

    .common-state-container[stateId="3"] {
        background-color: #0A3150;
    }

.common-state {
    color: white;
    font-size: .75rem;
    font-weight: bold;
    padding: 8px 16px;
    float: right;
}

.common-state-button {
    text-align: left;
    padding: 0.8rem;
    color: #fff;
    font-size: 1rem;
    display: block;
    cursor: pointer;
    margin-bottom: 1rem;
}

.common-state-main-button {
    z-index: 5;
    position: absolute;
    background-color: rgb(122, 139, 152);
    margin-left: 0;
}

/* Login */

.login-box .app-title {
    font-size: 2rem;
}

.login-box .button {
    width: 100%;
}

/*Overriding is-active when multiple table filter is used and icon-button-back:before. Update into Shared.Web after new version >= (24.09.2021)*/

.table-filter-items .table-filter-item.is-active {
    background-color: #c6cfcf;
}

a.button.secondary.icon-button-back:before {
    top: 0;
}

/*******/