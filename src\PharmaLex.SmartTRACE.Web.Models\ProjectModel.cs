﻿using AutoMapper;
using PharmaLex.SmartTRACE.Entities;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class ProjectModel : IModel
    {
        public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        [Required]
        [MaxLength(32)]
        public string Code { get; set; }
        [Required]
        [MaxLength(32)]
        public string OpportunityNumber { get; set; }
        public int ClientId { get; set; }
        public string ClientName { get; set; }
    }

    public class ProjectMappingProfile : Profile
    {
        public ProjectMappingProfile()
        {
            this.CreateMap<Project, ProjectModel>()
                .ForMember(d => d.ClientName, s => s.MapFrom(x => x.Client.Name));
            this.CreateMap<ProjectModel, Project>();

        }
    }
}
