﻿using NPOI.SS.Formula.Functions;
using PharmaLex.SmartTRACE.Web.Helpers;
using System.Linq.Expressions;
using System.Reflection;

namespace PharmaLex.SmartTRACE.Web.HelpersTests
{


    public class ExpressionParameterReplacerTests
    {
        [Fact]
        public void ExpressionParameterReplacer_Returns_Not_Null()
        {
            //Arrange
            Expression<Func<int, bool>> fromParameters = x => x > 5;
            Expression<Func<int, bool>> toParameters = x => x < 10;
            //Act
            var result = new ExpressionParameterReplacer(fromParameters.Parameters, toParameters.Parameters);
            //Assert
            Assert.NotNull(result);
        }
        [Fact]
        public void VisitParameter_Returns_Not_Null()
        {
            //Arrange
            Type t = typeof(int);
            ParameterExpression node = Expression.Parameter(t, "x");
            Expression<Func<int, bool>> fromParameters = x => x > 5;
            Expression<Func<int, bool>> toParameters = x => x < 10;
            var expressionparameterreplacer = new ExpressionParameterReplacer(fromParameters.Parameters, toParameters.Parameters);
            var methodinfo = typeof(ExpressionParameterReplacer).GetMethod("VisitParameter", BindingFlags.Instance | BindingFlags.NonPublic);
            //Act
            var result = methodinfo?.Invoke(expressionparameterreplacer, new object[] { node });
            //Assert
            Assert.NotNull(result);

        }
    }
}
