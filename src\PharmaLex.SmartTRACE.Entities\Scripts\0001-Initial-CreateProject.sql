﻿CREATE TRIGGER [dbo].[Project_Insert] ON [dbo].[Project]
FOR INSERT AS
INSERT INTO [Audit].[Project_Audit]
SELECT 'I', [Id], [Name], [Code], [OpportunityNumber], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Project_Update] ON [dbo].[Project]
FOR UPDATE AS
INSERT INTO [Audit].[Project_Audit]
SELECT 'U', [Id], [Name], [Code], [OpportunityNumber], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Project_Delete] ON [dbo].[Project]
FOR DELETE AS
INSERT INTO [Audit].[Project_Audit]
SELECT 'D', [Id], [Name], [Code], [OpportunityNumber], [ClientId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO