﻿using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Models.ViewModels;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class ApiPagedListResultTests
    {
        [Fact]
        public void ApiPagedListResult_Get_SetValue()
        {
            //Arrange
            SubmissionTableViewModel subTableModel = new SubmissionTableViewModel()
            {
                Id = 1,
                ApplicationNumber = "11",
                ClientName = "Client1"
            };
            List<SubmissionTableViewModel> subList = new List<SubmissionTableViewModel> { subTableModel };

            ApiPagedListResult<SubmissionTableViewModel> apiModel = new ApiPagedListResult<SubmissionTableViewModel>
          (
             items: subList,
              offset: 0,
              limit: 25,
              totalItemCount: subList.Count,
              filteredCount: subList.Count
            );
            apiModel.Data[0].ClientName = "Client1";
            apiModel.Paging.FilteredCount = 1;
            //Assert
            Assert.NotNull(apiModel);
        }
        [Fact]
        public void ApiPagedListResults_Get_SetValue()
        {
            //Arrange
            SubmissionTableViewModel subTableModel = new SubmissionTableViewModel()
            {
                Id = 1,
                ApplicationNumber = "11",
                ClientName = "Client1"
            };
            List<SubmissionTableViewModel> subList = new List<SubmissionTableViewModel> { subTableModel };
            PagingInfo paginationInfo = new PagingInfo();
            paginationInfo.TotalItemCount = 1;
            ApiPagedListResult<SubmissionTableViewModel> apiModel = new ApiPagedListResult<SubmissionTableViewModel>
          (
             data: subList,
              paging: paginationInfo
            );
            //Assert
            Assert.NotNull(apiModel);
        }
    }
}
