﻿using PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.StudyReport
{
    public partial class studyidentifier: CtdSectionBase
    {
        
    }

    public partial class category : CtdSectionBase
    {
        public override string ToString()
        {
            if (string.IsNullOrEmpty(infotype))
                return $"{name}:{Value}";

            return $"[{infotype}]{name}:{Value}";
        }
    }

    public partial class studydocument : CtdSectionBase
    {
        
    }

    public partial class contentblock : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes => new List<CtdSectionBase>(this.Items.OfType<contentblock>());        
    }

    public partial class property : CtdSectionBase
    {
        
    }

    public partial class filetag : CtdSectionBase
    {
        
    }

    public partial class doccontent : CtdSectionBase
    {
        
    }

    public partial class study : CtdSectionBase
    {
        
    }
}
