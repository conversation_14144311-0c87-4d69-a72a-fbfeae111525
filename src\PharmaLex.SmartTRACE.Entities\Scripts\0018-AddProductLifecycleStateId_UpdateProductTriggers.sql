﻿ALTER TRIGGER[dbo].[Product_Insert] ON[dbo].[Product] 
FOR INSERT AS
INSERT INTO [Audit].[Product_Audit] ([AuditAction], [Id], [Name], [DosageFormId], [Strength], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LifecycleStateId])
SELECT 'I', [Id], [Name], [DosageFormId], [Strength], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LifecycleStateId] FROM [Inserted]
GO

ALTER TRIGGER[dbo].[Product_Update] ON[dbo].[Product]
FOR UPDATE AS
INSERT INTO [Audit].[Product_Audit] ([AuditAction], [Id], [Name], [DosageFormId], [Strength], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LifecycleStateId])
SELECT 'U', [Id], [Name], [DosageFormId], [Strength], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LifecycleStateId] FROM [Inserted]
GO

ALTER TRIGGER[dbo].[Product_Delete] ON[dbo].[Product]
FOR DELETE AS
INSERT INTO [Audit].[Product_Audit] ([AuditAction], [Id], [Name], [DosageFormId], [Strength], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LifecycleStateId])
SELECT 'D', [Id], [Name], [DosageFormId], [Strength], [ClientId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()), [LifecycleStateId] FROM [Deleted]
GO