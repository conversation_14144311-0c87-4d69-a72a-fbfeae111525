﻿ALTER TRIGGER [dbo].[DocshifterDocumentFile_Insert] ON [dbo].[DocshifterDocumentFile]
FOR INSERT AS
INSERT INTO [Audit].[DocshifterDocumentFile_Audit]
SELECT 'I', [Id], [FileName], [Converted], [DocumentId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [StateId] FROM [Inserted]
GO

ALTER TRIGGER [dbo].[DocshifterDocumentFile_Update] ON [dbo].[DocshifterDocumentFile]
FOR UPDATE AS
INSERT INTO [Audit].[DocshifterDocumentFile_Audit]
SELECT 'U', [Id], [FileName], [Converted], [DocumentId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [StateId] FROM [Inserted]
GO

ALTER TRIGGER [dbo].[DocshifterDocumentFile_Delete] ON [dbo].[DocshifterDocumentFile]
FOR DELETE AS
INSERT INTO [Audit].[DocshifterDocumentFile_Audit]
SELECT 'D', [Id], [FileName], [Converted], [DocumentId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()), [StateId] FROM [Deleted]
GO