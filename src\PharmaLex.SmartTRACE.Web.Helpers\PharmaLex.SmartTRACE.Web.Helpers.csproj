﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="PharmaLex.Authentication.B2C" Version="8.0.0.203" />
    <PackageReference Include="PharmaLex.AzureCloudStorage" Version="8.0.0.174" />
    <PackageReference Include="PharmaLex.Caching.Data" Version="8.0.0.202" />
    <PackageReference Include="PharmaLex.Caching" Version="8.0.0.202" />
	<PackageReference Include="PharmaLex.Office" Version="8.0.0.125" />
    <PackageReference Include="SendGrid" Version="9.29.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.SmartTRACE.Entities\PharmaLex.SmartTRACE.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.SmartTRACE.Web.Models\PharmaLex.SmartTRACE.Web.Models.csproj" />
  </ItemGroup>

</Project>
