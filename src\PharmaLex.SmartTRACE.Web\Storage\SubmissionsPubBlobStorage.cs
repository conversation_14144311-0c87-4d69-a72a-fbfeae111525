﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Pharmalex.AzureCloudStorage;
using Pharmalex.AzureCloudStorage.Interfaces;
using PharmaLex.Authentication.B2C;
using PharmaLex.SmartTRACE.Web.CommonFnctions;
using PharmaLex.SmartTRACE.Web.DataFactory;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Storage
{
    public interface ISubmissionPubBlobStorage : IAzureBlobClient { }

    public class SubmissionPubBlobStorage : AzureBlobClient, ISubmissionPubBlobStorage
    {
        public SubmissionPubBlobStorage(IOptions<StorageSettings> settings) : base(new AzureBlobClientConfiguration { StorageAccountName = settings.Value.PubAccount, ContainerName = settings.Value.Container, VisualStudioTenantId = settings.Value.TenantId }) { }
    }

    public interface ISubmissionPubBlobContainer : IAzureBlobContainerClient
    {
        Task<string> Pub_GetSequenceUploadLinkAsync(string fileName, string clientId = null, string applicationId = null, 
                                                string submissionUniqueId = null, string documentTypeId = null, string version = null);
    }

    public class SubmissionPubBlobContainer : AzureBlobContainerClient, ISubmissionPubBlobContainer
    {
        private readonly IAzureDataFactoryManagementClient adfClient;

        public SubmissionPubBlobContainer(
            ISubmissionPubBlobStorage storage,
            IAzureDataFactoryManagementClient adfClient,
            IOptions<DataFactoryPipelineSettings> pipelineSettings,
            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration)
            : base(storage)
        {
            this.adfClient = adfClient;
        }
        public async Task<string> Pub_GetSequenceUploadLinkAsync(string fileName, 
            string clientId = null, 
            string applicationId = null, 
            string submissionUniqueId = null, 
            string documentTypeId = null,
            string version = null)
        {
            string uploadFilePath = BlobCommonFunctions.GetUploadLink(adfClient, fileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);
            return (await this.GetBlobClientAsync(uploadFilePath)).GetUploadLink();
        }
    }
}
