﻿SET IDENTITY_INSERT [dbo].[PicklistData] ON 

INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (1, N'Human Use', 1, CAST(N'2020-11-27T11:04:50.993' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:50.993' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (2, N'Veterinary Use', 1, CAST(N'2020-11-27T11:04:51.000' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.000' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (3, N'Human and Veterinary Use', 1, CAST(N'2020-11-27T11:04:51.000' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.000' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (4, N'Investigational', 2, CAST(N'2020-11-27T11:04:51.000' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.000' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (5, N'Authorised', 2, CAST(N'2020-11-27T11:04:51.000' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.000' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (47, N'Tablet', 3, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (49, N'Cream', 3, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (62, N'Mutual Recognition Procedure', 5, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (63, N'National Procedure', 5, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (64, N'Decentralised Procedure', 5, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (65, N'Centralised Procedure', 5, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (66, N'Publish & Submit to Health Authority', 6, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (67, N'Publish & Send to Client', 6, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (68, N'Submit to Health Authority', 6, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (69, N'ASEAN', 7, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (70, N'eCTD', 7, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (71, N'NeeS', 7, CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (72, N'Not Specified', 7, CAST(N'2020-11-27T11:04:51.020' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.020' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (73, N'Other', 7, CAST(N'2020-11-27T11:04:51.020' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.020' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (74, N'Paper', 7, CAST(N'2020-11-27T11:04:51.020' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.020' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (75, N'Unknown', 7, CAST(N'2020-11-27T11:04:51.020' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.020' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (76, N'VNeeS', 7, CAST(N'2020-11-27T11:04:51.020' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:51.020' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (77, N'Client or Internal Deadline', 11, CAST(N'2020-11-27T11:04:55.163' AS DateTime), N'<EMAIL>', CAST(N'2020-12-08T15:49:34.207' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (78, N'Health Authority Deadline', 11, CAST(N'2020-11-27T11:04:55.167' AS DateTime), N'<EMAIL>', CAST(N'2020-12-08T15:49:50.563' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (80, N'Small', 12, CAST(N'2020-11-27T11:04:55.167' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.167' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (81, N'Medium', 12, CAST(N'2020-11-27T11:04:55.167' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.167' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (82, N'Large', 12, CAST(N'2020-11-27T11:04:55.167' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.167' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (83, N'Marketing Authorization Application', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:45:19.130' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (84, N'Variation Type IA', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (85, N'Variation Type IAIN', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (86, N'Variation Type IB', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (87, N'Variation Type II', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (88, N'Variation National', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (89, N'Extension', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (90, N'Repeat Use Procedure', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (91, N'Periodic Safety Update Report', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (92, N'PSUR Single Assessment Procedure', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (93, N'Risk Management Plan', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (94, N'Renewal', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (95, N'PAM', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:26:26.950' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (103, N'PASS', 8, CAST(N'2020-11-27T11:04:55.680' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:27:12.297' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (105, N'Active Substance Master File', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (106, N'Plasma Master File', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (107, N'Referral', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:24:50.640' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (117, N'Annual Reassessment', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (118, N'Urgent Safety Restriction', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (119, N'Clinical Data - Redacted', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (120, N'Clinical Data - Final Version', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (121, N'Paediatric Submission 7,8 or 30', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (122, N'Paediatric Submission 29', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (123, N'Paediatric Submission 45', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (124, N'Article 58', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (125, N'Notification 61(3)', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (126, N'Transfer of Marketing Authorization', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:45:28.520' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (127, N'Lifting a Suspension', 8, CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.683' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (128, N'Withdrawal', 8, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (129, N'Certificate of Suitability (CEP)', 8, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (130, N'None', 8, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (131, N'Initial', 9, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (132, N'Validation Response', 9, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (133, N'Response', 9, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (134, N'Additional Information', 9, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (135, N'Closing', 9, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (136, N'Consolidating', 9, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (137, N'Corrigendum', 9, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (138, N'Reformat', 9, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (139, N'Single', 10, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (140, N'Grouping', 10, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (141, N'Worksharing', 10, CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:55.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (142, N'PLX - US', 13, CAST(N'2020-12-08T14:13:57.930' AS DateTime), N'<EMAIL>', CAST(N'2020-12-08T14:13:57.930' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (143, N'PLX - BG', 13, CAST(N'2020-12-08T14:13:58.033' AS DateTime), N'<EMAIL>', CAST(N'2020-12-08T14:13:58.033' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (144, N'PLX - ES', 13, CAST(N'2020-12-08T14:13:58.033' AS DateTime), N'<EMAIL>', CAST(N'2020-12-08T14:13:58.033' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (145, N'PLX - DE', 13, CAST(N'2020-12-08T14:13:58.033' AS DateTime), N'<EMAIL>', CAST(N'2020-12-08T14:13:58.033' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (146, N'PLX - IN', 13, CAST(N'2020-12-08T14:13:58.033' AS DateTime), N'<EMAIL>', CAST(N'2020-12-08T14:13:58.033' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (147, N'PLX - GE', 13, CAST(N'2020-12-08T14:13:58.033' AS DateTime), N'<EMAIL>', CAST(N'2020-12-08T14:13:58.033' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (149, N'Solution for cardioplegia/organ preservation', 3, CAST(N'2020-12-09T10:35:22.500' AS DateTime), N'<EMAIL>', CAST(N'2020-12-09T10:35:22.500' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (150, N'Soluble Tablet', 3, CAST(N'2020-12-09T16:20:15.417' AS DateTime), N'<EMAIL>', CAST(N'2020-12-09T16:20:15.417' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (151, N'Inhalation Vapour Liquid', 3, CAST(N'2020-12-10T09:47:54.333' AS DateTime), N'<EMAIL>', CAST(N'2020-12-10T09:47:54.333' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (152, N'PLx Bulgaria', 14, CAST(N'2020-12-11T15:18:59.810' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:18:59.810' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (153, N'PLx France', 14, CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (154, N'PLx Germany', 14, CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (155, N'PLx India', 14, CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (156, N'PLx Italy', 14, CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (157, N'PLx Nordics', 14, CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (158, N'PLx Spain', 14, CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (159, N'PLx UK', 14, CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (160, N'PLx US', 14, CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:18:59.813' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (161, N'Abbreviated Drug Application', 4, CAST(N'2020-12-11T15:24:36.160' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:24:36.160' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (162, N'Active Substance Master File', 4, CAST(N'2020-12-11T15:24:36.180' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:24:36.180' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (163, N'Biologics License Application', 4, CAST(N'2020-12-11T15:24:36.190' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:24:36.190' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (164, N'Clinical Trial - Competent Authority', 4, CAST(N'2020-12-11T15:24:36.197' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:24:36.197' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (165, N'Clinical Trial - Ethics Committee', 4, CAST(N'2020-12-11T15:24:36.200' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:24:36.200' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (166, N'Drug Master File', 4, CAST(N'2020-12-11T15:24:36.207' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:24:36.207' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (167, N'Investigational New Drug ', 4, CAST(N'2020-12-11T15:24:36.213' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:24:36.213' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (168, N'Marketing Authorization', 4, CAST(N'2020-12-11T15:24:36.217' AS DateTime), N'<EMAIL>', CAST(N'2020-12-18T12:22:16.303' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (170, N'New Drug Application', 4, CAST(N'2020-12-11T15:24:36.233' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:24:36.233' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (172, N'Plasma Master File', 4, CAST(N'2020-12-11T15:24:36.247' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:24:36.247' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (173, N'Scientific Opinion', 4, CAST(N'2020-12-11T15:24:36.253' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:24:36.253' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (174, N'Vaccine Antigen Master File', 4, CAST(N'2020-12-11T15:24:36.260' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:24:36.260' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (175, N'Annual Report', 8, CAST(N'2020-12-11T15:25:44.920' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:44.920' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (176, N'CMC Supplement', 8, CAST(N'2020-12-11T15:25:44.943' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:44.943' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (177, N'Efficacy Supplement', 8, CAST(N'2020-12-11T15:25:44.973' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:44.973' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (178, N'IND Safety Reports', 8, CAST(N'2020-12-11T15:25:44.980' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:44.980' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (179, N'Labeling Supplement', 8, CAST(N'2020-12-11T15:25:44.987' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:44.987' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (180, N'Original Application', 8, CAST(N'2020-12-11T15:25:44.990' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:44.990' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (181, N'Periodic Safety Reports', 8, CAST(N'2020-12-11T15:25:44.997' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:44.997' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (182, N'Postmarketing Requirements or Postmarketing Commitments', 8, CAST(N'2020-12-11T15:25:45.003' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.003' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (183, N'Product Correspondense', 8, CAST(N'2020-12-11T15:25:45.010' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.010' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (184, N'Promotional Labeling Advertising', 8, CAST(N'2020-12-11T15:25:45.017' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.017' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (185, N'REMS Supplement', 8, CAST(N'2020-12-11T15:25:45.040' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.040' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (186, N'New Application - New Active Substance', 8, CAST(N'2020-12-11T15:25:45.047' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.047' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (187, N'New Application - Known Active Substance', 8, CAST(N'2020-12-11T15:25:45.053' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.053' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (188, N'New Application - Co-Marketing', 8, CAST(N'2020-12-11T15:25:45.073' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.073' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (189, N'New Application - Parallel Import', 8, CAST(N'2020-12-11T15:25:45.080' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.080' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (190, N'Follow-up Measure', 8, CAST(N'2020-12-11T15:25:45.090' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.090' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (193, N'Drug Master File', 8, CAST(N'2020-12-11T15:25:45.137' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.137' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (194, N'Orphan Fast Track', 8, CAST(N'2020-12-11T15:25:45.143' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.143' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (195, N'Reformat', 8, CAST(N'2020-12-11T15:25:45.170' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.170' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (196, N'Supplemental Information', 8, CAST(N'2020-12-11T15:25:45.177' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.177' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (197, N'Corrigendum', 8, CAST(N'2020-12-11T15:25:45.183' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.183' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (198, N'Advice', 8, CAST(N'2020-12-11T15:25:45.190' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.190' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (199, N'Extension Submission', 8, CAST(N'2020-12-11T15:25:45.197' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.197' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (200, N'MAA - Biological', 8, CAST(N'2020-12-11T15:25:45.203' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.203' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (201, N'MAA - Generic (Multisource)', 8, CAST(N'2020-12-11T15:25:45.210' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.210' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (202, N'MAA - New chemical Entity', 8, CAST(N'2020-12-11T15:25:45.217' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.217' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (203, N'MAA - Radiopharmaceuticals', 8, CAST(N'2020-12-11T15:25:45.223' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.223' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (204, N'Reformatting', 8, CAST(N'2020-12-11T15:25:45.230' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.230' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (211, N'New Application - New Chemical Entity - Pharmaceutical', 8, CAST(N'2020-12-11T15:25:45.277' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.277' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (212, N'New Application - New Chemical Entity - Biological', 8, CAST(N'2020-12-11T15:25:45.287' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.287' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (213, N'New Application - Multisource', 8, CAST(N'2020-12-11T15:25:45.293' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.293' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (214, N'New Application - Biosimilar', 8, CAST(N'2020-12-11T15:25:45.300' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.300' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (215, N'New Application - Line Extension', 8, CAST(N'2020-12-11T15:25:45.303' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.303' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (216, N'New Application - Call-up', 8, CAST(N'2020-12-11T15:25:45.310' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.310' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (217, N'New Application - Complementary Medicines', 8, CAST(N'2020-12-11T15:25:45.317' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.317' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (218, N'Pre-Reg Response - Pharmaceutical and Analyatical', 8, CAST(N'2020-12-11T15:25:45.320' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.320' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (219, N'Pre-Reg Response - Clinical', 8, CAST(N'2020-12-11T15:25:45.327' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.327' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (220, N'Pre-Reg Response - Proprietary Name', 8, CAST(N'2020-12-11T15:25:45.333' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.333' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (221, N'Pre-Reg Response - Scheduling', 8, CAST(N'2020-12-11T15:25:45.340' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.340' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (222, N'Pre-Reg Response - Inspectorate', 8, CAST(N'2020-12-11T15:25:45.347' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.347' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (223, N'Pre-Reg Response - Biological committee response', 8, CAST(N'2020-12-11T15:25:45.350' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.350' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (224, N'Pre-Reg Response - Complementary Medicines', 8, CAST(N'2020-12-11T15:25:45.357' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.357' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (225, N'Pre-Reg Response - Response to Authority (Council) Resolutions', 8, CAST(N'2020-12-11T15:25:45.363' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.363' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (226, N'Post Registration - Inspectorate', 8, CAST(N'2020-12-11T15:25:45.370' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.370' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (227, N'Post Registration - Pharmaceutical and Analytical', 8, CAST(N'2020-12-11T15:25:45.373' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.373' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (228, N'Post Registration - Clinical', 8, CAST(N'2020-12-11T15:25:45.380' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.380' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (229, N'Post Registration - Proprietary Name Change', 8, CAST(N'2020-12-11T15:25:45.387' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.387' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (230, N'Post Registration - Updates following Proprietary Name Change', 8, CAST(N'2020-12-11T15:25:45.390' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.390' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (231, N'Post Registration - Applicant Transfer', 8, CAST(N'2020-12-11T15:25:45.397' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.397' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (232, N'Post Registration - Biologicals and Biosimilars', 8, CAST(N'2020-12-11T15:25:45.400' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.400' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (233, N'Post Registration - Complementary Medicines', 8, CAST(N'2020-12-11T15:25:45.407' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.407' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (234, N'Response to Post Registration - Inspectorate', 8, CAST(N'2020-12-11T15:25:45.413' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.413' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (235, N'Response to Post Registration - Pharmaceutical and Analytical', 8, CAST(N'2020-12-11T15:25:45.420' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.420' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (236, N'Response to Post Registration - Clinical', 8, CAST(N'2020-12-11T15:25:45.423' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.423' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (237, N'Response to Post Registration - Proprietary Name Change', 8, CAST(N'2020-12-11T15:25:45.430' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.430' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (238, N'Response to Post Registration - Updates following Proprietary Name Change', 8, CAST(N'2020-12-11T15:25:45.437' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.437' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (239, N'Response to Post Registration - Applicant Transfer', 8, CAST(N'2020-12-11T15:25:45.440' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.440' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (240, N'Response to Post Registration - Biologicals and Biosimilars', 8, CAST(N'2020-12-11T15:25:45.447' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.447' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (241, N'Response to Post Registration - Complementary Medicines', 8, CAST(N'2020-12-11T15:25:45.450' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.450' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (242, N'Cancellation', 8, CAST(N'2020-12-11T15:25:45.457' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.457' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (243, N'Baseline', 8, CAST(N'2020-12-11T15:25:45.463' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.463' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (244, N'CEP application - cep', 8, CAST(N'2020-12-11T15:25:45.470' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.470' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (246, N'MAA - Originator Drug', 8, CAST(N'2020-12-11T15:25:45.480' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.480' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (247, N'MAA - Originator Vitamin Drug', 8, CAST(N'2020-12-11T15:25:45.490' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.490' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (248, N'MAA - Originator Narcotics', 8, CAST(N'2020-12-11T15:25:45.497' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.497' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (249, N'MAA - Generic Drug', 8, CAST(N'2020-12-11T15:25:45.503' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.503' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (250, N'MAA - Generic Vitamin Drug', 8, CAST(N'2020-12-11T15:25:45.510' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.510' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (251, N'MAA - Generic Narcotics', 8, CAST(N'2020-12-11T15:25:45.530' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.530' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (252, N'MAA - New Drug', 8, CAST(N'2020-12-11T15:25:45.533' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.533' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (253, N'MAA - New Vitamin Drug', 8, CAST(N'2020-12-11T15:25:45.540' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.540' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (254, N'MAA - New Narcotics ', 8, CAST(N'2020-12-11T15:25:45.547' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.547' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (255, N'MAA - Radiopharmaceutical Ready-for-use Radioactive Product', 8, CAST(N'2020-12-11T15:25:45.553' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.553' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (256, N'MAA - Radiopharmaceutical Non-Radioactive Components (kits)', 8, CAST(N'2020-12-11T15:25:45.557' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.557' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (257, N'MAA - Herbal Drug', 8, CAST(N'2020-12-11T15:25:45.563' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.563' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (258, N'MAA - Biological Drug', 8, CAST(N'2020-12-11T15:25:45.570' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.570' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (259, N'MAA - Biological Vaccine', 8, CAST(N'2020-12-11T15:25:45.577' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.577' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (260, N'MAA - Biological Blood Product', 8, CAST(N'2020-12-11T15:25:45.580' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.580' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (261, N'MAA - Biological Allergen', 8, CAST(N'2020-12-11T15:25:45.587' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.587' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (262, N'MAA - Biological Bio-similar', 8, CAST(N'2020-12-11T15:25:45.593' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.593' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (263, N'MAA - Value Added Medicine', 8, CAST(N'2020-12-11T15:25:45.600' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.600' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (264, N'MAA - Veterinary Drug', 8, CAST(N'2020-12-11T15:25:45.610' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.610' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (266, N'Periodic Benefit-Risk Evaluation Report', 8, CAST(N'2020-12-11T15:25:45.620' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.620' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (271, N'Technical Committee Approval', 8, CAST(N'2020-12-11T15:25:45.650' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.650' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (272, N'Registration Department Approval', 8, CAST(N'2020-12-11T15:25:45.657' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.657' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (273, N'Notification without pricing - VAR n1', 8, CAST(N'2020-12-11T15:25:45.663' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.663' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (274, N'Notification with pricing - VAR n2', 8, CAST(N'2020-12-11T15:25:45.670' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.670' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (275, N'A - New Chemical Entity', 8, CAST(N'2020-12-11T15:25:45.677' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.677' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (276, N'A - New Salt or Ester of Existing Active Ingredient', 8, CAST(N'2020-12-11T15:25:45.680' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (277, N'A - Similar Biological Medicinal Product', 8, CAST(N'2020-12-11T15:25:45.687' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.687' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (278, N'B - New Combination', 8, CAST(N'2020-12-11T15:25:45.693' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.693' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (279, N'C - Extension of Indication', 8, CAST(N'2020-12-11T15:25:45.697' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.697' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (280, N'D - New Generic Medicine', 8, CAST(N'2020-12-11T15:25:45.700' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.700' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (281, N'F - Major Variation - New Strength', 8, CAST(N'2020-12-11T15:25:45.707' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.707' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (282, N'F - Major Variation - New Dosage Form', 8, CAST(N'2020-12-11T15:25:45.713' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.713' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (283, N'F - Major Variation - New Route of Administration', 8, CAST(N'2020-12-11T15:25:45.720' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.720' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (284, N'F - Major Variation - Change in Patient Group', 8, CAST(N'2020-12-11T15:25:45.723' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.723' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (285, N'F - Major Variation - Change in Dosage', 8, CAST(N'2020-12-11T15:25:45.730' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.730' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (286, N'J - PI Change Requiring Evaluation', 8, CAST(N'2020-12-11T15:25:45.737' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.737' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (287, N'G - Minor Variation - Change of Formulation', 8, CAST(N'2020-12-11T15:25:45.740' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.740' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (288, N'G - Minor Variation - New Container Type', 8, CAST(N'2020-12-11T15:25:45.747' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.747' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (289, N'H  Minor Variation, Not Resulting in a New Register Entry', 8, CAST(N'2020-12-11T15:25:45.753' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.753' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (290, N'E - Additional Tradename', 8, CAST(N'2020-12-11T15:25:45.760' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T16:40:04.707' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (291, N'Change of Tradename', 8, CAST(N'2020-12-11T15:25:45.763' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.763' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (292, N'9D(1) - Correction of Register Entry', 8, CAST(N'2020-12-11T15:25:45.770' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.770' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (293, N'9D(2) - Safety Related Request', 8, CAST(N'2020-12-11T15:25:45.777' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.777' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (294, N'9D(3) - Change to PI (not J)', 8, CAST(N'2020-12-11T15:25:45.787' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.787' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (295, N'Provisional registration - TGA initiated variation', 8, CAST(N'2020-12-11T15:25:45.790' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T16:42:29.120' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (296, N'Notification', 8, CAST(N'2020-12-11T15:25:45.797' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.797' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (297, N'OTC - N1', 8, CAST(N'2020-12-11T15:25:45.803' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.803' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (298, N'OTC - N2', 8, CAST(N'2020-12-11T15:25:45.810' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.810' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (299, N'OTC - N3', 8, CAST(N'2020-12-11T15:25:45.813' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.813' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (300, N'OTC - N4', 8, CAST(N'2020-12-11T15:25:45.820' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.820' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (301, N'OTC - N5', 8, CAST(N'2020-12-11T15:25:45.827' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.827' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (302, N'CN', 8, CAST(N'2020-12-11T15:25:45.830' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.830' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (303, N'OTC - C1', 8, CAST(N'2020-12-11T15:25:45.837' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.837' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (304, N'OTC - C2', 8, CAST(N'2020-12-11T15:25:45.843' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.843' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (305, N'OTC - C3', 8, CAST(N'2020-12-11T15:25:45.850' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.850' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (306, N'OTC - C4', 8, CAST(N'2020-12-11T15:25:45.853' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.853' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (307, N'Ingredient - New', 8, CAST(N'2020-12-11T15:25:45.860' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.860' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (308, N'Ingredient - Variation', 8, CAST(N'2020-12-11T15:25:45.867' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.867' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (309, N'Complementary Medicine - New', 8, CAST(N'2020-12-11T15:25:45.870' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.870' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (310, N'Complementary Medicine - Variation', 8, CAST(N'2020-12-11T15:25:45.877' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.877' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (311, N'Biologicals - Class 1', 8, CAST(N'2020-12-11T15:25:45.883' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.883' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (312, N'Biologicals - Class 2', 8, CAST(N'2020-12-11T15:25:45.887' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.887' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (313, N'Biologicals - Class 3', 8, CAST(N'2020-12-11T15:25:45.893' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.893' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (314, N'Biologicals - Class 4', 8, CAST(N'2020-12-11T15:25:45.900' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.900' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (315, N'Biologicals - Variation', 8, CAST(N'2020-12-11T15:25:45.903' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.903' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (316, N'ASMF / DMF', 8, CAST(N'2020-12-11T15:25:45.910' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.910' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (318, N'Tissue Master File (TMF)', 8, CAST(N'2020-12-11T15:25:45.923' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.923' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (319, N'Biological Master File (BMF)', 8, CAST(N'2020-12-11T15:25:45.930' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.930' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (322, N'Extension of provisional registration', 8, CAST(N'2020-12-11T15:25:45.947' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.947' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (323, N'Change of Sponsor', 8, CAST(N'2020-12-11T15:25:45.953' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.953' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (326, N'Duplicate', 8, CAST(N'2020-12-11T15:25:45.970' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.970' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (327, N'Clinical Trial Application', 8, CAST(N'2020-12-11T15:25:45.973' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.973' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (328, N'Product Withdrawal', 8, CAST(N'2020-12-11T15:25:45.980' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.980' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (329, N'Undefined Regulatory Activity', 8, CAST(N'2020-12-11T15:25:45.987' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:25:45.987' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (330, N'Original', 9, CAST(N'2020-12-11T15:26:34.170' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:26:34.170' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (331, N'PreSubmission', 9, CAST(N'2020-12-11T15:26:34.177' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:26:34.177' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (332, N'Application', 9, CAST(N'2020-12-11T15:26:34.183' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:26:34.183' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (333, N'Amendment', 9, CAST(N'2020-12-11T15:26:34.190' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:26:34.190' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (334, N'Resubmission', 9, CAST(N'2020-12-11T15:26:34.193' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:26:34.193' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (335, N'Report', 9, CAST(N'2020-12-11T15:26:34.200' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:26:34.200' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (336, N'Correspondence', 9, CAST(N'2020-12-11T15:26:34.207' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:26:34.207' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (337, N'Correction', 9, CAST(N'2020-12-11T15:26:34.210' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T15:26:34.210' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (338, N'Salt', 3, CAST(N'2020-12-16T09:42:26.757' AS DateTime), N'<EMAIL>', CAST(N'2020-12-16T09:42:26.757' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (339, N'Film Coated Tablet', 3, CAST(N'2020-12-16T14:18:15.563' AS DateTime), N'<EMAIL>', CAST(N'2020-12-16T14:18:15.563' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (340, N'Supplemental Information', 9, CAST(N'2020-12-17T16:36:25.760' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T16:36:25.760' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (341, N'Abbreviated New Drug Submission (ANDS)', 8, CAST(N'2020-12-17T19:35:57.903' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:35:57.903' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (342, N'Clinical Trial Application Amendement (CTA-A)', 8, CAST(N'2020-12-17T19:36:10.073' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:36:10.073' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (343, N'Development Safety Update Report (DSUR)', 8, CAST(N'2020-12-17T19:36:22.153' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:36:22.153' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (344, N'Drug Identification Number - Biologics (DINB)', 8, CAST(N'2020-12-17T19:36:34.897' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:36:34.897' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (345, N'Drug Identification Number Application (DINA)', 8, CAST(N'2020-12-17T19:36:44.897' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:36:44.897' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (346, N'Drug Master File Type I (DMF Type I)', 8, CAST(N'2020-12-17T19:36:54.493' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:36:54.493' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (347, N'Drug Master File Type II (DMF Type II)', 8, CAST(N'2020-12-17T19:37:02.733' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:37:02.733' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (348, N'Drug Master File Type III (DMF Type III)', 8, CAST(N'2020-12-17T19:37:11.877' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:37:11.877' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (349, N'Drug Master File Type IV (DMF Type IV)', 8, CAST(N'2020-12-17T19:37:31.583' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:37:31.583' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (350, N'Extraordinary Use New Drug Submission (EU NDS)', 8, CAST(N'2020-12-17T19:37:41.990' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:37:41.990' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (351, N'Extraordinary Use Supplement to a New Drug Submission (EU SNDS)', 8, CAST(N'2020-12-17T19:37:52.247' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:37:52.247' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (352, N'New Drug Submission (NDS)', 8, CAST(N'2020-12-17T19:38:01.850' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:38:01.850' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (353, N'Notifiable Change (NC)', 8, CAST(N'2020-12-17T19:38:12.403' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:38:12.403' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (354, N'Pandemic Application (PAND)', 8, CAST(N'2020-12-17T19:38:22.367' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:38:22.367' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (355, N'Periodic Safety Update Report - Conditional (PSUR-C)', 8, CAST(N'2020-12-17T19:38:35.197' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:38:35.197' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (356, N'Periodic Safety Update Report - Pharmacovigilance (PSUR-PV)', 8, CAST(N'2020-12-17T19:38:43.280' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:38:43.280' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (357, N'Post DIN Change - Biologics (PDC-B)', 8, CAST(N'2020-12-17T19:38:52.400' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:38:52.400' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (358, N'Post DIN Change (PDC)', 8, CAST(N'2020-12-17T19:39:02.083' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:39:02.083' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (359, N'Post-Notice of Compliance Changes - Level III (Level III)', 8, CAST(N'2020-12-17T19:39:11.763' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:39:11.763' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (360, N'Pre-Clinical Trial Application Meeting (PRECTA)', 8, CAST(N'2020-12-17T19:39:22.667' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:39:22.667' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (361, N'Pre-DIN Meeting (MPDIN)', 8, CAST(N'2020-12-17T19:39:33.047' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:39:33.047' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (362, N'Pre-NC Meeting (MPNC)', 8, CAST(N'2020-12-17T19:39:44.773' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:39:44.773' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (363, N'Pre-NDS Meeting (MPNDS)', 8, CAST(N'2020-12-17T19:39:54.520' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:39:54.520' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (364, N'Pre-SNDS Meeting (MPSNDS)', 8, CAST(N'2020-12-17T19:40:06.180' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:40:06.180' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (365, N'Priority Request NDS (PRNDS)', 8, CAST(N'2020-12-17T19:40:16.223' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:40:16.223' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (366, N'Priority Request SNDS (PRSNDS)', 8, CAST(N'2020-12-17T19:40:25.247' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:40:25.247' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (367, N'Risk Management Plan - Pharmacovigilance (RMP-PV)', 8, CAST(N'2020-12-17T19:40:34.370' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:40:34.370' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (368, N'Supplement to a New Drug Submission - Conditional (SNDS-C)', 8, CAST(N'2020-12-17T19:40:43.417' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:40:43.417' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (369, N'Supplement to a New Drug Submission (SNDS)', 8, CAST(N'2020-12-17T19:40:52.190' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:40:52.190' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (370, N'Supplement to an Abbreviated New Drug Submission (SANDS)', 8, CAST(N'2020-12-17T19:41:02.147' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:41:02.147' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (371, N'Undefined Data Pharmacovigilance (UD-PV)', 8, CAST(N'2020-12-17T19:41:11.570' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:41:11.570' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (372, N'Undefined Regulatory Activity (UDRA)', 8, CAST(N'2020-12-17T19:41:21.113' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:41:21.113' AS DateTime), N'<EMAIL>')
INSERT [dbo].[PicklistData] ([Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (373, N'Yearly Biologic Product Report (YBPR)', 8, CAST(N'2020-12-17T19:41:31.350' AS DateTime), N'<EMAIL>', CAST(N'2020-12-17T19:41:31.350' AS DateTime), N'<EMAIL>')
SET IDENTITY_INSERT [dbo].[PicklistData] OFF
GO
