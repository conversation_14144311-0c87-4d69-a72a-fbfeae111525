﻿param([string]$Server,
	  [string]$Database,
	  [string]$User,
	  [string]$Password,
      [string]$Script)

try {
	Write-Host "Received Input Parameters:"
	Write-Host "Server:" -NoNewline
	Write-Host $Server
	Write-Host "Database:" -NoNewline
	Write-Host $Database
	Write-Host "User:" -NoNewline
	Write-Host $User
	Write-Host "Script:" -NoNewline
	Write-Host $Script

	Write-Host "Attempting database reset..." -NoNewline
	Invoke-Sqlcmd -ServerInstance $Server -Database $Database -Username $User -Password $Password -InputFile $Script
	Write-Host "Success"
} catch {
	Write-Host "Failed to reset sql database:"
	Write-Host $error
	exit -1
}