﻿using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class EditSubmissionViewModel
    {
        public EditSubmissionViewModel()
        {
            this.Picklists = new List<PicklistDataModel>();
            this.AllCountries = new List<CountryModel>();
            this.Projects = new List<ProjectModel>();
            this.InvalidFields = new List<string>();
            this.ApplicationCountries = new List<CountryModel>();
        }

        public SubmissionModel Submission { get; set; }
        public ProjectModel Project { get; set; }
        public SubmissionResourceModel SubmissionResource { get; set; }
        public ApplicationModel Application { get; set; }
        public string Product { get; set; }
        public ClientModel Client { get; set; }
        public IList<string> InvalidFields { get; set; }

        public IList<PicklistDataModel> Picklists { get; set; }
        public IList<CountryModel> AllCountries { get; set; }
        public IList<ProjectModel> Projects { get; set; }
        public List<CountryModel> ApplicationCountries { get; set; }
    }
}
