﻿using System.ComponentModel;

namespace PharmaLex.SmartTRACE.Entities.Enums
{
    public enum SubmissionLifeCycleState
    {
        Draft = 1,

        Planned = 2,

        [Description("In Progress")]
        InProgress = 3,

        [Description("Ready for Publishing")]
        ReadyForPublishing = 4,

        [Description("QC Review")]
        QCReview = 5,

        [Description("Approved for Submission")]
        ApprovedForSubmission = 6,

        Submitted = 7,

        Archived = 8,

        [Description("HA Withdrawn")]
        WithdrawnFromHA = 9,

        Obsolete = 10
    }
}
