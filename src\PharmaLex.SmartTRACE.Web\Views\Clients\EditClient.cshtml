﻿@model EditClientViewModel
@using PharmaLex.SmartTRACE.Entities.Enums
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization

@{
    var contractOwnerSelectList = Model.AllContractOwners.OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString()
            });
}

<div id="client" class="manage-container">
    <header class="manage-header">
        <h3>@Html.Encode(Model.Client.Name) Client</h3>
        <a href="/clients" class="button secondary icon-button-back">Back to Client list</a>
    </header>
    <form method="post">
        @Html.AntiForgeryToken()
        <div class="form-col form-col-halfform-col-half">
            <h5>Client Info</h5>
            <label for="Client.Name">Name*</label>
            <input asp-for="Client.Name" type="text" v-bind:class="{'validation-error' : hasError}" required />
            @if (Model.Client.HasError)
            {
                <span class="field-duplication-error">@Model.Client.ErrorMessage</span>
            }
            <label for="Client.ContractOwnerId">Contract Owner*</label>
            <div class="select-wrapper">
                <select asp-for="Client.ContractOwnerId" asp-items="contractOwnerSelectList" required>
                    <option value="">Select contract owner</option>
                </select>
            </div>
        </div>
        @if (Model.Client.Id == 0)
        {
            <div class="form-col form-col-half">
                <h5>Project Info</h5>
                <label for="Project.Name">Name*</label>
                <input asp-for="Project.Name" type="text" required />
                <label for="Project.Code">Code*</label>
                <input asp-for="Project.Code" type="text" required />
                <label for="Project.OpportunityNumber">Opportunity Number*</label>
                <input asp-for="Project.OpportunityNumber" type="text" required />
            </div>
        }
        <input type="hidden" asp-for="Client.Id" />
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/clients">Cancel</a><button class="icon-button-save">Save</button>
        </div>
    </form>

    @if (Model.Client.Id != 0)
    {
        <br />
        <h3>Projects</h3>
        <filtered-table :items="projects" :columns="columns" :filters="filters" :link="link" addurl="/projects/new/@Model.Client.Id"></filtered-table>
    }
</div>

@section Scripts {
    <script type="text/javascript">

        var pageConfig = {
            appElement: '#client',
            data() {
                return {
                    link: '/projects/edit/',
                    projects: @Html.Raw(JsonConvert.SerializeObject(Model.Projects, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })),
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'name',
                                sortKey: 'name',
                                header: 'Name',
                                type: 'text'
                            },
                            {
                                dataKey: 'code',
                                sortKey: 'code',
                                header: 'Code',
                                type: 'text'
                            },
                            {
                                dataKey: 'opportunityNumber',
                                sortKey: 'opportunityNumber',
                                header: 'Opportunity Number',
                                type: 'text'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'name',
                            options: [],
                            type: 'search',
                            header: 'Search Name',
                            fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ],
                    hasError: @Model.Client.HasError.ToString().ToLower(),
                };
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}
