﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;
using System.Security.Claims;
using Claim = System.Security.Claims.Claim;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class UsersControllerTests
    {
        private readonly IMapper _mapper;
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IUserExport userExport;
        private readonly IAzureAdGraphService graphService;
        private UsersController _usersController;
        UserModel userModel;

        public UsersControllerTests()
        {
            _mapper = Substitute.For<IMapper>();
            _cache = Substitute.For<IDistributedCacheServiceFactory>();
            userExport = Substitute.For<IUserExport>();
            graphService = Substitute.For<IAzureAdGraphService>();
            _usersController = new UsersController(_cache, _mapper, userExport, graphService);
            var context = new DefaultHttpContext();
            context.User = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
                new System.Security.Claims.Claim("admin:", "58"),
                new System.Security.Claims.Claim("id", "1")
            }));
            _usersController.ControllerContext = new ControllerContext
            {
                HttpContext = context
            };
            userModel = new UserModel
            {
                Id = 1,
                Email = "<EMAIL>",
                GivenName = "John",
                FamilyName = "Doe",
                Claims = [1, 2, 3],
                DisplayClaims = new List<string>()

            };
        }
        #region picture
        [Fact]
        public async Task Picture_Returns_FileResult()
        {
            // Arrange
            var mockFileContent = new byte[] { 1, 2, 3 }; // Mock file content

            graphService.GetUserPicture(userModel.Email).ReturnsForAnyArgs(mockFileContent);
            // Act
            var result = await _usersController.Picture();
            // Assert
            var viewResult = Assert.IsType<FileContentResult>(result);
            Assert.NotNull(viewResult);
            Assert.NotNull(viewResult.FileContents);
        }
        #endregion  #region picture
        #region Find
        [Fact]
        public async Task Find_Returns_JsonResult()
        {
            // Arrange
            List<UserFindResultModel> usersList = new List<UserFindResultModel>()
            { new UserFindResultModel(){Id=1, Email="test", Name="sample1" } };
            List<Microsoft.Graph.Models.User> grphUserList = new List<Microsoft.Graph.Models.User>()
            { new Microsoft.Graph.Models.User(){Id="1", DisplayName="sample1", GivenName="sample1" },
            new Microsoft.Graph.Models.User(){Id="2", DisplayName="sample2", GivenName="sample2" }};

            var userCache = Substitute.For<IMappedEntityCacheServiceProxy<PharmaLex.SmartTRACE.Entities.User, UserFindResultModel>>();
            _cache.CreateMappedEntity<PharmaLex.SmartTRACE.Entities.User, UserFindResultModel>().ReturnsForAnyArgs(userCache);
            userCache.AllAsync().Returns(usersList);
            graphService.FindUsers("sample1").ReturnsForAnyArgs(grphUserList);
            _mapper.Map<List<UserFindResultModel>>(Arg.Any<List<Microsoft.Graph.Models.User>>()).ReturnsForAnyArgs
             (usersList);
            // Act
            var result = await _usersController.Find("sample1");
            // Assert
            var viewResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(viewResult);
            Assert.NotNull((viewResult).Value);

        }
        #endregion
        #region Find
        [Fact]
        public async Task Finds_Returns_JsonResult()
        {
            // Arrange
            var users = new List<UserModel>
            {
                new UserModel { Id = 1, Email = "<EMAIL>", GivenName = "John", FamilyName = "Doe", Claims = new List<int>(), DisplayClaims = new List<string>() },
                new UserModel { Id = 2, Email = "<EMAIL>", GivenName = "Jane", FamilyName = "Smith", Claims = new List<int>(), DisplayClaims = new List<string>() }
            };
            Task<List<UserModel>> TaskLstUser = Task.FromResult(users);
            var userCache = Substitute.For<IMappedEntityCacheServiceProxy<Entities.User, UserModel>>();
            _cache.CreateMappedEntity<Entities.User, UserModel>().ReturnsForAnyArgs(userCache);
            userCache.AllAsync().Returns(TaskLstUser);
            userCache.Configure(Arg.Any<Func<IIncludable<Entities.User>, IIncludable<Entities.User>>>()).Returns(userCache);
            userCache.WhereAsync(Arg.Any<Expression<Func<Entities.User, bool>>>()).ReturnsForAnyArgs(Task.FromResult(users));

            // Act
            var result = await _usersController.Index();
            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<ViewResult>(result);
            var viewModel = Assert.IsType<List<UserModel>>(viewResult.Model);
            Assert.Equal(users.Count, viewModel.Count);

        }
        #endregion
        #region Action Method Find(string term) for invalid
        [Fact]
        public async Task Find_Invalid()
        {
            // Arrange
            string term = "pavan";
            _usersController.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await _usersController.Find(term) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region New Valid state
        [Fact]
        public async Task New_Returns_NewView_With_UserModel()
        {
            //Arrange

            PharmaLex.SmartTRACE.Entities.User user = new();
            var localUserModel = new UserModel();
            UserClientsModel users = new UserClientsModel()
            { Clients = new List<ClientModel>() { }, /*User= userModel*/ };
            var userCache = Substitute.For<ITrackedEntityCacheServiceProxy<PharmaLex.SmartTRACE.Entities.User>>();
            userCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PharmaLex.SmartTRACE.Entities.User, bool>>>()).Returns(user);
            _mapper.Map<UserModel, PharmaLex.SmartTRACE.Entities.User>(localUserModel).ReturnsForAnyArgs(user);
            // Act            
            var result = await _usersController.New(users) as IActionResult;
            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<RedirectResult>(result);
            Assert.NotNull(viewResult.Url);
            Assert.Equal("/manage/users", viewResult.Url);
        }
        #endregion
        #region new - invalid state
        [Fact]
        public async Task New_Returns_EditView_For_InvalidState()
        {
            //Arrange
            _usersController.ModelState.AddModelError("SessionName", "Required");
            UserClientsModel users = new UserClientsModel()
            { Clients = new List<ClientModel>() { } };
            PharmaLex.SmartTRACE.Entities.User user = new();
            var localUserModel = new UserModel();
            List<ClientModel> client = new List<ClientModel>()
            { new ClientModel(){Name="test", Id=1, ContractOwner="test2" } };
            var clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            _cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(clientCache);
            clientCache.AllAsync().Returns(client);
            // Act            
            var result = await _usersController.New(users) as IActionResult;
            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult.Model);
            Assert.Equal("Edit", viewResult.ViewName);
        }
        #endregion
        #region New Valid state-edit
        [Fact]
        public async Task New_Returns_EditUserModel()
        {
            //Arrange
            List<ClientModel> client = new List<ClientModel>()
            { new ClientModel(){Name="test", Id=1, ContractOwner="test2" } };
            var clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            _cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(clientCache);
            clientCache.AllAsync().Returns(client);

            // Act            
            var result = await _usersController.New() as IActionResult;
            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult.Model);
            Assert.Equal("Edit", viewResult.ViewName);
        }
        #endregion
        #region Action Method Edit(string term) for invalid
        [Fact]
        public async Task Edit_Invalid()
        {
            // Arrange
            int id = 10;
            _usersController.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await _usersController.Edit(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region Edit_Returns_ViewResult
        [Fact]
        public async Task Edit_Returns_ViewResult()
        {
            //Arrange
            List<UserFindResultModel> usersList = new List<UserFindResultModel>();
            var userCache = Substitute.For<IMappedEntityCacheServiceProxy<PharmaLex.SmartTRACE.Entities.User, UserModel>>();
            _cache.CreateMappedEntity<PharmaLex.SmartTRACE.Entities.User, UserModel>().ReturnsForAnyArgs(userCache);
            userCache.Configure(Arg.Any<Func<IIncludable<PharmaLex.SmartTRACE.Entities.User>, IIncludable<PharmaLex.SmartTRACE.Entities.User>>>()).Returns(userCache);

            userCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PharmaLex.SmartTRACE.Entities.User, bool>>>()).Returns(userModel);
            { new UserFindResultModel() { Id = 1, Email = "test", Name = "sample1" }; };
            List<ClientModel> client = new List<ClientModel>()
            { new ClientModel(){Name="test", Id=1, ContractOwner="test2" } };
            var clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            _cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(clientCache);
            clientCache.AllAsync().Returns(client);
            // Act            
            var result = await _usersController.Edit(userModel.Id) as IActionResult;
            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult.Model);
        }
        #endregion
        #region Edit_Returns_ViewResult
        [Fact]
        public async Task Edit_Returns_ViewResultForExternalUser()
        {
            //Arrange
            var userCache = Substitute.For<IMappedEntityCacheServiceProxy<PharmaLex.SmartTRACE.Entities.User, UserModel>>();
            _cache.CreateMappedEntity<PharmaLex.SmartTRACE.Entities.User, UserModel>().ReturnsForAnyArgs(userCache);
            userCache.Configure(Arg.Any<Func<IIncludable<PharmaLex.SmartTRACE.Entities.User>, IIncludable<PharmaLex.SmartTRACE.Entities.User>>>()).Returns(userCache);
            userModel.UserTypeId = 3;
            userCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PharmaLex.SmartTRACE.Entities.User, bool>>>()).Returns(userModel);
            { new UserFindResultModel() { Id = 1, Email = "test", Name = "sample1" }; };
            List<ClientModel> client = new List<ClientModel>()
            { new ClientModel(){Name="test", Id=1, ContractOwner="test2" } };
            var clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            _cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(clientCache);
            clientCache.AllAsync().Returns(client);
            // Act            
            var result = await _usersController.Edit(userModel.Id) as IActionResult;
            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult.Model);
            Assert.Equal("../ExternalUsers/EditExternalUser", viewResult.ViewName);
        }
        #endregion
        #region Edit_Returns_ViewResultForExternalUser
        [Fact]
        public async Task EditWithModel_Returns_ViewResultForExternalUser()
        {
            //Arrange
            Entities.User user = new() { Id = 1, UserTypeId = 3, GivenName = "test1" };
            List<Entities.Claim> claims = [new Entities.Claim() { Id = 1, Name = "cliam2" , ClaimType = "type"}];
            _cache.CreateEntity<Entities.Claim>().AllAsync().Returns(claims);

            // Mock the ITrackedEntityCacheServiceProxy<User>
            var userCache = Substitute.For<ITrackedEntityCacheServiceProxy<Entities.User>>();

            // Setup FirstOrDefaultAsync to return our test user
            userCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Entities.User, bool>>>())
                .Returns(Task.FromResult(user));

            // Setup Configure to return the same mock (method chaining)
            userCache.Configure(Arg.Any<Func<IIncludable<Entities.User>, IIncludable<Entities.User>>>())
            .Returns(callInfo =>
            {
                // The actual include operations would happen here in real code
                return userCache;
            });

            // Mock the IDistributedCacheServiceFactory to return our userCache
            _cache.CreateTrackedEntity<Entities.User>()
                    .Returns(userCache);

            // Setup mapper
            _mapper.Map<UserModel, Entities.User>(Arg.Any<UserModel>())
                .Returns(user);
            UserClientsModel users = new()
            { Clients = [], User = userModel };

            // Act            
            var result = await _usersController.Edit(userModel.Id, users);
            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<RedirectResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal("/manage/users", viewResult.Url);
        }

        [Fact]
        public async Task Edit_HtmlTagsInFamilyName_ShouldReturnViewResult()
        {
            // Arrange
            var users = new UserClientsModel()
            {
                Clients = new List<ClientModel>() { },
                User = userModel
            };

            var claims = new List<Entities.Claim>()
            {
                new Entities.Claim() 
                {
                    Id=1,
                    Name="cliam2",
                    ClaimType="type"
                }
            };
            _cache.CreateEntity<Entities.Claim>().AllAsync().Returns(claims);

            var client = new List<ClientModel>()
            {
                new ClientModel()
            {
                Name="test",
                Id= 1,
                ContractOwner="test2"
            }
            };
            var clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            _cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(clientCache);
            clientCache.AllAsync().Returns(client);
            userModel.FamilyName = "<div>Test</div>";

            // Act
            var result = await _usersController.Edit(userModel.Id, users);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public async Task Edit_HtmlTagsInGivenName_ShouldReturnViewResult()
        {
            // Arrange
            var users = new UserClientsModel()
            {
                Clients = new List<ClientModel>() { },
                User = userModel
            };

            var claims = new List<Entities.Claim>()
            {
                new Entities.Claim()
                {
                    Id=1,
                    Name="cliam2",
                    ClaimType="type"
                }
            };
            _cache.CreateEntity<Entities.Claim>().AllAsync().Returns(claims);

            var client = new List<ClientModel>()
            {
                new ClientModel()
            {
                Name="test",
                Id= 1,
                ContractOwner="test2"
            }
            };
            var clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            _cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(clientCache);
            clientCache.AllAsync().Returns(client);
            userModel.GivenName = "<div>Test</div>";

            // Act
            var result = await _usersController.Edit(userModel.Id, users);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public async Task Edit_HtmlTagsInEmail_ShouldReturnViewResult()
        {
            // Arrange
            var users = new UserClientsModel()
            {
                Clients = new List<ClientModel>() { },
                User = userModel
            };

            var claims = new List<Entities.Claim>()
            {
                new Entities.Claim()
                {
                    Id=1,
                    Name="cliam2",
                    ClaimType="type"
                }
            };
            _cache.CreateEntity<Entities.Claim>().AllAsync().Returns(claims);

            var client = new List<ClientModel>()
            {
                new ClientModel()
            {
                Name="test",
                Id= 1,
                ContractOwner="test2"
            }
            };
            var clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            _cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(clientCache);
            clientCache.AllAsync().Returns(client);
            userModel.Email = "<script><EMAIL></csript>";

            // Act
            var result = await _usersController.Edit(userModel.Id, users);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
        }

        #endregion
        #region Edit_Returns_ViewResult
        [Fact]
        public async Task EditSuperAdmin_Returns_ViewResultForExternalUser()
        {
            // Arrange
            // Setup HttpContext with claims
            var context = new DefaultHttpContext
            {
                User = new ClaimsPrincipal(new ClaimsIdentity(
            [
                    new Claim("admin:SuperAdmin", "58")
                ]))
            };
            _usersController.ControllerContext = new ControllerContext { HttpContext = context };

            // Create a complete test user with all required properties
            var testUser = new Entities.User
            {
                Id = 1,
                UserTypeId = (int)Entities.Enums.UserType.External, 
                GivenName = "Original",
                FamilyName = "User",
                Email = "<EMAIL>",
                UserClaim = [],
                UserClient = []
            };

            // Mock the ITrackedEntityCacheServiceProxy<User>
            var userCache = Substitute.For<ITrackedEntityCacheServiceProxy<Entities.User>>();

            // Setup FirstOrDefaultAsync to return our test user
            userCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Entities.User, bool>>>())
                .Returns(Task.FromResult(testUser));

            // Setup Configure to return the same mock (method chaining)
            userCache.Configure(Arg.Any<Func<IIncludable<Entities.User>, IIncludable<Entities.User>>>())
            .Returns(callInfo =>
            {
                // The actual include operations would happen here in real code
                return userCache;
            });

            // Mock the IDistributedCacheServiceFactory to return our userCache
            _cache.CreateTrackedEntity<PharmaLex.SmartTRACE.Entities.User>()
                    .Returns(userCache);

            // Setup mapper
            _mapper.Map<UserModel, PharmaLex.SmartTRACE.Entities.User>(Arg.Any<UserModel>())
                .Returns(testUser);

            // Create test model
            var userClientsModel = new UserClientsModel
            {
                Clients = [],
                User = new UserModel
                {
                    Id = 1,
                    GivenName = "New",
                    FamilyName = "Name",
                    Email = "<EMAIL>",
                    UserTypeId = 1 // Different from external
                }
            };

            // Act
            var result = await _usersController.Edit(1, userClientsModel);

            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<RedirectResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal("/manage/users", viewResult.Url);
        }
        #endregion

        [Fact]
        public async Task Edit_InvalidModel()
        {
            // Arrange
            List<ClientModel> client = new List<ClientModel>()
            { new ClientModel(){Name="test", Id=1, ContractOwner="test2" } };
            var clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            _cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(clientCache);
            clientCache.AllAsync().Returns(client);
            var localUserModel = new UserClientsModel() { User = userModel };
            localUserModel.User.FamilyName = "Test";
            _usersController.ModelState.AddModelError("SessionName", "Required");

            //Act
            var result = await _usersController.Edit(10, localUserModel) as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Model);

        }
        #region Edit_Returns_UnauthorizedAccessException
        [Fact]
        public async Task Edit_Returns_UnauthorizedAccessException()
        {
            //Arrange
            var context = new DefaultHttpContext();
            context.User = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
                new System.Security.Claims.Claim("user", "58")
            }));
            _usersController.ControllerContext = new ControllerContext
            {
                HttpContext = context
            };
            PharmaLex.SmartTRACE.Entities.User user = new() { Id = 1, UserTypeId = 3, GivenName = "test1" };
            var localUserModel = new UserModel();

            List<PharmaLex.SmartTRACE.Entities.Claim> claims = new List<PharmaLex.SmartTRACE.Entities.Claim>()
            { new PharmaLex.SmartTRACE.Entities.Claim() {Id=1, Name="cliam2" , ClaimType="type"} };
            _cache.CreateEntity<PharmaLex.SmartTRACE.Entities.Claim>()

               .AllAsync()
               .Returns(claims);
            var userCache = Substitute.For<ITrackedEntityCacheServiceProxy<PharmaLex.SmartTRACE.Entities.User>>();
            userCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PharmaLex.SmartTRACE.Entities.User, bool>>>()).Returns(user);
            _mapper.Map<UserModel, PharmaLex.SmartTRACE.Entities.User>(localUserModel).ReturnsForAnyArgs(user);

            UserClientsModel users = new UserClientsModel()
            { Clients = new List<ClientModel>() { }, User = userModel };
            // Act            
            var ex = await Assert.ThrowsAsync<UnauthorizedAccessException>(() => _usersController.Edit(userModel.Id, users));
            // Assert
            Assert.Equal("The logged in user does not have permission to set the attempted claim(s)", ex.Message);

        }
        #endregion
        #region Edit_Returns_ViewResult
        [Fact]
        public async Task EditAdmin_Returns_RedirectResult()
        {
            //Arrange
            var context = new DefaultHttpContext();
            context.User = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
                new System.Security.Claims.Claim("admin:SuperAdmin", "58")
            }));
            _usersController.ControllerContext = new ControllerContext
            {
                HttpContext = context
            };
            PharmaLex.SmartTRACE.Entities.User user = new()
            {
                Id = 1,
                UserTypeId = 3,
                GivenName = "test1",
                UserClaim = new List<UserClaim>(),
                UserClient = new List<UserClient>()
            };
            var localUserModel = new UserModel();
            List<PharmaLex.SmartTRACE.Entities.Claim> claims = new List<PharmaLex.SmartTRACE.Entities.Claim>()
            { new PharmaLex.SmartTRACE.Entities.Claim() {Id=1, Name="claim2" , ClaimType="type"} };
            _cache.CreateEntity<PharmaLex.SmartTRACE.Entities.Claim>().AllAsync().Returns(claims);

            var userCache = Substitute.For<ITrackedEntityCacheServiceProxy<PharmaLex.SmartTRACE.Entities.User>>();
            _cache.CreateTrackedEntity<PharmaLex.SmartTRACE.Entities.User>().ReturnsForAnyArgs(userCache);
            userCache.Configure(Arg.Any<Func<IIncludable<PharmaLex.SmartTRACE.Entities.User>, IIncludable<PharmaLex.SmartTRACE.Entities.User>>>()).Returns(userCache);
            userCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PharmaLex.SmartTRACE.Entities.User, bool>>>()).Returns(user);

            _mapper.Map<UserModel, PharmaLex.SmartTRACE.Entities.User>(localUserModel).ReturnsForAnyArgs(user);

            UserClientsModel users = new UserClientsModel()
            { Clients = new List<ClientModel>() { }, User = userModel };
            // Act            
            var result = await _usersController.Edit(userModel.Id, users) as IActionResult;
            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<RedirectResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal("/manage/users", viewResult.Url);
        }
        #endregion




        #region  Method Export Post

        [Fact]
        public async Task Export_Returns_FileResult()
        {
            // Arrange
            var mockFileContent = new byte[] { 1, 2, 3 }; 
            var exportResultStream = new MemoryStream(mockFileContent);
            userExport.Export().Returns(Task.FromResult<byte[]>(mockFileContent));
            // Act
            var result = await _usersController.Export();

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileResult.ContentType);
            Assert.NotNull(fileResult.FileContents);
        }

        #endregion
    }
}


