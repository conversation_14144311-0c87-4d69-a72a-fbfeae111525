﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.HTMLTags;
using PharmaLex.SmartTRACE.Web.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    [Authorize("BusinessAdmin")]
    public class ClientsController : BaseController
    {
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IMapper _mapper;
        private readonly IClientExport _clientExport;
        private const string UniqueErrorMessage = "Name must be unique";

        public ClientsController(IDistributedCacheServiceFactory cache, IMapper mapper, IClientExport clientExport)
        {
            this._cache = cache;
            this._mapper = mapper;
            this._clientExport = clientExport;
        }

        [HttpGet, Route("/clients")]
        public async Task<IActionResult> Index()
        {
            var clientsCache = _cache.CreateMappedEntity<Client, ClientModel>();
            var clients = await clientsCache.AllAsync();
            foreach (var client in clients)
            {
                client.ContractOwner = (await PicklistHelper.ExtractPicklist(_cache, client.ContractOwnerId))?.Name;
            }
            return View(clients);
        }

        [HttpGet, Route("/clients/new")]
        public async Task<IActionResult> New()
        {
            return View("EditClient", new EditClientViewModel()
            {
                Client = new ClientModel(),
                AllContractOwners = await GetAllContractOwners()
            });
        }

        [HttpPost, Route("/clients/new"), ValidateAntiForgeryToken]
        public async Task<IActionResult> New(EditClientViewModel model)
        {
            try
            {
                if (!this.ModelState.IsValid || HtmlTags.CheckHTMLTags(model.Client.Name))
                {
                    model.AllContractOwners = await GetAllContractOwners();
                    return View("EditClient", model);
                }

                var clients = _cache.CreateTrackedEntity<Client>();
                var client = _mapper.Map<ClientModel, Client>(model.Client);
                clients.Add(client);
                await clients.SaveChangesAsync();
                this.AddConfirmationNotification($"<em>{client.Name}</em> created");

                var projects = _cache.CreateTrackedEntity<Project>();
                var project = _mapper.Map<ProjectModel, Project>(model.Project);
                project.ClientId = client.Id;
                projects.Add(project);
                await projects.SaveChangesAsync();
                this.AddConfirmationNotification($"<em>{project.Name}</em> created");

                var userCache = _cache.CreateTrackedEntity<User>()
                    .Configure(o => o
                        .Include(x => x.UserClient));
                var users = await userCache.WhereAsync(x => x.AutoAccessClients);

                foreach (var user in users)
                {
                    user.UserClient.Add(new UserClient
                    {
                        UserId = user.Id,
                        ClientId = client.Id
                    });

                    await userCache.SaveChangesAsync();
                }

                return Redirect($"/clients/edit/{client.Id}");
            }
            catch (DbUpdateException)
            {
                model.Client.HasError = true;
                model.Client.ErrorMessage = UniqueErrorMessage;
                model.AllContractOwners = await GetAllContractOwners();
                return View("EditClient", model);
            }
        }

        [HttpGet, Route("/clients/edit/{id}")]
        public async Task<IActionResult> Edit(int id)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var clients = _cache.CreateMappedEntity<Client, ClientModel>();
            var projects = _cache.CreateMappedEntity<Project, ProjectModel>();
            var currentClient = await clients.FirstOrDefaultAsync(x => x.Id == id);
            var clientProjects = await projects.WhereAsync(x => x.ClientId == id);
            return View("EditClient", new EditClientViewModel()
            {
                Client = currentClient,
                Projects = clientProjects,
                AllContractOwners = await GetAllContractOwners()
            });
        }

        [HttpPost, Route("/clients/edit/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EditClientViewModel model)
        {
            try
            {
                if (!this.ModelState.IsValid || HtmlTags.CheckHTMLTags(model.Client.Name))
                {
                    model.AllContractOwners = await GetAllContractOwners();
                    return View("EditClient", model);
                }
                var clients = _cache.CreateTrackedEntity<Client>();
                Client c = await clients.FirstOrDefaultAsync(x => x.Id == id);
                _mapper.Map(model.Client, c);
                await clients.SaveChangesAsync();
                this.AddConfirmationNotification($"<em>{model.Client.Name}</em> updated");

                return await this.Edit(id);
            }
            catch (DbUpdateException)
            {
                model.Client.HasError = true;
                model.Client.ErrorMessage = UniqueErrorMessage;
                model.AllContractOwners = await GetAllContractOwners();
                return View("EditClient", model);
            }
        }

        [HttpPost("/clients/export"), Authorize(Policy = "Reader"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Export()
        {
            return File(await this._clientExport.Export(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"Clients_{System.DateTime.Now.ToString("yyyy-MM-dd")}.xlsx");
        }

        private async Task<IList<PicklistDataModel>> GetAllContractOwners()
        {
            var contractOwners = await _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().WhereAsync(x => x.PicklistTypeId == (int)PicklistType.ContractOwner);
            return contractOwners;
        }
    }
}
