﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.NotificationModels
{
    public class BaseNotificationModelTests
    {
        [Fact]
        public void BaseNotificationModel_Get_SetValue()
        {
            // Arrange
            var model = new BaseNotificationModel();
            //Act
            model.FullName = "Test";
            model.Link = "/test";
            model.Scheme = "http";
            model.Host = "localhost";
            // Assert            
            Assert.NotNull(model);
        }
    }
}
