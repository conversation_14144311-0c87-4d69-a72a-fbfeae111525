﻿@using Microsoft.AspNetCore.Authorization
@using PharmaLex.Caching.Data
@using PharmaLex.Helpers
@using PharmaLex.Authentication
@using PharmaLex.SmartTRACE.Entities.Enums
@using Microsoft.Extensions.Configuration
@using System.Reflection
@using PharmaLex.Authentication.B2C

@inject IAuthorizationService AuthorizationService
@inject AppSettingsHelper AppSettings
@inject IConfiguration Configuration
@inject IDistributedCacheServiceFactory cache

@{
    var menu = new Dictionary<(string href, string text), DataCategoryType>();
    var picklistTypes = Enum.GetValues(typeof(PicklistType));
    foreach (PicklistType pt in picklistTypes)
    {
        menu.Add(($"/data/{(int)pt}", @pt.GetDescription()), DataCategoryType.Picklist);
    }
    menu.Add(("/clients/", "Clients"), DataCategoryType.Clients);
    menu.Add(("/projects/", "Projects"), DataCategoryType.Clients);
    menu.Add(("/products/", "Products"), DataCategoryType.Products);
    menu.Add(("/active-substances/", "Active Substances"), DataCategoryType.Products);
    menu.Add(("/applications/", "Applications"), DataCategoryType.Applications);
    menu.Add(("/app-submissions/", "Submissions"), DataCategoryType.Applications);
    menu.Add(("/regions/", "Regions"), DataCategoryType.Countries);
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="SmartTRACE is a PharmaLex internal system">
    <link rel="shortcut icon" type="image/png" href="@Cdn.Host/images/favicon/cencora-favicon.png">
    <meta name="apple-mobile-web-app-title" content="PharmaLex">
    <meta name="application-name" content="PharmaLex">
    <meta name="theme-color" content="#233c4c">

    <title>SmartTRACE :: @ViewData["Title"]</title>
    <link rel="stylesheet" href="@Cdn.Host/css/site.min.css" asp-append-version="true" />
    <link rel="stylesheet" href="@Cdn.GetUrl("css/login.css")" asp-append-version="true" />
    <link rel="stylesheet" href="/css/smarttrace.css" asp-append-version="true" />
    <link rel="stylesheet" href="/css/smarttraceOverrides.css" asp-append-version="true" />

    <script src="@Cdn.GetUrl("lib/vue/3.2.6/vue.prod.js")"></script>
    <script src="/lib/vuex/4.0.0/vuex.global.js" asp-append-version="true"></script>
    <script src="@Cdn.GetUrl("lib/tippy/popper/1.15/popper.min.js")"></script>
    <script src="@Cdn.GetUrl("lib/tippy/4.3.5/tippy.min.js")"></script>
    <script src="@Cdn.GetUrl("js/plx.js")"></script>
    <script src="@Cdn.GetUrl("js/toast.js")"></script>
    @await RenderSectionAsync("Styles", required: false)

    <style>
        .toast-2.confirm:before {
            content: '\2714';
            background-color: var(--status-green);
        }

        .toast-2.failed:before {
            content: '\2716';
            background-color: #c60000;
        }
    </style>
</head>
<body>
    <header id="page-header" class="page-header">
        <div class="page-header-content">
            @if (User.Identity.IsAuthenticated)
            {
                <a href="/" class="page-header-title brand-name">SMARTTRACE</a>
                <nav>
                    <ul class="main-nav">
                        @if ((await AuthorizationService.AuthorizeAsync(User, "UserAdmin")).Succeeded)
                        {
                            <li class="has-children manage-nav-item">
                                <a href="">Manage</a>
                                <div class="main-sub-nav-container">
                                    <ul class="main-sub-nav">
                                        <li><a href="/manage/users">Users</a></li>
                                    </ul>
                                </div>
                            </li>
                        }
                        @if ((await AuthorizationService.AuthorizeAsync(User, "Reader")).Succeeded)
                        {
                            <li class="has-children data-nav-item">
                                <a href="">Data</a>
                                <div class="main-mega-menu-container">
                                    <ul class="main-sub-nav">
                                        @{
                                            int i = 0;
                                            foreach (DataCategoryType t in Enum.GetValues(typeof(DataCategoryType)))
                                            {
                                                if (t != DataCategoryType.Applications)
                                                {
                                                    if ((await AuthorizationService.AuthorizeAsync(User, "BusinessAdmin")).Succeeded)
                                                    {
                                                        <li class="@(i++ == 0 ? "selected" : "")">
                                                            @t.GetDescription()
                                                            <ul>
                                                                @foreach (var ct in menu.Where(x => x.Value == t))
                                                                {
                                                                    <li><a href="@ct.Key.href">@ct.Key.text</a></li>
                                                                }
                                                            </ul>
                                                        </li>
                                                    }
                                                }
                                                else
                                                {
                                                    <li class="@(i++ == 0 ? "selected" : "")">
                                                        @t.GetDescription()
                                                        <ul>
                                                            @foreach (var ct in menu.Where(x => x.Value == t))
                                                            {
                                                                <li><a href="@ct.Key.href">@ct.Key.text</a></li>
                                                            }
                                                        </ul>
                                                    </li>
                                                }
                                            }
                                        }
                                    </ul>
                                </div>
                            </li>
                        }
                        <li>
                            <a href="/submissions">eCTD Viewer</a>
                        </li>
                        <li class="has-children">
                            <a href="">About</a>
                            <div class="main-sub-nav-container">
                                <ul class="main-sub-nav">
                                    <li><a href="@Configuration["AppSettings:ZenDeskURL"]" target="_blank" rel="noopener noreferrer">Contact/Feedback</a></li>
                                    <li><a id="about-nav-link">About SmartTRACE</a></li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </nav>
                <nav aria-label="user account" class="user-nav-container">
                    <ul class="user-nav">
                        <li class="has-children">
                            <a class="profile-display">
                                <span class="profile-picture">account_circle</span>
                                @User.Claims.First(x => x.Type == "name").Value
                            </a>
                            <div class="main-sub-nav-container">
                                <ul class="main-sub-nav">
                                    @if (User.IsPharmaLexUser())
                                    {
                                        <li><a href="https://myaccount.microsoft.com" target="_blank" rel="noopener">View Account</a></li>
                                    }
                                    else
                                    {
                                        <li><a href="/MicrosoftIdentity/Account/ResetPassword">Reset Password</a></li>
                                    }
                                    <li><a href="/logout">Sign out</a></li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </nav>
            }
        </div>
    </header>

    @RenderBody()

    <div id="about-dialog" class="dialog-surface hidden">
        <div class="dialog-container">
            <i class="icon-cancel-circled dialog-closer"></i>
            <div class="dialog-content">
                <h5>About SmartTRACE</h5>
                <p><strong>Version</strong> @(Assembly.GetEntryAssembly().GetCustomAttribute<AssemblyInformationalVersionAttribute>().InformationalVersion).@AppSettings.BuildInfo.Substring(AppSettings.BuildInfo.IndexOf(":") + 1)</p>
                <p><strong>Build date</strong> @(Assembly.GetEntryAssembly().GetCustomAttribute<AssemblyDescriptionAttribute>().Description)</p>
                <footer>@(Assembly.GetEntryAssembly().GetCustomAttribute<AssemblyCopyrightAttribute>().Copyright)</footer>
            </div>
        </div>
    </div>

    @await RenderSectionAsync("Scripts", required: false)

    <script src="@Cdn.GetUrl("js/navbar.js")"></script>
    <script>
        if (typeof pageConfig !== 'undefined' && pageConfig.appElement) {
            var vueApp = Vue.createApp({ mixins: [pageConfig] });
            if (typeof store !== 'undefined') {
                vueApp.use(store);
            }
        }
    </script>

    @await RenderSectionAsync("VueComponentScripts", required: false)

    <script>
        //mount vue instance here with components now attached(if they exist)
        if (typeof pageConfig !== 'undefined' && pageConfig.appElement) {
            var vueRootComponent = vueApp.mount(pageConfig.appElement);
        }
    </script>
</body>
</html>


<script>
    (function () {
        var plx = plx || {};
        plx.about = {
            show: () => document.getElementById('about-dialog').classList.remove('hidden'),
            close: () => document.getElementById('about-dialog').classList.add('hidden')
        }
        document.getElementById('about-nav-link').addEventListener('click', plx.about.show);
        document.querySelectorAll('.dialog-surface, .dialog-closer').forEach(x => x.addEventListener('click', plx.about.close));
        document.querySelector('.dialog-container').addEventListener('click', (e) => e.stopPropagation());
        document.documentElement.style.setProperty('--main-theme-color', '@Configuration["LayoutSettings:mainColor"]');
    })();

    (function () {
        document.querySelectorAll('.main-mega-menu-container .main-sub-nav > li').forEach((x, i) => {
            x.addEventListener('click', (e) => {
                let sibs = e.target.parentElement.children;
                [].forEach.call(sibs, x => x.classList.remove('selected'));
                e.target.classList.add('selected');
            });
            x.firstElementChild.style.top = `-${i * 3}rem`;
        });
    })();
    function findUnmatchedCharacters(inputValue) {
        var encodedValue = $('<div/>').text(inputValue).html();
        encodedValue = encodedValue.replace(/&amp;/g, "&").replace('(', '%28;').replace(')', '%29;');
        var unmatchedChars = Vue.ref([]);
        for (var i = 0, j = 0; i < inputValue.length; i++, j++) {
            if (inputValue[i] !== encodedValue[j]) {
                j = encodedValue.indexOf(";", j);
                if (!unmatchedChars.value.includes(inputValue[i])) {
                    unmatchedChars.value.push(inputValue[i]);
                }
            }
        }
        return unmatchedChars;
    }
</script>