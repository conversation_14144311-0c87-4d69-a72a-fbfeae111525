﻿using PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces
{
    public class EctdModule1Data { }

    public interface IEctdModule
    {
        List<CtdSectionBase> Content { get; }
    }

    public interface IEctdModule1 : IEctdModule
    {
        List<EctdModule1Data> Data { get; }
        string DtdVersion { get; }
        string Region { get; }
    }
}
