﻿using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers.Services;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Services;

public class PicklistDataServiceTests
{
    private readonly IDistributedCacheServiceFactory cacheFactory;
    private readonly IEntityCacheServiceProxy<PicklistData> picklistDataCache;
    private readonly PicklistDataService picklistDataService;

    public PicklistDataServiceTests()
    {
        cacheFactory = Substitute.For<IDistributedCacheServiceFactory>();
        picklistDataCache = Substitute.For<IEntityCacheServiceProxy<PicklistData>>();
        cacheFactory.CreateEntity<PicklistData>().Returns(picklistDataCache);
        picklistDataService = new PicklistDataService(cacheFactory);
    }

    [Fact]
    public async Task IsPicklistNameDuplicate_DuplicatePicklistName_ReturnsTrue()
    {
        // Arrange
        string picklistName = "DuplicateName";
        var existingPicklistData = new List<PicklistData> { new PicklistData { Name = picklistName } };
        picklistDataCache.WhereAsync(Arg.Any<Expression<Func<PicklistData, bool>>>())
            .Returns(existingPicklistData);

        // Act
        var result = await picklistDataService.IsPicklistNameDuplicate(picklistName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsPicklistNameDuplicate_UniquePicklistName_ReturnsFalse()
    {
        // Arrange
        string picklistName = "UniqueName";
        var existingPicklistData = new List<PicklistData>();
        picklistDataCache.WhereAsync(Arg.Any<Expression<Func<PicklistData, bool>>>())
            .Returns(existingPicklistData);

        // Act
        var result = await picklistDataService.IsPicklistNameDuplicate(picklistName);

        // Assert
        Assert.False(result);
    }
}
