﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    [Authorize("BusinessAdmin")]
    public class ProjectsController : BaseController
    {
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IMapper _mapper;
        private readonly IProjectExport _projectExport;

        public ProjectsController(IDistributedCacheServiceFactory cache, IMapper mapper, IProjectExport projectExport)
        {
            this._cache = cache;
            this._mapper = mapper;
            this._projectExport = projectExport;
        }

        [HttpGet, Route("/projects")]
        public async Task<IActionResult> Index()
        {
            var projectCache = _cache.CreateMappedEntity<Project, ProjectModel>()
                .Configure(o => o
                    .Include(x => x.Client));
            var projects = await projectCache.AllAsync();
            return View(projects);
        }

        [HttpGet, Route("/projects/new/{clientId}")]
        public IActionResult New(int clientId)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            return View("EditProject", new ProjectModel()
            {
                ClientId = clientId
            });
        }

        [HttpPost, Route("/projects/new/{clientId}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveNew(int clientId, [Bind("Id,Name,Code,OpportunityNumber,ClientId")]ProjectModel model)
        {
            if (!ModelState.IsValid)
            {
                return View("EditProject", model);
            }

            var projectCache = _cache.CreateTrackedEntity<Project>();
            var project = _mapper.Map<Project>(model);
            projectCache.Add(project);
            await projectCache.SaveChangesAsync();

            this.AddConfirmationNotification($"<em>{project.Name}</em> created");
            return Redirect($"/clients/edit/{clientId}");
        }

        [HttpGet, Route("/projects/edit/{id}")]
        public async Task<IActionResult> Edit(int id)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var projectCache = _cache.CreateMappedEntity<Project, ProjectModel>();
            var existingProject = await projectCache.FirstOrDefaultAsync(x => x.Id == id);
            return View("EditProject", existingProject);
        }

        [HttpPost, Route("/projects/edit/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveEdit(int id, ProjectModel model)
        {
            if (!this.ModelState.IsValid)
            {
                return View("EditProject", model);
            }

            var projectCache = _cache.CreateTrackedEntity<Project>();
            var project = await projectCache.FirstOrDefaultAsync(x => x.Id == id);
            _mapper.Map(model, project);
            await projectCache.SaveChangesAsync();
            this.AddConfirmationNotification($"<em>{project.Name}</em> updated");
            return Redirect($"/clients/edit/{project.ClientId}");
        }

        [HttpPost("/projects/export"), Authorize(Policy = "Reader"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Export()
        {
            return File(await this._projectExport.Export(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"Projects_{System.DateTime.Now.ToString("yyyy-MM-dd")}.xlsx");
        }
    }
}
