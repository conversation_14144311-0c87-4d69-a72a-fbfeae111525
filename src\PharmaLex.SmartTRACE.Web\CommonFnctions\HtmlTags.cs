﻿using System.Text.RegularExpressions;

namespace PharmaLex.SmartTRACE.Web.HTMLTags
{
    public static class HtmlTags
    {
        public static bool CheckHTMLTags(string html)
        {
            if (!string.IsNullOrEmpty(html))
                return Regex.IsMatch(html, new GeneratedRegexAttribute(@"<.*?>|<|>").Pattern, RegexOptions.NonBacktracking);
            else
                return false;
        }
    }
}
