﻿using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32
{
    public class CustomCtdSection : CtdSectionBase
    {
        private readonly string ctdName;
        private readonly string ctdSection;
        private readonly CtdSectionBase[] childNodes;
        private readonly object[] items;
        public CustomCtdSection(string ctdName, string ctdSection, CtdSectionBase[] childNodes, object[] items = null)
        {
            this.ctdSection = ctdSection;
            this.ctdName = ctdName;
            this.childNodes = childNodes;
            this.items = items;
        }

        public override string CtdName => this.ctdName;

        public override string CtdSection => this.ctdSection;

        public override List<CtdSectionBase> ChildNodes => this.childNodes.ToList();

        public object[] Items => this.items;
    }
}
