﻿CREATE TRIGGER [dbo].[RegulatoryAuthority_Insert] ON [dbo].[RegulatoryAuthority]
FOR INSERT AS
INSERT INTO [Audit].[RegulatoryAuthority_Audit]
SELECT 'I', [Id], [Name], [Market], [Url], [NationalAuthority], [Acronym], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[RegulatoryAuthority_Update] ON [dbo].[RegulatoryAuthority]
FOR UPDATE AS
INSERT INTO [Audit].[RegulatoryAuthority_Audit]
SELECT 'U', [Id], [Name], [Market], [Url], [NationalAuthority], [Acronym], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[RegulatoryAuthority_Delete] ON [dbo].[RegulatoryAuthority]
FOR DELETE AS
INSERT INTO [Audit].[RegulatoryAuthority_Audit]
SELECT 'D', [Id], [Name], [Market], [Url], [NationalAuthority], [Acronym], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO