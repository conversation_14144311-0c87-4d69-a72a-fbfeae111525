﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using PharmaLex.Authentication;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.Helpers;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.Helpers.Builders;
using PharmaLex.SmartTRACE.Web.Helpers.Constants;
using PharmaLex.SmartTRACE.Web.Helpers.Extensions;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Models.ViewModels;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Security.Claims;
using System.Threading.Tasks;

#nullable enable

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public class ApplicationService : IApplicationService
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper mapper;
        private readonly IAuthorizationService authorizationService;
        private readonly IApplicationRepository applicationRepository;

        private static Dictionary<string, Expression<Func<Application, object>>> sortExpressions =
        new()
        {
                 { TableFilterConstants.ApplicationNumber, x => x.ApplicationNumber }
        };


        public ApplicationService(IDistributedCacheServiceFactory cache, IMapper mapper, IAuthorizationService authorizationService, IApplicationRepository applicationRepository)
        {
            this.cache = cache;
            this.mapper = mapper;
            this.authorizationService = authorizationService;
            this.applicationRepository = applicationRepository;
        }

        public async Task<IList<ApplicationViewModel>> ListApplications(ClaimsPrincipal user, ApplicationFilterModel? model = null)
        {
            IList<Application> allApplications = await GetApplications(user, model);

            IList<ApplicationCountry> allApplicationCountries = await GetApplicationCountries(allApplications);

            IList<Client> clients = await GetClients(allApplications);

            var applications = new List<ApplicationViewModel>();

            if (!(await authorizationService.AuthorizeAsync(user, "BusinessAdmin")).Succeeded)
            {
                allApplications = allApplications.Where(x => x.LifecycleStateId != (int)CommonLifecycleState.Obsolete).ToList();
            }

            foreach (var app in allApplications)
            {
                var appCountries = allApplicationCountries.Where(x => x.ApplicationId == app.Id);

                var client = clients.FirstOrDefault(x => x.Id == app.ApplicationProduct.FirstOrDefault()?.Product?.ClientId);

                applications.Add(new ApplicationViewModel()
                {
                    Id = app.Id,
                    Region = string.Join(", ", appCountries.Select(x => x.Country).Select(x => x.Region.Name).Distinct().OrderBy(x => x).ToList()),
                    Country = string.Join(", ", appCountries.Where(x => x.AuthorityRoleId == (int)AuthorityRoles.ReferenceMemberState || x.AuthorityRoleId is null)
                                                      .Select(x => x.Country).Select(x => x.Name).Distinct().OrderBy(x => x).ToList()),
                    ApplicationNumber = app.ApplicationNumber,
                    ProcedureType = (await PicklistHelper.ExtractPicklist(cache, app.ProcedureTypeId))?.Name,
                    ApplicationType = (await PicklistHelper.ExtractPicklist(cache, app.ApplicationTypeId))?.Name,
                    MedicinalProductDomain = (await PicklistHelper.ExtractPicklist(cache, app.MedicinalProductDomainId))?.Name,
                    ClientName = client?.Name,
                    Product = string.Join(" | ", app.ApplicationProduct.Select(x => x.Product)
                                                                  .Select(async x => $"{x.Name}, {(await PicklistHelper.ExtractPicklist(cache, x.DosageFormId)).Name}, {x.Strength}")
                                                                  .Select(x => x.GetAwaiter().GetResult())
                                                                  .ToList()),
                    Comments = app.Comments,
                    LifecycleState = ((CommonLifecycleState)app.LifecycleStateId).GetDescription()
                });
            }

            return applications;
        }

        public async Task<ApiPagedListResult<ApplicationViewModel>> GetPagedApplicationsAsync(ClaimsPrincipal user, int skip, int take, ApplicationFilterModel model, string? sort)
        {
            var currentUserId = 122; // user.GetClaimValue<int>("plx:userid");
            var allowedClientIds = (await this.cache.CreateEntity<UserClient>().WhereAsync(x => x.UserId == currentUserId)).Select(x => x.ClientId).ToList();

            Expression<Func<Application, bool>> expression = x => allowedClientIds.Contains(x.ApplicationProduct.First().Product.ClientId);

            var isUserInBusinessAdminRole = (await authorizationService.AuthorizeAsync(user, "BusinessAdmin")).Succeeded;
            expression = expression.AndAlso(x => (!isUserInBusinessAdminRole && x.LifecycleStateId != (int)SubmissionLifeCycleState.Obsolete) || isUserInBusinessAdminRole);

            if (model != null)
            {
                expression = GetApplicationFilterExpression(model, expression);
            }

            var predicate = ExpressionBuilder<Application>.BuildSortExpression(sort, sortExpressions, x => x.ApplicationNumber);

            var query = applicationRepository.GetQueryableItems(a => a.Include(x => x.ApplicationProduct)
                                                                        .ThenInclude(y => y.Product)
                                                                            .ThenInclude(p => p.Client)
                                                                      .Include(x => x.ApplicationCountry)
                                                                        .ThenInclude(y => y.Country)
                                                                            .ThenInclude(c => c.Region));

            var entities = await query.FilterItems(expression, predicate, skip, take).ToListAsync();

            var applications = new List<ApplicationViewModel>();

            IList<ApplicationCountry> allApplicationCountries = await GetApplicationCountries(entities);

            IList<Client> clients = await GetClients(entities);

            foreach (var app in entities)
            {
                var appCountries = allApplicationCountries.Where(x => x.ApplicationId == app.Id);

                var client = clients.FirstOrDefault(x => x.Id == app.ApplicationProduct.First().Product.ClientId);

                applications.Add(new ApplicationViewModel()
                {
                    Id = app.Id,
                    Region = string.Join(", ", appCountries.Select(x => x.Country).Select(x => x.Region.Name).Distinct().OrderBy(x => x).ToList()),
                    Country = string.Join(", ", appCountries.Where(x => x.AuthorityRoleId == (int)AuthorityRoles.ReferenceMemberState || x.AuthorityRoleId is null)
                                                      .Select(x => x.Country).Select(x => x.Name).Distinct().OrderBy(x => x).ToList()),
                    ApplicationNumber = app.ApplicationNumber,
                    ProcedureType = (await PicklistHelper.ExtractPicklist(cache, app.ProcedureTypeId))?.Name,
                    ApplicationType = (await PicklistHelper.ExtractPicklist(cache, app.ApplicationTypeId))?.Name,
                    MedicinalProductDomain = (await PicklistHelper.ExtractPicklist(cache, app.MedicinalProductDomainId))?.Name,
                    ClientName = client?.Name,
                    Product = string.Join(" | ", app.ApplicationProduct.Select(x => x.Product)
                                                                  .Select(async x => $"{x.Name}, {(await PicklistHelper.ExtractPicklist(cache, x.DosageFormId)).Name}, {x.Strength}")
                                                                  .Select(x => x.GetAwaiter().GetResult())
                                                                  .ToList()),
                    Comments = app.Comments,
                    LifecycleState = ((CommonLifecycleState)app.LifecycleStateId).GetDescription()
                });
            }

            return new ApiPagedListResult<ApplicationViewModel>(
               applications,
               new()
               {
                   TotalItemCount = await query.CountAsync(),
                   FilteredCount = await query.CountAsync(expression),
                   Offset = skip,
                   Limit = take,
               });
        }

        public async Task<ApplicationModel> GetApplication(int applicationId)
        {
            var appCache = cache.CreateMappedEntity<Application, ApplicationModel>();
            return await appCache.FirstOrDefaultAsync(x => x.Id == applicationId);
        }

        public async Task<Application> InsertApplication(EditApplicationViewModel model, IList<PicklistDataModel> allPicklists)
        {
            var appCache = cache.CreateTrackedEntity<Application>();
            var application = mapper.Map<ApplicationModel, Application>(model.Application);
            appCache.Add(application);
            await appCache.SaveChangesAsync();

            (int? decentralisedId, int? mutualId, int? centralisedId, int? asmfWorkshareId) = GetProcedureTypeIds(allPicklists);

            var appCountryCache = cache.CreateTrackedEntity<ApplicationCountry>();
            foreach (var id in model.Application.CountriesIds)
            {
                var appCountry = new ApplicationCountry()
                {
                    ApplicationId = application.Id,
                    CountryId = id
                };
                if (model.Application.ProcedureTypeId == decentralisedId || model.Application.ProcedureTypeId == mutualId || model.Application.ProcedureTypeId == asmfWorkshareId)
                {
                    appCountry.AuthorityRoleId = (int)AuthorityRoles.ConcernedMemberState;
                }
                appCountryCache.Add(appCountry);
            }

            if (model.Application.CountryId != null)
            {
                var singleAppCountry = new ApplicationCountry()
                {
                    ApplicationId = application.Id,
                    CountryId = (int)model.Application.CountryId
                };
                if (model.Application.ProcedureTypeId == decentralisedId || model.Application.ProcedureTypeId == mutualId || model.Application.ProcedureTypeId == asmfWorkshareId)
                {
                    singleAppCountry.AuthorityRoleId = (int)AuthorityRoles.ReferenceMemberState;
                }
                appCountryCache.Add(singleAppCountry);
            }

            await appCountryCache.SaveChangesAsync();

            var appProductCache = cache.CreateTrackedEntity<ApplicationProduct>();
            foreach (var id in model.Application.ProductsIds)
            {
                appProductCache.Add(new ApplicationProduct()
                {
                    ApplicationId = application.Id,
                    ProductId = id
                });
            }

            await appProductCache.SaveChangesAsync();

            return application;
        }

        public async Task<Application> UpdateApplication(EditApplicationViewModel model, IList<PicklistDataModel> allPicklists)
        {
            var applicationCache = cache.CreateTrackedEntity<Application>();
            var application = await applicationCache.FirstOrDefaultAsync(x => x.Id == model.Application.Id);
            mapper.Map(model.Application, application);
            await applicationCache.SaveChangesAsync();

            (int? decentralisedId, int? mutualId, int? centralisedId, int? asmfWorkshareId) = GetProcedureTypeIds(allPicklists);

            var appCountryCache = cache.CreateTrackedEntity<ApplicationCountry>();
            var appCountries = (await appCountryCache.WhereAsync(x => x.ApplicationId == model.Application.Id)).ToList();

            var missingCountries = model.Application.CountriesIds.Where(x => !appCountries.Select(y => y.CountryId).Contains(x)).ToList();
            foreach (var missingCountry in missingCountries)
            {
                var appCountry = new ApplicationCountry
                {
                    CountryId = missingCountry,
                    ApplicationId = model.Application.Id
                };
                if (application.ProcedureTypeId == decentralisedId || application.ProcedureTypeId == mutualId || application.ProcedureTypeId == asmfWorkshareId)
                {
                    appCountry.AuthorityRoleId = (int)AuthorityRoles.ConcernedMemberState;
                }
                appCountryCache.Add(appCountry);
            }

            var deletedAppCountries = appCountries.Where(x => !model.Application.CountriesIds.Select(y => y).Contains(x.CountryId)).ToList();

            foreach (var deletedAppCountry in deletedAppCountries)
            {
                appCountryCache.Remove(deletedAppCountry);
            }

            var existingAppCountries = appCountries.Except(deletedAppCountries);

            foreach (var existingAppCountry in existingAppCountries)
            {
                if (application.ProcedureTypeId == decentralisedId || application.ProcedureTypeId == mutualId || application.ProcedureTypeId == asmfWorkshareId)
                {
                    existingAppCountry.AuthorityRoleId = (int)AuthorityRoles.ConcernedMemberState;
                }
                else if (application.ProcedureTypeId == centralisedId)
                {
                    existingAppCountry.AuthorityRoleId = null;
                }
            }

            if (model.Application.CountryId != null)
            {
                var country = new ApplicationCountry()
                {
                    CountryId = (int)model.Application.CountryId,
                    ApplicationId = model.Application.Id
                };
                if (application.ProcedureTypeId == decentralisedId || application.ProcedureTypeId == mutualId || application.ProcedureTypeId == asmfWorkshareId)
                {
                    country.AuthorityRoleId = (int)AuthorityRoles.ReferenceMemberState;
                }
                appCountryCache.Add(country);
            }

            await appCountryCache.SaveChangesAsync();

            var appProductCache = cache.CreateTrackedEntity<ApplicationProduct>();
            var appProducts = (await appProductCache.WhereAsync(x => x.ApplicationId == model.Application.Id)).ToList();
            var missingProductIds = model.Application.ProductsIds.Where(x => !appProducts.Select(y => y.ProductId).Contains(x)).ToList();

            foreach (var missingProductId in missingProductIds)
            {
                appProductCache.Add(new ApplicationProduct
                {
                    ProductId = missingProductId,
                    ApplicationId = model.Application.Id
                });
            }

            var deletedAppProductIds = appProducts.Where(x => !model.Application.ProductsIds.Select(y => y).Contains(x.ProductId)).ToList();

            foreach (var deletedAppProductId in deletedAppProductIds)
            {
                appProductCache.Remove(deletedAppProductId);
            }

            await appProductCache.SaveChangesAsync();

            return application;
        }

        private Task<List<Client>> GetClients(IList<Application> allApplications)
        {
            var clientCache = this.cache.CreateEntity<Client>();

            var clientIds = allApplications.Select(x => x.ApplicationProduct.FirstOrDefault()?.Product.ClientId).Distinct();

            return clientCache.WhereAsync(x => clientIds.Contains(x.Id));
        }

        private Task<List<ApplicationCountry>> GetApplicationCountries(IList<Application> allApplications)
        {
            var applicationIds = allApplications.Select(x => x.Id).ToList();

            IEntityCacheServiceProxy<ApplicationCountry> appCountriesCache = GetApplicationCountryCache();

            return appCountriesCache.WhereAsync(x => applicationIds.Contains(x.ApplicationId));
        }

        private async Task<IList<Application>> GetApplications(ClaimsPrincipal user, ApplicationFilterModel? model = null)
        {
            IEntityCacheServiceProxy<ApplicationProduct> appProductCache = GetApplicationProductCache();

            var currentUserId = user.GetClaimValue<int>("plx:userid");
            var allowedClientIds = (await this.cache.CreateEntity<UserClient>().WhereAsync(x => x.UserId == currentUserId)).Select(x => x.ClientId).ToList();

            Expression<Func<ApplicationProduct, bool>> appProductExpression = x => allowedClientIds.Contains(x.Product.ClientId);

            if (model != null)
            {
                appProductExpression = GetFilterExpression(model, appProductExpression);
            }

            var allApplications = (await appProductCache.WhereAsync(appProductExpression))
                .GroupBy(x => new
                {
                    x.Application.Id,
                    x.Application.ApplicationNumber,
                    x.Application.ProcedureTypeId,
                    x.Application.ApplicationTypeId,
                    x.Application.MedicinalProductDomainId,
                    x.Application.Comments,
                    x.Application.LifecycleStateId
                })
                .Select(x => new Application
                {
                    Id = x.Key.Id,
                    ApplicationNumber = x.Key.ApplicationNumber,
                    ProcedureTypeId = x.Key.ProcedureTypeId,
                    ApplicationTypeId = x.Key.ApplicationTypeId,
                    MedicinalProductDomainId = x.Key.MedicinalProductDomainId,
                    Comments = x.Key.Comments,
                    LifecycleStateId = x.Key.LifecycleStateId,
                    ApplicationProduct = x.ToList()
                }).ToList();


            return allApplications;
        }

        private IEntityCacheServiceProxy<ApplicationCountry> GetApplicationCountryCache()
        {
            return this.cache.CreateEntity<ApplicationCountry>()
                                               .Configure(o => o
                                                    .Include(x => x.Country)
                                                        .ThenInclude(x => x.Region));
        }

        private Expression<Func<ApplicationProduct, bool>> GetFilterExpression(ApplicationFilterModel model, Expression<Func<ApplicationProduct, bool>> expression)
        {
            if (!string.IsNullOrEmpty(model.ClientName))
            {
                expression = expression.AndAlso(i => i.Product.Client.Name.ToLower().Contains(model.ClientName.ToLower()));
            }

            if (!string.IsNullOrEmpty(model.ApplicationNumber))
            {
                expression = expression.AndAlso(i => i.Application.ApplicationNumber.ToLower().Contains(model.ApplicationNumber.ToLower()));
            }

            if (!string.IsNullOrEmpty(model.MedicinalProductDomain))
            {
                var medicinalProductDomains = model.MedicinalProductDomain.Split(',');
                var medicinalProductDomainIds = new List<int?>();

                foreach (var medicinalProductDomain in medicinalProductDomains)
                {
                    var medicinalProductDomainId = PicklistHelper.GetPicklistByName(cache, medicinalProductDomain, (int)PicklistType.MedicinalProductDomain).GetAwaiter().GetResult()?.Id;
                    if (medicinalProductDomainId != null)
                    {
                        medicinalProductDomainIds.Add(medicinalProductDomainId);
                    }
                }

                expression = expression.AndAlso(i => medicinalProductDomainIds.Contains(i.Application.MedicinalProductDomainId));
            }

            if (!string.IsNullOrEmpty(model.ProcedureType))
            {
                var procedureTypes = model.ProcedureType.Split(',');
                var procedureTypeIds = new List<int?>();

                foreach (var procedureType in procedureTypes)
                {
                    var procedureTypeId = PicklistHelper.GetPicklistByName(cache, procedureType, (int)PicklistType.ProcedureType).GetAwaiter().GetResult()?.Id;
                    if (procedureTypeId != null)
                    {
                        procedureTypeIds.Add(procedureTypeId);
                    }
                }

                expression = expression.AndAlso(i => procedureTypeIds.Contains(i.Application.ProcedureTypeId));
            }

            if (!string.IsNullOrEmpty(model.LifecycleState))
            {
                var lifecycleStates = model.LifecycleState.Split(',');
                var lifecycleStateIds = new List<int>();

                foreach (var lifecycleState in lifecycleStates)
                {
                    var lifecycleStateId = (int)Extensions.EnumExtensions.GetValueFromDescription<CommonLifecycleState>(lifecycleState);
                    lifecycleStateIds.Add(lifecycleStateId);
                }

                expression = expression.AndAlso(i => lifecycleStateIds.Contains(i.Application.LifecycleStateId));
            }

            if (!string.IsNullOrEmpty(model.ApplicationType))
            {
                var applicationTypes = model.ApplicationType.Split(',');
                var applicationTypeIds = new List<int?>();

                foreach (var applicationType in applicationTypes)
                {
                    var applicationTypeId = PicklistHelper.GetPicklistByName(cache, applicationType, (int)PicklistType.ApplicationType).GetAwaiter().GetResult()?.Id;
                    if (applicationTypeId != null)
                    {
                        applicationTypeIds.Add(applicationTypeId);
                    }
                }

                expression = expression.AndAlso(i => applicationTypeIds.Contains(i.Application.ApplicationTypeId));
            }
            return expression;
        }

        private IEntityCacheServiceProxy<ApplicationProduct> GetApplicationProductCache(bool includeClient = false)
        {
            if (includeClient)
            {
                return this.cache.CreateEntity<ApplicationProduct>()
                              .Configure(o => o.Include(x => x.Application).Include(x => x.Product).ThenInclude(x => x.Client));
            }

            return this.cache.CreateEntity<ApplicationProduct>()
                              .Configure(o => o.Include(x => x.Application).Include(x => x.Product));


        }

        private (int?, int?, int?, int?) GetProcedureTypeIds(IList<PicklistDataModel> procedureTypes)
        {
            var decentralisedId = procedureTypes.FirstOrDefault(x => x.Name == "Decentralised Procedure")?.Id;
            var mutualId = procedureTypes.FirstOrDefault(x => x.Name == "Mutual Recognition Procedure")?.Id;
            var centralisedId = procedureTypes.FirstOrDefault(x => x.Name == "Centralised Procedure")?.Id;
            var asmfWorkshareId = procedureTypes.FirstOrDefault(x => x.Name == "ASMF Worksharing Procedure")?.Id;

            return (decentralisedId, mutualId, centralisedId, asmfWorkshareId);
        }

        public Expression<Func<Application, bool>> GetApplicationFilterExpression(ApplicationFilterModel model, Expression<Func<Application, bool>> expression)
        {
            if (!string.IsNullOrEmpty(model.ClientName))
            {
                expression = expression.AndAlso(i => i.ApplicationProduct.First().Product.Client.Name.ToLower().Contains(model.ClientName.ToLower()));
            }

            if (!string.IsNullOrEmpty(model.ApplicationNumber))
            {
                expression = expression.AndAlso(i => i.ApplicationNumber.ToLower().Contains(model.ApplicationNumber.ToLower()));
            }

            if (!string.IsNullOrEmpty(model.MedicinalProductDomain))
            {
                var medicinalProductDomains = model.MedicinalProductDomain.Split(',');
                var medicinalProductDomainIds = new List<int?>();

                foreach (var medicinalProductDomain in medicinalProductDomains)
                {
                    var medicinalProductDomainId = PicklistHelper.GetPicklistByName(cache, medicinalProductDomain, (int)PicklistType.MedicinalProductDomain).GetAwaiter().GetResult()?.Id;
                    if (medicinalProductDomainId != null)
                    {
                        medicinalProductDomainIds.Add(medicinalProductDomainId);
                    }
                }

                expression = expression.AndAlso(i => medicinalProductDomainIds.Contains(i.MedicinalProductDomainId));
            }

            if (!string.IsNullOrEmpty(model.ProcedureType))
            {
                var procedureTypes = model.ProcedureType.Split(',');
                var procedureTypeIds = new List<int?>();

                foreach (var procedureType in procedureTypes)
                {
                    var procedureTypeId = PicklistHelper.GetPicklistByName(cache, procedureType, (int)PicklistType.ProcedureType).GetAwaiter().GetResult()?.Id;
                    if (procedureTypeId != null)
                    {
                        procedureTypeIds.Add(procedureTypeId);
                    }
                }

                expression = expression.AndAlso(i => procedureTypeIds.Contains(i.ProcedureTypeId));
            }

            if (!string.IsNullOrEmpty(model.LifecycleState))
            {
                var lifecycleStates = model.LifecycleState.Split(',');
                var lifecycleStateIds = new List<int>();

                foreach (var lifecycleState in lifecycleStates)
                {
                    var lifecycleStateId = (int)Extensions.EnumExtensions.GetValueFromDescription<CommonLifecycleState>(lifecycleState);
                    lifecycleStateIds.Add(lifecycleStateId);
                }

                expression = expression.AndAlso(i => lifecycleStateIds.Contains(i.LifecycleStateId));
            }

            if (!string.IsNullOrEmpty(model.ApplicationType))
            {
                var applicationTypes = model.ApplicationType.Split(',');
                var applicationTypeIds = new List<int?>();

                foreach (var applicationType in applicationTypes)
                {
                    var applicationTypeId = PicklistHelper.GetPicklistByName(cache, applicationType, (int)PicklistType.ApplicationType).GetAwaiter().GetResult()?.Id;
                    if (applicationTypeId != null)
                    {
                        applicationTypeIds.Add(applicationTypeId);
                    }
                }

                expression = expression.AndAlso(i => applicationTypeIds.Contains(i.ApplicationTypeId));
            }

            if (!string.IsNullOrEmpty(model.Country))
            {
                var filteredCountry = this.cache.CreateEntity<Country>().FirstOrDefaultAsync(x => x.Name == model.Country).GetAwaiter().GetResult();

                if (filteredCountry is not null)
                {
                    expression = expression.AndAlso(i => i.ApplicationCountry.Where(x => x.AuthorityRoleId == (int)AuthorityRoles.ReferenceMemberState || x.AuthorityRoleId == null)
                    .Select(x => x.CountryId).Contains(filteredCountry.Id));
                }
            }

            if (!string.IsNullOrEmpty(model.Region))
            {
                var filteredRegion = this.cache.CreateEntity<Region>().FirstOrDefaultAsync(x => x.Name == model.Region).GetAwaiter().GetResult();

                if (filteredRegion is not null)
                {
                    expression = expression.AndAlso(i => i.ApplicationCountry.Select(x => x.Country.RegionId).Contains(filteredRegion.Id));
                }
            }

            if (!string.IsNullOrEmpty(model.Product))
            {
                expression = expression.AndAlso(i => i.ApplicationProduct.First().Product.Name.ToLower().Contains(model.Product.ToLower()));
            }

            return expression!;
        }
    }
}
