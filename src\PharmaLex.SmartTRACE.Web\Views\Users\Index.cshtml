﻿@model IEnumerable<UserModel>
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@using AutoMapper
@using PharmaLex.SmartTRACE.Entities
@using PharmaLex.SmartTRACE.Entities.Enums
@using System.Collections.Generic;
@using PharmaLex.Caching.Data;
@inject IDistributedCacheServiceFactory cache
@inject IMapper mapper
@{
    ViewData["Title"] = "Manage Users";
    IEnumerable<Claim> _claims = (await cache.CreateEntity<Claim>().AllAsync());
    var claims = _claims.Select(c => new { id = c.Id, name = c.Name });

    var userTypes = mapper.Map<IEnumerable<UserTypeList>>(Enum.GetValues(typeof(UserType)));
}

<div id="users" class="manage-container">
    <header class="manage-header">
        <h2>Manage Users</h2>
        <a id="exportButton" class="button icon-button-download">Export</a>
        <a class="button icon-button-add" href="/manage/users/new">Add user</a>
        <a class="button icon-button-add" href="/manage/external-users/new">Add external user</a>
    </header>
    <filtered-table :items="users" :columns="columns" :filters="filters" :link="link"></filtered-table>
    <form id="exportForm" style="display: none;" method="post" action="/manage/users/export">
        @Html.AntiForgeryToken()
    </form>
</div>

@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#users',
            data() {
                return {
                    link: '/manage/users/edit/',
                    users: @Html.Raw(JsonConvert.SerializeObject(Model, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })),
                    externalUserTypeId: @Html.Raw((int)UserType.External),
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'displayFullName',
                                sortKey: 'displayFullName',
                                header: 'Name',
                                type: 'text'
                            },
                            {
                                dataKey: 'email',
                                sortKey: 'email',
                                type: 'text'
                            },
                            {
                                dataKey: 'displayUserType',
                                sortKey: 'displayUserType',
                                header: 'User Type',
                                type: 'text'
                            },
                            {
                                dataKey: 'displayClaimsText',
                                sortKey: 'displayClaimsText',
                                header: 'Roles',
                                type: 'text'
                            },
                            {
                                dataKey: 'displayClientsText',
                                sortKey: 'displayClientsText',
                                header: 'Clients',
                                type: 'text'
                            },
                            {
                                dataKey: 'autoAccessClients',
                                sortKey: 'autoAccessClients',
                                header: 'Auto Access Client',
                                type: 'bool'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'displayFullName',
                            options: [],
                            type: 'search',
                            header: 'Search Name',
                            fn: v => p => p.displayFullName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'email',
                            options: [],
                            type: 'search',
                            header: 'Search Email',
                            fn: v => p => p.email.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'displayUserType',
                            options: @Html.Raw(userTypes.ToJson()),
                            filterCollection: 'displayUserType',
                            display: 'id',
                            type: 'select',
                            header: 'Filter By User Type',
                            fn: v => p => p.displayUserType === v,
                            convert: v => v
                        },
                        {
                            key: 'displayClaimsText',
                            options: @Html.Raw(Json.Serialize(claims)),
                            filterCollection: 'claims',
                            display: 'name',
                            type: 'select',
                            header: 'Filter By Role',
                            fn: v => p => p.claims.includes(v),
                            convert: v => parseInt(v)
                        },
                        {
                            key: 'displayClientsText',
                            options: [],
                            type: 'search',
                            header: 'Search Clients',
                            fn: v => p => p.displayClientsText.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'autoAccessClients',
                            options: [{ "id": true, "name": "Yes" }, { "id": false, "name": "No" }],
                            filterCollection: 'autoAccessClients',
                            display: 'name',
                            type: 'select',
                            header: 'Filter By AutoAccessClients',
                            fn: v => p => p.autoAccessClients === v,
                            convert: v => v
                        }
                    ]
                };
            },
            mounted() {
                document.getElementById('exportButton').addEventListener('click', (e) => {
                    e.preventDefault();
                    document.getElementById('exportForm').submit();
                });
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}