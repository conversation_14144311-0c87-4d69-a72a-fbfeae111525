﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="coverlet.collector" Version="6.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.10" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.10.0" />
    <PackageReference Include="NSubstitute" Version="5.1.0" />
    <PackageReference Include="PharmaLex.Caching.Data" Version="8.0.0.202" />
    <PackageReference Include="xunit" Version="2.8.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="XunitXml.TestLogger" Version="3.1.20" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\PharmaLex.SmartTRACE.Web.Helpers\PharmaLex.SmartTRACE.Web.Helpers.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.SmartTRACE.Web\PharmaLex.SmartTRACE.Web.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="TestSubmissionFiles\" />
    <Folder Include="TestSubmissionFiles\unittestsubau\0000\m1\au\100-correspondence\1001-cover\" />
    <Folder Include="TestSubmissionFiles\unittestsubch\0000\m1\ch\common\12-foapplvar\122-form-add\1221-formfulldecl\" />
    <Folder Include="TestSubmissionFiles\unittestsubeu\0000\m1\eu\10-cover\common\" />
    <Folder Include="TestSubmissionFiles\unittestsubus\0001\m1\us\1-forms\" />
  </ItemGroup>

	<ItemGroup>
		<None Update="TestSubmissionFiles\unittestsubau\0000\index.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="TestSubmissionFiles\unittestsubau\0000\m1\au\au-regional.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="TestSubmissionFiles\unittestsubch\0000\index.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="TestSubmissionFiles\unittestsubch\0000\m1\ch\ch-regional.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="TestSubmissionFiles\unittestsubeu\0000-workingdocuments\template.pdf">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="TestSubmissionFiles\unittestsubeu\0000\index.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="TestSubmissionFiles\unittestsubeu\0000\m1\eu\eu-regional.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="TestSubmissionFiles\unittestsubus\0001\index.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="TestSubmissionFiles\unittestsubus\0001\m1\us\us-regional.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="TestSubmissionFiles\unittestsubus\0001\m4\42-stud-rep\421-pharmacol\4211-prim-pd\teststudyf\stf-tsfa.xml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
