﻿@model DashboardSubmissionViewModel
@using Microsoft.AspNetCore.Authorization
@using PharmaLex.SmartTRACE.Entities.Enums;
@using Microsoft.Extensions.Configuration;

@inject IConfiguration configuration;
@inject IAuthorizationService AuthorizationService
@{
    var systemAccessLink = configuration.GetValue<string>("DashboardLinks:SystemAccessLink");
    var traceEmailLink = configuration.GetValue<string>("DashboardLinks:TraceEmailLink");
    var localBusinessAdministratorLink = configuration.GetValue<string>("DashboardLinks:LocalBusinessAdministratorLink");
    var isExternalEditor = (await AuthorizationService.AuthorizeAsync(User, "ExternalEditor")).Succeeded;
}

<div id="dashboard" class="manage-container">
    <div class="dashboard">
        <section class="dashboard-welcome">
            @{
                await Html.RenderPartialAsync("_WelcomeDashboardPartial", @Model.DashboardModel);
            }

            <div class="app-content welcome-details">
                Smart<strong>TRACE</strong> is a regulatory information and process management system to manage regulatory submissions
                from planning through to submission and archive.
                <div v-if="!isExternalEditor">
                    Your current permissions allow you to view and/or edit submission planning and tracking records. If you would like to change your permissions please complete the
                    <a href="@systemAccessLink" style="text-decoration: underline">
                        System Access Request Form
                    </a>.
                </div>
                <div>
                    To start browsing the Applications available in Smart<strong>TRACE</strong> click on <span class="dashboard-text"><strong>DATA</strong></span> in the Smart<strong>TRACE</strong> menu above, then click
                    <span class="contact-email"><strong>APPLICATIONS</strong></span> and select <span class="dashboard-text"><strong>APPLICATIONS</strong></span>.
                    Alternatively, to start browsing the Submission Records available in Smart<strong>TRACE</strong> click on <span class="dashboard-text"><strong>View submissions</strong></span> in the Submission Dashboard.
                </div>
                <div>
                    For all other queries about Smart<strong>TRACE</strong> please contact the Smart<strong>TRACE</strong> global team
                    (<a href="@traceEmailLink" style="text-decoration: underline"><EMAIL></a>)<span v-if="!isExternalEditor">
                        or your
                        <a href="@localBusinessAdministratorLink"
                           style="text-decoration: underline">Local Business Administrator</a>
                    </span>.
                </div>
            </div>
            @{
                await Html.RenderPartialAsync("_DashboardBAPartial", @Model.DashboardModel);
            }
        </section>

        <div class="submissions-dashboard">
            <h3>Submissions Dashboard</h3>
            <section class="dashboard-tile">
                <header>Submissions</header>
                <div class="dashboard-content">
                    <dl class="description-list-column-grid">
                        <dt>@SubmissionLifeCycleState.Draft.GetDescription()</dt>
                        <dd>@Model.DraftSubmissionsCount of @Model.AllSubmissionsCount</dd>
                        <dt>@SubmissionLifeCycleState.Planned.GetDescription()</dt>
                        <dd>@Model.PlannedSubmissionsCount of @Model.AllSubmissionsCount</dd>
                        <dt>@SubmissionLifeCycleState.InProgress.GetDescription()</dt>
                        <dd>@Model.InProgresstSubmissionsCount of @Model.AllSubmissionsCount</dd>
                        <dt>@SubmissionLifeCycleState.ReadyForPublishing.GetDescription()</dt>
                        <dd>@Model.ReadyForPublishingSubmissionsCount of @Model.AllSubmissionsCount</dd>
                        <dt>@SubmissionLifeCycleState.QCReview.GetDescription()</dt>
                        <dd>@Model.QCReviewSubmissionsCount of @Model.AllSubmissionsCount</dd>
                        <dt>@SubmissionLifeCycleState.ApprovedForSubmission.GetDescription()</dt>
                        <dd>@Model.ApprovedForSubmissionsCount of @Model.AllSubmissionsCount</dd>
                        <dt>@SubmissionLifeCycleState.Submitted.GetDescription()</dt>
                        <dd>@Model.SubmittedSubmissionsCount of @Model.AllSubmissionsCount</dd>
                        <dt>@SubmissionLifeCycleState.Archived.GetDescription()</dt>
                        <dd>@Model.ArchivedSubmissionsCount of @Model.AllSubmissionsCount</dd>
                        <dt>@SubmissionLifeCycleState.WithdrawnFromHA.GetDescription()</dt>
                        <dd>@Model.WithdrawnSubmissionsCount of @Model.AllSubmissionsCount</dd>
                    </dl>
                    <p><a href="/app-submissions" class="button">View submissions</a></p>
                </div>
            </section>
        </div>
    </div>
</div>

<script type="text/javascript">

    var pageConfig = {
        appElement: "#dashboard",
        data() {
            return {
                isExternalEditor: @Html.Raw(isExternalEditor.ToString().ToLower())
                };
        }
    };
</script>
