﻿using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers.Services;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Services;

public class CountryServiceTests
{
    private readonly IDistributedCacheServiceFactory cacheFactory;
    private readonly IEntityCacheServiceProxy<Country> countryCache;
    private readonly CountryService countryService;

    public CountryServiceTests()
    {
        cacheFactory = Substitute.For<IDistributedCacheServiceFactory>();
        countryCache = Substitute.For<IEntityCacheServiceProxy<Country>>();
        cacheFactory.CreateEntity<Country>().Returns(countryCache);
        countryService = new CountryService(cacheFactory);
    }

    [Fact]
    public async Task IsCountryNameDuplicate_UniqueCountryName_ReturnsTrue()
    {
        // Arrange
        string countryName = "TestCountry";
        var existingCountries = new List<Country> { new Country { Name = countryName } };
        countryCache.WhereAsync(Arg.Any<Expression<Func<Country, bool>>>())
            .Returns(existingCountries);

        // Act
        var result = await countryService.IsCountryNameDuplicate(countryName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsCountryNameDuplicate_DuplicateCountryName_ReturnsFalse_()
    {
        // Arrange
        string countryName = "TestCountryName";
        var existingCountries = new List<Country>();
        countryCache.WhereAsync(Arg.Any<Expression<Func<Country, bool>>>())
            .Returns(existingCountries);

        // Act
        var result = await countryService.IsCountryNameDuplicate(countryName);

        // Assert
        Assert.False(result);
    }
}

