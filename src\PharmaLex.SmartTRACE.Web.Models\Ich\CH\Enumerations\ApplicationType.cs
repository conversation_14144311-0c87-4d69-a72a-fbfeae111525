﻿using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.CH.Enumerations
{
    public enum applicationType
    {
        advice,
        [XmlEnum("na-nas")]
        nanas,
        [XmlEnum("na-ngf")]
        nangf,
        [XmlEnum("na-nko")]
        nanko,
        [XmlEnum("na-bws")]
        nabws,
        [XmlEnum("na-ie")]
        naie,
        [XmlEnum("na-nde")]
        nande,
        [XmlEnum("na-ndo")]
        nando,
        [XmlEnum("na-co-marketing")]
        nacomarketing,
        [XmlEnum("na-pi")]
        napi,
        notification,
        [XmlEnum("var-authorisation-scientific")]
        varauthorisationscientific,
        [XmlEnum("var-authorisation-admin")]
        varauthorisationadmin,
        [XmlEnum("var-type1a")]
        vartype1a,
        [XmlEnum("var-type1ain")]
        vartype1ain,
        [XmlEnum("var-type1b")]
        vartype1b,
        [XmlEnum("var-type2")]
        vartype2,
        extension,
        renewal,
        fum,
        psur,
        pi,
        eas,
        [XmlEnum("co-marketing")]
        comarketing,
        withdrawal,
        [XmlEnum("var-pi")]
        varpi,
        transfer,
        dmf,
        pmf,
        [XmlEnum("orphan-fasttrack")]
        orphanfasttrack,
        reformat,
        [XmlEnum("supplemental-info")]
        supplementalinfo,
        corrigendum,
    }
}
