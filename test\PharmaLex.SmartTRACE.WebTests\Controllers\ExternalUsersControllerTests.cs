﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.DataAccess;
using Newtonsoft.Json;
using Microsoft.Graph.Models.TermStore;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class ExternalUsersControllerTests
    {
        private readonly IMapper _mapper;
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IAzureAdB2CGraphService _graphService;
        private readonly IUserService _userService;
        private readonly ExternalUsersController controller;
        #region Constructor
        public ExternalUsersControllerTests()
        {
            _cache = Substitute.For<IDistributedCacheServiceFactory>();
            _mapper = Substitute.For<IMapper>();
            _graphService = Substitute.For<IAzureAdB2CGraphService>();
            _userService = Substitute.For<IUserService>();
            controller = new ExternalUsersController(_cache, _mapper, _graphService, _userService);
        }
        #endregion
        #region Action Method ExternalUserFind
        [Fact]
        public async Task ExternalUserFind_Returns_JsonResult()
        {
            var _cacheserviceofuser = Substitute.For<IEntityCacheServiceProxy<User>>();
            // Arrange
            var term = "search term";
            var dbUsers = new List<User>
            {
                new User { Id = 1, Email = "<EMAIL>", GivenName = "John", FamilyName = "Doe", LastLoginDate = DateTime.Now.AddDays(-5)},
                new User { Id = 2, Email = "<EMAIL>", GivenName = "Jane", FamilyName = "Doe", LastLoginDate = DateTime.Now.AddDays(-3) },
                new User { Id = 3, Email = "<EMAIL>", GivenName = "Alice", FamilyName = "Smith", LastLoginDate = DateTime.Now.AddDays(-2) },
                new User { Id = 4, Email = "<EMAIL>", GivenName = "Alice", FamilyName = "Smith", LastLoginDate = DateTime.Now.AddDays(-2) }
            }; // Mocked database users

            var graphUsers = new List<Microsoft.Graph.Models.User>
            {
                new Microsoft.Graph.Models.User { UserPrincipalName = "<EMAIL>" },
                new Microsoft.Graph.Models.User { UserPrincipalName = "<EMAIL>" },
                new Microsoft.Graph.Models.User { UserPrincipalName = "<EMAIL>" },
                new Microsoft.Graph.Models.User { UserPrincipalName = "<EMAIL>" }
            }; // Mocked graph users

            var expectedMappedUsers = new List<UserFindResultModel>
            {
                new UserFindResultModel {  Id = 1, Email = "<EMAIL>", GivenName = "John", FamilyName = "Doe" },
                new UserFindResultModel { Id = 2, Email = "<EMAIL>", GivenName = "Jane", FamilyName = "Doe" },
                new UserFindResultModel {Id = 3, Email = "<EMAIL>", GivenName = "Alice", FamilyName = "Smith" },
                  new UserFindResultModel {Id = 4, Email = "<EMAIL>", GivenName = "Alice", FamilyName = "Smith" }
            }; // Expected result after mapping


            _cache.CreateEntity<User>()
              .Configure(Arg.Any<Func<IIncludable<User>, IIncludable<User>>>()).Returns(_cacheserviceofuser);

            _cacheserviceofuser.WhereAsync(Arg.Any<Expression<Func<User, bool>>>())
                .Returns(dbUsers);

            _graphService.FindUsers(term).Returns(graphUsers);

            _mapper.Map<List<UserFindResultModel>>(graphUsers).Returns(expectedMappedUsers);
            for (int i = 0; i < dbUsers.Count; i++)
            {
                _mapper.Map<UserFindResultModel>(dbUsers[i]).Returns(expectedMappedUsers[i]);
            }

            // Act
            var result = await controller.ExternalUserFind(term);

            // Assert
            Assert.IsType<JsonResult>(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            var returnedUsers = Assert.IsAssignableFrom<IEnumerable<UserFindResultModel>>(jsonResult.Value);
            Assert.Equal(expectedMappedUsers, returnedUsers);
        }
        #endregion
        #region Action Method NewUser
        [Fact]
        public async Task NewUser_Returns_ViewWithModel()
        {
            // Arrange
            var clients = new List<ClientModel>
            {
                new ClientModel { Id = 1, Name = "John" },
                new ClientModel { Id = 2, Name = "Jane" }
            };
            // Act
            _cache.CreateMappedEntity<Client, ClientModel>().AllAsync().Returns(clients);
            var result = await controller.NewUser() as ViewResult;
            // Assert
            Assert.NotNull(result);
            Assert.Equal("EditExternalUser", result.ViewName);
            Assert.IsType<PharmaLex.SmartTRACE.Web.Models.UserClientsModel>(result.Model);
            var clientModel = result.Model as PharmaLex.SmartTRACE.Web.Models.UserClientsModel;
            Assert.NotNull(clientModel);
        }
        [Fact]
        public async Task SaveNewUser_Returns_EditView_With_UserModel()
        {
            UserClientsModel userModel = userModel = new UserClientsModel();
            _userService.ProcessExternalUser(Arg.Any<UserClientsModel>()).Returns(true);
            // Act            
            var result = await controller.SaveNewUser(userModel) as IActionResult;
            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<RedirectResult>(result);
            Assert.NotNull(viewResult.Url);
            Assert.Equal("/manage/users", viewResult.Url);
        }
        [Fact]
        public async Task SaveNewUser_Returns_EditView_With_UserModel_BadRequest()
        {
            UserClientsModel userModel = new UserClientsModel();
            _userService.ProcessExternalUser(Arg.Any<UserClientsModel>()).Returns(false);
            // Act            
            var result = await controller.SaveNewUser(userModel) as BadRequestResult;
            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        [Fact]
        public async Task SaveNewUser_InvalidModel_ReturnsEditExternalUserView()
        {
            // Arrange
            var localUserModel = new UserClientsModel();
            controller.ModelState.AddModelError("SessionName", "Required");
            //Act
            var result = await controller.SaveNewUser(localUserModel) as ViewResult;
            // Assert
            Assert.NotNull(result);
            Assert.Equal("EditExternalUser", result.ViewName);
        }
        [Fact]
        public async Task AvailableUser_Returns_WhenEmailIsAvailable()
        {
            User user = new User();
            user.Email = "<EMAIL>";
            var _cacheserviceofuser = Substitute.For<IEntityCacheServiceProxy<User>>();
            Task<User> TaskUser = Task.FromResult(user);

            _cache.CreateEntity<User>()
             .FirstOrDefaultAsync(x => x.Email.ToLower() == user.Email.ToLower()).ReturnsForAnyArgs(TaskUser);

            // Act
            var result = await controller.AvailableUser(user.Email);

            // Assert
            Assert.IsType<JsonResult>(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
            string jobData = JsonConvert.SerializeObject(jsonResult);
            Assert.NotEmpty(jobData);
            Assert.True(jobData.Length > 0);

        }
        [Fact]
        public async Task AvailableUser_ReturnsNull_WhenEmailIsNullOrWhiteSpace()
        {
            // Arrange
            var email = "";

            // Act
            var result = await controller.AvailableUser(email);

            // Assert
            Assert.Null(result);
        }
        #endregion
        #region Action Method ExternalUserFind(string term) for invalid
        [Fact]
        public async Task ExternalUserFind_Invalid()
        {
            // Arrange
            string term = "pavan";
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.ExternalUserFind(term) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region Action Method AvailableUser(string term) for invalid
        [Fact]
        public async Task AvailableUser_Invalid()
        {
            // Arrange
            string term = "<EMAIL>";
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.ExternalUserFind(term) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region ResendInvitationEmail([FromBody] UserModel model)
        [Fact]
        public async Task ResendInvitationEmail_Returns_InvitationLink()
        {
            // Arrange
            var userModel = new UserModel();
            string expectedInvitationLink = "https://decisions-dev.smartphlex.com/signup-invitation?id_token_hint=eyJhbGciOiJSUzI1NiIsImtpZCI6IkUxQkEzNTQxQkEyNTUzQjlCQzQwOTI3RjEzNTlGMjY0RTE3MjRENjEiLCJ4NXQiOiI0Ym8xUWJvbFU3bThRSkpfRTFueVpPRnlUV0UiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.noKWVSbZC3FRxvXekQaplQMaTgwTZZ3j-CJ84undWeVXkIx2xpKwq-Gr1iVmCjmEGyfAScSE4M8ukif26kLBHR4LrAWg1FW6bYHsg3UuGr0S5Q1LPXiJmuGq5FlzhrywzAbXPlIuhfGOYBPA4bMjfx55k7mrurIHkUf251xb4Gl_Lhvzseg-iWozGBkeTWOTGlpxB_XLoscOmqOwCWm8J1G3xoN9SrMroh3P0ZzNvZjLkTPWqccRPQLZWVZw3HoNiN1-OGGdLw88A6DQaVLNmNFS0v4pC_KEL1lNO5qu0LI4xfsmhZPhiezZ7skPSgmnfjA4XL1lFoOFNxMRsZ36wQ";
            _userService.ProcessSignupInvitationResend(Arg.Any<UserModel>()).Returns(expectedInvitationLink);

            // Act            
            var result = await controller.ResendInvitationEmail(userModel);

            // Assert
            Assert.IsType<JsonResult>(result);
            var jsonResult = Assert.IsType<JsonResult>(result);
            string returnedInvitationLink = Assert.IsAssignableFrom<string>(jsonResult.Value);
            Assert.Equal(expectedInvitationLink, returnedInvitationLink);
        }

        [Fact]
        public async Task ResendInvitationEmail_InvalidModel_Returns_BadRequest()
        {
            // Arrange
            var userModel = new UserModel();
            string expectedInvitationLink = "https://decisions-dev.smartphlex.com/signup-invitation?id_token_hint=eyJhbGciOiJSUzI1NiIsImtpZCI6IkUxQkEzNTQxQkEyNTUzQjlCQzQwOTI3RjEzNTlGMjY0RTE3MjRENjEiLCJ4NXQiOiI0Ym8xUWJvbFU3bThRSkpfRTFueVpPRnlUV0UiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.noKWVSbZC3FRxvXekQaplQMaTgwTZZ3j-CJ84undWeVXkIx2xpKwq-Gr1iVmCjmEGyfAScSE4M8ukif26kLBHR4LrAWg1FW6bYHsg3UuGr0S5Q1LPXiJmuGq5FlzhrywzAbXPlIuhfGOYBPA4bMjfx55k7mrurIHkUf251xb4Gl_Lhvzseg-iWozGBkeTWOTGlpxB_XLoscOmqOwCWm8J1G3xoN9SrMroh3P0ZzNvZjLkTPWqccRPQLZWVZw3HoNiN1-OGGdLw88A6DQaVLNmNFS0v4pC_KEL1lNO5qu0LI4xfsmhZPhiezZ7skPSgmnfjA4XL1lFoOFNxMRsZ36wQ";
            _userService.ProcessSignupInvitationResend(Arg.Any<UserModel>()).Returns(expectedInvitationLink);
            controller.ModelState.AddModelError("GivenName", "Required");

            // Act            
            var result = await controller.ResendInvitationEmail(userModel);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, (result as BadRequestObjectResult)!.StatusCode);
        }

        #endregion
    }
}
