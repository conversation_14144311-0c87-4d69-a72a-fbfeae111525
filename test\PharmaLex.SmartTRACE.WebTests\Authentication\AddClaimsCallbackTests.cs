﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using NSubstitute;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Authentication;
using System.Security.Claims;

namespace PharmaLex.SmartTRACE.WebTests.Authentication
{
    public class AddClaimsCallbackTests
    {
        readonly IServiceProvider provider;
        public AddClaimsCallbackTests()
        {
            #region Fake Distributed Cache Service and DbContextReslover
            var cacheService = Substitute.For<IDistributedCacheService>();
            var dbctxReslover = TestHelpers.GetPlxDbContextResolver();
            #endregion

            #region Fake Data
            var dbCtx = ((SmartTRACEContext)dbctxReslover.Context);
            if (!dbCtx.User.Any(x => x.Id == 1 || x.Id == 2))
            {
                dbCtx.User.AddRange(new List<User> {
                new User { Id = 1, Email = "<EMAIL>", CreatedBy = "support", LastUpdatedBy = "support" },
                new User { Id = 2, Email = "<EMAIL>", CreatedBy = "support", LastUpdatedBy = "support" }
                });
            }
            if (!dbCtx.Claim.Any(x => x.Id == 1))
                dbCtx.Claim.Add(new Entities.Claim { Id = 1, Name = "SystemAdmin", ClaimType = "RegulatoryLead", CreatedBy = "support", LastUpdatedBy = "support" });
            if (!dbCtx.UserClaim.Any(x => x.UserId == 1 && x.ClaimId == 1))
                dbCtx.UserClaim.Add(new UserClaim { UserId = 1, ClaimId = 1, CreatedBy = "support", LastUpdatedBy = "support" });
            dbCtx.SaveChanges();
            #endregion

            #region Fake Azure AD Graph Service
            var graphService = Substitute.For<IAzureAdGraphService>();
            graphService.UserExists(Arg.Is("<EMAIL>")).Returns(true);
            graphService.UserExists(Arg.Is("<EMAIL>")).Returns(false);
            graphService.UserExists(Arg.Is("<EMAIL>")).Returns(false);
            graphService.UserExists(Arg.Is("<EMAIL>")).Returns(true);
            #endregion

            #region Fake Service Provider
            provider = Substitute.For<IServiceProvider>();
            provider.GetService(typeof(IAzureAdGraphService)).Returns(graphService);
            provider.GetService(typeof(IPlxDbContextResolver)).Returns(dbctxReslover);
            provider.GetService(typeof(IDistributedCacheService)).Returns(cacheService);
            #endregion
        }

        #region Test Methods
        [Theory]
        [InlineData("<EMAIL>", true)]
        [InlineData("<EMAIL>", false)]
        [InlineData("<EMAIL>", true)]
        public async Task Check_OnTicketReceived(string email, bool ispharmalex)
        {
            #region Fake Httpcontext with identity
            var context = new DefaultHttpContext();
            context.RequestServices = provider;
            if (ispharmalex)
                context.User = new ClaimsPrincipal(new ClaimsIdentity(new List<System.Security.Claims.Claim> {
                new System.Security.Claims.Claim("emails", email) ,
                new System.Security.Claims.Claim("http://schemas.microsoft.com/identity/claims/identityprovider", "https://login.microsoftonline.com/ff9ac3ce-3c41-41c3-b556-e1b32a662fed/v2.0")
            }));
            else
                context.User = new ClaimsPrincipal(new ClaimsIdentity(new List<System.Security.Claims.Claim> {
                new System.Security.Claims.Claim("emails", email)
            }));
            #endregion

            #region Fake Claims and auth scheme
            var authScheme = new AuthenticationScheme("auth", "Auth", typeof(RemoteAuthenticationHandler<>));
            var authOptions = new RemoteAuthenticationOptions();
            var authTicket = new AuthenticationTicket(context.User, "remote");
            #endregion

            var ctx = new TicketReceivedContext(context, authScheme, authOptions, authTicket);
            var sut = new AddClaimsCallback();
            var task = sut.OnTicketReceived(ctx);
            await task;
            Assert.True(task.IsCompletedSuccessfully);
        }
        #endregion
    }
}
