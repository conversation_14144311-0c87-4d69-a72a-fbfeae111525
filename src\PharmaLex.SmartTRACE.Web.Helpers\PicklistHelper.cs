﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public static class PicklistHelper
    {
        public static async Task<PicklistDataModel> ExtractPicklist(IDistributedCacheServiceFactory cache, int? typeId)
        {
            var picklistCache = cache.CreateMappedEntity<PicklistData, PicklistDataModel>();
            var picklistItem = await picklistCache.FirstOrDefaultAsync(x => x.Id == typeId);
            return picklistItem;
        }

        public static async Task<PicklistDataModel> GetPicklistByName(IDistributedCacheServiceFactory cache, string name, int pickListType)
        {
            var picklistCache = cache.CreateMappedEntity<PicklistData, PicklistDataModel>();
            var picklistItem = await picklistCache.FirstOrDefaultAsync(x => x.Name.ToLower() == name.ToLower() && x.PicklistTypeId == pickListType);
            return picklistItem;
        }

        public static async Task<IList<PicklistDataModel>> GetPicklistsByPartialName(IDistributedCacheServiceFactory cache, string name)
        {
            var picklistCache = cache.CreateMappedEntity<PicklistData, PicklistDataModel>();
            var picklistItems = await picklistCache.WhereAsync(x => x.Name.ToLower().Contains(name.ToLower()));
            return picklistItems;
        }

        public static async Task<IList<PicklistDataModel>> GetPicklistDataBasedOnCountry(IDistributedCacheServiceFactory cache, IList<CountryModel> submissionCountries)
        {
            var pickListDataCountry = await cache.CreateEntity<PicklistDataCountry>().AllAsync();
            var pickListsDataWithCountry = pickListDataCountry.Where(x => submissionCountries.Select(c => c.Id).Contains(x.CountryId));
            var picklistsWithCountry = (await cache.CreateMappedEntity<PicklistData, PicklistDataModel>().WhereAsync(x => pickListsDataWithCountry.Select(x => x.PicklistDataId).Contains(x.Id))).ToList();
            var picklistsWithoutCountry = await cache.CreateMappedEntity<PicklistData, PicklistDataModel>().WhereAsync(x => !pickListDataCountry.Select(x => x.PicklistDataId).Contains(x.Id));
            var picklists = picklistsWithCountry.Concat(picklistsWithoutCountry).OrderBy(x => x.PicklistTypeId).ThenBy(x => x.Name).ToList();

            return picklists;
        }
    }
}
