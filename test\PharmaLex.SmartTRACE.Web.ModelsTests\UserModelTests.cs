﻿using AutoMapper;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class UserModelTests
    {
        private readonly IMapper _mapper;

        public UserModelTests()
        {
            var mappingConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<UserMappingProfile>();
            });
            _mapper = mappingConfig.CreateMapper();
        }

        [Fact]
        public void Map_UserToUserModel_ShouldMapCorrectly()
        {
            // Arrange
            var user = new User
            {
                Id = 1,
                Email = "<EMAIL>",
                GivenName = "John",
                FamilyName = "Doe",
                UserClaim = new List<UserClaim>
            {
                new UserClaim { ClaimId = 101, Claim = new Claim { Name = "Admin" } },
                new UserClaim { ClaimId = 102, Claim = new Claim { Name = "User" } }
            }
            };

            // Act
            var model = _mapper.Map<UserModel>(user);

            // Assert
            Assert.Equal(user.Id, model.Id);
            Assert.Equal(user.Email, model.Email);
            Assert.Equal(user.GivenName, model.GivenName);
            Assert.Equal(user.FamilyName, model.FamilyName);
            Assert.Equal($"{user.GivenName} {user.FamilyName}", model.DisplayFullName);
            Assert.Equal(2, model.Claims.Count);
            Assert.Contains(101, model.Claims);
            Assert.Contains(102, model.Claims);
            Assert.Equal("Admin, User", model.DisplayClaimsText);
        }

        [Fact]
        public void Map_UserModelToUser_ShouldMapCorrectly()
        {
            // Arrange
            var userModel = new UserModel
            {
                Id = 12,
                Email = "<EMAIL>",
                GivenName = "John",
                FamilyName = "Doe",
                Claims = new List<int> { 12, 4, 102, 4, 12 },
                DisplayClaims = new List<string> { "s", "sdfsdf" }
            };
            var displayNameAndEmail = userModel.DisplayNameAndEmail;
            // Act
            var user = _mapper.Map<User>(userModel);

            // Assert
            Assert.Equal(userModel.Id, user.Id);
            Assert.Equal(userModel.Email, user.Email);
            Assert.Equal(userModel.GivenName, user.GivenName);
            Assert.NotNull(displayNameAndEmail);
            userModel.Id = 0;
            var displayNameAndEmail1 = userModel.DisplayNameAndEmail;
        }

        [Fact]
        public void Map_UserToUserFindResultModel_ShouldMapCorrectly()
        {
            // Arrange
            var user = new User
            {
                Id = 1,
                Email = "<EMAIL>",
                GivenName = "John",
                FamilyName = "Doe",
                UserClaim = new List<UserClaim>
            {
                new UserClaim { ClaimId = 1 },
                new UserClaim { ClaimId = 102 }
            }
            };

            // Act
            var findResult = _mapper.Map<UserFindResultModel>(user);

            // Assert
            Assert.Equal(user.Id, findResult.Id);
            Assert.Equal(user.Email, findResult.Email);
            Assert.Equal("John Doe (<EMAIL>)", findResult.Name);

        }
    }
}
