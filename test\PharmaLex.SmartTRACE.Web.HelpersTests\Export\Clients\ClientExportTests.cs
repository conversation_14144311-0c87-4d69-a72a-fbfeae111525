﻿using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Export.Clients
{
    public class ClientExportTests
    {
        private readonly IDistributedCacheServiceFactory cache;

        public ClientExportTests()
        {
            cache = Substitute.For<IDistributedCacheServiceFactory>();
        }
        [Fact]
        public async Task Export_NoClients_ReturnsOnlyHeaderRow()
        {
            // Arrange
            List<ClientModel> client = new List<ClientModel>()
            { };

            var clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(clientCache);
            clientCache.AllAsync().Returns(client);

            // Act
            ClientExport clientExport = new ClientExport(cache);
            var result = await clientExport.Export();

            Assert.NotNull(result);
            // Assert

        }
        [Fact]
        public async Task Export_Clients_ReturnsRows()
        {
            // Arrange
            List<ClientModel> client = new List<ClientModel>()
            { new ClientModel(){Name="test", Id=1, ContractOwner="test2" } };

            var clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(clientCache);
            clientCache.AllAsync().Returns(client);
            PicklistDataModel pkDatamodel = new()
            {
                Id = 11,
                Name = "name"
            };
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));


            // Act
            ClientExport clientExport = new ClientExport(cache);
            var result = await clientExport.Export();

            Assert.NotNull(result);
            // Assert
        }
        [Fact]
        public async Task Export_ClientsWithoutPicklist_ReturnsRows()
        {
            // Arrange
            List<ClientModel> client = new List<ClientModel>()
            { new ClientModel(){Name="test", Id=1, ContractOwner="test2" } };

            var clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(clientCache);
            clientCache.AllAsync().Returns(client);


            // Act
            ClientExport clientExport = new ClientExport(cache);
            var result = await clientExport.Export();

            Assert.NotNull(result);
            // Assert
        }
    }
}
