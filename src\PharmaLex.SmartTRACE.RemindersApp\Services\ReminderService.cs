﻿using Microsoft.AspNetCore.Components.Web;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.RemindersApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.RemindersApp.Services
{
    public class ReminderService : IReminderService
    {
        private readonly IRepositoryFactory _repoFactory;
        private readonly IEmailService _emailService;
        private readonly IPickListReminderHelper _pickListReminderHelper;
        private readonly IConfiguration _configuration;

        public ReminderService(IRepositoryFactory repoFactory, IEmailService emailService,
            IPickListReminderHelper pickListReminderHelper, IConfiguration configuration)
        {
            _repoFactory = repoFactory;
            _emailService = emailService;
            _pickListReminderHelper = pickListReminderHelper;
            _configuration = configuration;
        }

        public async Task SendPlannedSubmissionDateReminders(ILogger logger)
        {
            try
            {
                var notificationDays = new List<int>() { 30, 60, 90};
                var indSafetyReports = "IND Safety Reports";
                var annualReport = "Annual Report";

                var submissionTypeIds = await _repoFactory.Create<PicklistData>()
                    .Configure(o => o)
                    .Where(x => x.Name == indSafetyReports || x.Name == annualReport)
                    .Select(x => x.Id)
                    .ToListAsync();

                var submissionEntities = await _repoFactory.Create<Submission>()
                    .Configure(o => o)
                    .Include(x => x.SubmissionResource)
                    .Where(x => submissionTypeIds.Contains(x.SubmissionTypeId))
                    .ToListAsync();

                var submissions = submissionEntities.Where(x => notificationDays.Contains((x.PlannedSubmissionDate.Date - DateTime.Now.Date).Days));

                foreach (var submission in submissions)
                {
                    var application = await _repoFactory.Create<Application>()
                        .Configure(o => o)
                        .FirstOrDefaultAsync(x => x.Id == submission.ApplicationId);

                    var days = (submission.PlannedSubmissionDate.Date - DateTime.Now.Date).Days;

                    var model = new NotificationModel()
                    {
                        Application = application.ApplicationNumber,
                        Days = days,
                        Subject = $"{days} Day(s) Reminder for Application: {application.ApplicationNumber} ; Submission: {submission.UniqueId}",
                        Submission = submission.UniqueId,
                        Url = $"{_configuration.GetValue<string>("AppSettings:AppUrl")}{submission.Id}"
                    };

                    const string regexPattern = @"(.*)\s\((.*)\)";
                    Regex rx = new Regex(new GeneratedRegexAttribute(regexPattern).Pattern, RegexOptions.NonBacktracking);
                    var regulatoryEmail = submission.SubmissionResource.RegulatoryLead;

                    if (regulatoryEmail != null)
                    {
                        if (regulatoryEmail.Contains("(") || regulatoryEmail.Contains(")"))
                        {
                            var matches = rx.Matches(submission.SubmissionResource.RegulatoryLead);
                            regulatoryEmail = matches[0]?.Groups[2]?.ToString();
                        }

                        model.To = regulatoryEmail;

                        logger.LogInformation($"Submission: {JsonConvert.SerializeObject(model)}");

                        await _emailService.SendAsync(model, _configuration.GetValue<string>("AppSettings:PlannedSubmissionReminderNotificationTemplateId"));
                    }

                    var publishingLeadEmail = submission.SubmissionResource.PublishingLead;

                    if (publishingLeadEmail != null)
                    {
                        if (publishingLeadEmail.Contains("(") || publishingLeadEmail.Contains(")"))
                        {
                            var matches = rx.Matches(submission.SubmissionResource.PublishingLead);
                            publishingLeadEmail = matches[0]?.Groups[2]?.ToString();
                        }

                        if (publishingLeadEmail != regulatoryEmail)
                        {
                            model.To = publishingLeadEmail;

                            await _emailService.SendAsync(model, _configuration.GetValue<string>("AppSettings:PlannedSubmissionReminderNotificationTemplateId"));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"Error {MethodBase.GetCurrentMethod().Name} - {ex} {DateTime.Now}");
                throw;
            }
        }

        public async Task SendPlannedDispatchDateReminders(ILogger logger)
        {
            try
            {
                var notificationsDays = new List<int>() { 1, 2, 3 };

                var appProductCache = _repoFactory.Create<ApplicationProduct>()
                    .Configure(o => o
                        .Include(x => x.Product)
                        .Include(x => x.Application));

                var subPublisherCache = _repoFactory.Create<SubmissionPublisher>()
                   .Configure(o => o
                       .Include(x => x.Publisher));

                var submissionsEntities = await this._repoFactory.Create<Submission>()
                                            .Configure(s => s
                                                .Include(x => x.Application)
                                                    .ThenInclude(x => x.ApplicationProduct).ThenInclude(x => x.Product)
                                                .Include(x => x.Project)
                                                    .ThenInclude(x => x.Client)
                                                .Include(x => x.SubmissionResource))
                                            .Where(x => x.LifecycleStateId <= (int)SubmissionLifeCycleState.ReadyForPublishing && x.PlannedDispatchDate.HasValue)
                                            .ToListAsync();

                var submissions = submissionsEntities
                                            .Where(x => notificationsDays.Contains((x.PlannedDispatchDate.Value.Date - DateTime.Now.Date).Days))
                                            .ToList();

                foreach (var sub in submissions)
                {
                    var appProducts = appProductCache.Where(x => x.ApplicationId == sub.ApplicationId);

                    var product = string.Join(" | ", await appProducts.Select(x => x.Product)
                                                                       .Select(x => $"{x.Name}, {_pickListReminderHelper.GetPickListName(_repoFactory, x.DosageFormId)}, {x.Strength}")
                                                                       .ToListAsync());

                    var procedure = _pickListReminderHelper.GetPickListName(_repoFactory, sub.Application.ProcedureTypeId);

                    var sequenceNumber = sub.SequenceNumber;

                    var client = sub.Project.Client.Name;

                    const string regexPattern = @"(.*)\s\((.*)\)";
                    Regex rx = new Regex(new GeneratedRegexAttribute(regexPattern).Pattern, RegexOptions.NonBacktracking);

                    var model = new NotificationModel()
                    {
                        Subject = string.IsNullOrEmpty(sequenceNumber) ?
                        $"Reminder about Submission Record {sub.Application.ApplicationNumber} - {procedure} - {product} - {client}" :
                        $"Reminder about Submission Record {sub.Application.ApplicationNumber} - {procedure} - {sequenceNumber} - {product} - {client}",
                        Days = (sub.PlannedDispatchDate.Value.Date - DateTime.Now.Date).Days,
                        Url = $"{_configuration.GetValue<string>("AppSettings:AppUrl")}{sub.Id}"
                    };

                    var regulatoryEmail = sub.SubmissionResource.RegulatoryLead;

                    if (regulatoryEmail != null)
                    {
                        if (regulatoryEmail.Contains("(") || regulatoryEmail.Contains(")"))
                        {
                            var matches = rx.Matches(sub.SubmissionResource.RegulatoryLead);
                            regulatoryEmail = matches[0]?.Groups[2]?.ToString();
                        }

                        model.To = regulatoryEmail;

                        logger.LogInformation($"Submission: {JsonConvert.SerializeObject(model)}");

                        await _emailService.SendAsync(model, _configuration.GetValue<string>("AppSettings:SubmissionReminderNotificationTemplateId"));
                    }

                    var publishingLeadEmail = sub.SubmissionResource.PublishingLead;

                    if (publishingLeadEmail != null)
                    {
                        if (publishingLeadEmail.Contains("(") || publishingLeadEmail.Contains(")"))
                        {
                            var matches = rx.Matches(sub.SubmissionResource.PublishingLead);
                            publishingLeadEmail = matches[0]?.Groups[2]?.ToString();
                        }

                        if (publishingLeadEmail != regulatoryEmail)
                        {
                            model.To = publishingLeadEmail;

                            await _emailService.SendAsync(model, _configuration.GetValue<string>("AppSettings:SubmissionReminderNotificationTemplateId"));
                        }
                    }

                    var submissionPublishers = subPublisherCache.Where(x => x.SubmissionResourceId == sub.SubmissionResource.Id).Select(x => x.Publisher);

                    submissionPublishers = submissionPublishers.Where(x => x.Email != regulatoryEmail && x.Email != publishingLeadEmail);

                    foreach (var publisher in submissionPublishers)
                    {
                        model.To = publisher.Email;

                        await _emailService.SendAsync(model, _configuration.GetValue<string>("AppSettings:SubmissionReminderNotificationTemplateId"));
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"Error {MethodBase.GetCurrentMethod().Name} - {ex} {DateTime.Now}");
                throw;
            }
        }
    }
}
