﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class DashboardSubmissionViewModelTests
    {
        [Fact]
        public void DashboardSubmissionViewModel_Get_SetValue()
        {
            //Arrange
            var model = new DashboardSubmissionViewModel();
            model.DraftSubmissionsCount = 1;
            model.PlannedSubmissionsCount = 1;
            model.InProgresstSubmissionsCount = 1;
            model.ReadyForPublishingSubmissionsCount = 1;
            model.QCReviewSubmissionsCount = 1;
            model.ApprovedForSubmissionsCount = 1;
            model.SubmittedSubmissionsCount = 1;
            model.ArchivedSubmissionsCount = 1;
            model.WithdrawnSubmissionsCount = 1;
            model.AllSubmissionsCount = 1;
            model.DashboardModel=new DashboardViewModel();
            //Assert
            Assert.NotNull(model);
        }
    } 
}
