﻿@model EditProductViewModel
@using PharmaLex.SmartTRACE.Entities.Enums
@using Microsoft.AspNetCore.Authorization

@inject IAuthorizationService AuthorizationService
@inject AutoMapper.IMapper mapper

@{
    var clientsList = Model.AllClients.OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString()
            });
    var dosageFormSelectList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.DosageForm).OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString()
            });

    var productLifecycleStates = mapper.Map<IEnumerable<ProductLifecycleStateList>>(Enum.GetValues(typeof(CommonLifecycleState)));
}

<div id="product" class="manage-container">
    <header class="manage-header">
        <h3>@Model.Product.Name Product</h3>
        <div class="common-state-container" :stateId="lifecyclestateId">
            <span class="common-state">{{ lifecycleState }}</span>
        </div>
        <a href="/products" class="button secondary icon-button-back">Back to Product list</a>
        <button class="button secondary icon-button-next" id="obsoleteState" v-show="displayObsoleteButton" name="moveToObsolete">Move to @(SubmissionLifeCycleState.Obsolete.GetDescription())</button>
    </header>
    <form id="product-form" method="post">
        @Html.AntiForgeryToken()
        <div class="form-col">
            <label for="Product.Name">Name*</label>
            <input asp-for="Product.Name" type="text" v-bind:class="{'validation-error' : hasError}" required :aria-disabled="isObsolete" />
            <div id="validationMessage" style="color:red;">
            </div>
            <label for="Product.DosageFormId">Dosage Form*</label>
            <div :class="['select-wrapper', {'validation-error' : hasError}]">
                <select asp-for="Product.DosageFormId" asp-items="dosageFormSelectList" required :aria-disabled="isObsolete">
                    <option value="">Select dosage form</option>
                </select>
            </div>
            <label for="Product.Strength">Strength*</label>
            <input asp-for="Product.Strength" type="text" v-bind:class="{'validation-error' : hasError}" required :aria-disabled="isObsolete" />
            <label for="Product.ClientId">Client*</label>
            <div :class="['select-wrapper', {'validation-error' : hasError}]">
                <select asp-for="Product.ClientId" asp-items="clientsList" v-on:change="selectedItem($event)" required :aria-disabled="isObsolete">
                    <option value="">Select client</option>
                </select>
            </div>
            @if (Model.Product.HasError)
            {
                <span class="field-duplication-error">@Model.Product.ErrorMessage</span>
            }
            <label for="activesubstancelist" class="product-substance-label">Active Substances</label>
            <autocomplete-list :valid="true" :items="activeSubstances" :added-items="addedActiveSubstances" :config="config" :aria-disabled="isObsolete"></autocomplete-list>
        </div>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/products">Cancel</a><button class="icon-button-save" :aria-disabled="isObsolete">Save</button>
        </div>
        <input type="hidden" asp-for="Product.Id" />
        <input type="hidden" asp-for="Product.ClientId" />
        <input type="hidden" asp-for="Product.LifecycleStateId" />

        <yes-no-dialog :config="moveToConfig" v-on:button-pressed="onMoveTo"></yes-no-dialog>
    </form>
</div>

@section Scripts {
    <script src="@Cdn.GetUrl("lib/jquery/dist/jquery.min.js")"></script>
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        var pageConfig = {
            appElement: '#product',
            data() {
                return {
                    config: {
                        selectable: false,
                        showSelectedName: false,
                        modelProperty: 'Product.ActiveSubstances',
                        placeholder: 'Type to select active substances',
                        emptyMessage: 'No active substances selected',
                        active: true
                    },
                    obsoleteStateId: @Html.Raw((int)CommonLifecycleState.Obsolete),
                    lifecycleState: null,
                    lifecyclestateId: @Html.Raw(Model.Product.LifecycleStateId),
                    activeStateId: @Html.Raw((int)CommonLifecycleState.Active),
                    allActiveSubstances: @Html.Raw(Model.ActiveSubstances.ToJson()),
                    activeSubstances: @Html.Raw(Model.ActiveSubstances.ToJson()),
                    addedActiveSubstances: @Html.Raw(Model.Product.ActiveSubstances.ToJson()),
                    hasError: @Model.Product.HasError.ToString().ToLower(),
                    selectedClientId: @Model.Product.ClientId,
                    isApplicationAssigned: @Html.Raw(Model.IsApplicationAssigned.ToString().ToLower()),
                    productId: @Model.Product.Id,
                    productLifecycleStates: @Html.Raw(Json.Serialize(productLifecycleStates)),
                    moveToConfig: {
                        message: 'Do you want to move to Obsolete?',
                        noButton: 'Cancel',
                        yesButton: 'Continue',
                        elementName: 'moveToObsolete',
                        buttonClass: 'icon-button-next'
                    },
                    isObsolete: false,
                    activeSubstanceListInput: null,
                    autocompleteListElements: null
                }
            },
            methods: {
                selectedItem(event) {
                    this.activeSubstances = this.allActiveSubstances.filter(x => x.clientId == event.target.value);
                },
                handleSubmit(event) {
                    var inputValue = $('#Product_Name').val();
                    var unmatchedChars = findUnmatchedCharacters(inputValue);
                    if (unmatchedChars._value.length > 0) {
                        event.preventDefault();
                        $('#validationMessage').text("Name contains invalid characters: " + unmatchedChars._value.join(' '));
                        $('#validationMessage').css('color', 'red');
                        return false;
                    }
                    $('#validationMessage').text('');
                    return true;
                },
                handleChange(event) {
                    $('#Product_Name').change(function () {
                        $('#validationMessage').text('');
                    })
                },
                onMoveTo(yesButtonPressed) {
                    if (yesButtonPressed) {
                        fetch(`/products/state/${this.productId}`, this.getFetchOptions('POST', JSON.stringify(this.obsoleteStateId.toString())))
                            .then(r => r.json())
                            .then(stateId => {
                                this.lifecyclestateId = stateId;
                                this.lifecycleState = this.productLifecycleStates.find(x => x.id === this.lifecyclestateId)?.name;

                                    if (stateId == this.obsoleteStateId) {
                                        this.isObsolete = true;
                                        this.activeSubstanceListInput.disabled = true;

                                        for (var i = 0; i < this.autocompleteListElements.children.length; i++) {
                                            this.autocompleteListElements.children[i].children[1].style.pointerEvents = "none";
                                        }
                                    }
                            });
                     }
                },
                getFetchOptions: function (method, body) {
                    return {
                        method: method,
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        },
                        body: body
                    };
                },
            },
            computed: {
                displayObsoleteButton() {
                    return !this.isApplicationAssigned && (this.lifecyclestateId == this.activeStateId);
                }
            },
            created() {
                if (this.selectedClientId) {
                    this.activeSubstances = this.allActiveSubstances.filter(x => x.clientId == this.selectedClientId);
                }
                this.lifecycleState = this.productLifecycleStates.find(x => x.id === this.lifecyclestateId)?.name;
            },
            mounted() {
                document.getElementById('product-form').addEventListener('submit', this.handleSubmit);
                document.getElementById('Product_Name').addEventListener('change', this.handleChange);
                this.activeSubstanceListInput = document.getElementsByClassName('autocomplete')[0].firstChild;
                this.autocompleteListElements = document.getElementsByClassName('autocomplete-list')[0];

                if (this.lifecyclestateId == this.obsoleteStateId) {
                    this.activeSubstanceListInput.disabled = true;

                    for (var i = 0; i < this.autocompleteListElements.children.length; i++) {
                        this.autocompleteListElements.children[i].children[1].style.pointerEvents = "none";
                    }
                }

                this.isObsolete = this.lifecyclestateId == this.obsoleteStateId;
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/AutoCompleteList" />
    <partial name="Components/YesNoDialog" />
}