﻿using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class Submission : EntityBase
    {
        public Submission()
        {
            SubmissionCountry = new HashSet<SubmissionCountry>();
            Document = new HashSet<Document>();
        }
        public int Id { get; set; }
        public string UniqueId { get; set; }
        public int? DeliveryDetailsId { get; set; }
        public string SequenceNumber { get; set; }
        public string RelatedSequenceNumber { get; set; }
        public int? DossierFormatId { get; set; }
        public string SerialNumber { get; set; }
        public string ReferenceNumber { get; set; }
        public int SubmissionTypeId { get; set; }
        public int? SubmissionUnitId { get; set; }
        public int? SubmissionModeId { get; set; }
        public string Description { get; set; }
        public int LifecycleStateId { get; set; }
        public int? PreviousLifecycleStateId { get; set; }
        public string Comments { get; set; }
        public DateTime? HealthAuthorityDueDate { get; set; }
        public DateTime? AuthoringDeadline { get; set; }
        public DateTime? PlannedDispatchDate { get; set; }
        public DateTime? ActualDispatchDate { get; set; }
        public DateTime PlannedSubmissionDate { get; set; }
        public DateTime? ActualSubmissionDate { get; set; }
        public string CespNumber { get; set; }
        public DateTime? WithdrawalDate { get; set; }
        public string SourceDocumentsLocation { get; set; }
        public string ArchivedDocumentsLocation { get; set; }
        public string DocubridgeVersionId { get; set; }
        public int ApplicationId { get; set; }
        public int ProjectId { get; set; }

        public virtual Application Application { get; set; }
        public virtual Project Project { get; set; }
        public virtual ICollection<SubmissionCountry> SubmissionCountry { get; set; }
        public virtual SubmissionResource SubmissionResource { get; set; }
        public virtual ICollection<Document> Document { get; set; }
    }
}
