﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class SubmissionRequestFormModelTests
    {
        [Fact]
        public void SubmissionRequestFormModel_Get_SetValue()
        {
            //Arrange
            var model = new SubmissionRequestFormModel();
            model.DeliveryDetailsId = 1;
            model.ClientName = "Test";
            model.DeliveryDetailsId = 1;
            model.ApplicationType = "Test";
            model.Description = "Test";
            model.RegulatoryLead = "Test123";
            model.PublishingLead = "Test";
            model.Publisher = "Test";
            model.Description = "Test";
            model.Comments = "Test";
            model.ApplicationNumber = "001";
            model.OpportunityNumber = "Test";
            model.SequenceNumber = "Test";
            model.PriorityId = 1;
            model.SerialNumber = "Test";
            model.EstimatedSizeId = 1;
            model.SubmissionTypeId = 123;
            model.SubmissionUnitId = 123;
            model.SubmissionModeId = 123;
            model.Comments = "Test";
            model.EstimatedSize = "large";
            model.ProcedureType = "Test";
            model.PlannedSubmissionDate = DateTime.Now;
            model.PlannedDispatchDate = DateTime.Now;
            model.HealthAuthorityDueDate = DateTime.Now;
            model.ApplicationId =1;
            model.SendNotification = true;
            model.ProjectId = 1;
            model.InitialSentToEmail = "Test";
            model.ApplicationId = 123;
            model.ProjectId = 123;
            model.CountryIds = new List<int>();
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void SubmissionRequestMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new SubmissionRequestMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
    }
}
