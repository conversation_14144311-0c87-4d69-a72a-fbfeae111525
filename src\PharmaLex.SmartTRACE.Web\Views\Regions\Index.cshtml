﻿@model IEnumerable<RegionModel>

<div id="regions" class="manage-container">
    <header class="manage-header">
        <h2>Manage Regions</h2>
        <a class="button icon-button-add" href="/regions/new">Add region</a>
    </header>
    <filtered-table :items="regions" :columns="columns" :filters="filters" :link="link"></filtered-table>
</div>

@section Scripts {
    <script type="text/javascript">

    var pageConfig = {
        appElement: '#regions',
        data() {
            return {
                link: '/regions/edit/',
                regions: @Html.Raw(Model.ToJson()),
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            type: 'text'
                        },
                        {
                            dataKey: 'abbreviation',
                            sortKey: 'abbreviation',
                            header: 'Abbreviation',
                            type: 'text'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'name',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'abbreviation',
                        options: [],
                        type: 'search',
                        header: 'Search abbreviation',
                        fn: v => p => p.abbreviation.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    }
                ]
            };
        }
    };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}