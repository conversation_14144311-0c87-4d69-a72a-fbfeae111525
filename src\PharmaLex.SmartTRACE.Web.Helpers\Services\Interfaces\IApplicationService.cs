﻿using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Security.Claims;
using System;
using System.Linq.Expressions;
using PharmaLex.SmartTRACE.Web.Models.ViewModels;

#nullable enable

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public interface IApplicationService
    {
        Task<IList<ApplicationViewModel>> ListApplications(ClaimsPrincipal user, ApplicationFilterModel? model = null);
        Task<ApplicationModel> GetApplication(int applicationId);
        Task<Application> InsertApplication(EditApplicationViewModel model, IList<PicklistDataModel> allPicklists);
        Task<Application> UpdateApplication(EditApplicationViewModel model, IList<PicklistDataModel> allPicklists);
        Task<ApiPagedListResult<ApplicationViewModel>> GetPagedApplicationsAsync(ClaimsPrincipal user, int skip, int take, ApplicationFilterModel model, string? sort);
    }
}
