﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Pharmalex.AzureCloudStorage;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Storage;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    [Route("documents")]
    public class DocumentsController : BaseController
    {
        private readonly ISubmissionBlobContainer azureBlobContainer;
        private readonly IDistributedCacheServiceFactory cache;
        private readonly INotificationService notificationService;

        public DocumentsController(ISubmissionBlobContainer azureBlobContainer, 
            IDistributedCacheServiceFactory cache,
            INotificationService notificationService)
        {
            this.azureBlobContainer = azureBlobContainer;
            this.cache = cache;
            this.notificationService = notificationService;
        }

        [HttpGet, Route("download/{**fileUrl}")]
        public async Task<IActionResult> Download(string fileUrl)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            // Validate file path for basic security checks
            if (string.IsNullOrEmpty(fileUrl) || fileUrl.Contains(".."))
            {
                return BadRequest("Invalid file path.");
            }
            try
            {
                var blob = await this.azureBlobContainer.GetBlobClientAsync(fileUrl);
                // Download and return file directly instead of redirecting
                var download = await blob.DownloadAsync();
                string fileName = Path.GetFileName(fileUrl);
                return File(download.Value.Content, download.Value.Details.ContentType, fileName);
            }
            catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpPost, Route("notification")]
        public async Task<IActionResult> NotifyPublishers([FromBody]DocumentModel document)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var submission = await cache.CreateMappedEntity<Submission, SubmissionModel>().FirstOrDefaultAsync(x => x.Id == document.SubmissionId);
            var submissionResource = await cache.CreateMappedEntity<SubmissionResource, SubmissionResourceModel>().FirstOrDefaultAsync(x => x.SubmissionId == document.SubmissionId);
            var submissionPublisherCache = cache.CreateEntity<SubmissionPublisher>().Configure(o => o.Include(x => x.Publisher));
            var submissionPublishers = await submissionPublisherCache.WhereAsync(x => x.SubmissionResourceId == submissionResource.Id);
            var createdByUser = await cache.CreateMappedEntity<User, UserModel>().FirstOrDefaultAsync(x => x.Email.ToLower() == submission.CreatedBy.ToLower());

            foreach (var publisher in submissionPublishers.Select(x => x.Publisher))
            {
                if (publisher.Email != createdByUser.Email)
                {
                    await this.notificationService.SendNotification($"{publisher.GivenName} {publisher.FamilyName} {publisher.Email}", submission, document, this.Request);
                }
            }
            return Ok();
        }
    }
}
