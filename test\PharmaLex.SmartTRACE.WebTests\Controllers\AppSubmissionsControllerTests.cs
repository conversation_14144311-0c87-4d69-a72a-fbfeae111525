﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph.Models.Security;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using NSubstitute;
using NSubstitute.ReturnsExtensions;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Models.ViewModels;
using System.Linq.Expressions;
using System.Security.Claims;


namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class AppSubmissionsControllerTests
    {
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IMapper _mapper;
        private readonly INotificationService _notificationService;
        private readonly ISubmissionExport _subExport;
        private readonly ISubmissionService _submissionService;
        private readonly IAuthorizationService _authorizationService;
        AppSubmissionsController controller;
        #region Constructor 
        public AppSubmissionsControllerTests()
        {
            _cache = Substitute.For<IDistributedCacheServiceFactory>();
            _mapper = Substitute.For<IMapper>();
            _notificationService = Substitute.For<INotificationService>();
            _subExport = Substitute.For<ISubmissionExport>();
            _submissionService = Substitute.For<ISubmissionService>();
            _authorizationService = Substitute.For<IAuthorizationService>();
            controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
        }
        #endregion
        private static Task<List<UserClient>> GetUsersList()
        {
            var user1 = new UserClient { ClientId = 1, UserId = 11, CreatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), CreatedBy = "<EMAIL>", LastUpdatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), LastUpdatedBy = "Convert.ToDateTime(\"2022-01-27T04:39:03.977\")" };
            var user2 = new UserClient { ClientId = 2, UserId = 12, CreatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), CreatedBy = "<EMAIL>", LastUpdatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), LastUpdatedBy = "Convert.ToDateTime(\"2022-01-27T04:39:03.977\")" };
            var user3 = new UserClient { ClientId = 3, UserId = 13, CreatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), CreatedBy = "<EMAIL>", LastUpdatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), LastUpdatedBy = "Convert.ToDateTime(\"2022-01-27T04:39:03.977\")" };
            var user4 = new UserClient { ClientId = 4, UserId = 14, CreatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), CreatedBy = "<EMAIL>", LastUpdatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), LastUpdatedBy = "Convert.ToDateTime(\"2022-01-27T04:39:03.977\")" };
            var user5 = new UserClient { ClientId = 5, UserId = 15, CreatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), CreatedBy = "<EMAIL>", LastUpdatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), LastUpdatedBy = "Convert.ToDateTime(\"2022-01-27T04:39:03.977\")" };
            var user6 = new UserClient { ClientId = 6, UserId = 16, CreatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), CreatedBy = "<EMAIL>", LastUpdatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), LastUpdatedBy = "Convert.ToDateTime(\"2022-01-27T04:39:03.977\")" };
            var user7 = new UserClient { ClientId = 7, UserId = 17, CreatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), CreatedBy = "<EMAIL>", LastUpdatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), LastUpdatedBy = "Convert.ToDateTime(\"2022-01-27T04:39:03.977\")" };
            var user8 = new UserClient { ClientId = 8, UserId = 18, CreatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), CreatedBy = "<EMAIL>", LastUpdatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), LastUpdatedBy = "Convert.ToDateTime(\"2022-01-27T04:39:03.977\")" };
            var user9 = new UserClient { ClientId = 9, UserId = 10, CreatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), CreatedBy = "<EMAIL>", LastUpdatedDate = Convert.ToDateTime("2022-01-27T04:39:03.977"), LastUpdatedBy = "Convert.ToDateTime(\"2022-01-27T04:39:03.977\")" };

            var userList = new List<UserClient> { user1, user2, user3, user4, user5, user6, user7, user8, user9 };
            return Task.FromResult(userList);
        }
        private static Task<List<Submission>> GetSubmissionsList(List<int> clientIds)
        {
            var submissions = new List<Submission>
        {
            new Submission { Project = new Project { Client = new Client { Id = 1 } }, LifecycleStateId = (int)SubmissionLifeCycleState.Draft },
            new Submission { Project = new Project { Client = new Client { Id = 2 } }, LifecycleStateId = (int)SubmissionLifeCycleState.Planned },
            new Submission { Project = new Project { Client = new Client { Id = 3 } }, LifecycleStateId = (int)SubmissionLifeCycleState.InProgress },
            new Submission { Project = new Project { Client = new Client { Id = 4 } }, LifecycleStateId = (int)SubmissionLifeCycleState.ReadyForPublishing },
            new Submission { Project = new Project { Client = new Client { Id = 5 } }, LifecycleStateId = (int)SubmissionLifeCycleState.QCReview },
            new Submission { Project = new Project { Client = new Client { Id = 6 } }, LifecycleStateId = (int)SubmissionLifeCycleState.ApprovedForSubmission },
            new Submission { Project = new Project { Client = new Client { Id = 7 } }, LifecycleStateId = (int)SubmissionLifeCycleState.Submitted },
            new Submission { Project = new Project { Client = new Client { Id = 8 } }, LifecycleStateId = (int)SubmissionLifeCycleState.Archived },
            new Submission { Project = new Project { Client = new Client { Id = 9 } }, LifecycleStateId = (int)SubmissionLifeCycleState.WithdrawnFromHA }
        };

            return Task.FromResult(submissions.Where(x => clientIds.Contains(x.Project.Client.Id)).ToList());
        }
        #region IndexMethod
        [Fact]
        public async Task Index_ReturnsViewResult_WithSubmissions()
        {
            // Arrange

            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
                 new System.Security.Claims.Claim(ClaimTypes.Name, "testuser"),
                 new System.Security.Claims.Claim(ClaimTypes.Role, "Reader")
            }));
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            var submissions = new List<SubmissionFilterModel>
                 {
                 new SubmissionFilterModel { RegulatoryLead = "RL11", ApplicationNumber = "11", ClientName = "Client1" },
                 new SubmissionFilterModel { RegulatoryLead = "RL12", ApplicationNumber = "12", ClientName = "Client2" },
                 new SubmissionFilterModel { RegulatoryLead = "RL13", ApplicationNumber = "13", ClientName = "Client3" }
                 };
            SubmissionTableViewModel subTableModel = new SubmissionTableViewModel()
            {
                Id = 1,
                ApplicationNumber = "11",
                ClientName = "Client1"
            };
            List<SubmissionTableViewModel> subList = new List<SubmissionTableViewModel> { subTableModel };
            ApiPagedListResult<SubmissionTableViewModel> apiModel = new ApiPagedListResult<SubmissionTableViewModel>
            (
               items: subList,
                offset: 0,
                limit: 25,
                totalItemCount: subList.Count,
                filteredCount: subList.Count
            );
            _submissionService.GetPagedSubmissionsAsync(
            Arg.Any<ClaimsPrincipal>(), Arg.Any<int>(), Arg.Any<int>(), Arg.Any<SubmissionFilterModel>(), Arg.Any<string>())
            .Returns(Task.FromResult(apiModel));

            // Act
            var result = await controller.Index(skip: 0, take: 25, sort: null, filters: null) as ViewResult;

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var viewModel = Assert.IsType<ApiPagedListResult<SubmissionTableViewModel>>(viewResult.Model);
            var clientName = viewModel.Data[0].ClientName;
            Assert.Equal(subTableModel.ClientName, clientName);
        }
        #endregion
        #region IndexMethod_InvalidObject
        [Fact]
        public async Task Index_ReturnsViewResult_InvalidObject()
        {
            // Arrange

            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
                 new System.Security.Claims.Claim(ClaimTypes.Name, "testuser"),
                 new System.Security.Claims.Claim(ClaimTypes.Role, "Reader")
            }));
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            // Act
            var result = await controller.Index(skip: 0, take: 25, sort: null, filters: null) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        
        #region IndexwithSubmission
        [Fact]
        public async Task Index_ReturnsViewResult_IndexPagedSubmissions()
        {
            // Arrange

            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
             new System.Security.Claims.Claim(ClaimTypes.Name, "testuser"),
             new System.Security.Claims.Claim(ClaimTypes.Role, "Reader")
            }));
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            var submissions = new List<SubmissionFilterModel>
                 {
                 new SubmissionFilterModel { RegulatoryLead = "RL11", ApplicationNumber = "11", ClientName = "Client1" },
                 new SubmissionFilterModel { RegulatoryLead = "RL12", ApplicationNumber = "12", ClientName = "Client2" },
                 new SubmissionFilterModel { RegulatoryLead = "RL13", ApplicationNumber = "13", ClientName = "Client3" }
                 };
            SubmissionTableViewModel subTableModel = new SubmissionTableViewModel()
            {
                Id = 1,
                ApplicationNumber = "11",
                ClientName = "Client1"
            };
            SubmissionFilterModel model = new SubmissionFilterModel()
            {
                RegulatoryLead = "RL11",
                ApplicationNumber = "11",
                ClientName = "Client1"
            };
            var subList = new List<SubmissionTableViewModel> { subTableModel };
            var apiModel = new ApiPagedListResult<SubmissionTableViewModel>
            (
               items: subList,
                offset: 0,
                limit: 25,
                totalItemCount: subList.Count,
                filteredCount: subList.Count
            );
            _submissionService.GetPagedSubmissionsAsync(
            Arg.Any<ClaimsPrincipal>(),
            Arg.Any<int>(),
            Arg.Any<int>(),
            Arg.Any<SubmissionFilterModel>(),
            Arg.Any<string>()
            ).Returns(Task.FromResult(apiModel));
            controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext { User = user }
            };
            string[] arrFilters = { "clientname=>Pk_Clinet", "applicationnumber=>Pk_applicationnumber", "sequencenumber=>Pk_sequencenumber" };

            // Act
            var result = await controller.IndexPagedSubmissions(skip: 0, take: 25, sort: null, filters: arrFilters) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var viewModel = Assert.IsType<ApiPagedListResult<SubmissionTableViewModel>>(result.Value);
            var clientName = viewModel.Data[0].ClientName; // Ensure to access the correct index
            Assert.Equal(subTableModel.ClientName, clientName);
        }
        #endregion
        #region IndexwithSubmission_InvalidObject
        [Fact]
        public async Task Index_ReturnsInvalidObject_IndexPagedSubmissions()
        {
            // Arrange

            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
             new System.Security.Claims.Claim(ClaimTypes.Name, "testuser"),
             new System.Security.Claims.Claim(ClaimTypes.Role, "Reader")
            }));
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            controller.ModelState.AddModelError("Key", "ErrorMessage");
            string[] arrFilters = { "clientname=>Pk_Clinet", "applicationnumber=>Pk_applicationnumber", "sequencenumber=>Pk_sequencenumber" };

            // Act
            var result = await controller.IndexPagedSubmissions(skip: 0, take: 25, sort: null, filters: arrFilters) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);

        }
        #endregion

        #region Edit_Returns_EditCountryModel
        [Fact]
        public async Task Edit_Returns_EditCountryModel()
        {
            // Arrange
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            var Id = 1;
            string ViewName = "EditSubmission";

            LoadSubmissionData(Id, 1, true);

            // Act
            var result = await controller.Edit(Id) as ViewResult;

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal(ViewName, viewResult.ViewName);
        }
        #endregion
        #region Edit_ForBid
        [Fact]
        public async Task Edit_ReturnsForbid()
        {
            // Arrange
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            var Id = 1;
            LoadSubmissionData(Id, 1, true, 1,10);

            // Act
            var result = await controller.Edit(Id) as ForbidResult;

            // Assert
            Assert.IsType<ForbidResult>(result);
        }
        #endregion
        #region Edit_Returns_InvalidObject
        [Fact]
        public async Task Edit_Returns_InvalidObject()
        {
            // Arrange
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            var Id = 1;
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            // Act
            var result = await controller.Edit(Id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region Edit_Reader
        [Fact]
        public async Task Edit_SubmissionExistsAndUserIsReader_ReturnsView()
        {
            // Arrange
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            var Id = 1;
            LoadSubmissionData(Id, 1, true ,8);
            var context = new DefaultHttpContext();
            context.User = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
                new System.Security.Claims.Claim("application:Reader", "true")
            }));
            controller.ControllerContext = new ControllerContext
            {
                HttpContext = context
            };
            _authorizationService.AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object>(), Arg.Any<string>())
                .Returns(Task.FromResult(AuthorizationResult.Success()));

            // Act
            var result = await controller.Edit(Id);

            // Assert
            var viewResult = Assert.IsType<RedirectResult>(result);
            var redirectResult = viewResult as RedirectResult;
            var ResposeUrl = $"/submissions/view/{Id}";
            Assert.NotNull(redirectResult);
            Assert.Equal(redirectResult.Url, ResposeUrl);
        }
        #endregion
        #region Edit_Reader_InvalidObject
        [Fact]
        public async Task Edit_SubmissionExistsAndUserIsReader_ReturnsInvalidObject()
        {
            // Arrange
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            var Id = 1;
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            // Act
            var result = await controller.Edit(Id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region SaveEdit_SubmissionIsUpdatedAndRedirectsToEdit
        [Theory]
        [InlineData("Test")]
        [InlineData("")]
        public async Task SaveEdit_SubmissionIsUpdatedAndRedirectsToEdit(string comments)
        {
            // Arrange
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            var id = 1;
            string ViewName = "EditSubmission";
            var cancellationToken = new CancellationToken(false);
            var model = new EditSubmissionViewModel
            {
                Submission = new SubmissionModel { Id = id },
                SubmissionResource = new SubmissionResourceModel { SubmissionId = id,Comments= comments }
            };
            var subResourceCache = Substitute.For<ITrackedEntityCacheServiceProxy<Submission>>();
            _cache.CreateTrackedEntity<Submission>().Returns(subResourceCache);

            List<Submission> submissions =
               [
                   new Submission { Id = 1, UniqueId="UI1" },
             new Submission { Id = 2, UniqueId="UI2",  },
             ];
            subResourceCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submissions[0]);

            _mapper
             .Map<EditSubmissionViewModel, Submission>(Arg.Any<EditSubmissionViewModel>())
             .Returns(callInfo =>
             {
                 var submissionViewModel = callInfo.Arg<EditSubmissionViewModel>();
                 return submissions.FirstOrDefault();
             });

            List<SubmissionResource> submissionResources =
             [
                 new SubmissionResource { Id = 1, RegulatoryLead="UI1" },
           new SubmissionResource { Id = 2, RegulatoryLead="UI2", }
               ];
            var submissionResourcCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionResource>>();
            _cache.CreateTrackedEntity<SubmissionResource>().Returns(submissionResourcCache);
            submissionResourcCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<SubmissionResource, bool>>>()).Returns(submissionResources[0]);
            _mapper
             .Map<EditSubmissionViewModel, SubmissionResource>(Arg.Any<EditSubmissionViewModel>())
             .Returns(callInfo =>
             {
                 var submissionResource = callInfo.Arg<EditSubmissionViewModel>();
                 return submissionResources.FirstOrDefault();
             });
            ///upload code
            var submissionCountry = new List<SubmissionCountry>()
             {
                 new SubmissionCountry{ Id = 1, Country = new Country() { Id = 1, Name = "Test1" }, SubmissionId=2 },
                 new SubmissionCountry{ Id = 2, Country = new Country() { Id = 2, Name = "Test2" }, SubmissionId=2 }
             };
            var _subCountryCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionCountry>>();
            _cache.CreateTrackedEntity<SubmissionCountry>().Returns(_subCountryCache);
            _subCountryCache.Where(Arg.Any<Expression<Func<SubmissionCountry, bool>>>()).Returns(submissionCountry);
            
            var picklistdatamodel = new List<PicklistDataModel>
                 {
                     new PicklistDataModel {Id = 1,Name = "Dosage Form1" },
                     new PicklistDataModel {Id = 2,Name = "Dosage Form2" },
                 };
            var submissioresourcenmodel = new SubmissionResourceModel()
            {
                Id = 1,
                Comments = "Test",
                SubmissionId = 1
            };
            List<SubmissionPublisher> submissionPublishers = new List<SubmissionPublisher>()
             {
                 new SubmissionPublisher
                 {
                     Id=1,
                     SubmissionResourceId=1,
                     SubmissionResource=new SubmissionResource()
                     {
                         Id=1,InitialSentToEmail="<EMAIL>"
                     },
                     Publisher=new User()
                     {
                         Email="<EMAIL>",
                         GivenName="Test",
                         FamilyName="Test",
                     }
                 }
             };
            UserModel usermodel = new UserModel() { Id = 1, DisplayFullName = "abc", Email = "<EMAIL>" };
            var _missingCountries = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionCountry>>();
            _cache.CreateTrackedEntity<SubmissionCountry>().Returns(_missingCountries);
            _missingCountries.WhereAsync(Arg.Any<Expression<Func<SubmissionCountry, bool>>>()).Returns(submissionCountry);

            var _picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);

            for (int i = 0; i < picklistdatamodel.Count; i++)
            {
                _picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(picklistdatamodel[i]);
            }

            var userdCache = Substitute.For<IMappedEntityCacheServiceProxy<User, UserModel>>();
            _cache.CreateMappedEntity<User, UserModel>().Returns(userdCache);
            userdCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<User, bool>>>()).Returns(usermodel);

            var _submissionResourcecache = Substitute.For<IMappedEntityCacheServiceProxy<SubmissionResource, SubmissionResourceModel>>();
            _cache.CreateMappedEntity<SubmissionResource, SubmissionResourceModel>().Returns(_submissionResourcecache);
            _submissionResourcecache.FirstOrDefaultAsync(Arg.Any<Expression<Func<SubmissionResource, bool>>>()).Returns(submissioresourcenmodel);

            var _subPublisherCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionPublisher>>();
            _cache.CreateTrackedEntity<SubmissionPublisher>().Returns(_subPublisherCache);
            _subPublisherCache.Where(Arg.Any<Expression<Func<SubmissionPublisher, bool>>>()).Returns(submissionPublishers);

            var _missingPublishers = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionPublisher>>();
            _cache.CreateTrackedEntity<SubmissionPublisher>().Returns(_missingPublishers);
            _missingPublishers.WhereAsync(Arg.Any<Expression<Func<SubmissionPublisher, bool>>>()).Returns(submissionPublishers);

            var _PublishersCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionPublisher>>();
            _cache.CreateEntity<SubmissionPublisher>().Configure(Arg.Any<Func<IIncludable<SubmissionPublisher>, IIncludable<SubmissionPublisher>>>()).Returns(_PublishersCache);
            _PublishersCache.WhereAsync(Arg.Any<Expression<Func<SubmissionPublisher, bool>>>()).Returns(submissionPublishers);
            var mapper = Substitute.For<IMapper>();


            LoadSubmissionData(id, 1, true);
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            controller.TempData = tempData;

            // Act
            var result = await controller.SaveEdit(id, model, cancellationToken) as ViewResult;

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal(ViewName, viewResult.ViewName);
        }
        #endregion
        #region SaveEdit_SubmissionIsNotUpdated
        [Fact]
        public async Task SaveEdit_SubmissionIsNotUpdated()
        {
            // Arrange
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            var id = 1;
            var cancellationToken = new CancellationToken(true);
            var model = new EditSubmissionViewModel
            {
                Submission = new SubmissionModel { Id = id },
                SubmissionResource = new SubmissionResourceModel { SubmissionId = id,Comments="Test" }
            };

            // Act
            var result = await controller.SaveEdit(id, model, cancellationToken) as ViewResult;

            // Assert
            Assert.Null(result);
        }
        #endregion

        #region SaveEdit_InvalidObject
        [Fact]
        public async Task SaveEdit_InvalidObject()
        {
            // Arrange
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            controller.ModelState.AddModelError("Key", "ErrorMessage");
            var id = 1;
            var cancellationToken = new CancellationToken(true);
            var model = new EditSubmissionViewModel
            {
                Submission = new SubmissionModel { Id = id },
                SubmissionResource = new SubmissionResourceModel { SubmissionId = id }
            };

            // Act
            var result = await controller.SaveEdit(id, model, cancellationToken) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region MoveToObsoleteState_Returns_OkResult
        [Fact]
        public async Task MoveToObsoleteState_Returns_OkResult()
        {
            // Arrange
            var id = 1;
            Submission submission = new Submission()
            {
                Id = id,
                UniqueId = "91",
                Project = new Project() { Id = 1, Name = "proj1" },
                ProjectId = 1,
                LifecycleStateId = 1
            };
            var submissionCache = Substitute.For<ITrackedEntityCacheServiceProxy<Submission>>();
            _cache.CreateTrackedEntity<Submission>().Returns(submissionCache);
            submissionCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submission);
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);

            // Act
            var result = await controller.MoveToObsoleteState(id) as OkResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(200, result.StatusCode);
        }
        #endregion
        #region MoveToObsoleteState_Returns_InvalidObject
        [Fact]
        public async Task MoveToObsoleteState_Returns_InvalidObject()
        {
            // Arrange
            var id = 1;
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            // Act
            var result = await controller.MoveToObsoleteState(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region Action Method Export Post

        [Fact]
        public async Task Export_Returns_FileResult()
        {
            // Arrange
            var identity = new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
              new System.Security.Claims.Claim(ClaimTypes.Name, "testUser")
            }, "TestAuthentication");
            var user = new ClaimsPrincipal(identity);
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            SubmissionFilterModel filterModel = new SubmissionFilterModel();
            var mockFileContent = new byte[] { 1, 2, 3 }; // Mock file content
            var exportResultStream = new MemoryStream(mockFileContent);
            _subExport.Export(user, filterModel).Returns(mockFileContent);

            // Act
            var result = await controller.Export(filterModel);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileResult.ContentType);
            Assert.NotNull(fileResult.FileContents);
        }

        #endregion
        #region Action Method Export Post_Invalid

        [Fact]
        public async Task Export_Returns_Invalid()
        {
            // Arrange
            var identity = new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
              new System.Security.Claims.Claim(ClaimTypes.Name, "testUser")
            }, "TestAuthentication");
            var user = new ClaimsPrincipal(identity);
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            SubmissionFilterModel filterModel = new SubmissionFilterModel();
            var mockFileContent = new byte[] { 1, 2, 3 }; // Mock file content
            var exportResultStream = new MemoryStream(mockFileContent);
            _subExport.Export(user, filterModel).Returns(mockFileContent);
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            // Act
            var result = await controller.Export(filterModel) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }

        #endregion

        #region SaveDocument_Returns_JsonResultDocument
        [Fact]
        public async Task SaveDocument_Returns_JsonResultDocument()
        {
            // Arrange
            DocumentModel documentModel = new DocumentModel()
            { Id = 1, Name = "doc", Version = 2 };
            Document document = new Document()
            { Id = 1, Name = "doc", Version = 2 };
            var docCache = Substitute.For<ITrackedEntityCacheServiceProxy<Document>>();
            _cache.CreateTrackedEntity<Document>().Returns(docCache);
            _mapper.Map<DocumentModel, Document>(documentModel).Returns(document);
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);

            // Act
            var result = await controller.SaveDocument(documentModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(document, result.Value);
        }
        #endregion
        #region SaveDocument_Returns_InvalidObject
        [Fact]
        public async Task SaveDocument_Returns_InvalidObject()
        {
            // Arrange
            DocumentModel documentModel = new DocumentModel()
            { Id = 1, Name = "doc", Version = 2 };
            Document document = new Document()
            { Id = 1, Name = "doc", Version = 2 };
            var docCache = Substitute.For<ITrackedEntityCacheServiceProxy<Document>>();
            _cache.CreateTrackedEntity<Document>().Returns(docCache);
            _mapper.Map<DocumentModel, Document>(documentModel).Returns(document);
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            // Act
            var result = await controller.SaveDocument(documentModel) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region DeleteDocument_Returns_JsonResultDocument
        [Fact]
        public async Task DeleteDocument_Returns_JsonResultDocument()
        {
            // Arrange
            DocumentModel documentModel = new DocumentModel()
            { Id = 1, Name = "doc", Version = 2 };
            Document document = new Document()
            { Id = 1, Name = "doc", Version = 2 };
            var docCache = Substitute.For<ITrackedEntityCacheServiceProxy<Document>>();
            _cache.CreateTrackedEntity<Document>().Returns(docCache);
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);

            // Act
            var result = await controller.DeleteDocument(1, documentModel.Id) as NoContentResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(204, result.StatusCode);
        }
        #endregion
        #region DeleteDocument_Returns_InvalidObject
        [Fact]
        public async Task DeleteDocument_Returns_InvalidObject()
        {
            // Arrange
            DocumentModel documentModel = new DocumentModel()
            { Id = 1, Name = "doc", Version = 2 };
            Document document = new Document()
            { Id = 1, Name = "doc", Version = 2 };
            var docCache = Substitute.For<ITrackedEntityCacheServiceProxy<Document>>();
            _cache.CreateTrackedEntity<Document>().Returns(docCache);
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            // Act
            var result = await controller.DeleteDocument(1, documentModel.Id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region GetLatestFileVersion_Returns_JsonResultDocument
        [Fact]
        public async Task GetLatestFileVersion_Returns_JsonResultDocument()
        {
            // Arrange
            List<DocumentModel> documents = new List<DocumentModel>() { new DocumentModel() { Id=1, DocumentTypeId=2, Version=3} ,
            new DocumentModel() { Id=2, DocumentTypeId=2, Version=8}};
            var docCache = Substitute.For<IMappedEntityCacheServiceProxy<Document, DocumentModel>>();
            _cache.CreateMappedEntity<Document, DocumentModel>().Returns(docCache);
            docCache.WhereAsync(Arg.Any<Expression<Func<Document, bool>>>())
                .Returns(documents);
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);

            // Act
            var result = await controller.GetLatestFileVersion(1, "file", "2") as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(8, result.Value);
        }
        #endregion
        #region GetLatestFileVersion_Returns_InvalidObject
        [Fact]
        public async Task GetLatestFileVersion_Returns_InvalidObject()
        {
            // Arrange
            List<DocumentModel> documents = new List<DocumentModel>() { new DocumentModel() { Id=1, DocumentTypeId=2, Version=3} ,
            new DocumentModel() { Id=2, DocumentTypeId=2, Version=8}};
            var docCache = Substitute.For<IMappedEntityCacheServiceProxy<Document, DocumentModel>>();
            _cache.CreateMappedEntity<Document, DocumentModel>().Returns(docCache);
            docCache.WhereAsync(Arg.Any<Expression<Func<Document, bool>>>())
                .Returns(documents);
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            // Act
            var result = await controller.GetLatestFileVersion(1, "file", "2") as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region SaveDossierFormat_Returns_OKResult
        [Fact]
        public async Task SaveDossierFormat_Returns_InvalidObject()
        {
            // Arrange
            Submission submission = new Submission()
            {
                Id = 1,
                UniqueId = "112",
                Project = new Project() { Id = 1, Name = "proj1" },
                ProjectId = 1,
                LifecycleStateId = 1,
                DossierFormatId = 2
            };
            var submissionCache = Substitute.For<ITrackedEntityCacheServiceProxy<Submission>>();
            _cache.CreateTrackedEntity<Submission>().Returns(submissionCache);
            submissionCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submission);

            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);

            // Act
            var result = await controller.SaveDossierFormat(1, "2") as OkResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(200, result.StatusCode);
        }
        [Theory]
        [InlineData("<EMAIL>", "TestData")]
        [InlineData("<EMAIL>", "TestData")]
        [InlineData("InvalidData", "RegulatoryLeadEmptyTestData")]
        [InlineData("InvalidData", "InvalidData")]
        [InlineData("InvalidData", "PublishingLeadTestData")]
        [InlineData("InvalidData", "InvalidIdentity")]
        [InlineData("InvalidData", "QCReview")]
        [InlineData("InvalidData", "ApprovedForSubmission")]
        [InlineData("application:Editor", "WithdrawnFromHA")]
        public async Task NextState_Returns_RedirectResult(string name, string testdata)
        {
            // Arrange
            int lifecyclestateid = 2;
            string publishinglead = "<EMAIL>";
            string regulatorylead = "<EMAIL>";
            if (name == "InvalidData" && testdata == "RegulatoryLeadEmptyTestData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                regulatorylead = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "InvalidData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "PublishingLeadTestData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "InvalidIdentity")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 3;
            }
            if (name == "InvalidData" && testdata == "QCReview")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 5;
            }
            if (name == "InvalidData" && testdata == "ApprovedForSubmission")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 6;
            }
            if (name == "application:Editor" && testdata == "WithdrawnFromHA")
            {
                lifecyclestateid = 8;
            }
            EditSubmissionViewModel model = new EditSubmissionViewModel
            {
                Submission = new SubmissionModel()
                {
                    ActualDispatchDate = DateTime.Now,
                    ActualSubmissionDate = DateTime.Now,
                    ApplicationId = 1,
                    Comments = "Test",
                    CountriesIds = [1, 2, 3],
                    LifecycleStateId = lifecyclestateid

                },
                SubmissionResource = new SubmissionResourceModel()
                {
                    Id = 1,
                    Publishers = new List<UserFindResultModel>() { new UserFindResultModel { Id = 1, Email = "<EMAIL>", FamilyName = "Test", GivenName = "Test", Name = "Test", Value = "Test" } },
                    Comments= "Test"
                },
                Product = "Prod1",
                Client = new ClientModel() { Name = "client1" }

            };
            
            UpdateSubmissionPublishersData(model, lifecyclestateid, regulatorylead, publishinglead);
            UpdateSubmissionCountriesData(model, lifecyclestateid);

            var clientsId = (await GetUsersList()).Select(x => x.ClientId).ToList();
            NotifyUserData(model, clientsId, regulatorylead, publishinglead, lifecyclestateid);

            var context = new DefaultHttpContext();
            var identity = new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
               new System.Security.Claims.Claim(ClaimTypes.Name, name)
            }, "TestAuthentication");
            var currentuser = new ClaimsPrincipal(identity);
            controller.ControllerContext = new ControllerContext
            {
                HttpContext = new Microsoft.AspNetCore.Http.DefaultHttpContext { User = currentuser }
            };
            // Act
            var result = await controller.NextState(1, model) as RedirectResult;
            // Assert
            if (result?.Url == "/submissions/edit/1")
            {
                Assert.Equal("/submissions/edit/1", result?.Url);
            }
            else if (result?.Url == "/submissions/view/1")
            {
                Assert.Equal("/submissions/view/1", result?.Url);
            }
        }
        #endregion
        #region SaveDossierFormat_Returns_Invalid
        [Fact]
        public async Task SaveDossierFormat_Returns_OKResult()
        {
            // Arrange
            Submission submission = new Submission()
            {
                Id = 1,
                UniqueId = "112",
                Project = new Project() { Id = 1, Name = "proj1" },
                ProjectId = 1,
                LifecycleStateId = 1,
                DossierFormatId = 2
            };
            var submissionCache = Substitute.For<ITrackedEntityCacheServiceProxy<Submission>>();
            _cache.CreateTrackedEntity<Submission>().Returns(submissionCache);
            submissionCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submission);

            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);

            // Act
            var result = await controller.SaveDossierFormat(1, "2") as OkResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(200, result.StatusCode);
        }
        [Theory]
        [InlineData("<EMAIL>", "TestData")]
        [InlineData("<EMAIL>", "TestData")]
        [InlineData("InvalidData", "RegulatoryLeadEmptyTestData")]
        [InlineData("InvalidData", "InvalidData")]
        [InlineData("InvalidData", "PublishingLeadTestData")]
        [InlineData("InvalidData", "InvalidIdentity")]
        [InlineData("InvalidData", "QCReview")]
        [InlineData("InvalidData", "ApprovedForSubmission")]
        [InlineData("application:Editor", "WithdrawnFromHA")]
        public async Task NextState_Returns_InvalidObject(string name, string testdata)
        {
            // Arrange
            int lifecyclestateid = 2;
            string publishinglead = "<EMAIL>";
            string regulatorylead = "<EMAIL>";
            if (name == "InvalidData" && testdata == "RegulatoryLeadEmptyTestData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                regulatorylead = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "InvalidData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "PublishingLeadTestData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "InvalidIdentity")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 3;
            }
            if (name == "InvalidData" && testdata == "QCReview")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 5;
            }
            if (name == "InvalidData" && testdata == "ApprovedForSubmission")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 6;
            }
            if (name == "application:Editor" && testdata == "WithdrawnFromHA")
            {
                lifecyclestateid = 8;
            }
            EditSubmissionViewModel model = new EditSubmissionViewModel
            {
                Submission = new SubmissionModel()
                {
                    ActualDispatchDate = DateTime.Now,
                    ActualSubmissionDate = DateTime.Now,
                    ApplicationId = 1,
                    Comments = "Test",
                    CountriesIds = [1, 2, 3],
                    LifecycleStateId = lifecyclestateid

                },
                SubmissionResource = new SubmissionResourceModel()
                {
                    Id = 1,
                    Publishers = new List<UserFindResultModel>() { new UserFindResultModel { Id = 1, Email = "<EMAIL>", FamilyName = "Test", GivenName = "Test", Name = "Test", Value = "Test" } },
                },
                Product = "Prod1",
                Client = new ClientModel() { Name = "client1" }

            };

            UpdateSubmissionPublishersData(model, lifecyclestateid, regulatorylead, publishinglead);
            UpdateSubmissionCountriesData(model, lifecyclestateid);

            var clientsId = (await GetUsersList()).Select(x => x.ClientId).ToList();
            NotifyUserData(model, clientsId, regulatorylead, publishinglead, lifecyclestateid);

            var context = new DefaultHttpContext();
            var identity = new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
               new System.Security.Claims.Claim(ClaimTypes.Name, name)
            }, "TestAuthentication");
            var currentuser = new ClaimsPrincipal(identity);
            controller.ControllerContext = new ControllerContext
            {
                HttpContext = new Microsoft.AspNetCore.Http.DefaultHttpContext { User = currentuser }
            };
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            // Act
            var result = await controller.NextState(1, model) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        [Fact]
        public async Task NextState_Edit_HtmlTagsInName_ShouldReturnBadRequest()
        {
            // Arrange          
            var model = new EditSubmissionViewModel
            {
                SubmissionResource = new SubmissionResourceModel { Comments = "<div>Test</div>" }
            };

            // Act
            var result = await controller.NextState(1, model) as BadRequestObjectResult;
            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        [Fact]
        public async Task SaveEdit_Edit_HtmlTagsInName_ShouldReturnBadRequest()
        {
            // Arrange            
            var model = new EditSubmissionViewModel
            {
                SubmissionResource = new SubmissionResourceModel { Comments = "<div>Test</div>" }
            };
            var cancellationToken = new CancellationToken(true);
            // Act            
            var result = await controller.SaveEdit(1, model, cancellationToken) as BadRequestObjectResult;
            // Assert            
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }

        #region PreviousState_Returns_RedirectResult
        [Theory]
        [InlineData("<EMAIL>", "TestData")]
        [InlineData("<EMAIL>", "TestData")]
        [InlineData("InvalidData", "RegulatoryLeadEmptyTestData")]
        [InlineData("InvalidData", "InvalidData")]
        [InlineData("InvalidData", "PublishingLeadTestData")]
        [InlineData("InvalidData", "InvalidIdentity")]
        [InlineData("InvalidData", "QCReview")]
        [InlineData("InvalidData", "ApprovedForSubmission")]
        public async Task PreviousState_Returns_RedirectResult(string name, string testdata)
        {
            // Arrange
            int lifecyclestateid = 2;
            string publishinglead = "<EMAIL>";
            string regulatorylead = "<EMAIL>";
            if (name == "InvalidData" && testdata == "RegulatoryLeadEmptyTestData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                regulatorylead = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "InvalidData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "PublishingLeadTestData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "InvalidIdentity")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 3;
            }
            if (name == "InvalidData" && testdata == "QCReview")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 5;
            }
            if (name == "InvalidData" && testdata == "ApprovedForSubmission")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 6;
            }
            EditSubmissionViewModel model = new EditSubmissionViewModel
            {
                Submission = new SubmissionModel()
                {
                    ActualDispatchDate = DateTime.Now,
                    ActualSubmissionDate = DateTime.Now,
                    ApplicationId = 1,
                    Comments = "Test",
                    CountriesIds = [1, 2, 3],
                    LifecycleStateId = lifecyclestateid

                },
                SubmissionResource = new SubmissionResourceModel()
                {
                    Id = 1,
                    Publishers = new List<UserFindResultModel>() { new UserFindResultModel { Id = 1, Email = "<EMAIL>", FamilyName = "Test", GivenName = "Test", Name = "Test", Value = "Test" } },
                },
                Product = "Prod1",
                Client = new ClientModel() { Name = "client1" }

            };

            UpdateSubmissionPublishersData(model, lifecyclestateid, regulatorylead, publishinglead);
            UpdateSubmissionCountriesData(model, lifecyclestateid);

            var clientsId = (await GetUsersList()).Select(x => x.ClientId).ToList();
            NotifyUserData(model, clientsId, regulatorylead, publishinglead, lifecyclestateid);

            var context = new DefaultHttpContext();
            var identity = new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
               new System.Security.Claims.Claim(ClaimTypes.Name, name)
            }, "TestAuthentication");
            var currentuser = new ClaimsPrincipal(identity);
            controller.ControllerContext = new ControllerContext
            {
                HttpContext = new Microsoft.AspNetCore.Http.DefaultHttpContext { User = currentuser }
            };
            var result = await controller.PreviousState(1, model) as RedirectResult;
            Assert.Equal("/submissions/edit/1", result?.Url);
        }
        #endregion
        #region PreviousState_Returns_InvalidObject
        [Theory]
        [InlineData("<EMAIL>", "TestData")]
        [InlineData("<EMAIL>", "TestData")]
        [InlineData("InvalidData", "RegulatoryLeadEmptyTestData")]
        [InlineData("InvalidData", "InvalidData")]
        [InlineData("InvalidData", "PublishingLeadTestData")]
        [InlineData("InvalidData", "InvalidIdentity")]
        [InlineData("InvalidData", "QCReview")]
        [InlineData("InvalidData", "ApprovedForSubmission")]
        public async Task PreviousState_Returns_InvalidObject(string name, string testdata)
        {
            // Arrange
            int lifecyclestateid = 2;
            string publishinglead = "<EMAIL>";
            string regulatorylead = "<EMAIL>";
            if (name == "InvalidData" && testdata == "RegulatoryLeadEmptyTestData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                regulatorylead = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "InvalidData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "PublishingLeadTestData")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
            }
            if (name == "InvalidData" && testdata == "InvalidIdentity")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 3;
            }
            if (name == "InvalidData" && testdata == "QCReview")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 5;
            }
            if (name == "InvalidData" && testdata == "ApprovedForSubmission")
            {
                publishinglead = "<EMAIL>";
                name = "<EMAIL>";
                lifecyclestateid = 6;
            }
            EditSubmissionViewModel model = new EditSubmissionViewModel
            {
                Submission = new SubmissionModel()
                {
                    ActualDispatchDate = DateTime.Now,
                    ActualSubmissionDate = DateTime.Now,
                    ApplicationId = 1,
                    Comments = "Test",
                    CountriesIds = [1, 2, 3],
                    LifecycleStateId = lifecyclestateid

                },
                SubmissionResource = new SubmissionResourceModel()
                {
                    Id = 1,
                    Publishers = new List<UserFindResultModel>() { new UserFindResultModel { Id = 1, Email = "<EMAIL>", FamilyName = "Test", GivenName = "Test", Name = "Test", Value = "Test" } },
                },
                Product = "Prod1",
                Client = new ClientModel() { Name = "client1" }

            };

            UpdateSubmissionPublishersData(model, lifecyclestateid, regulatorylead, publishinglead);
            UpdateSubmissionCountriesData(model, lifecyclestateid);

            var clientsId = (await GetUsersList()).Select(x => x.ClientId).ToList();
            NotifyUserData(model, clientsId, regulatorylead, publishinglead, lifecyclestateid);

            var context = new DefaultHttpContext();
            var identity = new ClaimsIdentity(new System.Security.Claims.Claim[]
            {
               new System.Security.Claims.Claim(ClaimTypes.Name, name)
            }, "TestAuthentication");
            var currentuser = new ClaimsPrincipal(identity);
            controller.ControllerContext = new ControllerContext
            {
                HttpContext = new Microsoft.AspNetCore.Http.DefaultHttpContext { User = currentuser }
            };
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            //Act
            var result = await controller.PreviousState(1, model) as BadRequestObjectResult;

            //Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region FinalState_Returns_NoContentResult
        [Fact]
        public async Task FinalState_Returns_NoContentResult()
        {
            Submission submission = new Submission()
            {
                Id = 1,
                UniqueId = "112",
                Project = new Project() { Id = 1, Name = "proj1", Client = new Client { Id = 9 } },
                ProjectId = 1,
                LifecycleStateId = 2,
                DossierFormatId = 2,
                Application = new Application() { ApplicationNumber = "1" }
            };
            SubmissionResource submissionresource = new SubmissionResource()
            {
                RegulatoryLead = "<EMAIL>",
                Submission = new Submission()
                {
                    Id = 1,
                    UniqueId = "112",
                    Project = new Project() { Id = 1, Name = "proj1" },
                    ProjectId = 1,
                    LifecycleStateId = 9,
                    DossierFormatId = 2
                },
                Id = 1,
                PublishingLead = "<EMAIL>",
                SubmissionPublisher = new List<SubmissionPublisher>()
                {
                    new SubmissionPublisher{ Publisher=new User(){ Email="<EMAIL>"} }
                }
            };
            var submissionCache = Substitute.For<ITrackedEntityCacheServiceProxy<Submission>>();
            _cache.CreateTrackedEntity<Submission>().Returns(submissionCache);
            submissionCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submission);

            var subResourceCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionResource>>();
            _cache.CreateTrackedEntity<SubmissionResource>()
                .Configure(Arg.Any<Func<IIncludable<SubmissionResource>, IIncludable<SubmissionResource>>>()).Returns(subResourceCache);
            subResourceCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<SubmissionResource, bool>>>()).Returns(submissionresource);



            var result = await controller.FinalState(1, DateTime.Now) as NoContentResult;
            Assert.NotNull(result);
            Assert.Equal(204, result?.StatusCode);
        }
        #endregion
        #region FinalState_Returns_InvalidObject
        [Fact]
        public async Task FinalState_Returns_InvalidObject()
        {
            Submission submission = new Submission()
            {
                Id = 1,
                UniqueId = "112",
                Project = new Project() { Id = 1, Name = "proj1", Client = new Client { Id = 9 } },
                ProjectId = 1,
                LifecycleStateId = 2,
                DossierFormatId = 2,
                Application = new Application() { ApplicationNumber = "1" }
            };
            SubmissionResource submissionresource = new SubmissionResource()
            {
                RegulatoryLead = "<EMAIL>",
                Submission = new Submission()
                {
                    Id = 1,
                    UniqueId = "112",
                    Project = new Project() { Id = 1, Name = "proj1" },
                    ProjectId = 1,
                    LifecycleStateId = 9,
                    DossierFormatId = 2
                },
                Id = 1,
                PublishingLead = "<EMAIL>",
                SubmissionPublisher = new List<SubmissionPublisher>()
                    {
                        new SubmissionPublisher{ Publisher=new User(){ Email="<EMAIL>"} }
                    }
            };
            var submissionCache = Substitute.For<ITrackedEntityCacheServiceProxy<Submission>>();
            _cache.CreateTrackedEntity<Submission>().Returns(submissionCache);
            submissionCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submission);

            var subResourceCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionResource>>();
            _cache.CreateTrackedEntity<SubmissionResource>()
                .Configure(Arg.Any<Func<IIncludable<SubmissionResource>, IIncludable<SubmissionResource>>>()).Returns(subResourceCache);
            subResourceCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<SubmissionResource, bool>>>()).Returns(submissionresource);
            controller.ModelState.AddModelError("Key", "ErrorMessage");


            //Act
            var result = await controller.FinalState(1, DateTime.Now) as BadRequestObjectResult;

            //Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region View_Returns_SubmissionModel
        [Fact]
        public async Task View_Returns_SubmissionModel()
        {
            var clientId = 1;
            LoadSubmissionData(clientId, 1, true);

            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);

            // Act
            var result = await controller.View(1) as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("ViewSubmission", result.ViewName);
        }
        #endregion
        #region View_Returns_SubmissionModel_Picker
        [Fact]
        public async Task View_Returns_SubmissionModel_Picker()
        {
            var clientId = 1;
            LoadSubmissionData(clientId, 1, false);

            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);

            // Act
            var result = await controller.View(1) as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("ViewSubmission", result.ViewName);
        }
        #endregion
        #region View_Returns_ForbidResult
        [Fact]
        public async Task View_Returns_ForbidResult()
        {
            //Arrange
            var clientid = 88;
            LoadSubmissionData(clientid, 1, true);
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);

            // Act
            var result = await controller.View(1);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ForbidResult>(result);

        }
        #endregion
        #region View_Returns_SubmissionModelFor_isActualDateNotRequired_True
        [Fact]
        public async Task View_Returns_SubmissionModelFor_isActualDateNotRequired_True()
        {
            var clientId = 1;
            var LifecycleStateId = 8;
            LoadSubmissionData(clientId, LifecycleStateId, true);

            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);

            // Act
            var result = await controller.View(1) as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("ViewSubmission", result.ViewName);
        }
        #endregion
        #region View_Returns_SubmissionModel_Picker_Invalid
        [Fact]
        public async Task View_Returns_SubmissionModel_Picker_Invalid()
        {
            var controller = new AppSubmissionsController(_cache, _mapper, _notificationService, _subExport, _submissionService);
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            // Act
            var result = await controller.View(1) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region private method LoadSubmissionData

        private void LoadSubmissionData(int clientId, int LifecycleStateId, bool picker, int LCStateId = 1,int? SubmissionClientID = null)
        {
            // Arrange
            SubmissionClientID = SubmissionClientID==null?clientId:SubmissionClientID;
            List<PicklistDataCountry> picklistDataCountry = new()
        {
            new PicklistDataCountry(){ Id = 1, CountryId=1},
            new PicklistDataCountry(){ Id = 2, CountryId=1}
        };
            List<Project> projDataModel = new List<Project>
            { new Project { Id=1,ClientId=1, Code="11" , Name="Project1", OpportunityNumber="111"}};
            Submission submission = new Submission()
            {
                Id = 1,
                UniqueId = "91",
                Project = new Project() { Id = 1, Name = "proj1", ClientId = Convert.ToInt32(SubmissionClientID) },
                ProjectId = 1,
                LifecycleStateId = 8,
                DeliveryDetailsId = 3,
                DossierFormatId = 9,
                SubmissionTypeId = 2,
                SubmissionModeId = 66,
                SubmissionUnitId = 5,

                SubmissionResource = new SubmissionResource() { Id = 3, PriorityId = 2, EstimatedSizeId = 3, PublishingTeamId = 4, SubmissionPublisher = new List<SubmissionPublisher>() { } }
            };

            List<DocumentModel> documents = new List<DocumentModel>()
            { new DocumentModel() { Id = 1, Name = "docmod", SubmissionId = 1,DocumentTypeId=2 ,Version=1} };
            SubmissionModel submissionModel = new SubmissionModel()
            {
                Id = 1,
                UniqueId = "91",
                PreviousLifecycleStateId = 1,
                ProjectId = 1,
                LifecycleStateId = LCStateId,
                Documents = documents,
                SubmissionTypeId = 2,
                SubmissionDeliveryDetails = "na"
            };
            List<DocshifterDocumentFile> docshifterDocuments = new List<DocshifterDocumentFile>()
            {new DocshifterDocumentFile(){ Id=1,DocumentId=2, FileName="f1", StateId=2} };
            List<Document> documentsList = new List<Document>();
            { new DocumentModel() { Id = 1, Name = "docmod", SubmissionId = 1, DocumentTypeId = 1 }; }
            Document document = new Document() { Id = 1, Name = "docmod", SubmissionId = 1, DocumentTypeId = 2 };
            List<PicklistDataModel> PicklistDataModel = new()
        {   new PicklistDataModel(){Id =1,Name= "prod1",CountriesIds=[1],PicklistTypeId=1, Selected=true, Country="country1"  },
            new PicklistDataModel(){Id =2,Name= "prod2",CountriesIds=[2],PicklistTypeId=1, Selected=true, Country="country2"}
        };
            PicklistDataModel pkDatamodel = new()
            {
                Id = 11,
                CountriesIds = [1],
                PicklistTypeId = 11
            };
            if (!picker)
            { pkDatamodel.Name = "Publish & Send to Client"; }
            IList<UserFindResultModel> userFindResultModels = new List<UserFindResultModel>() { };

            List<ProjectModel> projects = new List<ProjectModel>() { new ProjectModel() { Id = 8, ClientId = 7, ClientName = "abc", Code = "876" } };
            ClientModel clientModel = new ClientModel() { Id = 77 };
            SubmissionResourceModel submissionResourceModel = new SubmissionResourceModel() { Id = 45 };
            ProjectModel projectModel = new ProjectModel() { Id = 8, ClientId = 7, ClientName = "abc", Code = "876" };
            ApplicationModel appmodel = new ApplicationModel() { ApplicationNumber = "001", Id = 4, UniqueId = "77", LifecycleStateId = 1, ApplicationTypeId = 3, ProcedureTypeId = 2 };
            List<CountryModel> countryModels = new List<CountryModel>() { new CountryModel() { Id = 23, Name = "india" }, new CountryModel() { Id = 24, Name = "peru" } };
            List<int> clientid = [1, 2, 3];
            List<UserClient> clientlist = new List<UserClient>() { new UserClient() { ClientId = 1, UserId = 63 } };
            List<SubmissionCountry> countryList = new List<SubmissionCountry>()
            { new SubmissionCountry(){Id=11, SubmissionId=1,Country=new Country(){Id=23,Name="in"} } };
            List<ApplicationCountry> appcountryList = new List<ApplicationCountry>()
            { new ApplicationCountry(){Id=11, ApplicationId=100,Country=new Country(){Id=23,Name="in"} } };
            List<ApplicationProduct> productList = new List<ApplicationProduct>()
            { new ApplicationProduct(){Id=23, ApplicationId=100, ProductId=22,Application= new Application() { Id=1, ApplicationNumber="33"} ,Product= new Product(){Id=1, DosageFormId=3, Name="pro", Strength="4" } }};
            List<SubmissionPublisher> publisherList = new List<SubmissionPublisher>()
            { new SubmissionPublisher(){Id=33, PublisherId=00, SubmissionResourceId=89}};
            var appProductCache = Substitute.For<IEntityCacheServiceProxy<ApplicationProduct>>();
            _cache.CreateEntity<ApplicationProduct>()
               .Configure(Arg.Any<Func<IIncludable<ApplicationProduct>, IIncludable<ApplicationProduct>>>())
               .Returns(appProductCache);
            var subPublisherCache = Substitute.For<IEntityCacheServiceProxy<SubmissionPublisher>>();
            _cache.CreateEntity<SubmissionPublisher>()
               .Configure(Arg.Any<Func<IIncludable<SubmissionPublisher>, IIncludable<SubmissionPublisher>>>())
               .Returns(subPublisherCache);
            var subCountryCache = Substitute.For<IEntityCacheServiceProxy<SubmissionCountry>>();
            _cache.CreateEntity<SubmissionCountry>()
               .Configure(Arg.Any<Func<IIncludable<SubmissionCountry>, IIncludable<SubmissionCountry>>>())
               .Returns(subCountryCache);
            var appCountryCache = Substitute.For<IEntityCacheServiceProxy<ApplicationCountry>>();
            _cache.CreateEntity<ApplicationCountry>()
               .Configure(Arg.Any<Func<IIncludable<ApplicationCountry>, IIncludable<ApplicationCountry>>>())
               .Returns(appCountryCache);
            var submissionCache = Substitute.For<IEntityCacheServiceProxy<Submission>>();
            _cache.CreateEntity<Submission>()
               .Configure(Arg.Any<Func<IIncludable<Submission>, IIncludable<Submission>>>())
               .Returns(submissionCache);
            var userclientcache = Substitute.For<IEntityCacheServiceProxy<UserClient>>();
            _cache.CreateEntity<UserClient>()
               .Configure(Arg.Any<Func<IIncludable<UserClient>, IIncludable<UserClient>>>())
               .Returns(userclientcache);
            submissionCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submission);
            subCountryCache.WhereAsync(Arg.Any<Expression<Func<SubmissionCountry, bool>>>()).Returns(countryList);

            appCountryCache.WhereAsync(Arg.Any<Expression<Func<ApplicationCountry, bool>>>()).Returns(appcountryList);

            appProductCache.WhereAsync(Arg.Any<Expression<Func<ApplicationProduct, bool>>>()).Returns(productList);

            subPublisherCache.WhereAsync(Arg.Any<Expression<Func<SubmissionPublisher, bool>>>()).Returns(publisherList);
            _cache.CreateEntity<UserClient>()
              .WhereAsync(Arg.Any<Expression<Func<UserClient, bool>>>())
              .Returns(clientlist);
            _mapper.Map<IList<CountryModel>>(Arg.Any<List<SubmissionCountry>>()).ReturnsForAnyArgs(countryModels);
            _mapper.Map<IList<CountryModel>>(Arg.Any<List<ApplicationCountry>>()).ReturnsForAnyArgs(countryModels);

            _mapper.Map<ApplicationModel>(Arg.Any<Application>()).Returns(appmodel);
            _mapper.Map<SubmissionModel>(Arg.Any<Submission>()).Returns(submissionModel);
            _mapper.Map<ProjectModel>(Arg.Any<Project>()).Returns(projectModel);
            _mapper.Map<SubmissionResourceModel>(Arg.Any<SubmissionResource>()).Returns(submissionResourceModel);
            _mapper.Map<ClientModel>(Arg.Any<Client>()).Returns(clientModel);

            //  var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<Project, ProjectModel>>();
            _cache.CreateMappedEntity<Project, ProjectModel>().WhereAsync(Arg.Any<Expression<Func<Project, bool>>>()).Returns(projects);

            //picklistondatawithcountry
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));

            var PicklistDataCountryCache = Substitute.For<IEntityCacheServiceProxy<PicklistDataCountry>>();
            _cache.CreateEntity<PicklistDataCountry>().Returns(PicklistDataCountryCache);
            PicklistDataCountryCache.AllAsync().Returns(Task.FromResult(picklistDataCountry));
            picklistCache.WhereAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(PicklistDataModel));
            var projCache = Substitute.For<IEntityCacheServiceProxy<Project>>();
            _cache.CreateEntity<Project>().Returns(projCache);
            projCache.WhereAsync(Arg.Any<Expression<Func<Project, bool>>>()).Returns(projDataModel);
            _mapper.Map<IList<UserFindResultModel>>(Arg.Any<List<SubmissionPublisher>>()).Returns(userFindResultModels);
            var docentityCacheServiceProxy = Substitute.For<IEntityCacheServiceProxy<Document>>();
            _cache.CreateEntity<Document>().Returns(docentityCacheServiceProxy);
            docentityCacheServiceProxy.FirstOrDefaultAsync(Arg.Any<Expression<Func<Document, bool>>>()).Returns(document);

            _cache.CreateEntity<UserClient>()
             .WhereAsync(Arg.Any<Expression<Func<UserClient, bool>>>())
            .Returns(clientlist);

            var docshiftCache = Substitute.For<IEntityCacheServiceProxy<DocshifterDocumentFile>>();
            _cache.CreateEntity<DocshifterDocumentFile>().Returns(docshiftCache);
            docshiftCache.WhereAsync(Arg.Any<Expression<Func<DocshifterDocumentFile, bool>>>()).ReturnsForAnyArgs(docshifterDocuments);

        }
        #endregion
        #region private method
        private void UpdateSubmissionPublishersData(EditSubmissionViewModel model, int LifecycleStateId, string regulatorylead, string publishinglead)
        {
            Submission submission = new Submission()
            {
                Id = 1,
                UniqueId = "112",
                Project = new Project() { Id = 1, Name = "proj1", Client = new Client { Id = 9 } },
                ProjectId = 1,
                LifecycleStateId = LifecycleStateId,
                DossierFormatId = 2,
                Application = new Application() { ApplicationNumber = "1" }
            };
            SubmissionResource submissionresource = new SubmissionResource()
            {
                RegulatoryLead = regulatorylead,
                Submission = new Submission()
                {
                    Id = 1,
                    UniqueId = "112",
                    Project = new Project() { Id = 1, Name = "proj1" },
                    ProjectId = 1,
                    LifecycleStateId = 1,
                    DossierFormatId = 2
                },
                Id = 1,
                PublishingLead = publishinglead,
                SubmissionPublisher = new List<SubmissionPublisher>()
                {
                    new SubmissionPublisher{ Publisher=new User(){ Email="<EMAIL>"} }
                }
            };
            List<SubmissionPublisher> submissionPublishers = new List<SubmissionPublisher>()
            {
                new SubmissionPublisher
                {
                    Id=1,
                    SubmissionResourceId=1,
                    SubmissionResource=new SubmissionResource()
                    {
                        Id=1,InitialSentToEmail="<EMAIL>"
                    },
                    Publisher=new User()
                    {
                        Email="<EMAIL>",
                        GivenName="Test",
                        FamilyName="Test",
                    }
                }
            };

            IEnumerable<UserFindResultModel> userFindResultModel = new List<UserFindResultModel>()
            {
                new UserFindResultModel{ Email="<EMAIL>",GivenName="Test",Name="Test",FamilyName="Test",Id=1}
            };
            var picklistdatamodel = new List<PicklistDataModel>
            {
                new PicklistDataModel {Id = 1,Name = "Dosage Form1" },
                new PicklistDataModel {Id = 2,Name = "Dosage Form2" },
            };
            var submissioresourcenmodel = new SubmissionResourceModel()
            {
                Id = 1,
                Comments = "Test",
                SubmissionId = 1
            };
            UserModel usermodel = new UserModel() { Id = 1, DisplayFullName = "abc", Email = "<EMAIL>" };
            var submissionCache = Substitute.For<ITrackedEntityCacheServiceProxy<Submission>>();
            _cache.CreateTrackedEntity<Submission>().Configure(Arg.Any<Func<IIncludable<Submission>, IIncludable<Submission>>>()).Returns(submissionCache);
            submissionCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submission);
            _mapper.Map(model.Submission, submission).Returns(submission);

            var subResourceCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionResource>>();
            _cache.CreateTrackedEntity<SubmissionResource>()
                .Configure(Arg.Any<Func<IIncludable<SubmissionResource>, IIncludable<SubmissionResource>>>()).Returns(subResourceCache);
            subResourceCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<SubmissionResource, bool>>>()).Returns(submissionresource);
            _mapper.Map(model.SubmissionResource, subResourceCache).Returns(subResourceCache);

            var _subPublisherCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionPublisher>>();
            _cache.CreateTrackedEntity<SubmissionPublisher>().Returns(_subPublisherCache);
            _subPublisherCache.Where(Arg.Any<Expression<Func<SubmissionPublisher, bool>>>()).Returns(submissionPublishers);

            var _missingPublishers = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionPublisher>>();
            _cache.CreateTrackedEntity<SubmissionPublisher>().Returns(_missingPublishers);
            _missingPublishers.WhereAsync(Arg.Any<Expression<Func<SubmissionPublisher, bool>>>()).Returns(submissionPublishers);


            var _users = Substitute.For<ITrackedEntityCacheServiceProxy<User>>();
            _cache.CreateTrackedEntity<User>().Returns(_users);
            _users.FirstOrDefaultAsync(Arg.Any<Expression<Func<User, bool>>>()).ReturnsNull();

            User user = new User() { Email = "<EMAIL>", Id = 1, GivenName = "Test" };
            _mapper.Map<User>(Arg.Any<UserFindResultModel>()).Returns(user);


            var _picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);

            for (int i = 0; i < picklistdatamodel.Count; i++)
            {
                _picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(picklistdatamodel[i]);
            }
        }
        private void UpdateSubmissionCountriesData(EditSubmissionViewModel model, int LifecycleStateId)
        {
            Submission submission = new Submission()
            {
                Id = 1,
                UniqueId = "112",
                Project = new Project() { Id = 1, Name = "proj1", Client = new Client { Id = 9 } },
                ProjectId = 1,
                LifecycleStateId = LifecycleStateId,
                DossierFormatId = 2,
                Application = new Application() { ApplicationNumber = "1" }
            };
            var submissionCountry = new List<SubmissionCountry>()
            {
                new SubmissionCountry{ Id = 1, Country = new Country() { Id = 1, Name = "Test" }, SubmissionId=1 }

            };
            var _subCountryCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionCountry>>();
            _cache.CreateTrackedEntity<SubmissionCountry>().Returns(_subCountryCache);
            _subCountryCache.Where(Arg.Any<Expression<Func<SubmissionCountry, bool>>>()).Returns(submissionCountry);

            var _missingCountries = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionCountry>>();
            _cache.CreateTrackedEntity<SubmissionCountry>().Returns(_missingCountries);
            _missingCountries.WhereAsync(Arg.Any<Expression<Func<SubmissionCountry, bool>>>()).Returns(submissionCountry);

        }
        private void NotifyUserData(EditSubmissionViewModel model,List<int> clientsId, string regulatorylead, string publishinglead,int LifecycleStateId)
        {
            Submission submission = new Submission()
            {
                Id = 1,
                UniqueId = "112",
                Project = new Project() { Id = 1, Name = "proj1", Client = new Client { Id = 9 } },
                ProjectId = 1,
                LifecycleStateId = LifecycleStateId,
                DossierFormatId = 2,
                Application = new Application() { ApplicationNumber = "1" }
            };
            var submissioresourcenmodel = new SubmissionResourceModel()
            {
                Id = 1,
                Comments = "Test",
                SubmissionId = 1
            };
            SubmissionResource submissionresource = new SubmissionResource()
            {
                RegulatoryLead = regulatorylead,
                Submission = new Submission()
                {
                    Id = 1,
                    UniqueId = "112",
                    Project = new Project() { Id = 1, Name = "proj1" },
                    ProjectId = 1,
                    LifecycleStateId = 1,
                    DossierFormatId = 2
                },
                Id = 1,
                PublishingLead = publishinglead,
                SubmissionPublisher = new List<SubmissionPublisher>()
                {
                    new SubmissionPublisher{ Publisher=new User(){ Email="<EMAIL>"} }
                }
            };
            List<SubmissionPublisher> submissionPublishers = new List<SubmissionPublisher>()
            {
                new SubmissionPublisher
                {
                    Id=1,
                    SubmissionResourceId=1,
                    SubmissionResource=new SubmissionResource()
                    {
                        Id=1,InitialSentToEmail="<EMAIL>"
                    },
                    Publisher=new User()
                    {
                        Email="<EMAIL>",
                        GivenName="Test",
                        FamilyName="Test",
                    }
                }
            };
            var submissionCache = Substitute.For<ITrackedEntityCacheServiceProxy<Submission>>();
            _cache.CreateTrackedEntity<Submission>().Configure(Arg.Any<Func<IIncludable<Submission>, IIncludable<Submission>>>()).Returns(submissionCache);
            submissionCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submission);
            _mapper.Map(model.Submission, submission).Returns(submission);

            UserModel usermodel = new UserModel() { Id = 1, DisplayFullName = "abc", Email = "<EMAIL>" };
            var userdCache = Substitute.For<IMappedEntityCacheServiceProxy<User, UserModel>>();
            _cache.CreateMappedEntity<User, UserModel>().Returns(userdCache);
            userdCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<User, bool>>>()).Returns(usermodel);

            var _submissionResourcecache = Substitute.For<IMappedEntityCacheServiceProxy<SubmissionResource, SubmissionResourceModel>>();
            _cache.CreateMappedEntity<SubmissionResource, SubmissionResourceModel>().Returns(_submissionResourcecache);
            _submissionResourcecache.FirstOrDefaultAsync(Arg.Any<Expression<Func<SubmissionResource, bool>>>()).Returns(submissioresourcenmodel);

            var subResourceCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionResource>>();
            _cache.CreateTrackedEntity<SubmissionResource>()
                .Configure(Arg.Any<Func<IIncludable<SubmissionResource>, IIncludable<SubmissionResource>>>()).Returns(subResourceCache);
            subResourceCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<SubmissionResource, bool>>>()).Returns(submissionresource);

            var _PublishersCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionPublisher>>();
            _cache.CreateEntity<SubmissionPublisher>().Configure(Arg.Any<Func<IIncludable<SubmissionPublisher>, IIncludable<SubmissionPublisher>>>()).Returns(_PublishersCache);
            _PublishersCache.WhereAsync(Arg.Any<Expression<Func<SubmissionPublisher, bool>>>()).Returns(submissionPublishers);


        }
        #endregion private method
    }
}