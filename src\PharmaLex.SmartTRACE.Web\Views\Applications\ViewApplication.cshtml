﻿@model ApplicationViewModel
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@using PharmaLex.Caching.Data
@using PharmaLex.SmartTRACE.Entities
@using PharmaLex.SmartTRACE.Entities.Enums
@using Microsoft.AspNetCore.Authorization
@inject IDistributedCacheServiceFactory cache
@inject AutoMapper.IMapper mapper
@inject IAuthorizationService AuthorizationService
@{
    var dossierFormats = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                        .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.DossierFormat);
    var submissionTypes = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                          .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.SubmissionType);
    var submissionUnits = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                         .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.SubmissionUnit);
    var submissionModes = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                         .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.SubmissionMode);
    var procedureTypes = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                         .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.ProcedureType);
    var lifecycleStates = mapper.Map<IEnumerable<SubmissionLifecycleStateList>>(Enum.GetValues(typeof(SubmissionLifeCycleState)));
    var euRegion = await cache.CreateMappedEntity<Region, RegionModel>().FirstOrDefaultAsync(x => x.Name == "European Union");
    var euCountriesIds = (await cache.CreateMappedEntity<Country, CountryModel>().WhereAsync(x => x.RegionId == euRegion.Id)).Select(x => x.Id);
    var usCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "United States of America");
    var ukCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "United Kingdom");
    var swissCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "Switzerland");
    var auCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "Australia");
    var isExternalEditorUser = (await AuthorizationService.AuthorizeAsync(User, "ExternalEditor")).Succeeded;
}

<div id="application" class="manage-container" v-cloak>
    <header class="manage-header">
        <h3>@Model.ApplicationNumber Application</h3>
        <div class="common-state-container" :stateId="application.lifecycleStateId">
            <span class="common-state">{{ application.lifecycleState }}</span>
        </div>

        <a href="/applications" class="button secondary icon-button-list">View All Applications</a>
        @if ((await AuthorizationService.AuthorizeAsync(User, "BusinessAdmin")).Succeeded)
        {
            <a href="/applications/edit/@Model.Id" class="button icon-button-edit" v-if="application.lifecycleStateId !== obsoleteStateId">Edit</a>
        }
        @if (Model.Id != 0 && (await AuthorizationService.AuthorizeAsync(User, "Editor")).Succeeded)
        {
            <a href="/submission-request/new/@Model.Id" class="button icon-button-add" v-if="!appInObsoleteOrWithdrawnState()">Create Submission Record</a>
        }
    </header>
    <form method="post">
        <div class="form-col form-col-half">
            <h5>Application Details</h5>
            <div class="application-view">
                <span>Number:</span>
                <span>{{ application.applicationNumber }}</span>
                <span>Application Type:</span>
                <span>{{ application.applicationType }}</span>
                <span>Medicinal Product Domain:</span>
                <span>{{ application.medicinalProductDomain }}</span>
                <span>Medicinal Product Type:</span>
                <span>{{ application.medicinalProductType }}</span>
                @if (!isExternalEditorUser)
                {
                    <span>Client:</span>
                    <span>{{ application.clientName }}</span>
                }
                <span>Products:</span>
                <span>{{ application.product }}</span>
                <span>Active Substances:</span>
                <span v-if="application.activeSubstances">{{ application.activeSubstances }}</span>
                <span v-if="!application.activeSubstances">-</span>
            </div>
        </div>
        <div class="form-col form-col-half">
            <h5><span>-</span></h5>
            <div class="application-view">
                <span>Procedure type:</span>
                <span>{{ application.procedureType }}</span>
                <span v-if="showCountryData()">Country:</span>
                <span v-if="showCountriesData()">Countries:</span>
                <span v-if="!showMemberStateData()">{{ application.country }}</span>
                <span v-if="showMemberStateData()">Reference Member State:</span>
                <span v-if="showMemberStateData()">{{ application.referenceMemberState }}</span>
                <span v-if="showMemberStateData()">Concerned Member States:</span>
                <span v-if="showMemberStateData() && application.concernedMemberState">{{ application.concernedMemberState }}</span>
                <span v-if="showMemberStateData() && !application.concernedMemberState">-</span>
                <span>Comments:</span>
                <span v-if="application.comments">{{ application.comments }}</span>
                <span v-if="!application.comments">-</span>
            </div>
        </div>
    </form>

    @if (Model.Id != 0)
    {
        <br />
        <div class="flex-row-justified">
            <h3>Submissions</h3>
            <a :href="'/submissions/dossiers?clientId=' + application.clientId + '&applicationId=' + application.id + '&documentTypeId=3&version=1'" v-if="showViewSubmissionButton()" class="button icon-button-view">View</a>
        </div>
        <filtered-table :items="submissions" :columns="columns" :filters="filters" :link="link"></filtered-table>
    }
</div>

@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#application',
            data() {
                return {
                    application: @Html.Raw(JsonConvert.SerializeObject(Model, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })),
                    link: '/submissions/view/',
                    submissions: @Html.Raw(Model.Submissions.ToJson()),
                    isExternalUser: @Html.Raw(Json.Serialize(isExternalEditorUser)),
                    columns: {
                        idKey: 'id',
                        styleKey: 'lifecycleState',
                        config: [
                            {
                                dataKey: 'sequenceNumber',
                                sortKey: 'sequenceNumber',
                                header: 'Sequence Number',
                                type: 'text'
                            },
                            {
                                dataKey: 'dossierFormat',
                                sortKey: 'dossierFormat',
                                header: 'Dossier Format',
                                type: 'text'
                            },
                            {
                                dataKey: 'submissionType',
                                sortKey: 'submissionType',
                                header: 'Submission Type',
                                type: 'text'
                            },
                            {
                                dataKey: 'submissionUnit',
                                sortKey: 'submissionUnit',
                                header: 'Submission Unit',
                                type: 'text'
                            },
                            {
                                dataKey: 'submissionMode',
                                sortKey: 'submissionMode',
                                header: 'Submission Mode',
                                type: 'text'
                            },
                            {
                                dataKey: 'description',
                                sortKey: 'description',
                                header: 'Submission Description',
                                type: 'text'
                            },
                            {
                                dataKey: 'lifecycleState',
                                sortKey: 'lifecycleState',
                                header: 'Lifecycle State',
                                type: 'text'
                            },
                            {
                                dataKey: 'displayRegulatoryLead',
                                sortKey: 'displayRegulatoryLead',
                                header: 'Regulatory Lead',
                                type: 'text'
                            },
                            {
                                dataKey: 'displayPublishingLead',
                                sortKey: 'displayPublishingLead',
                                header: 'Publishing Lead',
                                type: 'text'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'sequenceNumber',
                            options: [],
                            type: 'search',
                            header: 'Search Sequence Number',
                            fn: v => p => (p.sequenceNumber || '').toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'dossierFormat',
                            options: @Html.Raw(dossierFormats.ToJson()),
                            filterCollection: 'dossierFormat',
                            display: 'id',
                            type: 'select',
                            header: 'Filter By Dossier Format',
                            fn: v => p => p.dossierFormat === (v === '-' ? null : v),
                            convert: v => v
                        },
                        {
                            key: 'submissionType',
                            options: [],
                            type: 'search',
                            header: 'Search Submission Type',
                            fn: v => p => (p.submissionType || '').toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'submissionUnit',
                            options: @Html.Raw(submissionUnits.ToJson()),
                            filterCollection: 'submissionUnit',
                            display: 'id',
                            type: 'select',
                            header: 'Filter By Unit',
                            fn: v => p => p.submissionUnit === (v === '-' ? null : v),
                            convert: v => v
                        },
                        {
                            key: 'submissionMode',
                            options: @Html.Raw(submissionModes.ToJson()),
                            filterCollection: 'submissionMode',
                            display: 'id',
                            type: 'select',
                            header: 'Filter By Mode',
                            fn: v => p => p.submissionMode === (v === '-' ? null : v),
                            convert: v => v
                        },
                        {
                            key: 'lifecycleState',
                            options: @Html.Raw(lifecycleStates.ToJson()),
                            filterCollection: 'lifecycleState',
                            display: 'id',
                            type: 'select',
                            header: 'Filter By State',
                            fn: v => p => p.lifecycleState === v,
                            convert: v => v
                        },
                        {
                            key: 'description',
                            options: [],
                            type: 'search',
                            header: 'Search Description',
                            fn: v => p => (p.description || '').toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'displayRegulatoryLead',
                            options: [],
                            type: 'search',
                            header: 'Search Regulatory Lead',
                            fn: v => p => (p.displayRegulatoryLead || '').toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'displayPublishingLead',
                            options: [],
                            type: 'search',
                            header: 'Search Publishing Lead',
                            fn: v => p => (p.displayPublishingLead || '').toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ],
                    procedureTypes: @Html.Raw(Json.Serialize(procedureTypes)),
                    countryProcedures: ['National Procedure', 'GCC National Procedure'],
                    countriesProcedures: ['Centralised Procedure', 'GCC Community Procedure'],
                    memberStateProcedures: ['Decentralised Procedure', 'Mutual Recognition Procedure', 'ASMF Worksharing Procedure'],
                    dossierDocumentTypeId: @Html.Raw((int)DocumentType.Dossier),
                    euCountriesIds: @Html.Raw(Json.Serialize(euCountriesIds)),
                    usCountry: @Html.Raw(Json.Serialize(usCountry)),
                    nonEuUkCountry: @Html.Raw(Json.Serialize(ukCountry)),
                    swissCountry: @Html.Raw(Json.Serialize(swissCountry)),
                    auCountry: @Html.Raw(Json.Serialize(auCountry)),
                    obsoleteStateId: @Html.Raw((int)CommonLifecycleState.Obsolete),
                    withdrawnStateId: @Html.Raw((int)CommonLifecycleState.Withdrawn),
                };
            },
            methods: {
                showCountryData() {
                    return this.countryProcedures.includes(this.getSelectedProcedureName());
                },
                showCountriesData() {
                    if (this.getSelectedProcedureName()) {
                        if (this.countriesProcedures.includes(this.getSelectedProcedureName())) {
                            return true;
                        }

                        if (!this.showCountryData() && !this.showMemberStateData()) {
                            return true;
                        }

                        return false;
                    }

                    return this.countriesProcedures.includes(this.getSelectedProcedureName());
                },
                showMemberStateData() {
                    return this.memberStateProcedures.includes(this.getSelectedProcedureName());
                },
                showViewSubmissionButton() {
                    let submissionDossierUploaded = this.submissions
                        .map(x => x.documents)
                        .reduce(function (a, b) { return a.concat(b); }, [])
                        .some(x => x.documentTypeId === this.dossierDocumentTypeId);

                    let containsSupportedCountries =
                        this.submissions.map(x => x.countriesIds).reduce(function (a, b) { return a.concat(b); }, []).some(x => this.euCountriesIds.includes(x)) ||
                        this.submissions.map(x => x.countriesIds).reduce(function (a, b) { return a.concat(b); }, []).includes(this.usCountry.id) ||
                        this.submissions.map(x => x.countriesIds).reduce(function (a, b) { return a.concat(b); }, []).includes(this.nonEuUkCountry.id) ||
                        this.submissions.map(x => x.countriesIds).reduce(function (a, b) { return a.concat(b); }, []).includes(this.swissCountry.id) ||
                        this.submissions.map(x => x.countriesIds).reduce(function (a, b) { return a.concat(b); }, []).includes(this.auCountry.id);

                    let isECTDFormat = this.submissions.map(x => x.dossierFormat).reduce(function (a, b) { return a.concat(b); }, []).some(x => x === 'eCTD');

                    return submissionDossierUploaded && containsSupportedCountries && isECTDFormat;
                },
                getSelectedProcedureName() {
                    var selectedProcedure = this.procedureTypes.find(x => x.id === this.application.procedureTypeId)
                    if (selectedProcedure) {
                        return selectedProcedure.name;
                    }
                },
                appInObsoleteOrWithdrawnState() {
                    return this.application.lifecycleStateId === this.obsoleteStateId || this.application.lifecycleStateId === this.withdrawnStateId;
                },
            },
            created() {
                if (this.isExternalUser) {
                    this.columns.config = this.columns.config.filter(c => c.dataKey != 'displayRegulatoryLead');
                    this.columns.config = this.columns.config.filter(c => c.dataKey != 'displayPublishingLead');
                    this.filters = this.filters.filter(c => c.key != 'displayRegulatoryLead');
                    this.filters = this.filters.filter(c => c.key != 'displayPublishingLead');
                }
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}