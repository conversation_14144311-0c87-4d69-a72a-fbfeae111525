﻿using System;
using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.AU
{
    [Serializable]
    [XmlRoot("tga_ectd", Namespace = "tga_ectd", IsNullable = false)]
    public partial class tga_ectd
    {
        [XmlElement("au-envelope")]
        public auenvelope auenvelope { get; set; }

        [XmlElement("m1-au")]
        public m1aucontent m1au { get; set; }

        [XmlAttribute("schema-version")]
        public tga_ectdSchemaversion schemaversion { get; set; }
    }
    
    [Serializable]
    [XmlRoot("au-envelope", IsNullable = false)]
    public partial class auenvelope
    {
        [XmlElement("esub-id")]
        public string esubid { get; set; }

        [XmlElement("client-id")]
        public string clientid { get; set; }

        [XmlElement("aan")]
        public string[] aan { get; set; }

        [XmlElement("product-name")]
        public string[] productname { get; set; }

        [XmlElement("artg-number")]
        public string[] artgnumber { get; set; }

        [XmlElement("submission-number")]
        public string[] submissionnumber { get; set; }

        [XmlElement("sequence-number", DataType = "integer")]
        public string sequencenumber { get; set; }

        [XmlElement("related-sequence-number", DataType = "integer")]
        public string relatedsequencenumber { get; set; }

        [XmlElement("reg-activity-lead")]
        public auannotatedelementtype regactivitylead { get; set; }

        [XmlElement("sequence-type")]
        public ausequencetype[] sequencetype { get; set; }

        [XmlElement("submission-mode")]
        public ausubmissionmode submissionmode { get; set; }

        [XmlElement("work-sharing")]
        public auworksharing[] worksharing { get; set; }

        [XmlElement("email")]
        public string[] email { get; set; }
    }
    
    [Serializable]
    [XmlRoot("au-annotated-element-type", IsNullable = false)]
    public partial class auannotatedelementtype
    {
        [XmlElement("data")]
        public auannotatedelementtypeData[] data { get; set; }

        [XmlAttribute("code-version")]
        public string codeversion { get; set; }

        [XmlAttribute()]
        public string code { get; set; }
    }
    
    [Serializable]
    [XmlRoot(IsNullable = false)]
    public partial class auannotatedelementtypeData
    {
        [XmlAttribute()]
        public string use { get; set; }

        [XmlText()]
        public string Value { get; set; }
    }
    
    [Serializable]
    [XmlRoot("au-node-extension", IsNullable = false)]
    public partial class aunodeextension
    {
        public aunodeextensionTitle title { get; set; }

        [XmlElement("leaf")]
        public auleaf[] leaf { get; set; }

        [XmlElement("node-extension")]
        public aunodeextension nodeextension { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public string lang { get; set; }
    }
    
    [Serializable]
    [XmlRoot(IsNullable = false)]
    public partial class aunodeextensionTitle
    {
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlText()]
        public string Value { get; set; }
    }
    
    [Serializable]
    [XmlRoot("au-leaf", IsNullable = false)]
    public partial class auleaf
    {
        public auleaf()
        {
            this.type = typeType.simple;
        }
        public auleafTitle title { get; set; }

        [XmlElement("link-text")]
        public auleafLinktext linktext { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute()]
        public ichleafoperations operation { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public string href { get; set; }

        [XmlAttribute("modified-file")]
        public string modifiedfile { get; set; }

        [XmlAttribute("checksum-type")]
        public ichchecksumtype checksumtype { get; set; }

        [XmlIgnore()]
        public bool checksumtypeSpecified { get; set; }

        [XmlAttribute()]
        public string checksum { get; set; }

        [XmlAttribute("application-version")]
        public string applicationversion { get; set; }

        [XmlAttribute()]
        public string version { get; set; }

        [XmlAttribute()]
        public string keywords { get; set; }

        [XmlAttribute("font-library")]
        public string fontlibrary { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public typeType type { get; set; }

        [XmlIgnore()]
        public bool typeSpecified { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public string role { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public showType show { get; set; }

        [XmlIgnore()]
        public bool showSpecified { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public actuateType actuate { get; set; }

        [XmlIgnore()]
        public bool actuateSpecified { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public string lang { get; set; }
    }
    
    [Serializable]
    [XmlRoot(IsNullable = false)]
    public partial class auleafTitle
    {
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlText()]
        public string Value { get; set; }
    }
    
    [Serializable]
    [XmlRoot(IsNullable = false)]
    public partial class auleafLinktext
    {
        public auleafLinktextXref xref { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
    }
    
    [Serializable]
    [XmlRoot(IsNullable = false)]
    public partial class auleafLinktextXref
    {
        public auleafLinktextXref()
        {
            this.type = typeType.simple;
        }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public typeType type { get; set; }

        [XmlIgnore()]
        public bool typeSpecified { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public string role { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public string title { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public string href { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public showType show { get; set; }

        [XmlIgnore()]
        public bool showSpecified { get; set; }
    }
    
    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3.org/1999/xlink")]
    public enum typeType
    {
        simple,
        extended,
        title,
        resource,
        locator,
        arc,
    }
    
    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3.org/1999/xlink")]
    public enum showType
    {
        @new,
        replace,
        embed,
        other,
        none,
    }
    
    [Serializable]
    [XmlType("ich-leaf-operations", Namespace = "http://www.w3.org/1999/xlink")]
    public enum ichleafoperations
    {
        @new,
        append,
        replace,
        delete,
    }
    
    [Serializable]
    [XmlType("ich-checksum-type", Namespace = "http://www.w3.org/1999/xlink")]
    public enum ichchecksumtype
    {
        MD5,
        md5,
    }
    
    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3.org/1999/xlink")]
    public enum actuateType
    {
        onLoad,
        onRequest,
        other,
        none,
    }
    
    [Serializable]
    [XmlRoot("m1-au", IsNullable = false)]
    public partial class m1aucontent
    {
        [XmlElement("m1-0-correspondence")]
        public m1aucontentM10correspondence m10correspondence { get; set; }

        [XmlElement("m1-2-admin-info")]
        public m1aucontentM12admininfo m12admininfo { get; set; }

        [XmlElement("m1-3-med-info")]
        public m1aucontentM13medinfo m13medinfo { get; set; }

        [XmlElement("m1-4-experts")]
        public m1aucontentM14experts m14experts { get; set; }

        [XmlElement("m1-5-specific")]
        public m1aucontentM15specific m15specific { get; set; }

        [XmlElement("m1-6-master-files")]
        public m1aucontentM16masterfiles m16masterfiles { get; set; }

        [XmlElement("m1-7-compliance")]
        public m1aucontentM17compliance m17compliance { get; set; }

        [XmlElement("m1-8-pv")]
        public m1aucontentM18pv m18pv { get; set; }

        [XmlElement("m1-9-biopharm")]
        public m1aucontentM19biopharm m19biopharm { get; set; }

        [XmlElement("c")]
        public m110paediatrics m110paediatrics { get; set; }

        [XmlElement("m1-11-foreign")]
        public m1aucontentM111foreign m111foreign { get; set; }

        [XmlElement("m1-12-antibiotic")]
        public m112antibiotic m112antibiotic { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-10-paediatrics")]
    public partial class m110paediatrics
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-antibiotic")]
    public partial class m112antibiotic
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-0-correspondence", IsNullable = false)]
    public partial class m1aucontentM10correspondence
    {
        [XmlElement("m1-0-1-cover")]
        public m101cover m101cover { get; set; }

        [XmlElement("m1-0-2-tracking-table")]
        public m102trackingtable m102trackingtable { get; set; }

        [XmlElement("m1-0-3-response")]
        public m103response m103response { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-0-1-cover", IsNullable = false)]
    public partial class m101cover
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-0-2-tracking-table", IsNullable = false)]
    public partial class m102trackingtable
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-0-3-response", IsNullable = false)]
    public partial class m103response
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }


    [Serializable]
    [XmlRoot("m1-2-admin-info", IsNullable = false)]
    public partial class m1aucontentM12admininfo
    {
        [XmlElement("m1-2-1-app-form")]
        public m121appform m121appform { get; set; }

        [XmlElement("m1-2-2-pre-sub-details")]
        public m122presubdetails m122presubdetails { get; set; }

        [XmlElement("m1-2-3-pat-cert")]
        public m123patcert m123patcert { get; set; }

        [XmlElement("m1-2-4-change-sponsor")]
        public m124changesponsor m124changesponsor { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-1-app-form", IsNullable = false)]
    public partial class m121appform
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-pre-sub-details", IsNullable = false)]
    public partial class m122presubdetails
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-3-pat-cert", IsNullable = false)]
    public partial class m123patcert
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-4-change-sponsor", IsNullable = false)]
    public partial class m124changesponsor
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-med-info", IsNullable = false)]
    public partial class m1aucontentM13medinfo
    {
        [XmlElement("m1-3-1-pi")]
        public m1aucontentM13medinfoM131pi m131pi { get; set; }

        [XmlElement("m1-3-2-cmi")]
        public m1aucontentM13medinfoM132cmi m132cmi { get; set; }

        [XmlElement("m1-3-3-mockup")]
        public m1aucontentM13medinfoM133mockup m133mockup { get; set; }
    }
    
    [Serializable]
    [XmlRoot("m1-3-1-pi", IsNullable = false)]
    public partial class m1aucontentM13medinfoM131pi
    {
        [XmlElement("m1-3-1-1-pi-clean")]
        public m1311piclean m1311piclean { get; set; }

        [XmlElement("m1-3-1-2-pi-annotated")]
        public m1312piannotated m1312piannotated { get; set; }

        [XmlElement("m1-3-1-3-pi-approved")]
        public m1313piapproved m1313piapproved { get; set; }

        [XmlElement("m1-3-1-3-pack-ins")]
        public m1313packins m1313packins { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-1-pi-clean", IsNullable = false)]
    public partial class m1311piclean
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-2-pi-annotated", IsNullable = false)]
    public partial class m1312piannotated
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-3-pi-approved", IsNullable = false)]
    public partial class m1313piapproved
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }
    [Serializable]
    [XmlRoot("m1-3-1-3-pack-ins", IsNullable = false)]
    public partial class m1313packins
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-2-cmi", IsNullable = false)]
    public partial class m1aucontentM13medinfoM132cmi
    {
        [XmlElement("m1-3-2-1-cmi-clean")]
        public m1321cmiclean m1321cmiclean { get; set; }

        [XmlElement("m1-3-2-2-cmi-annotated")]
        public m1322cmiannotated m1322cmiannotated { get; set; }

        [XmlElement("m1-3-2-3-cmi-approved")]
        public m1323cmiapproved m1323cmiapproved { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-2-1-cmi-clean", IsNullable = false)]
    public partial class m1321cmiclean
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-2-2-cmi-annotated", IsNullable = false)]
    public partial class m1322cmiannotated
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-2-3-cmi-approved", IsNullable = false)]
    public partial class m1323cmiapproved
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-3-mockup", IsNullable = false)]
    public partial class m1aucontentM13medinfoM133mockup
    {
        [XmlElement("m1-3-3-1-mockup-clean")]
        public m1331mockupclean m1331mockupclean { get; set; }

        [XmlElement("m1-3-3-2-mockup-annotated")]
        public m1332mockupannotated m1332mockupannotated { get; set; }

        [XmlElement("m1-3-3-3-mockup-approved")]
        public m1333mockupapproved m1333mockupapproved { get; set; }

        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-3-1-mockup-clean", IsNullable = false)]
    public partial class m1331mockupclean
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-3-2-mockup-annotated", IsNullable = false)]
    public partial class m1332mockupannotated
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-3-3-mockup-approved", IsNullable = false)]
    public partial class m1333mockupapproved
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-experts", IsNullable = false)]
    public partial class m1aucontentM14experts
    {
        [XmlElement("m1-4-1-quality")]
        public m141quality m141quality { get; set; }

        [XmlElement("m1-4-2-nonclinical")]
        public m142nonclinical m142nonclinical { get; set; }

        [XmlElement("m1-4-3-clinical")]
        public m143clinical m143clinical { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-1-quality", IsNullable = false)]
    public partial class m141quality
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-2-nonclinical", IsNullable = false)]
    public partial class m142nonclinical
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-3-clinical", IsNullable = false)]
    public partial class m143clinical
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-specific", IsNullable = false)]
    public partial class m1aucontentM15specific
    {
        [XmlElement("m1-5-1-lit-based")]
        public m151litbased m151litbased { get; set; }

        [XmlElement("m1-5-2-orphan")]
        public m152orphan m152orphan { get; set; }

        [XmlElement("m1-5-3-gmo")]
        public m153gmo m153gmo { get; set; }

        [XmlElement("m1-5-4-trade-name")]
        public m154tradename m154tradename { get; set; }

        [XmlElement("m1-5-5-co-marketed")]
        public m155comarketed m155comarketed { get; set; }

        [XmlElement("m1-5-6-comb-med")]
        public m156combmed m156combmed { get; set; }

        [XmlElement("m1-5-7-prod-assurance")]
        public m157prodassurance m157prodassurance { get; set; }

        [XmlElement("m1-5-8-umbrella")]
        public m158umbrella m158umbrella { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-1-lit-based", IsNullable = false)]
    public partial class m151litbased
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-2-orphan", IsNullable = false)]
    public partial class m152orphan
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-3-gmo", IsNullable = false)]
    public partial class m153gmo
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-4-trade-name", IsNullable = false)]
    public partial class m154tradename
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-5-co-marketed", IsNullable = false)]
    public partial class m155comarketed
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-6-comb-med", IsNullable = false)]
    public partial class m156combmed
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-7-prod-assurance", IsNullable = false)]
    public partial class m157prodassurance
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-8-umbrella", IsNullable = false)]
    public partial class m158umbrella
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-master-files", IsNullable = false)]
    public partial class m1aucontentM16masterfiles
    {
        [XmlElement("m1-6-1-ext-sources")]
        public m161extsources m161extsources { get; set; }

        [XmlElement("m1-6-2-app-decl")]
        public m162appdecl m162appdecl { get; set; }

        [XmlElement("m1-6-3-loa")]
        public m163loa m163loa { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-1-ext-sources", IsNullable = false)]
    public partial class m161extsources
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-2-app-decl", IsNullable = false)]
    public partial class m162appdecl
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-3-loa", IsNullable = false)]
    public partial class m163loa
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-compliance", IsNullable = false)]
    public partial class m1aucontentM17compliance
    {
        [XmlElement("m1-7-1-pre-sub")]
        public m171presub m171presub { get; set; }

        [XmlElement("m1-7-2-add-data")]
        public m172adddata m172adddata { get; set; }

        [XmlElement("m1-7-3-planning")]
        public m173planning m173planning { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-1-pre-sub", IsNullable = false)]
    public partial class m171presub
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-2-add-data", IsNullable = false)]
    public partial class m172adddata
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-3-planning", IsNullable = false)]
    public partial class m173planning
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-pv", IsNullable = false)]
    public partial class m1aucontentM18pv
    {
        [XmlElement("m1-8-1-pv-systems")]
        public m181pvsystems m181pvsystems { get; set; }

        [XmlElement("m1-8-2-risk")]
        public m182risk m182risk { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-1-pv-systems", IsNullable = false)]
    public partial class m181pvsystems
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-2-risk", IsNullable = false)]
    public partial class m182risk
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-biopharm", IsNullable = false)]
    public partial class m1aucontentM19biopharm
    {
        [XmlElement("m1-9-1-ba-be")]
        public m191babe m191babe { get; set; }

        [XmlElement("m1-9-2-justification")]
        public m192justification m192justification { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-1-ba-be", IsNullable = false)]
    public partial class m191babe
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-2-justification", IsNullable = false)]
    public partial class m192justification
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-foreign", IsNullable = false)]
    public partial class m1aucontentM111foreign
    {
        [XmlElement("m1-11-1-status")]
        public m1111status m1111status { get; set; }

        [XmlElement("m1-11-2-pi")]
        public m1112pi m1112pi { get; set; }

        [XmlElement("m1-11-3-similarities")]
        public m1113similarities m1113similarities { get; set; }

        [XmlElement("m1-11-4-eval-reports")]
        public m1114evalreports m1114evalreports { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-1-status", IsNullable = false)]
    public partial class m1111status
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-2-pi", IsNullable = false)]
    public partial class m1112pi
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-3-similarities", IsNullable = false)]
    public partial class m1113similarities
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-4-eval-reportsr", IsNullable = false)]
    public partial class m1114evalreports
    {
        [XmlElement("leaf", typeof(auleaf))]
        [XmlElement("node-extension", typeof(aunodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("au-work-sharing",  IsNullable = false)]
    public partial class auworksharing
    {
        [XmlElement("esub-id")]
        public string esubid { get; set; }

        [XmlElement("submission-number")]
        public string[] submissionnumber { get; set; }

        [XmlElement("sequence-number", DataType = "integer")]
        public string sequencenumber { get; set; }

    }
    
    [Serializable]
    [XmlRoot("au-sequence-type", IsNullable = false)]
    public partial class ausequencetype
    {
        [XmlElement("data")]
        public ausequencetypeData[] data { get; set; }

        [XmlElement("sequence-description")]
        public auannotatedelementtype sequencedescription { get; set; }

        [XmlAttribute("code-version")]
        public string codeversion { get; set; }

        [XmlAttribute()]
        public string code { get; set; }
    }
    
    [Serializable]
    [XmlRoot("data", IsNullable = false)]
    public partial class ausequencetypeData
    {
        [XmlAttribute()]
        public string use { get; set; }

        [XmlText()]
        public string Value { get; set; }

    }
    
    [Serializable]
    [XmlType("au-submission-mode", Namespace = "http://www.w3.org/1999/xlink")]
    public enum ausubmissionmode
    {
        single,
        [XmlEnum("work-grouping")]
        workgrouping,
        [XmlEnum("work-sharing")]
        worksharing,
    }
    
    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3.org/1999/xlink")]
    public enum tga_ectdSchemaversion
    {
        [XmlEnum("3.1")]
        Item31,
        [XmlEnum("3.0")]
        Item30,
    }
}
