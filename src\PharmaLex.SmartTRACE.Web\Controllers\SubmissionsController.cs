﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Web.CTD;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models;
using PharmaLex.SmartTRACE.Web.Storage;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    public class SubmissionsController : BaseController
    {
        private readonly IEctdViewer ectdViewer;
        private readonly ISubmissionBlobContainer submissionBlobContainer;
        private readonly ISubmissionPubBlobContainer submissionPubBlobContainer;
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IApplicationService applicationService;
        private readonly ISubmissionService submissionService;
        private readonly IConfiguration configuration;
       
        public SubmissionsController(
            ISubmissionBlobContainer submissionBlobContainer,
            ISubmissionPubBlobContainer submissionPubBlobContainer,
            IEctdViewer ectdViewer,
            IDistributedCacheServiceFactory cache,
            IApplicationService applicationService,
            ISubmissionService submissionService, IConfiguration objConfig)
        {
            this.ectdViewer = ectdViewer;
            this.submissionBlobContainer = submissionBlobContainer;
            this.submissionPubBlobContainer = submissionPubBlobContainer;
            this.cache = cache;
            this.applicationService = applicationService;
            this.submissionService = submissionService;
            this.configuration = objConfig;
        }

        [HttpDelete, Route("/submissions/clear/{filename}")]
        public async Task<IActionResult> Clear(string filename)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            await this.submissionBlobContainer.ClearSequenceAsync(filename);

            return this.Ok();
        }

        [HttpDelete, Route("/submissions/{submissionUniqueId}/clear/{filename}")]
        [AuthorizeMultiple("Editor;ExternalEditor", false)]
        public async Task<IActionResult> Clear(
            string filename,
            string submissionUniqueId,
            [FromQuery] string clientId,
            [FromQuery] string applicationId,
            [FromQuery] string documentTypeId,
            [FromQuery] string version)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            await this.submissionBlobContainer.ClearSequenceAsync(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version);

            return this.Ok();
        }

        [HttpGet, Route("/submissions/{filename}/download/{*file}")]
        public async Task<IActionResult> Download(string filename, string file)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            try
            {
                var response = await this.submissionBlobContainer.GetFileDownloadAsync(filename, file);
                return File(response.Content.ToArray(), response.Details.ContentType, Path.GetFileName(filename));
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError);
            }
        }

        [HttpGet, Route("/submissions/download/{clientId}/{applicationId}/{*file}")]
        public async Task<IActionResult> DownloadPdf(string file,
            string clientId,
            string applicationId)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            try
            {
                var response = await this.submissionBlobContainer.GetFileDownloadAsync(file, clientId, applicationId);
                return File(response.Content.ToArray(), response.Details.ContentType, Path.GetFileName(file));
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError);
            }
        }

        [HttpGet("/submissions/{submissionUniqueId}/download/{filename}")]
        [AuthorizeMultiple("Editor;ExternalEditor", false)]
        public async Task<IActionResult> Download(
            string filename,
            string submissionUniqueId,
            [FromQuery] string clientId,
            [FromQuery] string applicationId,
            [FromQuery] string documentTypeId,
            [FromQuery] string version)
        {

            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            try
            {
                var response = await this.submissionBlobContainer.GetFileDownloadAsync(filename, null, clientId, applicationId, submissionUniqueId, documentTypeId, version);
                return File(response.Content.ToArray(), response.Details.ContentType, Path.GetFileName(filename));
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError);
            }
        }

        [HttpGet, Route("/submissions/link/{filename}")]
        public async Task<IActionResult> GetUploadLink(string filename)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            if (string.IsNullOrEmpty(filename) || !filename.EndsWith(".zip"))
                return this.BadRequest("Submission dossier(.zip) file name is required");

            return this.Json(await this.submissionPubBlobContainer.Pub_GetSequenceUploadLinkAsync(filename));
        }
        [HttpGet, Route("/submissions/{submissionUniqueId}/link/{filename}")]
        [AuthorizeMultiple("Editor;ExternalEditor", false)]
        public async Task<IActionResult> GetUploadLink(
           string filename,
           string submissionUniqueId,
           [FromQuery] string clientId,
           [FromQuery] string applicationId,
           [FromQuery] string documentTypeId,
           [FromQuery] string version)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            if (string.IsNullOrEmpty(filename) || !filename.EndsWith(".zip"))
                return this.BadRequest("Submission dossier(.zip) file name is required");

            return this.Json(await this.submissionPubBlobContainer.Pub_GetSequenceUploadLinkAsync(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version));
        }
        [HttpPost, Route("/submissions/unpack/{filename}")]
        public async Task<IActionResult> Unpack(string filename)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            await this.submissionBlobContainer.CopySequenceFromPublicToPrivate(filename);
            bool success = await this.submissionBlobContainer.UnpackSequenceAsync(filename);
            return this.Json(success);
        }
        [HttpPost, Route("/submissions/{submissionUniqueId}/unpack/{filename}"), ValidateAntiForgeryToken]
        [AuthorizeMultiple("Editor;ExternalEditor", false)]
        public async Task<IActionResult> Unpack(
            string filename,
            string submissionUniqueId,
            [FromQuery] string clientId,
            [FromQuery] string applicationId,
            [FromQuery] string documentTypeId,
            [FromQuery] string version)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            await this.submissionBlobContainer.CopySequenceFromPublicToPrivate(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version);
            bool success = await this.submissionBlobContainer.UnpackSequenceAsync(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version);
            if (success)
            {
                await this.submissionBlobContainer.UpdateSequenceMetadata(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version);
            }

            return this.Json(success);
        }

        [HttpGet]
        [Route("/submissions")]
        [Route("/submissions/{filename}")]
        public async Task<IActionResult> Index(string filename)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            if (string.IsNullOrEmpty(filename))
            {
                var blobs = await this.submissionBlobContainer.ListSequencesAsync();
                return this.View(blobs);
            }

            string sequenceLocation = this.submissionBlobContainer.GetFullFilePath(filename);
            (IList<CtdNodeModel> tree, string errorMessage) = await this.Read(sequenceLocation);

            return this.View("Viewer", new SubmissionsModel
            {
                Sequence = filename,
                Tree = tree,
                Origin = "eCTD",
                Error = errorMessage
            });
        }

        [HttpGet, Route("/submissions/{id}/dossier/{filename}"), Authorize(Policy = "Reader")]
        public async Task<IActionResult> ViewSingleDossier(
            string id,
            string filename,
            [FromQuery] string clientId,
            [FromQuery] string applicationId,
            [FromQuery] string documentTypeId,
            [FromQuery] string version)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            string sequenceLocation = this.submissionBlobContainer.GetFullFilePath(filename, clientId, applicationId);
            (IList<CtdNodeModel> tree, string errorMessage) = await this.Read(sequenceLocation);
            var maxTime = this.configuration.GetValue<double>("AppSettings:MaxTime");
            TimeSpan RegexTimeout = TimeSpan.FromMilliseconds(maxTime);
            var submission = await this.submissionService.GetSubmission(int.Parse(Regex.Match(id, @"\d+", RegexOptions.None, RegexTimeout).Value));

            return this.View("Viewer", new SubmissionsModel
            {
                Sequence = filename,
                ClientId = clientId,
                ApplicationId = applicationId,
                Tree = tree,
                SubmissionId = id,
                DocumentTypeId = documentTypeId,
                Version = version,
                Origin = "Submission",
                Error = errorMessage,
                SubmissionDetails = new SubmissionDetailsModel()
                {
                    ApplicationType = (await PicklistHelper.ExtractPicklist(cache, submission.Application.ApplicationTypeId))?.Name,
                    ApplicationNumber = submission.Application.ApplicationNumber,
                    ProcedureType = (await PicklistHelper.ExtractPicklist(cache, submission.Application.ProcedureTypeId))?.Name,
                    Product = string.Join(" | ", submission.Application.ApplicationProduct.Select(x => x.Product)
                                                                   .Select(async x => $"{x.Name}, {(await PicklistHelper.ExtractPicklist(cache, x.DosageFormId)).Name}, {x.Strength}")
                                                                   .Select(x => x.GetAwaiter().GetResult())
                                                                   .ToList())

                }
            });
        }

        [HttpGet, Route("/submissions/dossiers"), Authorize(Policy = "Reader")]
        public async Task<IActionResult> ViewDossiersPerApplication(
            [FromQuery] string clientId,
            [FromQuery] string applicationId)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            string sequenceLocation = this.submissionBlobContainer.GetFullFilePath(null, clientId, applicationId).TrimEnd('/');
            (IList<CtdNodeModel> tree, string errorMessage) = await this.Read(sequenceLocation, true);

            var application = await this.applicationService.GetApplication(int.Parse(applicationId));

            return this.View("Viewer", new SubmissionsModel
            {
                Sequence = "Current",
                Recursive = true,
                ClientId = clientId,
                ApplicationId = applicationId,
                Tree = tree,
                Origin = "Application",
                Error = errorMessage,
                SubmissionDetails = new SubmissionDetailsModel()
                {
                    ApplicationType = (await PicklistHelper.ExtractPicklist(cache, application.ApplicationTypeId))?.Name,
                    ApplicationNumber = application.ApplicationNumber,
                    ProcedureType = (await PicklistHelper.ExtractPicklist(cache, application.ProcedureTypeId))?.Name
                }
            });
        }

        [HttpGet, Route("/submissions/tree/{filename}")]
        public async Task<IActionResult> Tree(
            string filename,
            [FromQuery] string clientId,
            [FromQuery] string applicationId,
            [FromQuery] string[] submissions = null,
            [FromQuery] bool recursive = false)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            if (string.IsNullOrEmpty(filename)) return this.Json(new List<CtdNodeModel>());

            var sequenceLocation = this.submissionBlobContainer.GetFullFilePath(filename, clientId, applicationId);

            if (recursive)
            {
                sequenceLocation = sequenceLocation.Substring(0, sequenceLocation.LastIndexOf('/'));
            }

            return this.Json(await this.ectdViewer.BuildTree(sequenceLocation, submissions?.ToList(), recursive));
        }

        [HttpGet, Route("/submissions/sequences/{filename}")]
        [AuthorizeMultiple("Editor;ExternalEditor", false)]
        public async Task<IActionResult> DossierSequences(string filename, [FromQuery] string clientId, [FromQuery] string applicationId)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            if (string.IsNullOrEmpty(filename)) return this.Json(new List<CtdNodeModel>());

            string sequenceLocation = this.submissionBlobContainer.GetFullFilePath(filename, clientId, applicationId);
            var sequences = await this.submissionBlobContainer.GetSequences(sequenceLocation);
            return this.Json(sequences);
        }

        private async Task<(IList<CtdNodeModel>, string)> Read(string location, bool recursive = false)
        {
            List<CtdNodeModel> tree = new List<CtdNodeModel>();
            string errorMessage = null;
            try
            {
                tree = await this.ectdViewer.BuildTree(location, recursive: recursive);
            }
            catch (NotSupportedException e)
            {
                errorMessage = e.Message;
            }
            catch
            {
                errorMessage = "An error occurred. Could not read dossier.";
            }

            return (tree, errorMessage);
        }
    }
}
