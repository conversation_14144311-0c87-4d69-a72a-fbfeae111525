﻿@inject PharmaLex.SmartTRACE.Web.Helpers.VersionCdnHelper Cdn

<script type="text/x-template" id="plx-map-template">
    <div>
        <select id="countrySelect" v-show="showList" v-bind:aria-disabled="!editable" multiple v-model="selectedCountries" v-on:change="onCountryListSelectionChanged" :class="['company-countries-select']">
            <option v-for="country in countries" :value="country.id">{{country.name}}</option>
        </select>
        <div v-show="!showList" id="map-container" :class="['company-countries-map']"></div>
        <div style="display:flex; justify-content:space-between;">
            <a class="action" @@click="showList=!showList">{{toggleText}}</a>
            <select class="on-map" v-if="regions.length && editable" v-model="copyFromRegion" v-on:change="onCopyFromRegionChange">
                <option value="-1">Select region</option>
                <option v-for="(r, i) in regions" :value="r.id">{{r.name}}</option>
            </select>
            <a v-if="editable" class="action" @@click="onSelectAllCountries">Select all</a>
            <a v-if="editable" class="action" @@click="onClear">Clear all</a>
        </div>
    </div>
</script>
<script src="@Cdn.GetUrl("js/vue/plx-map.js")"></script>
<script src="@Cdn.GetUrl("data/countries.topo.js")"></script>
<script src="@Cdn.GetUrl("lib/d3/d3.v4.js")"></script>
<script src="@Cdn.GetUrl("lib/d3/topojson.js")"></script>
<script src="@Cdn.GetUrl("js/map.js")" asp-append-version="true"></script>