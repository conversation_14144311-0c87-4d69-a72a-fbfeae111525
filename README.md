﻿# Smart Trace
[![Build Status](https://dev.azure.com/Phlexglobal/SmartTrace/_apis/build/status/SmartTrace.Build?repoName=SmartTrace&branchName=develop)](https://dev.azure.com/Phlexglobal/SmartTrace/_build/latest?definitionId=2323&repoName=SmartTrace&branchName=develop)

## Project Overview
SmartTrace is a regulatory information and process management system to manage regulatory submissions from planning through to submission and archive. The application allows Regulatory Leads, Publishing Leads, Publishers to create a Submission Record to:

- request publishing support from another team, (e.g. RegOps-Request)
- track the details, dates, documents, dossiers and delivery receipts for a submission

SmartTrace also includes automatic e-mail notifications to inform everyone involved on the status of the Submission Record as it moves through the workflow
Notifications are reduced if you are the only person involved in the Submission.

### Project Structure
```
├── src
     ├── PharmaLex.SmartTRACE.Data                        # Data layer providing repositories
     ├── PharmaLex.SmartTrace.Entities                    # Domain layer for domain entities, migrations, and DB scripts
     ├── PharmaLex.SmartTRACE.RemindersApp                # Azure function app used to send reminder emails
     ├── PharmaLex.SmartTrace.Web                         # MVC/Razor Web Application
          ├── Authentication                              # Azure AD B2C user authentication callbacks
          ├── Controllers                                 # MVC Controllers
          ├── CTD                                         # Common Technical Document (CTD) file helpers
          ├── DataFactory                                 # Classes to work with Azure Data Factory
          ├── HealthCheck                                 # Extension methods for custom application health checks
          ├── newrelic                                    # Folder for NewRelic license and configuration
          ├── Views                                       # MVC Views (Vue.js)
          ├── wwwroot                                     # Folder storing styles, images and templates
     ├── PharmaLex.SmartTrace.Web.Helpers                 # Helper methods and services
     ├── PharmaLex.SmartTrace.Web.Models                  # EntityModels and ViewModels, model binders
├── test
     ├── PharmaLex.SmartTRACE.ReminderAppTests            # Service unit tests for the RemindersApp function app
     ├── PharmaLex.SmartTrace.Web.HelpersTests            # Unit tests for the PharmaLex.SmartTrace.Web.Helpers project
     ├── PharmaLex.SmartTrace.Web.ModelsTests             # Unit tests for the PharmaLex.SmartTrace.Web.Models project
     ├── PharmaLex.SmartTrace.WebTests                    # Unit tests for the PharmaLex.SmartTrace.Web project
```

## Local Infrastructure
SmartTrace requires the following infrastructure locally:
- Microsoft SQL Server
- Azurite emulator for local Azure Storage development

## Migrations
To add new migration open Package Manager Console, set the default project to PharmaLex.SmartTrace.Entities and use command Add-Migration <migration name>
Example command:
```
Add-Migration InitialCreate
```

To create or update the database with new migrations open Package Manager Console, set the default project to PharmaLex.SmartTrace.Entities and use command Update-Database.
This will update database specified in setting `ConnectionStrings:default`.

## Getting Started
1. Clone the repository. If using the command line, run the following commands.
	- `git clone https://<EMAIL>/Phlexglobal/SmartTrace/_git/SmartTrace` to fetch the parent repository and check out the default branch.
	- `cd SmartTrace` to change into the repository.
1. As SSO is enabled, the application needs to be changed to run on port 5001.
Update the launchSettings.json (src/PharmaLex.SmartTrace.Web/Properties/launchSettings.json) file to have an `applicationUrl` of `"https://localhost:5001;http://localhost:61424"`.
1. Update the "ConnectionStrings:default" setting in the appsettings file in PharmaLex.SmartTrace.Web to the connection string of the local SQL Server database.
1. Using the package manager console, set the default project to PharmaLex.SmartTrace.Entities and run the command Script-Migration.
1. Copy the script and apply to the local database.

### Build and Test SmartTrace Web
In order to run the web project directly from Visual Studio:
	- VPN should be connected
	- Ensure the startup project is set to PharmaLex.SmartTrace.Web

### Authentication and Authorisation users
For local development you need to add your user in the `dbo.User` table and then add an entry in the `dbo.UserClaim` table with a claim (e.g., SuperAdmin) for the user.

### Build and Test PharmaLex.SmartTRACE.RemindersApp
In order to run and debug the RemindersApp application (Azure function):
- Set the startup project as PharmaLex.SmartTRACE.RemindersApp
- Update the appsettings.json file in the PharmaLex.SmartTRACE.RemindersApp project:
    - Change the emailsSendingEnabled setting as needed
- Run the project in Visual Studio