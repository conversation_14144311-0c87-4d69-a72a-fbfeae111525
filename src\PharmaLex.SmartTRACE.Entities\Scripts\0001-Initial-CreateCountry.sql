﻿CREATE TRIGGER [dbo].[Country_Insert] ON [dbo].[Country]
FOR INSERT AS
INSERT INTO [Audit].[Country_Audit]
SELECT 'I', [Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Country_Update] ON [dbo].[Country]
FOR UPDATE AS
INSERT INTO [Audit].[Country_Audit]
SELECT 'U', [Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Country_Delete] ON [dbo].[Country]
FOR DELETE AS
INSERT INTO [Audit].[Country_Audit]
SELECT 'D', [Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO