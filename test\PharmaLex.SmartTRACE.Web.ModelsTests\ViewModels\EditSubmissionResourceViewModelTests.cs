﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class EditSubmissionResourceViewModelTests
    {
        [Fact]
        public void EditSubmissionResourceViewModel_Get_SetValue()
        {
            //Arrange
            var model = new EditSubmissionResourceViewModel();
            model.SubmissionResource = new SubmissionResourceModel();
            model.Priorities = new List<PicklistDataModel>();
            model.EstimatedSizes=new List<PicklistDataModel>();
            model.AllSubmissions=new List<SubmissionModel>();
            model.Publishers=new List<UserModel>();
            //Assert
            Assert.NotNull(model);
        }
    }
}
