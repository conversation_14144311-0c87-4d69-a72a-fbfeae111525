﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class ActiveSubstance : EntityBase
    {
        public ActiveSubstance()
        {
            ActiveSubstanceProduct = new HashSet<ActiveSubstanceProduct>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public int ClientId { get; set; }
        public int LifecycleStateId { get; set; }
        public virtual Client Client { get; set; }
        public virtual ICollection<ActiveSubstanceProduct> ActiveSubstanceProduct { get; set; }
    }
}
