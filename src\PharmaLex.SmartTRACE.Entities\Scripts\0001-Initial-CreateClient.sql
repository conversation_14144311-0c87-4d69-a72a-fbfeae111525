﻿CREATE TRIGGER [dbo].[Client_Insert] ON [dbo].[Client]
FOR INSERT AS
INSERT INTO [Audit].[Client_Audit]
SELECT 'I', [Id], [Name], [ContractOwnerId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Client_Update] ON [dbo].[Client]
FOR UPDATE AS
INSERT INTO [Audit].[Client_Audit]
SELECT 'U', [Id], [Name], [ContractOwnerId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Client_Delete] ON [dbo].[Client]
FOR DELETE AS
INSERT INTO [Audit].[Client_Audit]
SELECT 'D', [Id], [Name], [ContractOwnerId], [CreatedDate], [CreatedBy], GE<PERSON><PERSON><PERSON>(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO