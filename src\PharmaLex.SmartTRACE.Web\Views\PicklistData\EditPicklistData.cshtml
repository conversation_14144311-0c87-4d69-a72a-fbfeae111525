﻿@model EditPicklistDataViewModel
@using PharmaLex.SmartTRACE.Web.Models
@using PharmaLex.SmartTRACE.Entities.Enums
@{
    var picklistType = Html.Encode(((PicklistType)Model.Picklist.PicklistTypeId).GetDescription());
    var allCountries = Model.AllCountries.OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = Html.Encode(x.Name),
                Value = x.Id.ToString()
            });
}

<div id="picklist" class="manage-container">
    <header class="manage-header">
        <h3>@Html.Encode(Model.Picklist.Name) @picklistType</h3>
        @if (Model.Picklist.Id != 0)
        {
            <a class="button icon-button-delete" href="/data/delete/@Html.Raw(Model.Picklist.Id)">Delete</a>
        }
        <a href="/data/@Model.Picklist.PicklistTypeId" class="button secondary icon-button-back">Back to @picklistType list</a>
    </header>
    <form method="post">
        @Html.AntiForgeryToken()
        <div class="form-col">
            <label for="Picklist.Name">Name*</label>
            <input asp-for="Picklist.Name" type="text" v-bind:class="{'validation-error' : hasError}" required />
            <span asp-validation-for="Picklist.Name" class="text-danger"></span>
            <br />
            @if (Model.HasError)
            {
                <span class="field-duplication-error">@Model.ErrorMessage</span>
            }
            <label for="Picklist.CountriesIds">Country</label>
            <div class="multi-select-wrapper">
                <select asp-for="Picklist.CountriesIds" asp-items="allCountries" multiple>
                </select>
            </div>
        </div>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/data/@Html.Raw(Model.Picklist.PicklistTypeId)">Cancel</a><button class="icon-button-save">Save</button>
        </div>
        <input type="hidden" asp-for="Picklist.Id" />
        <input type="hidden" asp-for="Picklist.PicklistTypeId" />
    </form>
</div>

@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#picklist',
            data() {
                return {
                    hasError: @Model.HasError.ToString().ToLower()
                    };
            }
        };
    </script>
}