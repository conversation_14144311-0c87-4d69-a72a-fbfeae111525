﻿CREATE TRIGGER [dbo].[SubmissionResource_Insert] ON [dbo].[SubmissionResource]
FOR INSERT AS
INSERT INTO [Audit].[SubmissionResource_Audit]
SELECT 'I', [Id], [PriorityId], [EstimatedSizeId], [AllocatedHours], [RegulatoryLead], [PublishingLead], [PublishingTeamId], [Comments], [SubmissionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[SubmissionResource_Update] ON [dbo].[SubmissionResource]
FOR UPDATE AS
INSERT INTO [Audit].[SubmissionResource_Audit]
SELECT 'U', [Id], [PriorityId], [EstimatedSizeId], [AllocatedHours], [RegulatoryLead], [PublishingLead], [PublishingTeamId], [Comments], [SubmissionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[SubmissionResource_Delete] ON [dbo].[SubmissionResource]
FOR DELETE AS
INSERT INTO [Audit].[SubmissionResource_Audit]
SELECT 'D', [Id], [PriorityId], [EstimatedSizeId], [AllocatedHours], [RegulatoryLead], [PublishingLead], [PublishingTeamId], [Comments], [SubmissionId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO