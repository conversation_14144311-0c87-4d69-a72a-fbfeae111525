<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../../util/style/ch-regional.xsl" ?>
<!DOCTYPE ch:ch-backbone SYSTEM "../../util/dtd/ch-regional.dtd">
<!--Generated by Cunesoft eCTD Engine Date: 7/23/2024 Time: 12:17 PM-->
<ch:ch-backbone xmlns:xlink="http://www.w3c.org/1999/xlink" dtd-version="1.5" xmlns:ch="http://www.swissmedic.ch">
  <ch-envelope>
    <envelope country="ch">
      <application-number>as</application-number>
      <submission-description>age</submission-description>
      <invented-name>ds</invented-name>
      <dmf-number>ss</dmf-number>
      <pmf-number>ss</pmf-number>
      <inn>ss</inn>
      <applicant>piyush</applicant>
      <dmf-holder>ss</dmf-holder>
      <pmf-holder>gk</pmf-holder>
      <agency>Swissmedic</agency>
      <article-13-tpa>no</article-13-tpa>
      <ectd-sequence>0000</ectd-sequence>
      <related-ectd-sequence>none</related-ectd-sequence>
    </envelope>
  </ch-envelope>
  <m1-ch>
    <m1-galenic-form name="common">
      <m1-2-applvar>
        <m1-2-2-form-add>
          <m1-2-2-1-form-full-declaration>
            <leaf xlink:href="common/12-foapplvar/122-form-add/1221-formfulldecl/file123-2-1-1-1.pdf" checksum-type="md5" checksum="a973a2aa208482a677768c69355aaed1" operation="new" ID="IDa50cb96832194599aa76201fee2b6ae04a249e1b2b8b43c6aacbf10fa0ee5aaf">
              <title>File123 (2) (1) (1) (1)</title>
            </leaf>
          </m1-2-2-1-form-full-declaration>
        </m1-2-2-form-add>
      </m1-2-applvar>
    </m1-galenic-form>
  </m1-ch>
</ch:ch-backbone>