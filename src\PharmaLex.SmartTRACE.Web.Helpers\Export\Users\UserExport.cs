﻿using NPOI.SS.UserModel;
using NPOI.SS.Util;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.Office;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public class UserExport : ExcelWriter, IUserExport
    {
        private readonly IDistributedCacheServiceFactory cache;

        public UserExport(IDistributedCacheServiceFactory cache)
        {
            this.cache = cache;
        }

        public async Task<byte[]> Export()
        {
            var wb = this.CreateWorkbook();
            ISheet exSheet = wb.Workbook.CreateSheet("Users");
            int rowIndex = 0;

            var userCache = this.cache.CreateMappedEntity<User, UserModel>()
                .Configure(o => o
                    .Include(x => x.UserClaim)
                        .ThenInclude(x => x.Claim)
                     .Include(x => x.UserClient)
                        .ThenInclude(x => x.Client)
                     .AsSplitQuery());

            var allUsers = await userCache.AllAsync();

            List<string> columnNames = new List<string>
            {
                "Name", "Email", "User Type", "Auto Access Clients", "Roles", "Clients"
            };
            exSheet.CreateRow(rowIndex++, wb.Styles["header"], columnNames.ToArray());

            foreach (var user in allUsers.OrderBy(x => x.DisplayFullName))
            {
                List<string> userProps = new List<string>()
                {
                    user.DisplayFullName,
                    user.Email,
                    user.DisplayUserType,
                    user.AutoAccessClients ? "Yes" : "No",
                    user.DisplayClaimsText,
                    user.DisplayClientsText
                };
                var row = exSheet.CreateRow(rowIndex++);
                for (int i = 0; i < userProps.Count; i++)
                {
                    row.CreateCell(i, userProps[i], wb.Styles["wrapped"]);
                }
            }

            exSheet.AutoSizeColumns(0, columnNames.Count);
            exSheet.SetAutoFilter(new CellRangeAddress(0, 0, 0, columnNames.Count - 1));

            return wb.Workbook.ToByteArray();
        }
    }
}
