﻿using Azure.Storage.Blobs.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.CTD;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models;
using PharmaLex.SmartTRACE.Web.Storage;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using Application = PharmaLex.SmartTRACE.Entities.Application;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class SubmissionsControllerTests
    {
        private readonly IEctdViewer _ectdViewer;
        private readonly ISubmissionBlobContainer _submissionBlobContainer;
        private readonly ISubmissionPubBlobContainer _submissionPubBlobContainer;
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IApplicationService _applicationService;
        private readonly ISubmissionService _submissionService;
        private readonly IConfiguration configuration;
        public ApplicationModel appModel = new()
        {
            Id = 1,
            ApplicationNumber = "ap11",
            ProcedureTypeId = 11,
            ApplicationTypeId = 110,
            ClientId = 1100,
            UniqueId = "uid1",
        };
        public PicklistDataModel pkDatamodel = new()
        {
            Id = 11,
            Name = "prod1",
            CountriesIds = [1],
            PicklistTypeId = 11
        };
        #region Constructor 
        public SubmissionsControllerTests()
        {
            _cache = Substitute.For<IDistributedCacheServiceFactory>();
            _ectdViewer = Substitute.For<IEctdViewer>();
            _submissionBlobContainer = Substitute.For<ISubmissionBlobContainer>();
            _submissionPubBlobContainer = Substitute.For<ISubmissionPubBlobContainer>();
            _applicationService = Substitute.For<IApplicationService>();
            _submissionService = Substitute.For<ISubmissionService>();
            configuration = Substitute.For<IConfiguration>();
        }
        #endregion
        #region clear delete
        [Fact]
        public async Task Clear_Returns_OkObjectResult()
        {
            // Arrange
            var filename = "testFile";
            await _submissionBlobContainer.ClearSequenceAsync(filename);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService, configuration);

            // Act
            var result = await controller.Clear(filename);

            // Assert
            var viewResult = Assert.IsType<OkResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(200, viewResult.StatusCode);
        }
        #endregion
        #region clear delete_Invalid
        [Fact]
        public async Task Clear_Returns_InvalidObject()
        {
            // Arrange
            string filename = "new";
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.Clear(filename) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region Clear_With_Parameter
        [Fact]
        public async Task Clear_With_Parameters_Returns_OkObjectResult()
        {
            // Arrange
            var filename = "testFile";
            var clientId = "123";
            var applicationId = "1";
            var submissionUniqueId = "109";
            var documentTypeId = "2";
            var version = "1";
            await _submissionBlobContainer.ClearSequenceAsync(filename);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.Clear(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version);

            // Assert
            var viewResult = Assert.IsType<OkResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(200, viewResult.StatusCode);

        }
        #endregion
        #region Clear_With_Parameter_Invalid
        [Fact]
        public async Task Clear_With_Parameters_Returns_InvalidResult()
        {
            // Arrange
            var filename = "testFile";
            var clientId = "123";
            var applicationId = "1";
            var submissionUniqueId = "109";
            var documentTypeId = "2";
            var version = "1";
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.Clear(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);

        }
        #endregion
        # region Download_With_filename_Returns_DownloadLink
        [Fact]
        public async Task Download_With_filename_Returns_StatusCode()
        {
            // Arrange
            var filename = "testFile";
            var file = "file1";
            var downloadlink = "downloadlink";

            var blobContent = new BinaryData(downloadlink);

            var downloadResult = BlobsModelFactory.BlobDownloadResult(content: blobContent,details: null);
            _submissionBlobContainer.GetFileDownloadAsync(filename,file).Returns(downloadResult);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.Download(filename, file) as StatusCodeResult;

            // Assert
            var viewResult = Assert.IsType<StatusCodeResult>(result);
            Assert.NotNull(viewResult);
           Assert.Equal("500", viewResult.StatusCode.ToString());

        }
        #endregion
        #region Download_With_filename_Returns_Invalid_DownloadLink
        [Fact]
        public async Task Download_With_filename_Returns_Invalid_StatusCode()
        {
            // Arrange
            var filename = "testFile";
            var file = "file1";
            var downloadlink = "downloadlink";

            var blobContent = new BinaryData(downloadlink);

            var downloadResult = BlobsModelFactory.BlobDownloadResult(content: blobContent, details: null);
            _submissionBlobContainer.GetFileDownloadAsync(filename, file).Returns(downloadResult);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");


            // Act
            var result = await controller.Download(filename, file) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);

        }
        #endregion
        #region DownloadPDF With filename Returns DownloadLink
        [Fact]
        public async Task DownloadPDF_With_filename_Returns_StatusCode()
        {
            // Arrange
            var filename = "testFile";
            var clientId = "123";
            var applicationId = "1";
            var downloadlink = "downloadlinkPDF";

            var blobContent = new BinaryData(downloadlink);

            var downloadResult = BlobsModelFactory.BlobDownloadResult(content: blobContent, details: null);

            _submissionBlobContainer.GetFileDownloadAsync(filename, clientId, applicationId).Returns(downloadResult);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.DownloadPdf(filename, clientId, applicationId) as StatusCodeResult;

            // Assert
            var viewResult = Assert.IsType<StatusCodeResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal("500", viewResult.StatusCode.ToString());

        }
        #endregion
        #region DownloadPDF With filename Returns Invalid DownloadLink
        [Fact]
        public async Task DownloadPDF_With_filename_Returns_Invalid_StatusCode()
        {
            // Arrange
            var filename = "testFile";
            var clientId = "123";
            var applicationId = "1";
            var downloadlink = "downloadlinkPDF";

            var blobContent = new BinaryData(downloadlink);

            var downloadResult = BlobsModelFactory.BlobDownloadResult(content: blobContent, details: null);

            _submissionBlobContainer.GetFileDownloadAsync(filename, clientId, applicationId).Returns(downloadResult);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.DownloadPdf(filename, clientId, applicationId) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region Download_With_Parameters_Returns_jsonResult
        [Fact]
        public async Task Download_With_Parameters_Returns_StatusCode()
        {
            // Arrange
            var filename = "testFile";
            var clientId = "123";
            var applicationId = "1";
            var downloadlink = "downloadlinkPDF";
            var submissionUniqueId = "109";
            var documentTypeId = "2";
            var version = "1";
            var blobContent = new BinaryData(downloadlink);

            var downloadResult = BlobsModelFactory.BlobDownloadResult(content: blobContent, details: null);

            _submissionBlobContainer.GetFileDownloadAsync(filename, clientId, applicationId).Returns(downloadResult);

            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.Download(filename, submissionUniqueId, clientId, applicationId, documentTypeId, version) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);

        }
        #endregion
        #region Download_With_Parameters_Returns_Invalid_jsonResult
        [Fact]
        public async Task Download_With_Parameters_Returns_Invalid_StatusCode()
        {
            // Arrange
            var filename = "testFile";
            var clientId = "123";
            var applicationId = "1";
            var downloadlink = "downloadlinkPDF";
            var submissionUniqueId = "109";
            var documentTypeId = "2";
            var version = "1";
            var blobContent = new BinaryData(downloadlink);

            var downloadResult = BlobsModelFactory.BlobDownloadResult(content: blobContent, details: null);

            _submissionBlobContainer.GetFileDownloadAsync(filename, clientId, applicationId).Returns(downloadResult);

            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.Download(filename, submissionUniqueId, clientId, applicationId, documentTypeId, version) as StatusCodeResult;

            // Assert
            var viewResult = Assert.IsType<StatusCodeResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal("500", viewResult.StatusCode.ToString());

        }
        #endregion
        
        #region GetUploadLink_Returns_JsonResult
        [Fact]
        public async Task GetUploadLink_Returns_JsonResult()
        {
            // Arrange
            var filename = "testFile.zip";
            var uploadLink = "uploadLink";
            _submissionPubBlobContainer.Pub_GetSequenceUploadLinkAsync(filename).Returns(uploadLink);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer,_ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.GetUploadLink(filename);

            // Assert
            var viewResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(uploadLink, viewResult.Value);
        }
        #endregion
        #region GetUploadLink_Returns_BadRequest
        [Fact]
        public async Task GetUploadLink_Returns_BadRequest()
        {
            // Arrange
            var filename = "testFile.py";
            var uploadLink = "uploadLink";
            var code = 400;
            _submissionBlobContainer.GetSequenceUploadLinkAsync(filename).Returns(uploadLink);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.GetUploadLink(filename);

            // Assert
            var viewResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(code, viewResult.StatusCode);
        }
        #endregion
        #region GetUploadLink_Returns_BadRequest_When_File_Is_Nulls
        [Fact]
        public async Task GetUploadLink_Returns_BadRequest_When_File_Is_Null()
        {
            // Arrange
            var uploadLink = "uploadLink";
            var code = 400;
            _submissionBlobContainer.GetSequenceUploadLinkAsync(null).Returns(uploadLink);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.GetUploadLink(null);

            // Assert
            var viewResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(code, viewResult.StatusCode);
        }
        #endregion
        #region GetUploadLink_Returns_InvalidObject
        [Fact]
        public async Task GetUploadLink_Returns_InvalidObject()
        {
            // Arrange
            var filename = "testFile.zip";
            var uploadLink = "uploadLink";
            _submissionBlobContainer.GetSequenceUploadLinkAsync(filename).Returns(uploadLink);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.GetUploadLink(filename) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        //fr
        #region GetUploadLink_parameters_Returns_JsonResult
        [Fact]
        public async Task GetUploadLink_parameters_Returns_JsonResult()
        {
            // Arrange
            var clientId = "123";
            var applicationId = "1";
            var submissionUniqueId = "109";
            var documentTypeId = "2";
            var version = "1";
            var filename = "testFile.zip";
            var uploadLink = "uploadLink";
            _submissionPubBlobContainer.Pub_GetSequenceUploadLinkAsync(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version).Returns(uploadLink);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.GetUploadLink(filename, submissionUniqueId, clientId, applicationId, documentTypeId, version);

            // Assert
            var viewResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(uploadLink, viewResult.Value);
        }
        #endregion
        #region GetUploadLink_Parameters_Returns_BadRequest
        [Fact]
        public async Task GetUploadLink_Parameters_Returns_BadRequest()
        {
            // Arrange
            var clientId = "123";
            var applicationId = "1";
            var submissionUniqueId = "109";
            var documentTypeId = "2";
            var version = "1";
            var filename = "testFile.py";
            var uploadLink = "uploadLink";
            var code = 400;
            _submissionBlobContainer.GetSequenceUploadLinkAsync(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version).Returns(uploadLink);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.GetUploadLink(filename, submissionUniqueId, clientId, applicationId, documentTypeId, version);

            // Assert
            var viewResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(code, viewResult.StatusCode);
        }
        #endregion
        #region GetUploadLink_With_Parameters_Returns_BadRequest_When_File_Is_Null
        [Fact]
        public async Task GetUploadLink_With_Parameters_Returns_BadRequest()
        {
            // Arrange
            var clientId = "123";
            var applicationId = "1";
            var submissionUniqueId = "109";
            var documentTypeId = "2";
            var version = "1";
            var uploadLink = "uploadLink";
            var code = 400;
            _submissionBlobContainer.GetSequenceUploadLinkAsync(null, clientId, applicationId, submissionUniqueId, documentTypeId, version).ReturnsForAnyArgs(uploadLink);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.GetUploadLink(null, submissionUniqueId, clientId, applicationId, documentTypeId, version);

            // Assert
            var viewResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(code, viewResult.StatusCode);
        }
        #endregion
        #region GetUploadLink_parameters_Returns_InvalidObject
        [Fact]
        public async Task GetUploadLink_parameters_Returns_InvalidObject()
        {
            // Arrange
            var clientId = "123";
            var applicationId = "1";
            var submissionUniqueId = "109";
            var documentTypeId = "2";
            var version = "1";
            var filename = "testFile.zip";
            var uploadLink = "uploadLink";
            _submissionBlobContainer.GetSequenceUploadLinkAsync(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version).Returns(uploadLink);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.GetUploadLink(filename, submissionUniqueId, clientId, applicationId, documentTypeId, version) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region Unpack_Returns_JsonResult
        [Fact]
        public async Task Unpack_Returns_JsonResult()
        {
            // Arrange
            var filename = "testFile.zip";
            _submissionBlobContainer.UnpackSequenceAsync(filename).Returns(true);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.Unpack(filename);

            // Assert
            var viewResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(true, viewResult.Value);
        }
        #endregion
        #region Unpack_Returns_InvalidObject
        [Fact]
        public async Task Unpack_Returns_InvalidObject()
        {
            // Arrange
            var filename = "testFile.zip";
            _submissionBlobContainer.UnpackSequenceAsync(filename).Returns(true);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.Unpack(filename) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region UnpackSequenceAsync_Returns_Json_FalseResult
        [Fact]
        public async Task UnpackSequenceAsync_Returns_Json_FalseResult()
        {
            // Arrange
            var filename = "testFile.zip";
            var clientId = "123";
            var applicationId = "1";
            var submissionUniqueId = "109";
            var documentTypeId = "2";
            var version = "1";
            _submissionBlobContainer.UnpackSequenceAsync(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version).Returns(false);

            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.Unpack(filename,
             submissionUniqueId, clientId, applicationId, documentTypeId, version);

            // Assert
            var viewResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(false, viewResult.Value);
        }
        #endregion
        #region UnpackSequenceAsync_Returns_Json_TrueResult
        [Fact]
        public async Task UnpackSequenceAsync_Returns_Json_TrueResult()
        {
            // Arrange
            var filename = "testFile.zip";
            var clientId = "123";
            var applicationId = "1";
            var submissionUniqueId = "109";
            var documentTypeId = "2";
            var version = "1";
            _submissionBlobContainer.UnpackSequenceAsync(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version).Returns(true);

            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.Unpack(filename,
             submissionUniqueId, clientId, applicationId, documentTypeId, version);

            // Assert
            var viewResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(true, viewResult.Value);
        }
        #endregion
        #region UnpackSequenceAsync_Returns_InvalidObject
        [Fact]
        public async Task UnpackSequenceAsync_Returns_InvalidObject()
        {
            // Arrange
            var filename = "testFile.zip";
            var clientId = "123";
            var applicationId = "1";
            var submissionUniqueId = "109";
            var documentTypeId = "2";
            var version = "1";
            _submissionBlobContainer.UnpackSequenceAsync(filename, clientId, applicationId, submissionUniqueId, documentTypeId, version).Returns(true);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.Unpack(filename,
             submissionUniqueId, clientId, applicationId, documentTypeId, version) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region Index_With_NullFile_Returns_View_With_Model
        [Fact]
        public async Task Index_With_NullFile_Returns_View_With_Model()
        {
            // Arrange
            List<string> blobs = ["blob1", "blob2"];
            _submissionBlobContainer.ListSequencesAsync().Returns(blobs);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);

            // Act
            var result = await controller.Index(null);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal(blobs, viewResult.Model);
        }
        #endregion
        #region Index_With_NullFile_Returns_View_InvalidObject
        [Fact]
        public async Task Index_With_NullFile_Returns_View_InvalidObject()
        {
            // Arrange
            List<string> blobs = ["blob1", "blob2"];
            var filename = "testfile";
            _submissionBlobContainer.ListSequencesAsync().Returns(blobs);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.Index(filename) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region Index_With_File_Returns_ViewWith_ErrorResult
        [Fact]
        public async Task Index_With_File_Returns_ViewWith_ErrorResult()
        {
            // Arrange
            List<CtdNodeModel> tree = new List<CtdNodeModel>() { new CtdNodeModel() { Id = 1, Name = "c2" } };
            var filename = "testfile";
            string sequenceLocation = "seqloc";
            List<string> blobs = ["blob1", "blob2"];
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            _submissionBlobContainer.GetFullFilePath(filename).Returns(sequenceLocation);
            _ectdViewer.BuildTree(sequenceLocation, blobs).ReturnsForAnyArgs(tree);
            var result = await controller.Index(filename);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal("Viewer", viewResult.ViewName);
            Assert.NotNull(viewResult.Model);

        }
        #endregion
        #region ViewSingleDossier_With_File_Returns_ViewWith_ErrorResult
        [Fact]
        public async Task ViewSingleDossier_With_File_Returns_ViewWith_ErrorResult()
        {
            // Arrange
            var id = "1";
            var clientId = "123";
            var applicationId = "1";
            var documentTypeId = "2";
            var version = "1";
            List<ApplicationProduct> ap = new List<ApplicationProduct>() {
                new ApplicationProduct(){ Id=1, ProductId=2,Product=new Product(){ Id=1, Name="p1",DosageFormId=1,Strength="30"} } };
            Submission submission = new ()
            {
                Id = 1,
                UniqueId = "91",
                Project = new Project() { Id = 1, Name = "proj1" },
                ProjectId = 1,
                LifecycleStateId = 1,
                Application = new Application()
                { Id = 1, ApplicationTypeId = 1, ProcedureTypeId = 2, ApplicationProduct = ap }
            };
            var objConfiguration = new ConfigurationBuilder()
             .AddInMemoryCollection(
             [
                 new KeyValuePair<string, string?>("AppSettings:MaxTime", "100")
             ])
             .Build();
            List<CtdNodeModel> tree = new List<CtdNodeModel>() { new CtdNodeModel() { Id = 1, Name = "c2" } };
            var filename = "testfile";
            string sequenceLocation = "seqloc";
            List<string> blobs = ["blob1", "blob2"];
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  objConfiguration);
            _submissionBlobContainer.GetFullFilePath(filename).Returns(sequenceLocation);
            _ectdViewer.BuildTree(sequenceLocation, blobs).ReturnsForAnyArgs(tree);
            _submissionService.GetSubmission(int.Parse(Regex.Match(id, @"\d+").Value)).Returns(submission);

            //picklist
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));


            var result = await controller.ViewSingleDossier( id, filename, clientId, applicationId, documentTypeId, version);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
            Assert.NotNull(viewResult.Model);

        }
        #endregion
        #region ViewSingleDossier_With_File_Returns_ViewWith_InvalidObject
        [Fact]
        public async Task ViewSingleDossier_With_File_Returns_ViewWith_InvalidObject()
        {
            // Arrange
            var id = "1";
            var clientId = "123";
            var applicationId = "1";
            var documentTypeId = "2";
            var version = "1";
            List<ApplicationProduct> ap = new List<ApplicationProduct>() {
                new ApplicationProduct(){ Id=1, ProductId=2,Product=new Product(){ Id=1, Name="p1",DosageFormId=1,Strength="30"} } };
            Submission submission = new()
            {
                Id = 1,
                UniqueId = "91",
                Project = new Project() { Id = 1, Name = "proj1" },
                ProjectId = 1,
                LifecycleStateId = 1,
                Application = new Application()
                { Id = 1, ApplicationTypeId = 1, ProcedureTypeId = 2, ApplicationProduct = ap }
            };
            var objConfiguration = new ConfigurationBuilder()
             .AddInMemoryCollection(
             [
                 new KeyValuePair<string, string?>("AppSettings:MaxTime", "100")
             ])
             .Build();
            List<CtdNodeModel> tree = new List<CtdNodeModel>() { new CtdNodeModel() { Id = 1, Name = "c2" } };
            var filename = "testfile";
            string sequenceLocation = "seqloc";
            List<string> blobs = ["blob1", "blob2"];
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  objConfiguration);
            _submissionBlobContainer.GetFullFilePath(filename).Returns(sequenceLocation);
            _ectdViewer.BuildTree(sequenceLocation, blobs).ReturnsForAnyArgs(tree);
            _submissionService.GetSubmission(int.Parse(Regex.Match(id, @"\d+").Value)).Returns(submission);

            //picklist
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));
            controller.ModelState.AddModelError("id", "ErrorMessage");

            //Act
            var result = await controller.ViewSingleDossier(id, filename, clientId, applicationId, documentTypeId, version) as BadRequestObjectResult;


            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);

        }
        #endregion
        #region ViewDossiersPerApplication_Returns_ViewWith_SubmissionModel
        [Fact]
        public async Task ViewDossiersPerApplication_Returns_ViewWith_SubmissionModel()
        {
            // Arrange

            var clientId = "123";
            var applicationId = "1";
            List<string> blobs = ["blob1", "blob2"];
            List<CtdNodeModel> tree = new List<CtdNodeModel>() { new CtdNodeModel() { Id = 1, Name = "c2" } };
            string sequenceLocation = "location";
           _submissionBlobContainer.GetFullFilePath(null, clientId, applicationId).TrimEnd('/').Returns(sequenceLocation);
            _applicationService.GetApplication(int.Parse(applicationId)).Returns(appModel);
            _ectdViewer.BuildTree(sequenceLocation, blobs).ReturnsForAnyArgs(tree);
            //picklist
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));


            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
           //Act
            var result = await controller.ViewDossiersPerApplication(clientId,applicationId);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal("Viewer", viewResult.ViewName);
            Assert.NotNull(viewResult.Model);

        }
        #endregion
        #region ViewDossiersPerApplication_Returns_ViewWith_InvalidObject
        [Fact]
        public async Task ViewDossiersPerApplication_Returns_ViewWith_InvalidObject()
        {
            // Arrange

            var clientId = "123";
            var applicationId = "1";
            List<string> blobs = ["blob1", "blob2"];
            List<CtdNodeModel> tree = new List<CtdNodeModel>() { new CtdNodeModel() { Id = 1, Name = "c2" } };
            string sequenceLocation = "location";
            _submissionBlobContainer.GetFullFilePath(null, clientId, applicationId).TrimEnd('/').Returns(sequenceLocation);
            _applicationService.GetApplication(int.Parse(applicationId)).Returns(appModel);
            _ectdViewer.BuildTree(sequenceLocation, blobs).ReturnsForAnyArgs(tree);
            //picklist
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            //Act
            var result = await controller.ViewDossiersPerApplication(clientId, applicationId) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);

        }
        #endregion
        #region Tree_Returns_JsonResult
        [Fact]
        public async Task Tree_Returns_JsonResult()
        {
            // Arrange
            List<string> blobs = ["blob1", "blob2"];
            var clientId = "123";
            var applicationId = "1";
            var filename = "filename";
            string[] sub = ["sub1", "sub2"];
            string sequenceLocation = "location/path/file";
            _submissionBlobContainer.GetFullFilePath(filename, clientId, applicationId).Returns(sequenceLocation);
            
            List<CtdNodeModel> tree = new List<CtdNodeModel>() { new CtdNodeModel() { Id = 1, Name = "c2" } };
            
            _ectdViewer.BuildTree(sequenceLocation, blobs).ReturnsForAnyArgs(tree);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            //Act
            var result = await controller.Tree(filename,clientId, applicationId, sub, false);

            // Assert
            var viewResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(viewResult.Value);

        }
        #endregion
        #region Tree_Returns_JsonResultFor_Recursive path
        [Fact]
        public async Task Tree_Returns_JsonResultFor_Recursive()
        {
            // Arrange
            List<string> blobs = ["blob1", "blob2"];
            var clientId = "123";
            var applicationId = "1";
            var filename = "filename";
            string[] sub = ["sub1", "sub2"];
            string sequenceLocation = "location/path/file";
            _submissionBlobContainer.GetFullFilePath(filename, clientId, applicationId).Returns(sequenceLocation);

            List<CtdNodeModel> tree = new List<CtdNodeModel>() { new CtdNodeModel() { Id = 1, Name = "c2" } };

            _ectdViewer.BuildTree(sequenceLocation, blobs).ReturnsForAnyArgs(tree);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            //Act
            var result = await controller.Tree(filename, clientId, applicationId, sub, true);

            // Assert
            var viewResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(viewResult.Value);

        }
        #endregion
        #region Tree_Returns_TreeResult_When_File_Is_Null
        [Fact]
        public async Task Tree_Returns_TreeResult_When_File_Is_Null()
        {
            // Arrange
            List<string> blobs = ["blob1", "blob2"];
            var clientId = "123";
            var applicationId = "1";
            string[] sub = ["sub1", "sub2"];
           
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            //Act
            var result = await controller.Tree(null, clientId, applicationId, sub, false);

            // Assert
            var viewResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(viewResult.Value);

        }
        #endregion
        #region Tree_Returns_InvalidObject
        [Fact]
        public async Task Tree_Returns_InvalidObject()
        {
            // Arrange
            List<string> blobs = ["blob1", "blob2"];
            var clientId = "123";
            var applicationId = "1";
            var filename = "filename";
            string[] sub = ["sub1", "sub2"];
            string sequenceLocation = "location/path/file";
            _submissionBlobContainer.GetFullFilePath(filename, clientId, applicationId).Returns(sequenceLocation);
            List<CtdNodeModel> tree = new List<CtdNodeModel>() { new CtdNodeModel() { Id = 1, Name = "c2" } };
            _ectdViewer.BuildTree(sequenceLocation, blobs).ReturnsForAnyArgs(tree);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            //Act
            var result = await controller.Tree(filename, clientId, applicationId, sub, false) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);

        }
        #endregion
        #region DossierSequences_Returns_JsonResult
        [Fact]
        public async Task DossierSequences_Returns_JsonResult()
        {
            // Arrange
            var filename = "file";
            var clientId = "123";
            var seqLocation = "123";
            var applicationId = "1";
            List<string> seq=["c2","c3"];
            _submissionBlobContainer.GetFullFilePath(filename, clientId, applicationId).Returns(seqLocation);
            _submissionBlobContainer.GetSequences(seqLocation).Returns(seq);

            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            //Act
            var result = await controller.DossierSequences(filename,clientId, applicationId);

            // Assert
            var viewResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(viewResult.Value);
            Assert.IsType<List<string>>(viewResult.Value);

        }
        #endregion
        #region DossierSequences_Returns_InvalidObject
        [Fact]
        public async Task DossierSequences_Returns_InvalidObject()
        {
            // Arrange
            var filename = "file";
            var clientId = "123";
            var seqLocation = "123";
            var applicationId = "1";
            List<string> seq = ["c2", "c3"];
            _submissionBlobContainer.GetFullFilePath(filename, clientId, applicationId).Returns(seqLocation);
            _submissionBlobContainer.GetSequences(seqLocation).Returns(seq);
            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            //Act
            var result = await controller.DossierSequences(filename, clientId, applicationId) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);

        }
        #endregion
        #region DossierSequences_NullFile_Returns__JsonResult
        [Fact]
        public async Task DossierSequences_NullFile_Returns__JsonResult()
        {
            // Arrange
            var filename = "file";
            var clientId = "123";
            var seqLocation = "123";
            var applicationId = "1";
            List<string> seq = ["c2", "c3"];
            _submissionBlobContainer.GetFullFilePath(filename, clientId, applicationId).Returns(seqLocation);
            _submissionBlobContainer.GetSequences(seqLocation).Returns(seq);

            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            //Act
            var result = await controller.DossierSequences(null, clientId, applicationId);

            // Assert
            var viewResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(viewResult.Value);
            Assert.IsNotType<List<string>>(viewResult.Value);
           
        }
        #endregion
        #region ViewDossiersPerApplication_Returns_Error
        [Fact]
        public async Task ViewDossiersPerApplication_Returns_Error()
        {
            // Arrange

            var clientId = "123";
            var applicationId = "1";
            List<string> blobs = ["blob1", "blob2"];
            List<CtdNodeModel> tree = new List<CtdNodeModel>() { new CtdNodeModel() { Id = 1, Name = "c2" } };
            string sequenceLocation = "location";
            _submissionBlobContainer.GetFullFilePath(null, clientId, applicationId).TrimEnd('/').Returns(sequenceLocation);
            _applicationService.GetApplication(int.Parse(applicationId)).Returns(appModel);
            _ectdViewer.BuildTree(sequenceLocation, blobs).ReturnsForAnyArgs(Task.FromException<List<CtdNodeModel>>(new Exception()));
            //picklist
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));


            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            //Act
            var result = await controller.ViewDossiersPerApplication(clientId, applicationId);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal("Viewer", viewResult.ViewName);
            Assert.NotNull(viewResult.Model);
            var submissionsModel = viewResult.Model as SubmissionsModel;
            Assert.Equal("An error occurred. Could not read dossier.", submissionsModel?.Error);

        }
        #endregion
        #region ViewDossiersPerApplication_Returns_NotSupportedException
        [Fact]
        public async Task ViewDossiersPerApplication_Returns_NotSupportedException()
        {
            // Arrange

            var clientId = "123";
            var applicationId = "1";
            List<string> blobs = ["blob1", "blob2"];
            List<CtdNodeModel> tree = new List<CtdNodeModel>() { new CtdNodeModel() { Id = 1, Name = "c2" } };
            string sequenceLocation = "location";
            _submissionBlobContainer.GetFullFilePath(null, clientId, applicationId).TrimEnd('/').Returns(sequenceLocation);
            _applicationService.GetApplication(int.Parse(applicationId)).Returns(appModel);
            _ectdViewer.BuildTree(sequenceLocation, blobs).ReturnsForAnyArgs(Task.FromException<List<CtdNodeModel>>(new NotSupportedException()));
            //picklist
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));


            var controller = new SubmissionsController(_submissionBlobContainer, _submissionPubBlobContainer, _ectdViewer, _cache, _applicationService, _submissionService,  configuration);
            //Act
            var result = await controller.ViewDossiersPerApplication(clientId, applicationId);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
            Assert.Equal("Viewer", viewResult.ViewName);
            Assert.NotNull(viewResult.Model);
            var submissionsModel = viewResult.Model as SubmissionsModel;
            Assert.Equal("Specified method is not supported.", submissionsModel?.Error);

        }
        #endregion



    }
}
