﻿using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models
{
    public class SubmissionsModel : IModel
    {
        public string Error { get; set; }
        public string Sequence { get; set; }
        public bool Recursive { get; set; }
        public string ClientId { get; set; }
        public string ApplicationId { get; set; }
        public string SubmissionId { get; set; }
        public string DocumentTypeId { get; set; }
        public string Version { get; set; }
        public string Origin { get; set; }
        public SubmissionDetailsModel SubmissionDetails { get; set; }

        public IList<CtdNodeModel> Tree { get; set; } = new List<CtdNodeModel>();
    }
}
