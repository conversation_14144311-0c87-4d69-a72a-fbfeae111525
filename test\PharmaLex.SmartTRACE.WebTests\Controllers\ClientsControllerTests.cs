﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using PharmaLex.Caching;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class ClientsControllerTests
    {
        private IDistributedCacheServiceFactory _cache;
        private ClientsController _clientsController;
        private readonly IClientExport _clientExport;
        private IMapper _mapper;

        public ClientsControllerTests()
        {
            _mapper = Substitute.For<IMapper>();
            _cache = Substitute.For<IDistributedCacheServiceFactory>();
            _clientExport = Substitute.For<IClientExport>();
            _clientsController = new ClientsController(_cache, _mapper, _clientExport);
        }

        private static List<ClientModel> GetClientModel()
        {
            return new List<ClientModel>
            {
                new ClientModel { Id=1,ContractOwnerId=1, Name="client1"},
                new ClientModel { Id=2,ContractOwnerId=2, Name="client2"},
                new ClientModel { Id=3,ContractOwnerId=3, Name="client3"}
            };
        }
        #region Index
        [Fact]
        public async Task Index_ReturnsClientList()
        {
            // Arrange
            var mappedCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            var Clients = GetClientModel();
            _cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(mappedCache);
            mappedCache.AllAsync().Returns(Task.FromResult(Clients));
            PicklistHelper.ExtractPicklist(_cache, 2)
                .ReturnsForAnyArgs(Task.FromResult(new PicklistDataModel { Name = "client2" }));
            // Act
            var result = await _clientsController.Index();
            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsAssignableFrom<IEnumerable<ClientModel>>(viewResult.ViewData.Model);
            Assert.Equal(Clients, model);
        }
        #endregion

        #region NewClient HTTPGet
        [Fact]
        public async Task New_Returns()
        {
            // Arrange
            string ViewName = "EditClient";
            List<PicklistDataModel> lstPicklistDataModel = new List<PicklistDataModel>()
            {
                new PicklistDataModel { Name = "client2" }
            };
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.ContractOwner).ReturnsForAnyArgs(Task.FromResult(lstPicklistDataModel));
            // Act
            var result = await _clientsController.New() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(ViewName, result.ViewName);   
        }
        #endregion

        #region HTTPPOST-NewClient-Valid Data
        [Fact]
        public async Task New_Post_ValidData()
        {
            // Arrange
            var id = 1;
            var ResposeUrl = "/clients/edit/";
            var clientModel = new ClientModel { Name = "Test Client" };
            var projectModel = new ProjectModel { Name = "Test Project" };
            var model = new EditClientViewModel { Client = clientModel, Project = projectModel };

            var client = new Client { Id = 1, Name = "Test Client" };
            var project = new Project { Id = 1, Name = "Test Project", ClientId = 1 };
            var users = new User { Id = 1, AutoAccessClients = true };

            _mapper.Map<ClientModel, Client>(model.Client).Returns(client);
            _mapper.Map<ProjectModel, Project>(model.Project).Returns(project);
            List<User> userList = new List<User>
                {
                    new() { Id = 1, Email = "<EMAIL>" ,AutoAccessClients= true },
                    new() { Id = 1, Email = "<EMAIL>" ,AutoAccessClients= false},
                };

            var userCache = Substitute.For<ITrackedEntityCacheServiceProxy<User>>();
            _cache.CreateTrackedEntity<User>()
                .Configure(Arg.Any<Func<IIncludable<User>, IIncludable<User>>>()).Returns(userCache);
            userCache.WhereAsync(Arg.Any<Expression<Func<User, bool>>>()).Returns(userList);
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            _clientsController.TempData = tempData;

            // Act
            var result = await _clientsController.New(model);

            // Assert
            var viewResult = Assert.IsType<RedirectResult>(result);
            Assert.NotNull(viewResult);
            var resp = result as RedirectResult;
            Assert.Equal(resp?.Url, ResposeUrl + id);
        }
        #endregion

        #region HTTPPOST-NewClient-InValid Data
        [Fact]
        public async Task New_Post_InValidData()
        {
            // Arrange
            string ViewName = "EditClient";
            var clientModel = new ClientModel { Name = "Test Client" };
            var projectModel = new ProjectModel { Name = "Test Project" };
            var model = new EditClientViewModel { Client = clientModel, Project = projectModel };
            var client = new Client { Id = 1, Name = "Test Client" };
            var project = new Project { Id = 1, Name = "Test Project", ClientId = 1 };
            var users = new User { Id = 1, AutoAccessClients = true };
            _mapper.Map<ClientModel, Client>(model.Client).Returns(client);
            _mapper.Map<ProjectModel, Project>(model.Project).Returns(project);
            var userCache = Substitute.For<ITrackedEntityCacheServiceProxy<User>>();
            List<User> userList = new List<User>
                {
                    new() { Id = 1, Email = "<EMAIL>" ,AutoAccessClients= true },
                    new() { Id = 1, Email = "<EMAIL>" ,AutoAccessClients= false},
                };
            _cache.CreateTrackedEntity<User>()
                .Configure(Arg.Any<Func<IIncludable<User>, IIncludable<User>>>()).Returns(userCache);
            userCache.WhereAsync(Arg.Any<Expression<Func<User, bool>>>()).Returns(userList);
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            _clientsController.TempData = tempData;
            _clientsController.ModelState.AddModelError("SessionName", "Required");
            _clientsController.TempData = tempData;

            // Act
            var result = await _clientsController.New(model) as ViewResult;

            // Assert
            Assert.Equal(ViewName, result?.ViewName);

        }
        #endregion

        #region HTTPPOST-NewClient-CatchBlock
        [Fact]
        public async Task New_Post_CatchBlock()
        {
            // Arrange
            var clientModel = new ClientModel { Name = "Test Client" };
            var projectModel = new ProjectModel { Name = "Test Project" };
            var model = new EditClientViewModel { Client = clientModel, Project = projectModel };
            var client = new Client { Id = 1, Name = "Test Client" };
            var project = new Project { Id = 1, Name = "Test Project", ClientId = 1 };
            var users = new User { Id = 1, AutoAccessClients = true };
            _mapper.Map<ClientModel, Client>(model.Client).Returns(client);
            _mapper.Map<ProjectModel, Project>(model.Project).Returns(project);
            var userCache = Substitute.For<ITrackedEntityCacheServiceProxy<User>>();
            List<User> userList = new List<User>
                {
                    new() { Id = 1, Email = "<EMAIL>" ,AutoAccessClients= true },
                    new() { Id = 1, Email = "<EMAIL>" ,AutoAccessClients= false},
                };
            _cache.CreateTrackedEntity<User>()
                .Configure(Arg.Any<Func<IIncludable<User>, IIncludable<User>>>()).Returns(userCache);
            userCache.WhereAsync(Arg.Any<Expression<Func<User, bool>>>()).Returns(userList);
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            _clientsController.TempData = tempData;
            userCache.SaveChangesAsync().Returns(Task.FromException<int>(new DbUpdateException()));
            // Act
            var result = await _clientsController.New(model);
            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
            Assert.True(model.Client.HasError);
            Assert.Equal("EditClient", viewResult.ViewName);
        }
        #endregion

        #region EditGet
        [Fact]
        public async Task Edit_Get()
        {
            // Arrange
            string ViewName = "EditClient";
            var mappedCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            var Clients = GetClientModel();
            _cache.CreateMappedEntity<Client, ClientModel>().ReturnsForAnyArgs(mappedCache);
            mappedCache.AllAsync().Returns(Task.FromResult(Clients));
            // Act
            var result = await _clientsController.Edit(1) as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(ViewName, result.ViewName);
        }
        #endregion

        #region Edit_Get_InvalidModel
        [Fact]
        public async Task Edit_InvalidModel()
        {
            // Arrange
            int id = 10;
            _clientsController.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await _clientsController.Edit(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region HTTPPOST-Edit-Valid Data
        [Fact]
        public async Task Edit_Post_ValidData()
        {
            // Arrange
            var id = 1;
            string ViewName = "EditClient";

            var model1 = new EditClientViewModel
            {
                Client = new ClientModel { Name = "client1" }
            };
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            _clientsController.TempData = tempData;

            // Act
            var result = await _clientsController.Edit(id, model1) as ViewResult;

            // Assert
            Assert.Equal(ViewName, result?.ViewName);
        }
        #endregion

        #region HTTPPOST-Edit-InValid Data
        [Fact]
        public async Task Edit_Post_InValidData()
        {
            // Arrange
            var id = 1;
            string ViewName = "EditClient";

            var model1 = new EditClientViewModel
            {
                Client = new ClientModel { Name = "client1" }
            };
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            _clientsController.TempData = tempData;
            _clientsController.ModelState.AddModelError("SessionName", "Required");
            List<PicklistDataModel> lstPicklistDataModel = new List<PicklistDataModel>()
            {
                new PicklistDataModel { Name = "client2" }
            };
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.ContractOwner).ReturnsForAnyArgs(Task.FromResult(lstPicklistDataModel));

            // Act
            var result = await _clientsController.Edit(id, new EditClientViewModel()) as ViewResult;

            // Assert
            Assert.Equal(ViewName, result?.ViewName);
        }
        #endregion

        #region HTTPPOST-EDIT-Catch Block
        [Fact]
        public async Task Edit_Post_CatchBlock()
        {
            // Arrange
            var id = 1;
            string ViewName = "EditClient";

            var model1 = new EditClientViewModel
            {
                Client = new ClientModel { Name = "client1" }
            };
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            _clientsController.TempData = tempData;
            var objClientCache = Substitute.For<ITrackedEntityCacheServiceProxy<Client>>();
            _cache.CreateTrackedEntity<Client>().Returns(objClientCache);
            objClientCache.SaveChangesAsync().Returns(Task.FromException<int>(new DbUpdateException()));

            // Act
            var result = await _clientsController.Edit(id, model1) as ViewResult;

            // Assert
            Assert.Equal(ViewName, result?.ViewName);
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
            Assert.True(model1.Client.HasError);
            Assert.Equal("EditClient", viewResult.ViewName);
        }
        #endregion

        #region Export
        [Fact]
        public async Task Export_Returns_FileResult()
        {
            // Arrange
            var mockFileContent = new byte[] { 1, 2, 3 }; 
            var exportResultStream = new MemoryStream(mockFileContent);
            _clientExport.Export().Returns(Task.FromResult<byte[]>(mockFileContent));
            // Act
            var result = await _clientsController.Export() as FileContentResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", result.ContentType);
            Assert.NotNull(result.FileContents);
        }
        #endregion
    }
}
