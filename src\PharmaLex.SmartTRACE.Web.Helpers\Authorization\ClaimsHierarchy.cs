﻿using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public static class ClaimsHierarchy
    {
        public static Dictionary<string, List<string>> GetClaims()
        {
            return new Dictionary<string, List<string>>
                {
                    { "admin:UserAdmin", new List<string> { "admin:UserAdmin" } },
                    { "admin:SuperAdmin", new List<string> { "admin:SuperAdmin", "admin:UserAdmin", "admin:BusinessAdmin", "application:Editor", "application:Reader" } },
                    { "admin:BusinessAdmin", new List<string> { "admin:BusinessAdmin", "application:Editor", "application:Reader" } },
                    { "application:Editor", new List<string> { "application:Editor", "application:Reader" } },
                    { "application:ExternalEditor", new List<string> { "application:ExternalEditor", "application:Reader" } },
                    { "application:PrivilegedExternalEditor", new List<string> { "application:PrivilegedExternalEditor", "application:ExternalEditor", "application:Reader" } },
                    { "application:Reader", new List<string> { "application:Reader" } }
                };
        }

        public static IEnumerable<string> GetAllAdmins()
        {
            return GetClaims().Keys.Where(x => x.StartsWith("admin:"));
        }

        public static IEnumerable<string> GetAllEditors()
        {
            return GetClaims().Keys.Where(x => x != "application:Reader" && x != "application:ExternalEditor");
        }

        public static IEnumerable<string> GetAllReaders()
        {
            return GetClaims().Keys.Where(x => x != "admin:UserAdmin");
        }

        public static IEnumerable<string> GetAllExternalEditors()
        {
            return GetClaims().Keys.Where(x => x == "application:ExternalEditor");
        }
        public static IEnumerable<string> GetAllPrivilegedExternalEditor()
        {
            return GetClaims().Keys.Where(x => x == "application:PrivilegedExternalEditor");
        }
    }
}
