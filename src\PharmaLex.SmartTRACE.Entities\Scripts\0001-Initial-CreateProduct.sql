﻿CREATE TRIGGER [dbo].[Product_Insert] ON [dbo].[Product]
FOR INSERT AS
INSERT INTO [Audit].[Product_Audit]
SELECT 'I', [Id], [Name], [DosageFormId], [Strength], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Product_Update] ON [dbo].[Product]
FOR UPDATE AS
INSERT INTO [Audit].[Product_Audit]
SELECT 'U', [Id], [Name], [DosageFormId], [Strength], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Product_Delete] ON [dbo].[Product]
FOR DELETE AS
INSERT INTO [Audit].[Product_Audit]
SELECT 'D', [Id], [Name], [DosageFormId], [Strength], [ClientId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO