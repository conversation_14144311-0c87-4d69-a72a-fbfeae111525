﻿using Azure.ResourceManager;
using Azure.ResourceManager.DataFactory;
using Azure.ResourceManager.DataFactory.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using NSubstitute;
using PharmaLex.SmartTRACE.Web.DataFactory;
using System.Security.Claims;

namespace PharmaLex.SmartTRACE.WebTests.DataFactory
{
    public class AzureDataFactoryManagementClientTests
    {
        readonly IAzureDataFactoryManagementClient sut;
        readonly IDataFactoryAuthenticationPovider dataFactoryAuthenticationPovider;
        readonly IOptions<DataFactoryPipelineSettings> settings;
        readonly ArmClient client;
        public AzureDataFactoryManagementClientTests()
        {
            #region Fake Httpcontext with identity
            IHttpContextAccessor httpContextAccessor = new HttpContextAccessor();
            var context = new DefaultHttpContext();
            context.User = new ClaimsPrincipal(new ClaimsIdentity(new List<System.Security.Claims.Claim> { new System.Security.Claims.Claim("emails", "<EMAIL>") }));
            httpContextAccessor.HttpContext = context;
            #endregion

            #region Settings
            settings = Substitute.For<IOptions<DataFactoryPipelineSettings>>();
            settings.Value.Returns(new DataFactoryPipelineSettings
            {
                AllSequencesPath = "/all",
                DocumentsPath = "/documents",
                FactoryName = "test",
                Instance = "test",
                ResourceGroupName = "test",
                Subscription = "72c56c64-39f3-4fdd-86c7-2c323bfa8679",
                UnpackPath = "test",
                UnpackPipelineName = "test",
                UploadPath = "test"
            });
            dataFactoryAuthenticationPovider = Substitute.For<IDataFactoryAuthenticationPovider>();
            dataFactoryAuthenticationPovider.CreateClientCrdential().Returns(new Azure.Identity.DefaultAzureCredential());
            #endregion

            #region Fake Azure Datafactory
            //Fake azure response
            var azureResponse = Substitute.For<Azure.Response>();
            //Fake ResourceIdentifier
            var ri = DataFactoryResource.CreateResourceIdentifier("72c56c64-39f3-4fdd-86c7-2c323bfa8679", "test", "test");
            //Fake DataFactoryPipelineRunInfo
            var runInfoOk = ArmDataFactoryModelFactory.DataFactoryPipelineRunInfo(Guid.Parse("72c56c64-39f3-4fdd-86c7-2c323bfa8679"),status: "Succeeded");
            var runInfoFail = ArmDataFactoryModelFactory.DataFactoryPipelineRunInfo(Guid.Parse("9750fb76-dfdc-458a-91c6-703e8eb103ff"), status: "Failed");
            //Fake Pipeline Resource
            var pipelineResource = Substitute.For<DataFactoryPipelineResource>();
            var runResOk = Azure.Response.FromValue(ArmDataFactoryModelFactory.PipelineCreateRunResult(Guid.Parse("72c56c64-39f3-4fdd-86c7-2c323bfa8679")), azureResponse);
            var runResFail = Azure.Response.FromValue(ArmDataFactoryModelFactory.PipelineCreateRunResult(Guid.Parse("9750fb76-dfdc-458a-91c6-703e8eb103ff")), azureResponse);
            pipelineResource.CreateRunAsync(Arg.Is<IDictionary<string, BinaryData>>(x => x.ContainsKey("72c56c64-39f3-4fdd-86c7-2c323bfa8679"))).Returns(Task.FromResult(runResOk));
            pipelineResource.CreateRunAsync(Arg.Is<IDictionary<string, BinaryData>>(x => x.ContainsKey("9750fb76-dfdc-458a-91c6-703e8eb103ff"))).Returns(Task.FromResult(runResFail));
            //Fake DataFactoryResource
            var dfr = Substitute.For<DataFactoryResource>();
            dfr.GetDataFactoryPipelineAsync("okpipeline").ReturnsForAnyArgs(Task.FromResult(Azure.Response.FromValue(pipelineResource, azureResponse)));
            dfr.GetPipelineRunAsync("72c56c64-39f3-4fdd-86c7-2c323bfa8679").Returns(Task.FromResult(Azure.Response.FromValue(runInfoOk, azureResponse)));
            dfr.GetPipelineRunAsync("9750fb76-dfdc-458a-91c6-703e8eb103ff").Returns(Task.FromResult(Azure.Response.FromValue(runInfoFail, azureResponse)));
            //Fake ARM Client
            client = Substitute.For<ArmClient>();
            client.GetDataFactoryResource(ri).Returns(dfr);
            client.GetDataFactoryPipelineResource(ri).Returns(pipelineResource);
            #endregion

            sut = new AzureDataFactoryManagementClient(dataFactoryAuthenticationPovider, settings, httpContextAccessor);
        }

        #region Test Methods
        [Theory]
        [InlineData("72c56c64-39f3-4fdd-86c7-2c323bfa8679", true)]
        [InlineData("9750fb76-dfdc-458a-91c6-703e8eb103ff", false)]
        public async Task Check_RunPipelineAsync(string runid, bool expected)
        {
            IDictionary<string, object> paramValueSpecOk = new Dictionary<string, object>
            {
                { runid, "test"}
            };
            var actual = await sut.RunPipelineAsync(paramValueSpecOk, "okpipeline", client);
            Assert.Equal(expected, actual);
        }

        [Theory]
        [MemberData(nameof(PathTestData))]
        public void Check_GetUnpackPath(string zipFileName, string clientId, string applicationId, string submissionUniqueId, string documentTypeId, string version)
        {
            string expectedPath = GetExpectedPath(false, zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);
            string path = sut.GetUnpackPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);

            Assert.Equal(expectedPath, path);
        }

        [Theory]
        [MemberData(nameof(PathTestData))]
        public void Check_GetUploadPath(string zipFileName, string clientId, string applicationId, string submissionUniqueId, string documentTypeId, string version)
        {
            string expectedPath = GetExpectedPath(true, zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);
            string path = sut.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);

            Assert.Equal(expectedPath, path);
        }
        #endregion

        #region TestData
        public static TheoryData<string, string, string, string, string, string> PathTestData = new()
        {
            { "", "", "", "", "", "" },
            {"test.zip", "test", "test", "test", "test", "test" },
            {"", "test", "test", "", "", "" },
            {"", "test", "test", "test", "", "" }
        };
        private string GetExpectedPath(bool ifUpload, string zipFileName, string clientId, string applicationId, string submissionUniqueId, string documentTypeId, string version)
        {
            string unpackPath = "<EMAIL>";
            if (!string.IsNullOrEmpty(clientId) &&
                !string.IsNullOrEmpty(applicationId) &&
                !string.IsNullOrEmpty(submissionUniqueId) &&
            !string.IsNullOrEmpty(documentTypeId) &&
            !string.IsNullOrEmpty(version))
            {
                unpackPath = ifUpload ? 
                    $"{clientId}/{applicationId}/{submissionUniqueId}/{documentTypeId}/{version}" : 
                    $"{clientId}/{applicationId}/{this.settings.Value.DocumentsPath}/{submissionUniqueId}/{documentTypeId}/{version}";
            }
            else if (!ifUpload && !string.IsNullOrEmpty(clientId) &&
            !string.IsNullOrEmpty(applicationId) &&
                    string.IsNullOrEmpty(documentTypeId) &&
                    string.IsNullOrEmpty(version))
            {
                unpackPath = $"{clientId}/{applicationId}/{this.settings.Value.AllSequencesPath}";
            }
            else if (ifUpload && !string.IsNullOrEmpty(clientId) &&
                     !string.IsNullOrEmpty(applicationId) &&
                     !string.IsNullOrEmpty(submissionUniqueId) &&
                     string.IsNullOrEmpty(documentTypeId) &&
                     string.IsNullOrEmpty(version)) 
            {
                unpackPath = $"{clientId}/{applicationId}/{submissionUniqueId}";
            }
            if (!string.IsNullOrEmpty(zipFileName))
            {
                unpackPath = $"{unpackPath}/{zipFileName}";
            }
            return unpackPath;
        }
        #endregion
    }
}
