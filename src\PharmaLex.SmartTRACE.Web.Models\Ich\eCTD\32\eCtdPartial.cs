﻿using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32
{
    public partial class ectd : CtdSectionBase, IEctdModule
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m1administrativeinformationandprescribinginformation,
                    this.m2commontechnicaldocumentsummaries,
                    this.m3quality,
                    this.m4nonclinicalstudyreports,
                    this.m5clinicalstudyreports
                };

                return nodes.Where(x => x != null).ToList();
            }
        }

        public List<CtdSectionBase> Content
        {
            get
            {
                return this.ChildNodes;
            }
        }
    }
    public partial class m1administrativeinformationandprescribinginformation : CtdSectionBase
    {
        public override string CtdName => "Administrative Information and Prescribing Information";
    }

    public partial class m2commontechnicaldocumentsummaries : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                return new List<CtdSectionBase>
                {
                    this.m22introduction,
                    this.m23qualityoverallsummary,
                    this.m24nonclinicaloverview,
                    this.m25clinicaloverview,
                    this.m26nonclinicalwrittenandtabulatedsummaries,
                    this.m27clinicalsummary
                }.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m22introduction : CtdSectionBase
    {
    }
    public partial class nodeextension : CtdSectionBase, Inodeextension
    {
        public override string CtdName => this.title?.Value;
        public override string CtdModule => string.Empty;
        public override string CtdSection => string.Empty;
    }
    public partial class leaf : CtdSectionBase, Ileaf
    {
        public string op
        {
            get
            {
                string opr = operation.ToString();
                return $"{ char.ToUpper(opr[0]) }{ opr.Substring(1) }";
            }
        }

        public string text
        {
            get
            {
                return this.title.Value;
            }
        }

        public string Submission { get; set; }
        public string Path { get; set; }
    }
    public partial class m23qualityoverallsummary : CtdSectionBase
    {
        public override string CtdName => "Quality Overall Summary (QOS)";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m23introduction
                };
                if (this.m23sdrugsubstance != null)
                    nodes.AddRange(this.m23sdrugsubstance);
                if (this.m23pdrugproduct != null)
                    nodes.AddRange(this.m23pdrugproduct);
                nodes.Add(this.m23aappendices);
                nodes.Add(this.m23rregionalinformation);           

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m23introduction : CtdSectionBase
    {
    }
    public partial class m23sdrugsubstance : CtdSectionBase
    {
        public override string CtdName => "Drug Substance";
        public override string CtdSectionMetadata => $"- Drug Substance: {this.substance} - Manufacturer: {this.manufacturer}";
    }
    public partial class m23pdrugproduct : CtdSectionBase
    {
        public override string CtdName => "Drug Product";
        public override string CtdSectionMetadata => $"- Drug Product: {this.productname} - Dosage: {this.dosageform} - Manufacturer: {this.manufacturer}";
    }
    public partial class m23aappendices : CtdSectionBase
    {
    }
    public partial class m23rregionalinformation : CtdSectionBase
    {
        public override string CtdName => "Regional Information";
    }
    public partial class m24nonclinicaloverview : CtdSectionBase
    {
        public override string CtdName => "Nonclinical Overview";
    }
    public partial class m25clinicaloverview : CtdSectionBase
    {
        public override string CtdName => "Clinical Overview";
    }
    public partial class m26nonclinicalwrittenandtabulatedsummaries : CtdSectionBase
    {
        public override string CtdName => "Nonclinical Written and Tabulated Summaries";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m261introduction,
                    this.m262pharmacologywrittensummary,
                    this.m263pharmacologytabulatedsummary,
                    this.m264pharmacokineticswrittensummary,
                    this.m265pharmacokineticstabulatedsummary,
                    this.m266toxicologywrittensummary,
                    this.m267toxicologytabulatedsummary
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m261introduction : CtdSectionBase
    {
    }
    public partial class m262pharmacologywrittensummary : CtdSectionBase
    {
        public override string CtdName => "Pharmacology Written Summary";
    }
    public partial class m263pharmacologytabulatedsummary : CtdSectionBase
    {
        public override string CtdName => "Pharmacology Tabulated Summary";
    }
    public partial class m264pharmacokineticswrittensummary : CtdSectionBase
    {
        public override string CtdName => "Pharmacokinetics Written Summary";
    }
    public partial class m265pharmacokineticstabulatedsummary : CtdSectionBase
    {
        public override string CtdName => "Pharmacokinetics Tabulated Summary";
    }
    public partial class m266toxicologywrittensummary : CtdSectionBase
    {
        public override string CtdName => "Toxicology Written Summary";
    }
    public partial class m267toxicologytabulatedsummary : CtdSectionBase
    {
        public override string CtdName => "Toxicology Tabulated Summary";
    }
    public partial class m27clinicalsummary : CtdSectionBase
    {
        public override string CtdName => "Clinical Summary";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m271summaryofbiopharmaceuticstudiesandassociatedanalyticalmethods,
                    this.m272summaryofclinicalpharmacologystudies,
                    this.m274summaryofclinicalsafety,
                    this.m275literaturereferences,
                    this.m276synopsesofindividualstudies
                };
                if (this.m273summaryofclinicalefficacy != null)
                    nodes.AddRange(this.m273summaryofclinicalefficacy);

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m271summaryofbiopharmaceuticstudiesandassociatedanalyticalmethods : CtdSectionBase
    {
        public override string CtdName => "Summary of Biopharmaceutic Studies and Associated Analytical Methods";
    }
    public partial class m272summaryofclinicalpharmacologystudies : CtdSectionBase
    {
        public override string CtdName => "Summary of Clinical Pharmacology Studies";
    }
    public partial class m273summaryofclinicalefficacy : CtdSectionBase
    {
        public override string CtdName => "Summary of Clinical Efficacy";
        public override string CtdSectionMetadata => $"- Indication: {this.indication}";
    }
    public partial class m274summaryofclinicalsafety : CtdSectionBase
    {
        public override string CtdName => "Summary of Clinical Safety";
    }
    public partial class m275literaturereferences : CtdSectionBase
    {
        public override string CtdName => "Literature References";
    }
    public partial class m276synopsesofindividualstudies : CtdSectionBase
    {
        public override string CtdName => "Synopses of Individual Studies";
    }
    public partial class m3quality : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                return new List<CtdSectionBase>
                {
                    this.m32bodyofdata,
                    this.m33literaturereferences,
                }.Where(x => x != null).ToList(); 
            }
        }
    }
    public partial class m32bodyofdata : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m32sdrugsubstance != null)
                    nodes.AddRange(this.m32sdrugsubstance);
                if (this.m32pdrugproduct != null)
                    nodes.AddRange(this.m32pdrugproduct);
                nodes.Add(this.m32aappendices);
                nodes.Add(this.m32rregionalinformation);

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32sdrugsubstance : CtdSectionBase
    {
        public override string CtdName => "Drug Substance";
        public override string CtdSectionMetadata
        {
            get
            {
                return $"- Drug Substance: {this.substance} - Manufacturer: {this.manufacturer}";
            }
        }
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32s1generalinformation,
                    this.m32s2manufacture,
                    this.m32s3characterisation,
                    this.m32s4controlofdrugsubstance,
                    this.m32s5referencestandardsormaterials,
                    this.m32s6containerclosuresystem,
                    this.m32s7stability,
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32s1generalinformation : CtdSectionBase
    {
        public override string CtdName => "General Information";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32s11nomenclature,
                    this.m32s12structure,
                    this.m32s13generalproperties
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32s11nomenclature : CtdSectionBase
    {
    }
    public partial class m32s12structure : CtdSectionBase
    {
    }
    public partial class m32s13generalproperties : CtdSectionBase
    {
        public override string CtdName => "General Properties";
    }
    public partial class m32s2manufacture : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32s21manufacturer,
                    this.m32s22descriptionofmanufacturingprocessandprocesscontrols,
                    this.m32s23controlofmaterials,
                    this.m32s24controlsofcriticalstepsandintermediates,
                    this.m32s25processvalidationandorevaluation,
                    this.m32s26manufacturingprocessdevelopment

                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32s21manufacturer : CtdSectionBase
    {
        public override string CtdName => "Manufacturer(s)";
    }
    public partial class m32s22descriptionofmanufacturingprocessandprocesscontrols : CtdSectionBase
    {
        public override string CtdName => "Description of Manufacturing Process and Process Controls";
    }
    public partial class m32s23controlofmaterials : CtdSectionBase
    {
        public override string CtdName => "Control of Materials";
    }
    public partial class m32s24controlsofcriticalstepsandintermediates : CtdSectionBase
    {
        public override string CtdName => "Controls of Critical Steps and Intermediates";
    }
    public partial class m32s25processvalidationandorevaluation : CtdSectionBase
    {
        public override string CtdName => "Process Validation and/or Evaluation";
    }
    public partial class m32s26manufacturingprocessdevelopment : CtdSectionBase
    {
        public override string CtdName => "Manufacturing Process Development";
    }
    public partial class m32s3characterisation : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32s31elucidationofstructureandothercharacteristics,
                    this.m32s32impurities
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32s31elucidationofstructureandothercharacteristics : CtdSectionBase
    {
        public override string CtdName => "Elucidation of Structure and other Characteristics";
    }
    public partial class m32s32impurities : CtdSectionBase
    {
    }
    public partial class m32s4controlofdrugsubstance : CtdSectionBase
    {
        public override string CtdName => "Control of Drug Substance";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32s41specification,
                    this.m32s42analyticalprocedures,
                    this.m32s43validationofanalyticalprocedures,
                    this.m32s44batchanalyses,
                    this.m32s45justificationofspecification
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32s41specification : CtdSectionBase
    {
    }
    public partial class m32s42analyticalprocedures : CtdSectionBase
    {
        public override string CtdName => "Analytical Procedures";
    }
    public partial class m32s43validationofanalyticalprocedures : CtdSectionBase
    {
        public override string CtdName => "Validation of Analytical Procedures";
    }
    public partial class m32s44batchanalyses : CtdSectionBase
    {
        public override string CtdName => "Batch Analyses";
    }
    public partial class m32s45justificationofspecification : CtdSectionBase
    {
        public override string CtdName => "Justification of Specification";
    }
    public partial class m32s5referencestandardsormaterials : CtdSectionBase
    {
        public override string CtdName => "Reference Standards or Materials";
    }
    public partial class m32s6containerclosuresystem : CtdSectionBase
    {
        public override string CtdName => "Container Closure System";
    }
    public partial class m32s7stability : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32s71stabilitysummaryandconclusions,
                    this.m32s72postapprovalstabilityprotocolandstabilitycommitment,
                    this.m32s73stabilitydata
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32s71stabilitysummaryandconclusions : CtdSectionBase
    {
        public override string CtdName => "Stability Summary and Conclusions";
    }
    public partial class m32s72postapprovalstabilityprotocolandstabilitycommitment : CtdSectionBase
    {
        public override string CtdName => "Post-approval Stability Protocol and Stability Commitment";
    }
    public partial class m32s73stabilitydata : CtdSectionBase
    {
        public override string CtdName => "Stability Data";
    }
    public partial class m32pdrugproduct : CtdSectionBase
    {
        public override string CtdName => "Drug Product";
        public override string CtdSectionMetadata
        {
            get
            {
                return $"- Drug Product: {this.productname} - Dosage: {this.dosageform} - Manufacturer: {this.manufacturer}";
            }
        }
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32p1descriptionandcompositionofthedrugproduct,
                    this.m32p2pharmaceuticaldevelopment,
                    this.m32p3manufacture,
                    this.m32p5controlofdrugproduct,
                    this.m32p6referencestandardsormaterials,
                    this.m32p7containerclosuresystem,
                    this.m32p8stability
                };

                if (this.m32p4controlofexcipients != null)
                {
                    foreach (var excipient in this.m32p4controlofexcipients)
                    {
                        excipient.manufacturer = this.manufacturer;
                        excipient.dosageform = this.dosageform;
                        excipient.productname = this.productname;

                    }
                    nodes.AddRange(this.m32p4controlofexcipients);
                } 

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32p1descriptionandcompositionofthedrugproduct : CtdSectionBase
    {
        public override string CtdName => "Description and Composition of the Drug Product";
    }
    public partial class m32p2pharmaceuticaldevelopment : CtdSectionBase
    {
        public override string CtdName => "Pharmaceutical Development";
    }
    public partial class m32p3manufacture : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32p31manufacturers,
                    this.m32p32batchformula,
                    this.m32p33descriptionofmanufacturingprocessandprocesscontrols,
                    this.m32p34controlsofcriticalstepsandintermediates,
                    this.m32p35processvalidationandorevaluation
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32p31manufacturers : CtdSectionBase
    {
        public override string CtdName => "Manufacturer(s)";
    }
    public partial class m32p32batchformula : CtdSectionBase
    {
        public override string CtdName => "Batch Formula";
    }
    public partial class m32p33descriptionofmanufacturingprocessandprocesscontrols : CtdSectionBase
    {
        public override string CtdName => "Description of Manufacturing Process and Process Controls";
    }
    public partial class m32p34controlsofcriticalstepsandintermediates : CtdSectionBase
    {
        public override string CtdName => "Controls of Critical Steps and Intermediates";
    }
    public partial class m32p35processvalidationandorevaluation : CtdSectionBase
    {
        public override string CtdName => "Process Validation and/or Evaluation";
    }
    public partial class m32p4controlofexcipients : CtdSectionBase
    {
        public override string CtdName => "Control of Excipients";
        public override string CtdSectionMetadata => $"- Excipient: {this.excipient} - Drug Product: {this.productname} - Dosage: {this.dosageform} - Manufacturer: {this.manufacturer}";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32p41specifications,
                    this.m32p42analyticalprocedures,
                    this.m32p43validationofanalyticalprocedures,
                    this.m32p44justificationofspecifications,
                    this.m32p45excipientsofhumanoranimalorigin,
                    this.m32p46novelexcipients
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32p41specifications : CtdSectionBase
    {
    }
    public partial class m32p42analyticalprocedures : CtdSectionBase
    {
        public override string CtdName => "Analytical Procedures";
    }
    public partial class m32p43validationofanalyticalprocedures : CtdSectionBase
    {
        public override string CtdName => "Validation of Analytical Procedures";
    }
    public partial class m32p44justificationofspecifications : CtdSectionBase
    {
        public override string CtdName => "Justification of Specifications";
    }
    public partial class m32p45excipientsofhumanoranimalorigin : CtdSectionBase
    {
        public override string CtdName => "Excipients of Human or Animal Origin";
    }
    public partial class m32p46novelexcipients : CtdSectionBase
    {
        public override string CtdName => "Novel Excipients";
    }
    public partial class m32p5controlofdrugproduct : CtdSectionBase
    {
        public override string CtdName => "Control of Drug Product";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32p51specifications,
                    this.m32p52analyticalprocedures,
                    this.m32p53validationofanalyticalprocedures,
                    this.m32p54batchanalyses,
                    this.m32p55characterisationofimpurities,
                    this.m32p56justificationofspecifications
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32p51specifications : CtdSectionBase
    {
    }
    public partial class m32p52analyticalprocedures : CtdSectionBase
    {
        public override string CtdName => "Analytical Procedures";
    }
    public partial class m32p53validationofanalyticalprocedures : CtdSectionBase
    {
        public override string CtdName => "Validation of Analytical Procedures";
    }
    public partial class m32p54batchanalyses : CtdSectionBase
    {
        public override string CtdName => "Batch Analyses";
    }
    public partial class m32p55characterisationofimpurities : CtdSectionBase
    {
        public override string CtdName => "Characterisation of Impurities";
    }
    public partial class m32p56justificationofspecifications : CtdSectionBase
    {
        public override string CtdName => "Justification of Specification(s)";
    }
    public partial class m32p6referencestandardsormaterials : CtdSectionBase
    {
        public override string CtdName => "Reference Standards or Materials";
    }
    public partial class m32p7containerclosuresystem : CtdSectionBase
    {
        public override string CtdName => "Container Closure System";
    }
    public partial class m32p8stability : CtdSectionBase
    {    
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32p81stabilitysummaryandconclusion,
                    this.m32p82postapprovalstabilityprotocolandstabilitycommitment,
                    this.m32p83stabilitydata
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32p81stabilitysummaryandconclusion : CtdSectionBase
    {
        public override string CtdName => "Stability Summary and Conclusions";
    }
    public partial class m32p82postapprovalstabilityprotocolandstabilitycommitment : CtdSectionBase
    {
        public override string CtdName => "Post-approval Stability Protocol and Stability Commitment";
    }
    public partial class m32p83stabilitydata : CtdSectionBase
    {
        public override string CtdName => "Stability Data";
    }
    public partial class m32aappendices : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m32a3excipients
                };

                if (this.m32a1facilitiesandequipment != null)
                    nodes.AddRange(this.m32a1facilitiesandequipment);

                if (this.m32a2adventitiousagentssafetyevaluation != null)
                    nodes.AddRange(this.m32a2adventitiousagentssafetyevaluation);

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m32a1facilitiesandequipment : CtdSectionBase
    {
        public override string CtdName => "Facilities and Equipment";
        public override string CtdSectionMetadata
        {
            get
            {
                return $"- Manufacture: {this.manufacturer} - Substance: {this.substance} - Product Name: {this.productname} - Dosage Form: {this.dosageform}";
            }
        }
    }
    public partial class m32a2adventitiousagentssafetyevaluation : CtdSectionBase
    {
        public override string CtdName => "Adventitious Agents Safety Evaluation";
        public override string CtdSectionMetadata => $"- Manufacture: {this.manufacturer} - Substance: {this.substance} - Product Name: {this.productname} - Dosage Form: {this.dosageform}";
    }
    public partial class m32a3excipients : CtdSectionBase
    {
    }
    public partial class m32rregionalinformation : CtdSectionBase
    {
        public override string CtdName => "Regional Information";
    }
    public partial class m33literaturereferences : CtdSectionBase
    {
        public override string CtdName => "Literature References";
    }
    public partial class m4nonclinicalstudyreports : CtdSectionBase
    {
        public override string CtdName => "Nonclinical Study Reports";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m42studyreports,
                    this.m43literaturereferences
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m42studyreports : CtdSectionBase
    {
        public override string CtdName => "Study Reportss";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m421pharmacology,
                    this.m422pharmacokinetics,
                    this.m423toxicology
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m421pharmacology : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m4211primarypharmacodynamics,
                    this.m4212secondarypharmacodynamics,
                    this.m4213safetypharmacology,
                    this.m4214pharmacodynamicdruginteractions
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m4211primarypharmacodynamics : CtdSectionBase
    {
        public override string CtdName => "Primary Pharmacodynamics";
    }
    public partial class m4212secondarypharmacodynamics : CtdSectionBase
    {
        public override string CtdName => "Secondary Pharmacodynamics";
    }
    public partial class m4213safetypharmacology : CtdSectionBase
    {
        public override string CtdName => "Safety Pharmacology";
    }
    public partial class m4214pharmacodynamicdruginteractions : CtdSectionBase
    {
        public override string CtdName => "Pharmacodynamic Drug Interactions";
    }
    public partial class m422pharmacokinetics : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m4221analyticalmethodsandvalidationreports,
                    this.m4222absorption,
                    this.m4223distribution,
                    this.m4224metabolism,
                    this.m4225excretion,
                    this.m4226pharmacokineticdruginteractions,
                    this.m4227otherpharmacokineticstudies
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m4221analyticalmethodsandvalidationreports : CtdSectionBase
    {
        public override string CtdName => "Analytical Methods and Validation Reports";
    }
    public partial class m4222absorption : CtdSectionBase
    {
    }
    public partial class m4223distribution : CtdSectionBase
    {
    }
    public partial class m4224metabolism : CtdSectionBase
    {
    }
    public partial class m4225excretion : CtdSectionBase
    {
    }
    public partial class m4226pharmacokineticdruginteractions : CtdSectionBase
    {
        public override string CtdName => "Pharmacokinetic Drug Interactions (nonclinical)";
    }
    public partial class m4227otherpharmacokineticstudies : CtdSectionBase
    {
        public override string CtdName => "Other Pharmacokinetic Studies";
    }
    public partial class m423toxicology : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m4231singledosetoxicity,
                    this.m4232repeatdosetoxicity,
                    this.m4233genotoxicity,
                    this.m4234carcinogenicity,
                    this.m4235reproductiveanddevelopmentaltoxicity,
                    this.m4236localtolerance,
                    this.m4237othertoxicitystudies
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m4231singledosetoxicity : CtdSectionBase
    {
        public override string CtdName => "Single-Dose Toxicity (in order by species, by route)";
    }
    public partial class m4232repeatdosetoxicity : CtdSectionBase
    {
        public override string CtdName => "Repeat-Dose Toxicity (in order by species, by route, by duration; including supportive toxicokinetics evaluations)";
    }
    public partial class m4233genotoxicity : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m42331invitro,
                    this.m42332invivo
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m42331invitro : CtdSectionBase
    {
    }
    public partial class m42332invivo : CtdSectionBase
    {
        public override string CtdName => "In vivo (including supportive toxicokinetics evaluations)";
    }
    public partial class m4234carcinogenicity : CtdSectionBase
    {
        public override string CtdName => "Carcinogenicity (including supportive toxicokinetics evaluations)";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m42341longtermstudies,
                    this.m42342shortormediumtermstudies,
                    this.m42343otherstudies
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m42341longtermstudies : CtdSectionBase
    {
        public override string CtdName => "Long-term studies (in order by species; including range-finding studies that cannot appropriately be included under repeat-dose toxicity or pharmacokinetics)";
    }
    public partial class m42342shortormediumtermstudies : CtdSectionBase
    {
        public override string CtdName => "Short- or medium-term studies (including range-finding studies that cannot appropriately be included under repeat-dose toxicity or pharmacokinetics)";
    }
    public partial class m42343otherstudies : CtdSectionBase
    {
    }
    public partial class m4235reproductiveanddevelopmentaltoxicity : CtdSectionBase
    {
        public override string CtdName => "Reproductive and Developmental Toxicity (including range-finding studies and supportive toxicokinetics evaluations) (If modified study designs are used, the following sub - headings should be modified accordingly.)";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m42351fertilityandearlyembryonicdevelopment,
                    this.m42352embryofetaldevelopment,
                    this.m42353prenatalandpostnataldevelopmentincludingmaternalfunction,
                    this.m42354studiesinwhichtheoffspringjuvenileanimalsaredosedandorfurtherevaluated
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m42351fertilityandearlyembryonicdevelopment : CtdSectionBase
    {
        public override string CtdName => "Fertility and early embryonic development";
    }
    public partial class m42352embryofetaldevelopment : CtdSectionBase
    {
        public override string CtdName => "Embryo-fetal development";
    }
    public partial class m42353prenatalandpostnataldevelopmentincludingmaternalfunction : CtdSectionBase
    {
        public override string CtdName => "Prenatal and postnatal development, including maternal function";
    }
    public partial class m42354studiesinwhichtheoffspringjuvenileanimalsaredosedandorfurtherevaluated : CtdSectionBase
    {
        public override string CtdName => "Studies in which the offspring (juvenile animals) are dosed and/or further evaluated";
    }
    public partial class m4236localtolerance : CtdSectionBase
    {
        public override string CtdName => "Local Tolerance";
    }
    public partial class m4237othertoxicitystudies : CtdSectionBase
    {
        public override string CtdName => "Other Toxicity Studies";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m42371antigenicity,
                    this.m42372immunotoxicity,
                    this.m42373mechanisticstudies,
                    this.m42374dependence,
                    this.m42375metabolites,
                    this.m42376impurities,
                    this.m42377other
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m42371antigenicity : CtdSectionBase
    {
    }
    public partial class m42372immunotoxicity : CtdSectionBase
    {
    }
    public partial class m42373mechanisticstudies : CtdSectionBase
    {
        public override string CtdName => "Mechanistic studies (if not included elsewhere)";
    }
    public partial class m42374dependence : CtdSectionBase
    {
    }
    public partial class m42375metabolites : CtdSectionBase
    {
    }
    public partial class m42376impurities : CtdSectionBase
    {
    }
    public partial class m42377other : CtdSectionBase
    {
    }
    public partial class m43literaturereferences : CtdSectionBase
    {
    }
    public partial class m5clinicalstudyreports : CtdSectionBase
    {
        public override string CtdName => "Clinical Study Reports";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m52tabularlistingofallclinicalstudies,
                    this.m53clinicalstudyreports,
                    this.m54literaturereferences
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m52tabularlistingofallclinicalstudies : CtdSectionBase
    {
        public override string CtdName => "Tabular Listing of All Clinical Studies";
    }
    public partial class m53clinicalstudyreports : CtdSectionBase
    {
        public override string CtdName => "Clinical Study Reports";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m531reportsofbiopharmaceuticstudies,
                    this.m532reportsofstudiespertinenttopharmacokineticsusinghumanbiomaterials,
                    this.m533reportsofhumanpharmacokineticspkstudies,
                    this.m534reportsofhumanpharmacodynamicspdstudies,
                    this.m536reportsofpostmarketingexperience,
                    this.m537casereportformsandindividualpatientlistings
                };

                if (this.m535reportsofefficacyandsafetystudies != null)
                    nodes.AddRange(this.m535reportsofefficacyandsafetystudies);

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m531reportsofbiopharmaceuticstudies : CtdSectionBase
    {
        public override string CtdName => "Reports of Biopharmaceutic Studies";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m5311bioavailabilitystudyreports,
                    this.m5312comparativebaandbioequivalencestudyreports,
                    this.m5313invitroinvivocorrelationstudyreports,
                    this.m5314reportsofbioanalyticalandanalyticalmethodsforhumanstudies
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m5311bioavailabilitystudyreports : CtdSectionBase
    {
        public override string CtdName => "Bioavailability (BA) Study Reports";
    }
    public partial class m5312comparativebaandbioequivalencestudyreports : CtdSectionBase
    {
        public override string CtdName => "Comparative BA and Bioequivalence (BE) Study Reports";
    }
    public partial class m5313invitroinvivocorrelationstudyreports : CtdSectionBase
    {
        public override string CtdName => "In Vitro – In Vivo Correlation Study Reports";
    }
    public partial class m5314reportsofbioanalyticalandanalyticalmethodsforhumanstudies : CtdSectionBase
    {
        public override string CtdName => "Reports of Bioanalytical and Analytical Methods for Human Studies";
    }
    public partial class m532reportsofstudiespertinenttopharmacokineticsusinghumanbiomaterials : CtdSectionBase
    {
        public override string CtdName => "Reports of Studies Pertinent to Pharmacokinetics Using Human Biomaterials";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m5321plasmaproteinbindingstudyreports,
                    this.m5322reportsofhepaticmetabolismanddruginteractionstudies,
                    this.m5323reportsofstudiesusingotherhumanbiomaterials
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m5321plasmaproteinbindingstudyreports : CtdSectionBase
    {
        public override string CtdName => "Plasma Protein Binding Study Reports";
    }
    public partial class m5322reportsofhepaticmetabolismanddruginteractionstudies : CtdSectionBase
    {
        public override string CtdName => "Reports of Hepatic Metabolism and Drug Interaction Studies";
    }
    public partial class m5323reportsofstudiesusingotherhumanbiomaterials : CtdSectionBase
    {
        public override string CtdName => "Reports of Studies Using Other Human Biomaterials";
    }
    public partial class m533reportsofhumanpharmacokineticspkstudies : CtdSectionBase
    {
        public override string CtdName => "Reports of Human Pharmacokinetic (PK) Studies";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m5331healthysubjectpkandinitialtolerabilitystudyreports,
                    this.m5332patientpkandinitialtolerabilitystudyreports,
                    this.m5333intrinsicfactorpkstudyreports,
                    this.m5334extrinsicfactorpkstudyreports,
                    this.m5335populationpkstudyreports
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m5331healthysubjectpkandinitialtolerabilitystudyreports : CtdSectionBase
    {
        public override string CtdName => "Healthy Subject PK and Initial Tolerability Study Reports";
    }
    public partial class m5332patientpkandinitialtolerabilitystudyreports : CtdSectionBase
    {
        public override string CtdName => "Patient PK and Initial Tolerability Study Reports";
    }
    public partial class m5333intrinsicfactorpkstudyreports : CtdSectionBase
    {
        public override string CtdName => "Intrinsic Factor PK Study Reports";
    }
    public partial class m5334extrinsicfactorpkstudyreports : CtdSectionBase
    {
        public override string CtdName => "Extrinsic Factor PK Study Reports";
    }
    public partial class m5335populationpkstudyreports : CtdSectionBase
    {
        public override string CtdName => "Population PK Study Reports";
    }
    public partial class m534reportsofhumanpharmacodynamicspdstudies : CtdSectionBase
    {
        public override string CtdName => "Reports of Human Pharmacodynamic (PD) Studies";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m5341healthysubjectpdandpkpdstudyreports,
                    this.m5342patientpdandpkpdstudyreports
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m5341healthysubjectpdandpkpdstudyreports : CtdSectionBase
    {
        public override string CtdName => "Healthy Subject PD and PK/PD Study Reports";
    }
    public partial class m5342patientpdandpkpdstudyreports : CtdSectionBase
    {
        public override string CtdName => "Patient PD and PK/PD Study Reports";
    }
    public partial class m535reportsofefficacyandsafetystudies : CtdSectionBase
    {
        public override string CtdName => "Reports of Efficacy and Safety Studies";
        public override string CtdSectionMetadata => $"- Indication: {this.indication}";

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m5351studyreportsofcontrolledclinicalstudiespertinenttotheclaimedindication,
                    this.m5352studyreportsofuncontrolledclinicalstudies,
                    this.m5353reportsofanalysesofdatafrommorethanonestudy,
                    this.m5354otherstudyreports
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m5351studyreportsofcontrolledclinicalstudiespertinenttotheclaimedindication : CtdSectionBase
    {
        public override string CtdName => "Study Reports of Controlled Clinical Studies Pertinent to the Claimed Indication";
    }
    public partial class m5352studyreportsofuncontrolledclinicalstudies : CtdSectionBase
    {
        public override string CtdName => "Study Reports of Uncontrolled Clinical Studies";
    }
    public partial class m5353reportsofanalysesofdatafrommorethanonestudy : CtdSectionBase
    {
        public override string CtdName => "Reports of Analyses of Data from More than One Study";
    }
    public partial class m5354otherstudyreports : CtdSectionBase
    {
        public override string CtdName => "Other Study Reports";
    }
    public partial class m536reportsofpostmarketingexperience : CtdSectionBase
    {
        public override string CtdName => "Reports of Post-Marketing Experience";
    }
    public partial class m537casereportformsandindividualpatientlistings : CtdSectionBase
    {
        public override string CtdName => "Case Report Forms and Individual Patient Listings";
    }
    public partial class m54literaturereferences : CtdSectionBase
    {
        public override string CtdName => "Literature References";
    }
}
