﻿<script type="text/x-template" id="export-submissions-template">
    <div class="dialog-surface" v-if="!closed" v-on:click="close">
        <div class="dialog-container" v-on:click.stop style="display: block; width: 85%; height: 65%; text-align: center">
            <i class="icon-cancel-circled dialog-closer" v-on:click="close"></i>
            <span class="submission-export-title">{{this.config.message}}</span>
            <div style="margin-top:20px;">
                <span class="field-validation-error" v-if="!showExport">{{this.config.errorMessage}}</span>
                <div class="metric-cell">
                    <label class="switch-container">
                        <input type="checkbox" id="all" v-on:change="selectAll($event)" class="switch" />
                        <label for="all" class="switch"></label>
                        Select All
                    </label>
                </div>
                <div id="columns" class="submission-export-container">
                    <div class="submission-export-column">
                        <div class="submission-column-title">General</div>
                        <template v-for="(c, i) in this.config.generalTabColumns">
                            <div class="metric-cell">
                                <label class="switch-container">
                                    <input type="checkbox" :id="`$generalTabColumn-${i}`" v-on:change="onCheckedColumn('generalColumn', c)" class="switch" />
                                    <label :for="`$generalTabColumn-${i}`" class="switch"></label>
                                    {{c}}
                                </label>
                            </div>
                        </template>
                    </div>

                    <div class="submission-export-column">
                        <div class="submission-column-title">Client Details</div>
                        <template class="submission-export-checkbox" v-for="(c, i) in this.config.clientDetailsTabColumns">
                            <div class="metric-cell">
                                <label class="switch-container">
                                    <input type="checkbox" :id="`$clientDetailsColumn-${i}`" class="switch" v-on:change="onCheckedColumn('clientDetailsColumn', c)" />
                                    <label :for="`$clientDetailsColumn-${i}`" class="switch"></label>
                                    {{c}}
                                </label>
                            </div>
                        </template>
                    </div>

                    <div class="submission-export-column">
                        <div class="submission-column-title">Dates</div>
                        <template class="submission-export-checkbox" v-for="(c, i) in this.config.datesTabColumns">
                            <div class="metric-cell">
                                <label class="switch-container">
                                    <input type="checkbox" :id="`$datesColumn-${i}`" class="switch" v-on:change="onCheckedColumn('datesColumn', c)" />
                                    <label :for="`$datesColumn-${i}`" class="switch"></label>
                                    {{c}}
                                </label>
                            </div>
                        </template>
                    </div>

                    <div class="submission-export-column">
                        <div class="submission-column-title">Resources</div>
                        <template class="submission-export-checkbox" v-for="(c, i) in this.config.resourcesTabColumns">
                            <div class="metric-cell">
                                <label class="switch-container">
                                    <input type="checkbox" :id="`resourcesColumn-${i}`" class="switch" v-on:change="onCheckedColumn('resourcesColumn', c)" />
                                    <label :for="`resourcesColumn-${i}`" class="switch"></label>
                                    {{c}}
                                </label>
                            </div>
                        </template>
                    </div>

                    <div class="submission-export-column">
                        <div class="submission-column-title">Document/dossier</div>
                        <template class="submission-export-checkbox" v-for="(c, i) in this.config.dossierTabColumns">
                            <div class="metric-cell">
                                <label class="switch-container">
                                    <input type="checkbox" :id="`documentColumn-${i}`" class="switch" v-on:change="onCheckedColumn('documentColumn', c)" />
                                    <label :for="`documentColumn-${i}`" class="switch"></label>
                                    {{c}}
                                </label>
                            </div>
                        </template>
                    </div>
                </div>
                <div class="submission-export-buttons">
                    <button v-on:click="close" class="button secondary icon-button-cancel">{{this.config.noButton}}</button>
                    <button v-on:click="submit" :class="this.config.buttonClass" v-show="showExport">{{this.config.yesButton}}</button>
                </div>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
    vueApp.component('export-submissions', {
        template: '#export-submissions-template',
        data() {
            return {
                closed: true,
                pressedYesButton: false,
                showExport: false
            };
        },
        props: {
            config: {
                type: Object,
                default: {}
            }
        },
        methods: {
            init() {
                let inputColumns = Array.from(document.getElementById("columns").getElementsByTagName("input"));

                this.config.selectedColumns.forEach(col => {
                    inputColumns.find(x => x.labels[0].innerText == col.columnName).checked = true;
                });

                if (inputColumns.every(x => x.checked)) {
                    document.getElementById("all").checked = true;
                }

                this.showExport = inputColumns.some(x => x.checked);
            },
            onCheckedColumn(columnType, c) {
                this.$emit('changed-column', { columnType: columnType, columnName: c });

                let inputColumns = Array.from(document.getElementById("columns").getElementsByTagName("input"));

                if (inputColumns.some(x => !x.checked)) {
                    document.getElementById("all").checked = false;
                } 

                this.showExport = inputColumns.some(x => x.checked);
            },
            selectAll(event) {
                Array.from(document.getElementById("columns").getElementsByTagName("input"))
                    .forEach(x => {
                        x.checked = event.currentTarget.checked;
                    });

                this.showExport = event.currentTarget.checked;
                this.$emit('select-all', event);
            },
            submit() {
                this.$emit('button-pressed', !this.pressedYesButton);
                this.closed = true;
            },
            close() {
                this.$emit('button-pressed', this.pressedYesButton);
                this.closed = true;
            },
            open() {
                this.closed = false;
                setTimeout(() => this.init());
            }
        },
        mounted() {
            let link = document.getElementById(this.config.elementId);

            if (link) {
                link.addEventListener('click', this.open);
            }
        },
        unmounted() {
            let link = document.getElementById(this.config.elementId);

            if (link) {
                link.removeEventListener('click', this.open);
            }
        }
    });
</script>
