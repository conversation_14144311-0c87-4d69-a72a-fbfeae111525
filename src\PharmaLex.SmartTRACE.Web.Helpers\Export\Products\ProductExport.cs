﻿using NPOI.SS.UserModel;
using NPOI.SS.Util;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.Office;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public class ProductExport : ExcelWriter, IProductExport
    {
        private readonly IDistributedCacheServiceFactory cache;

        public ProductExport(IDistributedCacheServiceFactory cache)
        {
            this.cache = cache;
        }

        public async Task<byte[]> Export()
        {
            var wb = this.CreateWorkbook();
            ISheet exSheet = wb.Workbook.CreateSheet("Products");
            int rowIndex = 0;

            var productCache = this.cache.CreateMappedEntity<Product, ProductModel>()
                .Configure(o => o
                    .Include(x => x.Client)
                    .Include(x => x.ActiveSubstanceProduct)
                                                .ThenInclude(x => x.ActiveSubstance));

            var allProducts = await productCache.AllAsync();

            List<string> columnNames = new List<string>
            {
                "Name", "Dosage Form", "Strength", "Client Name", "Active Substances", "Lifecycle State"
            };
            exSheet.CreateRow(rowIndex++, wb.Styles["header"], columnNames.ToArray());

            foreach (var product in allProducts.OrderBy(x => x.Name))
            {
                product.LifecycleState = ((CommonLifecycleState)product.LifecycleStateId).ToString();
                List<string> productProps = new List<string>()
                {
                    product.Name,
                    (await PicklistHelper.ExtractPicklist(cache, product.DosageFormId))?.Name,
                    product.Strength,
                    product.ClientName,
                    string.Join(" | ", product.ActiveSubstances.Select(x => x.Name)),
                    product.LifecycleState
                };
                var row = exSheet.CreateRow(rowIndex++);
                for (int i = 0; i < productProps.Count; i++)
                {
                    row.CreateCell(i, productProps[i], wb.Styles["wrapped"]);
                }
            }

            exSheet.AutoSizeColumns(0, columnNames.Count);
            exSheet.SetAutoFilter(new CellRangeAddress(0, 0, 0, columnNames.Count - 1));
            exSheet.SetColumnWidth(4, 20 * 1000);

            return wb.Workbook.ToByteArray();
        }
    }
}
