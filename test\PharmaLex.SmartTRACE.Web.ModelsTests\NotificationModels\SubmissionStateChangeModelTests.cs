﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.NotificationModels
{
    public class SubmissionStateChangeModelTests
    {
        [Fact]
        public void SubmissionStateChangeModel_Get_SetValue()
        {
            // Arrange
            var model = new SubmissionStateChangeModel();
            //Act
            model.Application = "Test";
            model.LifecycleState = "1";
            model.RequestorName = "xyz";
            model.SubmissionType = "initial";
            model.RegulatoryLead = "nitish";
            model.InitialSentToEmail = "<EMAIL>";
            model.Publishers = "test";
            model.Client = "client";
            model.Product = "product";

            // Assert            
            Assert.NotNull(model);
        }
    }
}
