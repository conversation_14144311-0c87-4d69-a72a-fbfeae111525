﻿using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.Eu
{
    public class CtdSubmission
    {
        public string Type { get; set; }

        public string Mode { get; set; }
    }

    public abstract class EuEnvelope : EctdModule1Data
    {
        public abstract string Country { get; }
        public abstract string Identifier { get; }
        public abstract string SubmissionUnitType { get; }
        public abstract string Applicant { get; }
        public abstract CtdSubmission Submission { get; }
        public abstract string InventedName { get; }
        public abstract string ProcedureTrackingNumber { get; }
        public abstract string TrackingNumber { get; }
        public abstract string Procedure { get; }
        public abstract string AgencyCode { get; }
        public abstract string Inn { get; }
        public abstract string Sequence { get; }
        public abstract string RelatedSequence { get; }
        public abstract string SubmissionDescription { get; }
    }
}
