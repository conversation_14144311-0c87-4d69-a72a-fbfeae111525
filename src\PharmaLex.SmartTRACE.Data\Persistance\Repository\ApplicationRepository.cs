﻿using Microsoft.EntityFrameworkCore.Query;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using System;
using System.Linq;

namespace PharmaLex.SmartTRACE.Data.Persistance.Repository
{
    public class ApplicationRepository : TrackingGenericRepository<Application>, IApplicationRepository
    {
        public ApplicationRepository(SmartTRACEContext context, IUserContext userContext) : base(context, userContext.User)
        {
        }

        public IQueryable<Application> GetQueryableItems(Func<IQueryable<Application>, IIncludableQueryable<Application, object>> include = null)
        {
            var query = context.Set<Application>().AsQueryable();
            if (include != null)
            {
                query = include(query);
            }

            return query;
        }
    }
}
