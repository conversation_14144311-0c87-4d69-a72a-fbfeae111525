﻿using PharmaLex.DataAccess;
using System;

namespace PharmaLex.SmartTRACE.Entities.Audit
{
    public partial class SubmissionAudit: AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }

        public int? Id { get; set; }
        public string UniqueId { get; set; }
        public int? DeliveryDetailsId { get; set; }
        public string SequenceNumber { get; set; }
        public string RelatedSequenceNumber { get; set; }
        public int? DossierFormatId { get; set; }
        public string SerialNumber { get; set; }
        public string ReferenceNumber { get; set; }
        public int? SubmissionTypeId { get; set; }
        public int? SubmissionUnitId { get; set; }
        public int? SubmissionModeId { get; set; }
        public string Description { get; set; }
        public int? LifecycleStateId { get; set; }
        public int? PreviousLifecycleStateId { get; set; }
        public string Comments { get; set; }
        public DateTime? HealthAuthorityDueDate { get; set; }
        public DateTime? AuthoringDeadline { get; set; }
        public DateTime? PlannedDispatchDate { get; set; }
        public DateTime? ActualDispatchDate { get; set; }
        public DateTime? PlannedSubmissionDate { get; set; }
        public DateTime? ActualSubmissionDate { get; set; }
        public string CespNumber { get; set; }
        public DateTime? WithdrawalDate { get; set; }
        public string SourceDocumentsLocation { get; set; }
        public string ArchivedDocumentsLocation { get; set; }
        public string DocubridgeVersionId { get; set; }
        public int? ApplicationId { get; set; }
        public int? ProjectId { get; set; }
    }
}
