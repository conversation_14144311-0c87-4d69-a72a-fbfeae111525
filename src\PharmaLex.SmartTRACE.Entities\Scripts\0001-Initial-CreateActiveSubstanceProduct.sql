﻿CREATE TRIGGER [dbo].[ActiveSubstanceProduct_Insert] ON [dbo].[ActiveSubstanceProduct]
FOR INSERT AS
INSERT INTO [Audit].[ActiveSubstanceProduct_Audit]
SELECT 'I', [ProductId], [ActiveSubstanceId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[ActiveSubstanceProduct_Update] ON [dbo].[ActiveSubstanceProduct]
FOR UPDATE AS
INSERT INTO [Audit].[ActiveSubstanceProduct_Audit]
SELECT 'U', [ProductId], [ActiveSubstanceId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[ActiveSubstanceProduct_Delete] ON [dbo].[ActiveSubstanceProduct]
FOR DELETE AS
INSERT INTO [Audit].[ActiveSubstanceProduct_Audit]
SELECT 'D', [ProductId], [ActiveSubstanceId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO
