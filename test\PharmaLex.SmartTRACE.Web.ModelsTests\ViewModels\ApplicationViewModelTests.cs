﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class ApplicationViewModelTests
    {
        [Fact]
        public void ApplicationViewModel_Get_SetValue()
        { 
            
            // Arrange
            var model = new ApplicationViewModel();
            //Act
            model.Id = 1;
            model.Region = "test";
            model.Country = "belgium";
            model.ClientName = "client1";
            model.ClientId = 123;
            model.Product = "product";
            model.ProcedureType = "test";
            model.ProcedureTypeId = 01;
            model.ApplicationNumber = "test123";
            model.ProcedureNumber = "xyz123";
            model.MedicinalProductDomain = "test";
            model.MedicinalProductType = "test";
            model.ApplicationType= "test";
            model.ActiveSubstances = "test";
            model.ReferenceMemberState = "test";
            model.ConcernedMemberState = "test";
            model.ProductId = 1;
            model.Comments = "test";
            model.LifecycleStateId = 1;
            model.LifecycleState= "test";
            model.Submissions = new List<SubmissionModel>();
            // Assert            
            Assert.NotNull(model);
        }
    }
}
