﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class RegionModelTests
    {
        [Fact]
        public void RegionModel_Get_SetValue()
        {
            //Arrange
            var model = new RegionModel();
            model.Id = 1;
            model.Name = "Test";
            model.Countries = new List<CountryModel>();
            model.Abbreviation = "Test";
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void RegionMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new RegionMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
    }
}
