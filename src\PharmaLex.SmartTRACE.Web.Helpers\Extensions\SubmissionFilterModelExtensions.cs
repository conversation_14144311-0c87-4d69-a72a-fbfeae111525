﻿using PharmaLex.SmartTRACE.Web.Helpers.Constants;
using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.Helpers.Extensions
{
    public static class SubmissionFilterModelExtensions
    {
        public static SubmissionFilterModel FromFilters(this SubmissionFilterModel model, string[] filters)
        {
            foreach (var filter in filters ?? System.Array.Empty<string>())
            {
                var splitFilter = filter.Split("=>");
                var filterName = splitFilter[0];
                var filterValue = splitFilter[^1];

                if (!string.IsNullOrWhiteSpace(filterName) && !string.IsNullOrWhiteSpace(filterValue))
                {
                    switch (filterName.ToLower())
                    {
                        case TableFilterConstants.ClientName:
                            model.ClientName = filterValue;
                            break;
                        case TableFilterConstants.ApplicationNumber:
                            model.ApplicationNumber = filterValue;
                            break;
                        case TableFilterConstants.SequenceNumber:
                            model.SequenceNumber = filterValue;
                            break;
                        case TableFilterConstants.SubmissionUnit:
                            model.SubmissionUnit = filterValue;
                            break;
                        case TableFilterConstants.SubmissionMode:
                            model.SubmissionMode = filterValue;
                            break;
                        case TableFilterConstants.SubmissionType:
                            model.SubmissionType = filterValue;
                            break;
                        case TableFilterConstants.Description:
                            model.Description = filterValue;
                            break;
                        case TableFilterConstants.DossierFormat:
                            model.DossierFormat = filterValue;
                            break;
                        case TableFilterConstants.RegulatoryLead:
                            model.RegulatoryLead = filterValue;
                            break;
                        case TableFilterConstants.LifecycleState:
                            model.LifecycleState = filterValue;
                            break;
                    }
                }
            }
            return model;
        }
    }
}
