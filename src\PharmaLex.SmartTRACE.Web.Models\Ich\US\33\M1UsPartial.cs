﻿using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32;
using PharmaLex.SmartTRACE.Web.Models.Ich.US;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Models.eCTD32.US33
{
    public partial class leaf : CtdSectionBase, Ileaf
    {
        public string text => this.title.Value;

        public string op
        {
            get
            {
                string opr = this.operation.ToString();
                return $"{ char.ToUpper(opr[0]) }{ opr.Substring(1) }";
            }
        }

        public string Submission { get; set; }
        public string Path { get; set; }
    }
    public partial class title : CtdSectionBase
    {
    }
    public partial class linktext : CtdSectionBase
    {
    }
    public partial class xref : CtdSectionBase
    {
    }
    public partial class nodeextension : CtdSectionBase, Inodeextension
    {
        public override string CtdName => $"{this.title} {this.ID}";
        public override string CtdModule => string.Empty;
        public override string CtdSection => string.Empty;
    }
    public partial class admin : UsAdmin
    {
        public override UsEnvelopeSummary Summary => new UsEnvelopeSummary
        {
            ApplicantId = this.applicantinfo?.id,
            ApplicantName = this.applicantinfo?.companyname,
            Description = this.applicantinfo?.submissiondescription
        };

        public override UsApplicationInformation Application => new UsApplicationInformation
        {
            ApplicationContainingFiles = this.applicationset?.FirstOrDefault()?.applicationcontainingfiles.ToString(),
            ApplicationNumber = this.applicationset?.FirstOrDefault()?.applicationinformation?.applicationnumber?.Value,
            ApplicationType = this.applicationset?.FirstOrDefault()?.applicationinformation?.applicationnumber?.applicationtype,
            SequenceNumber = this.applicationset?.FirstOrDefault()?.submissioninformation?.sequencenumber?.Value,
            SubmissionId = this.applicationset?.FirstOrDefault()?.submissioninformation?.submissionid?.Value,
            SubmissionSubType = this.applicationset?.FirstOrDefault()?.submissioninformation?.sequencenumber?.submissionsubtype,
            SubmissionType = this.applicationset?.FirstOrDefault()?.submissioninformation?.submissionid?.submissiontype
        };

        public override List<UsEnvelopeContact> Contacts => this.applicantinfo?.applicantcontacts.Select(x => new UsEnvelopeContact
        {
            Name = $"{x.applicantcontactname.Value} ({x.applicantcontactname.applicantcontacttype})",
            Phone = $"{x.telephones.FirstOrDefault()?.Value} ({x.telephones.FirstOrDefault()?.telephonenumbertype})",
            Email = string.Join(',', x.emails)
        }).ToList();

        public form Form => this.applicationset?.FirstOrDefault()?.submissioninformation?.form;
    }
    public partial class applicantinfo : CtdSectionBase
    {
    }
    public partial class applicantcontact : CtdSectionBase
    {
    }
    public partial class applicantcontactname : CtdSectionBase
    {
    }
    public partial class telephone : CtdSectionBase
    {
    }
    public partial class application : CtdSectionBase
    {
    }
    public partial class applicationinformation : CtdSectionBase
    {
    }
    public partial class applicationnumber : CtdSectionBase
    {
    }
    public partial class crossreferenceapplicationnumber : CtdSectionBase
    {
    }
    public partial class submissioninformation : CtdSectionBase
    {
    }
    public partial class submissionid : CtdSectionBase
    {
    }
    public partial class sequencenumber : CtdSectionBase
    {
    }
    public partial class form : CtdSectionBase, Inodeextension
    {
        public override string CtdName => $"Form ({this.formtype})";

        public override string CtdModule => string.Empty;
        public override string CtdSection => string.Empty;
    }
    public partial class applicantcontacts : CtdSectionBase
    {
    }
    public partial class telephones : CtdSectionBase
    {
    }
    public partial class emails : CtdSectionBase
    {
    }
    public partial class applicationset : CtdSectionBase
    {
    }
    public partial class m1regional : CtdSectionBase
    {


        public CtdSectionBase Forms
        {
            get
            {
                fdaregional root = this.GetParent<fdaregional>();
                List<form> allForms = new List<form>();
                if (this.m11forms != null)
                {
                    allForms.AddRange(this.m11forms);
                }

                if (root?.admin?.Form != null)
                {
                    allForms.Add(root.admin.Form);
                }

                return allForms.Count > 0 ? new CustomCtdSection("Forms", "1.1", allForms.ToArray()) : null;
            }
        }

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.Forms,
                    this.m12coverletters,
                    this.m13administrativeinformation,
                    this.m14references,
                    this.m15applicationstatus,
                    this.m16meetings,
                    this.m17fasttrack,
                    this.m18specialprotocolassessmentrequest,
                    this.m19pediatricadministrativeinformation,
                    this.m110disputeresolution,
                    this.m111informationamendmentinformationnotcoveredundermodules2to5,
                    this.m112othercorrespondence,
                    this.m113annualreport,
                    this.m114labeling,
                    this.m115promotionalmaterial,
                    this.m116riskmanagementplan,
                    this.m117postmarketingstudies,
                    this.m118proprietarynames,
                    this.m119preeuaandeua,
                    this.m120generalinvestigationalplanforinitialind
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m12coverletters : CtdSectionBase
    {

    }
    public partial class m13administrativeinformation : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m131contactsponsorapplicantinformation != null)
                {
                    nodes.AddRange(this.m131contactsponsorapplicantinformation);
                }
                if (this.m132fieldcopycertification != null)
                {
                    nodes.AddRange(this.m132fieldcopycertification);
                }
                if (this.m133debarmentcertification != null)
                {
                    nodes.AddRange(this.m133debarmentcertification);
                }
                if (this.m134financialcertificationanddisclosure != null)
                {
                    nodes.AddRange(this.m134financialcertificationanddisclosure);
                }
                if (this.m135patentandexclusivity != null)
                {
                    nodes.AddRange(this.m135patentandexclusivity);
                }
                if (this.m136tropicaldiseasepriorityreviewvoucher != null)
                {
                    nodes.AddRange(this.m136tropicaldiseasepriorityreviewvoucher);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m131contactsponsorapplicantinformation : CtdSectionBase
    {
        public override string CtdName => "Contact/sponsor/applicant information";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();

                if (this.m1311changeofaddressorcorporatename != null)
                {
                    nodes.AddRange(this.m1311changeofaddressorcorporatename);
                }
                if (this.m1312changeincontactagent != null)
                {
                    nodes.AddRange(this.m1312changeincontactagent);
                }
                if (this.m1313changeinsponsor != null)
                {
                    nodes.AddRange(this.m1313changeinsponsor);
                }
                if (this.m1314transferofobligation != null)
                {
                    nodes.AddRange(this.m1314transferofobligation);
                }
                if (this.m1315changeinownershipofanapplicationorreissuanceoflicense != null)
                {
                    nodes.AddRange(this.m1315changeinownershipofanapplicationorreissuanceoflicense);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m1311changeofaddressorcorporatename : CtdSectionBase
    {
    }
    public partial class m1312changeincontactagent : CtdSectionBase
    {
        public override string CtdName => "Change in contact/agent";
    }
    public partial class m1313changeinsponsor : CtdSectionBase
    {
    }
    public partial class m1314transferofobligation : CtdSectionBase
    {
    }
    public partial class m1315changeinownershipofanapplicationorreissuanceoflicense : CtdSectionBase
    {
    }
    public partial class m132fieldcopycertification : CtdSectionBase
    {
    }
    public partial class m133debarmentcertification : CtdSectionBase
    {
    }
    public partial class m134financialcertificationanddisclosure : CtdSectionBase
    {
    }
    public partial class m135patentandexclusivity : CtdSectionBase
    {

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1351patentinformation != null)
                {
                    nodes.AddRange(this.m1351patentinformation);
                }
                if (this.m1352patentcertification != null)
                {
                    nodes.AddRange(this.m1352patentcertification);
                }
                if (this.m1353exclusivityclaim != null)
                {
                    nodes.AddRange(this.m1353exclusivityclaim);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m1351patentinformation : CtdSectionBase
    {
    }
    public partial class m1352patentcertification : CtdSectionBase
    {
    }
    public partial class m1353exclusivityclaim : CtdSectionBase
    {
    }
    public partial class m136tropicaldiseasepriorityreviewvoucher : CtdSectionBase
    {
    }
    public partial class m14references : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m141letterofauthorization != null)
                {
                    nodes.AddRange(this.m141letterofauthorization);
                }
                if (this.m142statementofrightofreference != null)
                {
                    nodes.AddRange(this.m142statementofrightofreference);
                }
                if (this.m143listofauthorizedpersonstoincorporatebyreference != null)
                {
                    nodes.AddRange(this.m143listofauthorizedpersonstoincorporatebyreference);
                }
                if (this.m144crossreferencetopreviouslysubmittedinformation != null)
                {
                    nodes.AddRange(this.m144crossreferencetopreviouslysubmittedinformation);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m141letterofauthorization : CtdSectionBase
    {
    }
    public partial class m142statementofrightofreference : CtdSectionBase
    {
    }
    public partial class m143listofauthorizedpersonstoincorporatebyreference : CtdSectionBase
    {
    }
    public partial class m144crossreferencetopreviouslysubmittedinformation : CtdSectionBase
    {
        public override string CtdName => "Cross-reference to previously submitted information";
    }
    public partial class m15applicationstatus : CtdSectionBase
    {

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m151withdrawalofanind != null)
                {
                    nodes.AddRange(this.m151withdrawalofanind);
                }
                if (this.m152inactivationrequest != null)
                {
                    nodes.AddRange(this.m152inactivationrequest);
                }
                if (this.m153reactivationrequest != null)
                {
                    nodes.AddRange(this.m153reactivationrequest);
                }
                if (this.m154reinstatementrequest != null)
                {
                    nodes.AddRange(this.m154reinstatementrequest);
                }
                if (this.m155withdrawalofanunapprovedblandaandaorsupplement != null)
                {
                    nodes.AddRange(this.m155withdrawalofanunapprovedblandaandaorsupplement);
                }
                if (this.m156withdrawaloflisteddrug != null)
                {
                    nodes.AddRange(this.m156withdrawaloflisteddrug);
                }
                if (this.m157withdrawalofapprovalofanapplicationorrevocationoflicense != null)
                {
                    nodes.AddRange(this.m157withdrawalofapprovalofanapplicationorrevocationoflicense);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m151withdrawalofanind : CtdSectionBase
    {
        public override string CtdName => "Withdrawal of an IND";
    }
    public partial class m152inactivationrequest : CtdSectionBase
    {
    }
    public partial class m153reactivationrequest : CtdSectionBase
    {
    }
    public partial class m154reinstatementrequest : CtdSectionBase
    {
    }
    public partial class m155withdrawalofanunapprovedblandaandaorsupplement : CtdSectionBase
    {
        public override string CtdName => "Withdrawal of an unapproved BLA, NDA, ANDA, or Supplement";
    }
    public partial class m156withdrawaloflisteddrug : CtdSectionBase
    {
    }
    public partial class m157withdrawalofapprovalofanapplicationorrevocationoflicense : CtdSectionBase
    {
    }
    public partial class m16meetings : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m161meetingrequest != null)
                {
                    nodes.AddRange(this.m161meetingrequest);
                }
                if (this.m162meetingbackgroundmaterials != null)
                {
                    nodes.AddRange(this.m162meetingbackgroundmaterials);
                }
                if (this.m163correspondenceregardingmeetings != null)
                {
                    nodes.AddRange(this.m163correspondenceregardingmeetings);
                }
                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m161meetingrequest : CtdSectionBase
    {
    }
    public partial class m162meetingbackgroundmaterials : CtdSectionBase
    {
    }
    public partial class m163correspondenceregardingmeetings : CtdSectionBase
    {
    }
    public partial class m17fasttrack : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m171fasttrackdesignationrequest != null)
                {
                    nodes.AddRange(this.m171fasttrackdesignationrequest);
                }
                if (this.m172fasttrackdesignationwithdrawalrequest != null)
                {
                    nodes.AddRange(this.m172fasttrackdesignationwithdrawalrequest);
                }
                if (this.m173rollingreviewrequest != null)
                {
                    nodes.AddRange(this.m173rollingreviewrequest);
                }
                if (this.m174correspondenceregardingfasttrackrollingreview != null)
                {
                    nodes.AddRange(this.m174correspondenceregardingfasttrackrollingreview);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m171fasttrackdesignationrequest : CtdSectionBase
    {
    }
    public partial class m172fasttrackdesignationwithdrawalrequest : CtdSectionBase
    {
    }
    public partial class m173rollingreviewrequest : CtdSectionBase
    {
    }
    public partial class m174correspondenceregardingfasttrackrollingreview : CtdSectionBase
    {
        public override string CtdName => "Correspondence regarding fast track/rolling review";
    }
    public partial class m18specialprotocolassessmentrequest : CtdSectionBase
    {

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m181clinicalstudy != null)
                {
                    nodes.AddRange(this.m181clinicalstudy);
                }
                if (this.m182carcinogenicitystudy != null)
                {
                    nodes.AddRange(this.m182carcinogenicitystudy);
                }
                if (this.m183stabilitystudy != null)
                {
                    nodes.AddRange(this.m183stabilitystudy);
                }
                if (this.m184animalefficacystudyforapprovalundertheanimalrule != null)
                {
                    nodes.AddRange(this.m184animalefficacystudyforapprovalundertheanimalrule);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m181clinicalstudy : CtdSectionBase
    {
    }
    public partial class m182carcinogenicitystudy : CtdSectionBase
    {
    }
    public partial class m183stabilitystudy : CtdSectionBase
    {
    }
    public partial class m184animalefficacystudyforapprovalundertheanimalrule : CtdSectionBase
    {
    }
    public partial class m19pediatricadministrativeinformation : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m191requestforwaiverofpediatricstudies != null)
                {
                    nodes.AddRange(this.m191requestforwaiverofpediatricstudies);
                }
                if (this.m192requestfordeferralofpediatricstudies != null)
                {
                    nodes.AddRange(this.m192requestfordeferralofpediatricstudies);
                }
                if (this.m193requestforpediatricexclusivitydetermination != null)
                {
                    nodes.AddRange(this.m193requestforpediatricexclusivitydetermination);
                }
                if (this.m194proposedpediatricstudyrequestandamendments != null)
                {
                    nodes.AddRange(this.m194proposedpediatricstudyrequestandamendments);
                }
                if (this.m196othercorrespondenceregardingpediatricexclusivityorstudyplans != null)
                {
                    nodes.AddRange(this.m196othercorrespondenceregardingpediatricexclusivityorstudyplans);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m191requestforwaiverofpediatricstudies : CtdSectionBase
    {
    }
    public partial class m192requestfordeferralofpediatricstudies : CtdSectionBase
    {
    }
    public partial class m193requestforpediatricexclusivitydetermination : CtdSectionBase
    {
    }
    public partial class m194proposedpediatricstudyrequestandamendments : CtdSectionBase
    {
    }
    public partial class m196othercorrespondenceregardingpediatricexclusivityorstudyplans : CtdSectionBase
    {
    }
    public partial class m110disputeresolution : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1101requestfordisputeresolution != null)
                {
                    nodes.AddRange(this.m1101requestfordisputeresolution);
                }
                if (this.m1102correspondencerelatedtodisputeresolution != null)
                {
                    nodes.AddRange(this.m1102correspondencerelatedtodisputeresolution);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m1101requestfordisputeresolution : CtdSectionBase
    {
    }
    public partial class m1102correspondencerelatedtodisputeresolution : CtdSectionBase
    {
    }
    public partial class m111informationamendmentinformationnotcoveredundermodules2to5 : CtdSectionBase
    {
        public override string CtdName => "Information amendment: Information not covered under modules 2 to 5";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1111qualityinformationamendment != null)
                {
                    nodes.AddRange(this.m1111qualityinformationamendment);
                }
                if (this.m1112nonclinicalinformationamendment != null)
                {
                    nodes.AddRange(this.m1112nonclinicalinformationamendment);
                }
                if (this.m1113clinicalinformationamendment != null)
                {
                    nodes.AddRange(this.m1113clinicalinformationamendment);
                }
                if (this.m1114multiplemoduleinformationamendment != null)
                {
                    nodes.AddRange(this.m1114multiplemoduleinformationamendment);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m1111qualityinformationamendment : CtdSectionBase
    {
    }
    public partial class m1112nonclinicalinformationamendment : CtdSectionBase
    {
    }
    public partial class m1113clinicalinformationamendment : CtdSectionBase
    {
    }
    public partial class m1114multiplemoduleinformationamendment : CtdSectionBase
    {
    }
    public partial class m112othercorrespondence : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1121preindcorrespondence != null)
                {
                    nodes.AddRange(this.m1121preindcorrespondence);
                }
                if (this.m1122requesttochargeforclinicaltrial != null)
                {
                    nodes.AddRange(this.m1122requesttochargeforclinicaltrial);
                }
                if (this.m1123requesttochargeforexpandedaccess != null)
                {
                    nodes.AddRange(this.m1123requesttochargeforexpandedaccess);
                }
                if (this.m1125requestforawaiver != null)
                {
                    nodes.AddRange(this.m1125requestforawaiver);
                }
                if (this.m1126exceptionfrominformedconsentforemergencyresearch != null)
                {
                    nodes.AddRange(this.m1126exceptionfrominformedconsentforemergencyresearch);
                }
                if (this.m1127publicdisclosurestatementforexceptionfrominformedconsentforemergencyresearch != null)
                {
                    nodes.AddRange(this.m1127publicdisclosurestatementforexceptionfrominformedconsentforemergencyresearch);
                }
                if (this.m1128correspondenceregardingexceptionfrominformedconsentforemergencyresearch != null)
                {
                    nodes.AddRange(this.m1128correspondenceregardingexceptionfrominformedconsentforemergencyresearch);
                }
                if (this.m1129notificationofdiscontinuationofclinicaltrial != null)
                {
                    nodes.AddRange(this.m1129notificationofdiscontinuationofclinicaltrial);
                }
                if (this.m11210genericdrugenforcementactstatement != null)
                {
                    nodes.AddRange(this.m11210genericdrugenforcementactstatement);
                }
                if (this.m11211andabasisforsubmissionstatement != null)
                {
                    nodes.AddRange(this.m11211andabasisforsubmissionstatement);
                }
                if (this.m11212comparisonofgenericdrugandreferencelisteddrug != null)
                {
                    nodes.AddRange(this.m11212comparisonofgenericdrugandreferencelisteddrug);
                }
                if (this.m11213requestforwaiverforinvivostudies != null)
                {
                    nodes.AddRange(this.m11213requestforwaiverforinvivostudies);
                }
                if (this.m11214environmentalanalysis != null)
                {
                    nodes.AddRange(this.m11214environmentalanalysis);
                }
                if (this.m11215requestforwaiverofinvivobioavailabilitystudies != null)
                {
                    nodes.AddRange(this.m11215requestforwaiverofinvivobioavailabilitystudies);
                }
                if (this.m11216fieldalertreports != null)
                {
                    nodes.AddRange(this.m11216fieldalertreports);
                }
                if (this.m11217orphandrugdesignation != null)
                {
                    nodes.AddRange(this.m11217orphandrugdesignation);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m1121preindcorrespondence : CtdSectionBase
    {
        public override string CtdName => "Pre IND correspondence";
    }
    public partial class m1122requesttochargeforclinicaltrial : CtdSectionBase
    {
    }
    public partial class m1123requesttochargeforexpandedaccess : CtdSectionBase
    {
    }
    public partial class m1124requestforcommentsandadvice : CtdSectionBase
    {
    }
    public partial class m1125requestforawaiver : CtdSectionBase
    {
    }
    public partial class m1126exceptionfrominformedconsentforemergencyresearch : CtdSectionBase
    {
    }
    public partial class m1127publicdisclosurestatementforexceptionfrominformedconsentforemergencyresearch : CtdSectionBase
    {
    }
    public partial class m1128correspondenceregardingexceptionfrominformedconsentforemergencyresearch : CtdSectionBase
    {
    }
    public partial class m1129notificationofdiscontinuationofclinicaltrial : CtdSectionBase
    {
    }
    public partial class m11210genericdrugenforcementactstatement : CtdSectionBase
    {
    }
    public partial class m11211andabasisforsubmissionstatement : CtdSectionBase
    {
        public override string CtdName => "ANDA basis for submission statement";
    }
    public partial class m11212comparisonofgenericdrugandreferencelisteddrug : CtdSectionBase
    {
    }
    public partial class m11213requestforwaiverforinvivostudies : CtdSectionBase
    {
    }
    public partial class m11214environmentalanalysis : CtdSectionBase
    {
    }
    public partial class m11215requestforwaiverofinvivobioavailabilitystudies : CtdSectionBase
    {
    }
    public partial class m11216fieldalertreports : CtdSectionBase
    {
    }
    public partial class m11217orphandrugdesignation : CtdSectionBase
    {
    }
    public partial class m113annualreport : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1131summaryfornonclinicalstudies != null)
                {
                    nodes.AddRange(this.m1131summaryfornonclinicalstudies);
                }
                if (this.m1132summaryofclinicalpharmacologyinformation != null)
                {
                    nodes.AddRange(this.m1132summaryofclinicalpharmacologyinformation);
                }
                if (this.m1133summaryofsafetyinformation != null)
                {
                    nodes.AddRange(this.m1133summaryofsafetyinformation);
                }
                if (this.m1134summaryoflabelingchanges != null)
                {
                    nodes.AddRange(this.m1134summaryoflabelingchanges);
                }
                if (this.m1135summaryofmanufacturingchanges != null)
                {
                    nodes.AddRange(this.m1135summaryofmanufacturingchanges);
                }
                if (this.m1136summaryofmicrobiologicalchanges != null)
                {
                    nodes.AddRange(this.m1136summaryofmicrobiologicalchanges);
                }
                if (this.m1137summaryofothersignificantnewinformation != null)
                {
                    nodes.AddRange(this.m1137summaryofothersignificantnewinformation);
                }
                if (this.m1138individualstudyinformation != null)
                {
                    nodes.AddRange(this.m1138individualstudyinformation);
                }
                if (this.m1139generalinvestigationalplan != null)
                {
                    nodes.AddRange(this.m1139generalinvestigationalplan);
                }
                if (this.m11310foreignmarketing != null)
                {
                    nodes.AddRange(this.m11310foreignmarketing);
                }
                if (this.m11311distributiondata != null)
                {
                    nodes.AddRange(this.m11311distributiondata);
                }
                if (this.m11312statusofpostmarketingstudycommitmentsandrequirements != null)
                {
                    nodes.AddRange(this.m11312statusofpostmarketingstudycommitmentsandrequirements);
                }
                if (this.m11313statusofotherpostmarketingstudiesandrequirements != null)
                {
                    nodes.AddRange(this.m11313statusofotherpostmarketingstudiesandrequirements);
                }
                if (this.m11314logofoutstandingregulatorybusiness != null)
                {
                    nodes.AddRange(this.m11314logofoutstandingregulatorybusiness);
                }
                if (this.m11315developmentsafetyupdatereportdsur != null)
                {
                    nodes.AddRange(this.m11315developmentsafetyupdatereportdsur);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }


    }
    public partial class m1131summaryfornonclinicalstudies : CtdSectionBase
    {
    }
    public partial class m1132summaryofclinicalpharmacologyinformation : CtdSectionBase
    {
    }
    public partial class m1133summaryofsafetyinformation : CtdSectionBase
    {
    }
    public partial class m1134summaryoflabelingchanges : CtdSectionBase
    {
    }
    public partial class m1135summaryofmanufacturingchanges : CtdSectionBase
    {
    }
    public partial class m1136summaryofmicrobiologicalchanges : CtdSectionBase
    {
    }
    public partial class m1137summaryofothersignificantnewinformation : CtdSectionBase
    {
    }
    public partial class m1138individualstudyinformation : CtdSectionBase
    {
    }
    public partial class m1139generalinvestigationalplan : CtdSectionBase
    {
    }
    public partial class m11310foreignmarketing : CtdSectionBase
    {
    }
    public partial class m11311distributiondata : CtdSectionBase
    {
    }
    public partial class m11312statusofpostmarketingstudycommitmentsandrequirements : CtdSectionBase
    {
    }
    public partial class m11313statusofotherpostmarketingstudiesandrequirements : CtdSectionBase
    {
    }
    public partial class m11314logofoutstandingregulatorybusiness : CtdSectionBase
    {
    }
    public partial class m11315developmentsafetyupdatereportdsur : CtdSectionBase
    {
        public override string CtdName => "Developmentsafety update report (DSUR)";
    }
    public partial class m114labeling : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1141draftlabeling != null)
                {
                    nodes.AddRange(this.m1141draftlabeling);
                }
                if (this.m1142finallabeling != null)
                {
                    nodes.AddRange(this.m1142finallabeling);
                }
                if (this.m1143listeddruglabeling != null)
                {
                    nodes.AddRange(this.m1143listeddruglabeling);
                }
                if (this.m1144investigationaldruglabeling != null)
                {
                    nodes.AddRange(this.m1144investigationaldruglabeling);
                }
                if (this.m1145foreignlabeling != null)
                {
                    nodes.AddRange(this.m1145foreignlabeling);
                }
                if (this.m1146productlabelingfor2253submissions != null)
                {
                    nodes.AddRange(this.m1146productlabelingfor2253submissions);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m1141draftlabeling : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m11411draftcartonandcontainerlabels != null)
                {
                    nodes.AddRange(this.m11411draftcartonandcontainerlabels);
                }
                if (this.m11412annotateddraftlabelingtext != null)
                {
                    nodes.AddRange(this.m11412annotateddraftlabelingtext);
                }
                if (this.m11413draftlabelingtext != null)
                {
                    nodes.AddRange(this.m11413draftlabelingtext);
                }
                if (this.m11414labelcomprehensionstudies != null)
                {
                    nodes.AddRange(this.m11414labelcomprehensionstudies);
                }
                if (this.m11415labelinghistory != null)
                {
                    nodes.AddRange(this.m11415labelinghistory);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m11411draftcartonandcontainerlabels : CtdSectionBase
    {
    }
    public partial class m11412annotateddraftlabelingtext : CtdSectionBase
    {
    }
    public partial class m11413draftlabelingtext : CtdSectionBase
    {
    }
    public partial class m11414labelcomprehensionstudies : CtdSectionBase
    {
    }
    public partial class m11415labelinghistory : CtdSectionBase
    {
    }
    public partial class m1142finallabeling : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m11421finalcartonorcontainerlabels != null)
                {
                    nodes.AddRange(this.m11421finalcartonorcontainerlabels);
                }
                if (this.m11422finalpackageinsertpackageinsertspatientinformationmedicationguides != null)
                {
                    nodes.AddRange(this.m11422finalpackageinsertpackageinsertspatientinformationmedicationguides);
                }
                if (this.m11423finallabelingtext != null)
                {
                    nodes.AddRange(this.m11423finallabelingtext);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m11421finalcartonorcontainerlabels : CtdSectionBase
    {
    }
    public partial class m11422finalpackageinsertpackageinsertspatientinformationmedicationguides : CtdSectionBase
    {
        public override string CtdName => "Final package insert (package inserts, patient information, medication guides)";
    }
    public partial class m11423finallabelingtext : CtdSectionBase
    {
    }
    public partial class m1143listeddruglabeling : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m11431annotatedcomparisonwithlisteddrug != null)
                {
                    nodes.AddRange(this.m11431annotatedcomparisonwithlisteddrug);
                }
                if (this.m11432approvedlabelingtextforlisteddrug != null)
                {
                    nodes.AddRange(this.m11432approvedlabelingtextforlisteddrug);
                }
                if (this.m11433labelingtextforreferencelisteddrug != null)
                {
                    nodes.AddRange(this.m11433labelingtextforreferencelisteddrug);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m11431annotatedcomparisonwithlisteddrug : CtdSectionBase
    {
    }
    public partial class m11432approvedlabelingtextforlisteddrug : CtdSectionBase
    {
    }
    public partial class m11433labelingtextforreferencelisteddrug : CtdSectionBase
    {
    }
    public partial class m1144investigationaldruglabeling : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m11441investigationalbrochure != null)
                {
                    nodes.AddRange(this.m11441investigationalbrochure);
                }
                if (this.m11442investigationaldruglabeling != null)
                {
                    nodes.AddRange(this.m11442investigationaldruglabeling);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m11441investigationalbrochure : CtdSectionBase
    {
    }
    public partial class m11442investigationaldruglabeling : CtdSectionBase
    {
    }
    public partial class m1145foreignlabeling : CtdSectionBase
    {
    }
    public partial class m1146productlabelingfor2253submissions : CtdSectionBase
    {
    }
    public partial class m115promotionalmaterial : CtdSectionBase
    {
        public override string CtdName => $"Promotional material ({this.promotionalmaterialaudiencetype})";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m1151correspondencerelatingtopromotionalmaterials,
                    this.m1152materials
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m1151correspondencerelatingtopromotionalmaterials : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m11511requestforadvisorycommentsonlaunchmaterials,
                    this.m11512requestforadvisorycommentsonnonlaunchmaterials,
                    this.m11513presubmissionoflaunchpromotionalmaterialsforacceleratedapprovalproducts,
                    this.m11514presubmissionofnonlaunchpromotionalmaterialsforacceleratedapprovalproducts,
                    this.m11515predisseminationreviewoftelevisionads,
                    this.m11516responsetountitledletterorwarningletter,
                    this.m11517responsetoinformationrequest,
                    this.m11518correspondenceaccompanyingmaterialspreviouslymissingorrejected,
                    this.m11519withdrawalrequest,
                    this.m115110submissionofannotatedreferences,
                    this.m115111generalcorrespondence
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m11511requestforadvisorycommentsonlaunchmaterials : CtdSectionBase
    {
    }
    public partial class m11512requestforadvisorycommentsonnonlaunchmaterials : CtdSectionBase
    {
    }
    public partial class m11513presubmissionoflaunchpromotionalmaterialsforacceleratedapprovalproducts : CtdSectionBase
    {
    }
    public partial class m11514presubmissionofnonlaunchpromotionalmaterialsforacceleratedapprovalproducts : CtdSectionBase
    {
        public override string CtdName => "Presubmission of non-launch promotional materials for accelerated approval products";
    }
    public partial class m11515predisseminationreviewoftelevisionads : CtdSectionBase
    {
        public override string CtdName => "Pre-dissemination review of television ads";
    }
    public partial class m11516responsetountitledletterorwarningletter : CtdSectionBase
    {
    }
    public partial class m11517responsetoinformationrequest : CtdSectionBase
    {
    }
    public partial class m11518correspondenceaccompanyingmaterialspreviouslymissingorrejected : CtdSectionBase
    {
    }
    public partial class m11519withdrawalrequest : CtdSectionBase
    {
    }
    public partial class m115110submissionofannotatedreferences : CtdSectionBase
    {
    }
    public partial class m115111generalcorrespondence : CtdSectionBase
    {
    }
    public partial class m1152materials : CtdSectionBase
    {
        public override string CtdName => $"Materials ({this.promotionalmaterialdoctype})";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m11521material != null)
                {
                    nodes.AddRange(this.m11521material);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m11521material : CtdSectionBase
    {
        public override string CtdName => $"Material ({this.promotionalmaterialtype}, {this.materialid}, {this.issuedate})";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m115211cleanversion,
                    this.m115212annotatedversion,
                    this.m115213annotatedlabelingversion,
                    this.m115214annotatedreferences
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m115211cleanversion : CtdSectionBase
    {
    }
    public partial class m115212annotatedversion : CtdSectionBase
    {
    }
    public partial class m115213annotatedlabelingversion : CtdSectionBase
    {
    }
    public partial class m115214annotatedreferences : CtdSectionBase
    {
    }
    public partial class m116riskmanagementplan : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m1161riskmanagementnonrems,
                    this.m1162riskevaluationandmitigationstrategiesrems
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m1161riskmanagementnonrems : CtdSectionBase
    {
        public override string CtdName => "Risk Management (Non-REMS)";
    }
    public partial class m1162riskevaluationandmitigationstrategiesrems : CtdSectionBase
    {
        public override string CtdName => "Risk Evaluation and Mitigation Strategy (REMS)";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m11621finalrems,
                    this.m11622draftrems,
                    this.m11623remsassessment,
                    this.m11624remsassessmentmethodology,
                    this.m11625remscorrespondence,
                    this.m11626remsmodificationhistory
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m11621finalrems : CtdSectionBase
    {
        public override string CtdName => "Final REMS";
    }
    public partial class m11622draftrems : CtdSectionBase
    {
        public override string CtdName => "Draft REMS";
    }
    public partial class m11623remsassessment : CtdSectionBase
    {
        public override string CtdName => "REMS Assessment";
    }
    public partial class m11624remsassessmentmethodology : CtdSectionBase
    {
        public override string CtdName => "REMS Assessment Methodology";
    }
    public partial class m11625remscorrespondence : CtdSectionBase
    {
        public override string CtdName => "REMS Assessment Correspondence";
    }
    public partial class m11626remsmodificationhistory : CtdSectionBase
    {
        public override string CtdName => "REMS Modification History";
    }
    public partial class m117postmarketingstudies : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1171correspondenceregardingpostmarketingcommitments != null)
                {
                    nodes.AddRange(this.m1171correspondenceregardingpostmarketingcommitments);
                }
                if (this.m1172correspondenceregardingpostmarketingrequirements != null)
                {
                    nodes.AddRange(this.m1172correspondenceregardingpostmarketingrequirements);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m1171correspondenceregardingpostmarketingcommitments : CtdSectionBase
    {
    }
    public partial class m1172correspondenceregardingpostmarketingrequirements : CtdSectionBase
    {
    }
    public partial class m118proprietarynames : CtdSectionBase
    {
    }
    public partial class m119preeuaandeua : CtdSectionBase
    {
        public override string CtdName => "Pre-EUA and EUA";
    }
    public partial class m120generalinvestigationalplanforinitialind : CtdSectionBase
    {
        public override string CtdName => "General investigational plan for initial IND";
    }
    public partial class m11forms : CtdSectionBase
    {
    }
    public partial class fdaregional : CtdSectionBase, IEctdModule1
    {
        public List<EctdModule1Data> Data => new List<EctdModule1Data>
        {
            this.admin
        };

        public string DtdVersion => this.dtdversion;

        public string Region => "US";

        public List<CtdSectionBase> Content => this.ChildNodes.FirstOrDefault()?.ChildNodes ?? new List<CtdSectionBase>();

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m1regional
                };

                return nodes.Where(x => x != null).Select(x => 
                {
                    x.Parent = this;
                    return x;
                }).ToList();
            }
        }
    }
}
