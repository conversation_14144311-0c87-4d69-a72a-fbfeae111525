﻿namespace PharmaLex.SmartTRACE.Web.Models
{
    public class DashboardSubmissionViewModel
    {
        public int DraftSubmissionsCount { get; set; }
        public int PlannedSubmissionsCount { get; set; }
        public int InProgresstSubmissionsCount { get; set; }
        public int ReadyForPublishingSubmissionsCount { get; set; }
        public int QCReviewSubmissionsCount { get; set; }
        public int ApprovedForSubmissionsCount { get; set; }
        public int SubmittedSubmissionsCount { get; set; }
        public int ArchivedSubmissionsCount { get; set; }
        public int WithdrawnSubmissionsCount { get; set; }
        public int AllSubmissionsCount { get; set; }

        public DashboardViewModel DashboardModel { get; set; }
    }
}
