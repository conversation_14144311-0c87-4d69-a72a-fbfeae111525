﻿using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Export.Products
{
    public class ProductExportTests
    {
        private readonly IDistributedCacheServiceFactory cache;

        public ProductExportTests()
        {
            cache = Substitute.For<IDistributedCacheServiceFactory>();
        }

        [Fact]
        public async Task Export_Product_ReturnsData()
        {
            // Arrange
            List<ActiveSubstanceModel> activeSubstances = new List<ActiveSubstanceModel>() { new ActiveSubstanceModel() { Id = 1, Name = "ac" } };
            List<ProductModel> prod = new List<ProductModel>() { new ProductModel() { Id = 8, ClientId = 7, ClientName = "abc", ActiveSubstances = activeSubstances } };
            PicklistDataModel pkDatamodel = new()
            {
                Id = 11,
                Name = "name"
            };
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));

            var ac = Substitute.For<IMappedEntityCacheServiceProxy<Product, ProductModel>>();
            cache.CreateMappedEntity<Product, ProductModel>()
               .Configure(Arg.Any<Func<IIncludable<Product>, IIncludable<Product>>>()).Returns(ac);
            ac.AllAsync().Returns(Task.FromResult(prod));

            // Act
            ProductExport prodExport = new ProductExport(cache);
            var result = await prodExport.Export();

            Assert.NotNull(result);
            // Assert

        }
        [Fact]
        public async Task Export_ProductWithoutPicklist_ReturnsData()
        {
            // Arrange
            List<ActiveSubstanceModel> activeSubstances = new List<ActiveSubstanceModel>() { new ActiveSubstanceModel() { Id = 1, Name = "ac" } };
            List<ProductModel> prod = new List<ProductModel>() { new ProductModel() { Id = 8, ClientId = 7, ClientName = "abc", ActiveSubstances = activeSubstances } };

            var ac = Substitute.For<IMappedEntityCacheServiceProxy<Product, ProductModel>>();
            cache.CreateMappedEntity<Product, ProductModel>()
               .Configure(Arg.Any<Func<IIncludable<Product>, IIncludable<Product>>>()).Returns(ac);
            ac.AllAsync().Returns(Task.FromResult(prod));

            // Act
            ProductExport prodExport = new ProductExport(cache);
            var result = await prodExport.Export();

            Assert.NotNull(result);
            // Assert

        }
        [Fact]
        public async Task Export_NoProduct_Returns_HeaderRow()
        {
            // Arrange
            List<ProductModel> prod = new List<ProductModel>() { };
            PicklistDataModel pkDatamodel = new()
            {
                Id = 11,
                Name = "name"
            };
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));

            var ac = Substitute.For<IMappedEntityCacheServiceProxy<Product, ProductModel>>();
            cache.CreateMappedEntity<Product, ProductModel>()
               .Configure(Arg.Any<Func<IIncludable<Product>, IIncludable<Product>>>()).Returns(ac);
            ac.AllAsync().Returns(Task.FromResult(prod));

            // Act
            ProductExport prodExport = new ProductExport(cache);
            var result = await prodExport.Export();

            Assert.NotNull(result);
            // Assert

        }
    }
}
