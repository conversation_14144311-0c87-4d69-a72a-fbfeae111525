﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Configuration;
using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{

    public class SubmissionRequestFormControllerTests
    {
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IMapper _mapper;
        private readonly INotificationService _notificationService;
        private readonly IConfiguration _configuration;

        public ApplicationModel appModel = new()
        {
            Id = 1,
            ApplicationNumber = "ap11",
            ProcedureTypeId = 11,
            ApplicationTypeId = 110,
            ClientId = 1100,
            UniqueId = "uid1",
        };
        public ApplicationProduct appProd = new()
        {
            Id = 1,
            ProductId = 1,
            ApplicationId = 1
        };
        public List<ApplicationCountry> appCountryList = new()
        {
          new ApplicationCountry(){ApplicationId=1,Country=new Country(){ Id=01, Name="country1"}, Id=001,CountryId=01}
        };
        public List<CountryModel> countryListModel = new()
        {
          new CountryModel(){ Id=1, Name="country1", RegionId=100, TwoLetterCode="CN" },
          new CountryModel(){ Id=2, Name="country2", RegionId=101, TwoLetterCode="CM"}
        };
        public PicklistDataModel pkDatamodel = new()
        {
            Id = 11,
            Name = "prod1",
            CountriesIds = [1],
            PicklistTypeId = 11
        };
        public List<PicklistDataModel> PicklistDataModel = new()
        {   new PicklistDataModel(){Id =1,Name= "prod1",CountriesIds=[1],PicklistTypeId=1, Selected=true, Country="country1"  },
            new PicklistDataModel(){Id =2,Name= "prod2",CountriesIds=[2],PicklistTypeId=1, Selected=true, Country="country2"}
        };
        public List<PicklistDataCountry> picklistDataCountry = new()
        {
            new PicklistDataCountry(){ Id = 1, CountryId=1},
            new PicklistDataCountry(){ Id = 2, CountryId=1}
        };
        public List<Project> projDataModel = new List<Project>
            { new Project { Id=1,ClientId=1, Code="11" , Name="Project1", OpportunityNumber="111"}};
        public Submission submission = new Submission()
        {
            Id = 1,
            UniqueId = "91",
            Project = new Project() { Id = 1, Name = "proj1" },
            ProjectId = 1,
            LifecycleStateId = 1
        };
        public SubmissionResource submissionRes = new SubmissionResource()
        {
            Id = 1,
            SubmissionId = 1,
            PriorityId = 2,
            RegulatoryLead = "ab"
        };
        public UserModel usermodel = new UserModel() { Id = 1, DisplayFullName = "abc", Email = "<EMAIL>" };
        public SubmissionModel submodel = new SubmissionModel() { Id = 1, ApplicationId = 1, CountriesIds = [1, 2, 3] };

        #region Constructor 
        public SubmissionRequestFormControllerTests()
        {
            _cache = Substitute.For<IDistributedCacheServiceFactory>();
            _mapper = Substitute.For<IMapper>();
            _notificationService = Substitute.For<INotificationService>();
            _configuration = Substitute.For<IConfiguration>();
        }
        #endregion
        #region Action GET-edit New(submission-request)
        [Fact]

        public async Task New_Returns_SubmissionRequestModel()
        {
            //Arrange
            var viewname = "EditSubmissionRequestForm";
            var controller = new SubmissionRequestFormController(_cache, _mapper, _notificationService, _configuration);
            var appMappedCache = Substitute.For<IMappedEntityCacheServiceProxy<Application, ApplicationModel>>();
            _cache.CreateMappedEntity<Application, ApplicationModel>().Returns(appMappedCache);
            appMappedCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Application, bool>>>()).Returns(appModel);
            GetApplicationAndProdModels();
            GetCountriesPerApplication();
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));

            //picklistondatawithcountry
            var PicklistDataCountryCache = Substitute.For<IEntityCacheServiceProxy<PicklistDataCountry>>();
            _cache.CreateEntity<PicklistDataCountry>().Returns(PicklistDataCountryCache);
            PicklistDataCountryCache.AllAsync().Returns(Task.FromResult(picklistDataCountry));
            picklistCache.WhereAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(PicklistDataModel));
            var projCache = Substitute.For<IEntityCacheServiceProxy<Project>>();
            _cache.CreateEntity<Project>().Returns(projCache);
            projCache.WhereAsync(Arg.Any<Expression<Func<Project, bool>>>()).Returns(projDataModel);


            // Act
            var result = await controller.New(appModel.Id) as ViewResult;
            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.ViewName);
            Assert.Equal(result.ViewName, viewname);

        }

        #endregion
        #region Action GET- New(int appId)
        [Fact]

        public async Task New_Returns_Invalid()
        {
            // Arrange
            int id = 10;
            var controller = new SubmissionRequestFormController(_cache, _mapper, _notificationService, _configuration);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.New(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);

        }

        #endregion
        #region Action POST-SaveNew(submission-request) with invalid data
        [Fact]

        public async Task SaveNew_For_InvalidDataModel_Returns_View()
        {
            //Arrange
            EditSubmissionRequestFormViewModel model = new EditSubmissionRequestFormViewModel();
            Client clientData = new Client() { Id = 101, Name = "client1" };
            var viewname = "EditSubmissionRequestForm";
            var controller = new SubmissionRequestFormController(_cache, _mapper, _notificationService, _configuration);
            GetApplicationAndProdModels();
            var clientCache = Substitute.For<IEntityCacheServiceProxy<Client>>();
            _cache.CreateEntity<Client>().Returns(clientCache);
            clientCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Client, bool>>>()).Returns(clientData);
            controller.ModelState.AddModelError("SessionName", "Required");
            //picklist
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.AllAsync().Returns(Task.FromResult(PicklistDataModel));
            GetCountriesPerApplication();

            //Act
            var result = await controller.SaveNew(appModel.Id, model) as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.ViewName);
            Assert.Equal(result.ViewName, viewname);

        }
        #endregion
        #region Action POST-SaveNew(submission-request) with valid data
        [Fact]

        public async Task SaveNew_For_ValidDataModel_Save()
        {
            //Arrange
            EditSubmissionRequestFormViewModel model = new EditSubmissionRequestFormViewModel()
            {
                SubmissionRequestForm = new SubmissionRequestFormModel { SubmissionTypeId = 1, SendNotification = false, InitialSentToEmail = null, CountryIds = [1, 2, 3] },
                Countries = countryListModel
            };

            var viewname = "/submissions/edit/" + submission.Id;
            Client clientData = new Client() { Id = 101, Name = "client1" };
            var controller = new SubmissionRequestFormController(_cache, _mapper, _notificationService, _configuration);
            GetApplicationAndProdModels();
            var clientCache = Substitute.For<IEntityCacheServiceProxy<Client>>();
            _cache.CreateEntity<Client>().Returns(clientCache);
            clientCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Client, bool>>>()).Returns(clientData);
            var appMappedCache = Substitute.For<IMappedEntityCacheServiceProxy<Application, ApplicationModel>>();
            _cache.CreateMappedEntity<Application, ApplicationModel>().Returns(appMappedCache);
            appMappedCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Application, bool>>>()).Returns(appModel);
            //var submissionCache = _cache.CreateTrackedEntity<Submission>().Configure(o => o.Include(x => x.Application));
            //var submission = _mapper.Map<Submission>(model.SubmissionRequestForm);
            var submissionCache = Substitute.For<ITrackedEntityCacheServiceProxy<Submission>>();
            _cache.CreateTrackedEntity<Submission>().Configure(Arg.Any<Func<IIncludable<Submission>, IIncludable<Submission>>>()).Returns(submissionCache);
            _mapper.Map<Submission>(Arg.Any<SubmissionRequestFormModel>()).Returns(submission);
            _mapper.Map<SubmissionResource>(Arg.Any<SubmissionRequestFormModel>()).Returns(submissionRes);
            var SubmissionCountryCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionCountry>>();
            _cache.CreateTrackedEntity<SubmissionCountry>().Returns(SubmissionCountryCache);

            var userdCache = Substitute.For<IMappedEntityCacheServiceProxy<User, UserModel>>();
            _cache.CreateMappedEntity<User, UserModel>().Returns(userdCache);
            userdCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<User, bool>>>()).Returns(usermodel);

            var subCache = Substitute.For<IMappedEntityCacheServiceProxy<Submission, SubmissionModel>>();
            _cache.CreateMappedEntity<Submission, SubmissionModel>()
                    .Configure(Arg.Any<Func<IIncludable<Submission>, IIncludable<Submission>>>()).Returns(subCache);
            subCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submodel);
            //picklist
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            controller.TempData = tempData;
            // Act            
            var result = await controller.SaveNew(submission.Id, model) as IActionResult;
            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<RedirectResult>(result);
            Assert.NotNull(viewResult.Url);
            Assert.Equal(viewname, viewResult.Url);

        }
        #endregion
        #region Action POST-SaveNew(submission-request) with valid data user notification is true
        [Fact]
        public async Task SaveNew_For_ValidDataModel_Save_And_SendNotification()
        {
            //Arrange
            _configuration.GetValue<string>("AppSettings:SmartTraceConfigurableEmail").ReturnsForAnyArgs("<EMAIL>");
            EditSubmissionRequestFormViewModel model = new()
            {
                SubmissionRequestForm = new SubmissionRequestFormModel { SubmissionTypeId = 1, SendNotification = true, InitialSentToEmail = null, CountryIds = [1, 2, 3] },
                Countries = countryListModel
            };
            var viewname = "/applications/view/" + submission.Id;
            Client clientData = new Client() { Id = 101, Name = "client1" };
            var controller = new SubmissionRequestFormController(_cache, _mapper, _notificationService, _configuration);
            GetApplicationAndProdModels();
            var clientCache = Substitute.For<IEntityCacheServiceProxy<Client>>();
            _cache.CreateEntity<Client>().Returns(clientCache);
            clientCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Client, bool>>>()).Returns(clientData);
            var appMappedCache = Substitute.For<IMappedEntityCacheServiceProxy<Application, ApplicationModel>>();
            _cache.CreateMappedEntity<Application, ApplicationModel>().Returns(appMappedCache);
            appMappedCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Application, bool>>>()).Returns(appModel);
            var submissionCache = Substitute.For<ITrackedEntityCacheServiceProxy<Submission>>();
            _cache.CreateTrackedEntity<Submission>().Configure(Arg.Any<Func<IIncludable<Submission>, IIncludable<Submission>>>()).Returns(submissionCache);
            _mapper.Map<Submission>(Arg.Any<SubmissionRequestFormModel>()).Returns(submission);
            _mapper.Map<SubmissionResource>(Arg.Any<SubmissionRequestFormModel>()).Returns(submissionRes);
            var SubmissionCountryCache = Substitute.For<ITrackedEntityCacheServiceProxy<SubmissionCountry>>();
            _cache.CreateTrackedEntity<SubmissionCountry>().Returns(SubmissionCountryCache);
            var userdCache = Substitute.For<IMappedEntityCacheServiceProxy<User, UserModel>>();
            _cache.CreateMappedEntity<User, UserModel>().Returns(userdCache);
            userdCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<User, bool>>>()).Returns(usermodel);

            var subCache = Substitute.For<IMappedEntityCacheServiceProxy<Submission, SubmissionModel>>();
            _cache.CreateMappedEntity<Submission, SubmissionModel>()
                    .Configure(Arg.Any<Func<IIncludable<Submission>, IIncludable<Submission>>>()).Returns(subCache);
            subCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Submission, bool>>>()).Returns(submodel);
            //picklist
            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(pkDatamodel));
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            controller.TempData = tempData;
            // Act            
            var result = await controller.SaveNew(submission.Id, model) as IActionResult;
            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<RedirectResult>(result);
            Assert.NotNull(viewResult.Url);
            Assert.Equal(viewname, viewResult.Url);

        }
        #endregion
        private void GetApplicationAndProdModels()
        {
            ProductModel prodModel = new()
            {
                Id = 1,
                Name = "prod1",
                ClientId = 112,
            };
            var appProdCache = Substitute.For<IEntityCacheServiceProxy<ApplicationProduct>>();
            _cache.CreateEntity<ApplicationProduct>().Returns(appProdCache);
            var prodMappedCache = Substitute.For<IMappedEntityCacheServiceProxy<Product, ProductModel>>();
            _cache.CreateMappedEntity<Product, ProductModel>()
            .Returns(prodMappedCache);
            appProdCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<ApplicationProduct, bool>>>()).Returns(appProd);
            prodMappedCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(prodModel);
        }

        private void GetCountriesPerApplication()
        {
            var appCountrycache = Substitute.For<IEntityCacheServiceProxy<ApplicationCountry>>();
            _cache.CreateEntity<ApplicationCountry>().Returns(appCountrycache);
            appCountrycache.WhereAsync(Arg.Any<Expression<Func<ApplicationCountry, bool>>>()).Returns(Task.FromResult(appCountryList));
            var countrycache = Substitute.For<IMappedEntityCacheServiceProxy<Country, CountryModel>>();
            _cache.CreateMappedEntity<Country, CountryModel>().Returns(countrycache);
            countrycache.WhereAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(Task.FromResult(countryListModel));
            var projCache = Substitute.For<IEntityCacheServiceProxy<Project>>();
            _cache.CreateEntity<Project>().Returns(projCache);
            projCache.WhereAsync(Arg.Any<Expression<Func<Project, bool>>>()).Returns(projDataModel);
        }




    }
}
