﻿using PharmaLex.DataAccess;

namespace PharmaLex.SmartTRACE.Entities.Audit
{
    public partial class ActiveSubstanceAudit: AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }

        public int? Id { get; set; }
        public string Name { get; set; }
        public int? ClientId { get; set; }
        public int LifecycleStateId { get; set; }
    }
}
