﻿using Microsoft.Extensions.Configuration;
using PharmaLex.SmartTRACE.Web.Models;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.Helpers;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public class NotificationService : INotificationService
    {
        private readonly IConfiguration configuration;

        public NotificationService(IConfiguration configuration)
        {
            this.configuration = configuration;
        }

        public async Task SendNotification(string user,
            SubmissionModel submission,
            string application,
            string createdByUser,
            HttpRequest request,
            string product,
            string client,
            string publishers = null)
        {
            const string regexPattern = @"(.*)\s\((.*)\)";
            Regex rx = new Regex(new GeneratedRegexAttribute(regexPattern).Pattern, RegexOptions.NonBacktracking);
            string email;
            string fullUserName;

            if (user.Contains("(") || user.Contains(")"))
            {
                MatchCollection matches = rx.Matches(user);
                fullUserName = matches[0]?.Groups[1]?.ToString().Trim();
                email = matches[0]?.Groups[2]?.ToString();
            }
            else
            {
                email = user;
                fullUserName = user.Split('@')[0].Replace(".", " ");
            }


            MatchCollection leadMatches = rx.Matches(submission.SubmissionResource.RegulatoryLead);
            var leadName = leadMatches[0].Groups[1].ToString();

            string senderEmail = this.configuration.GetValue<string>("AppSettings:SmartPHLEXAdminEmail");

            var from = new EmailAddress(senderEmail, "SmartPHLEX admin");
            var to = new EmailAddress(email);

            var model = new SubmissionStateChangeModel()
            {
                FullName = fullUserName,
                Scheme = request.Scheme,
                Host = request.Host.Value,
                Application = application,
                RequestorName = createdByUser,
                SubmissionType = submission.SubmissionType,
                RegulatoryLead = leadName,
                InitialSentToEmail = submission.SubmissionResource.InitialSentToEmail == user ? null : submission.SubmissionResource.InitialSentToEmail,
                Link = $"{request.Scheme}://{request.Host.Value}/submissions/view/{submission.Id}",
                LifecycleState = ((SubmissionLifeCycleState)submission.LifecycleStateId).GetDescription().ToUpper(),
                Publishers = publishers,
                Product = product,
                Client = client
            };

            await SendMessage(from, to, model);
        }

        public async Task SendNotification(string user, SubmissionModel submission, DocumentModel document, HttpRequest request)
        {
            string senderEmail = this.configuration.GetValue<string>("AppSettings:SmartPHLEXAdminEmail");

            var splittedUser = user.Split(' ');
            var firstName = splittedUser[0];
            var lastName = splittedUser[1];
            var email = splittedUser[2];

            var from = new EmailAddress(senderEmail, "SmartPHLEX admin");
            var to = new EmailAddress(email);

            var model = new SubmissionSourceDocumentsModel()
            {
                FullName = $"{firstName} {lastName}",
                Scheme = request.Scheme,
                Host = request.Host.Value,
                FileName = document.Name,
                Version = document.Version.ToString(),
                Link = $"{request.Scheme}://{request.Host.Value}/submissions/view/{submission.Id}",
            };

            await SendMessage(from, to, model);
        }

        private async Task SendMessage(EmailAddress from, EmailAddress to, BaseNotificationModel model)
        {
            bool IsemailsSendingEnabled = this.configuration.GetValue<bool>("emailsSendingEnabled");
            if (IsemailsSendingEnabled)
            {
                string apiKey = this.configuration.GetValue<string>("SendGrid");
                string templateId = string.Empty;
                if (model is SubmissionStateChangeModel)
                {
                    templateId = this.configuration.GetValue<string>("AppSettings:SubmissionRequestStateChangedTemplateId");
                }
                else if (model is SubmissionSourceDocumentsModel)
                {
                    templateId = this.configuration.GetValue<string>("AppSettings:SubmissionSourceDocumentsUploadedTemplateId");
                }

                SendGridClient client = new SendGridClient(apiKey);

                var msg = new SendGridMessage();
                msg.SetFrom(from);
                msg.AddTo(to);
                msg.SetTemplateId(templateId);
                msg.SetTemplateData(model);

                await client.SendEmailAsync(msg);
            }
        }
    }
}
