﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	<AddRazorSupportForMvc>true</AddRazorSupportForMvc>
  </PropertyGroup>

  <ItemGroup>
	<FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Microsoft.Graph" Version="5.63.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	<PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
	<PackageReference Include="PharmaLex.Authentication.B2C" Version="8.0.0.203" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.SmartTRACE.Entities\PharmaLex.SmartTRACE.Entities.csproj" />
  </ItemGroup>

</Project>
