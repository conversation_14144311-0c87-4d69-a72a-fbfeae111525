using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.RemindersApp;
using PharmaLex.SmartTRACE.RemindersApp.Services;
using System.Threading.Tasks;

namespace PharmaLex_SmartTRACE_RemindersApp
{
    public class Program
    {
        public static async Task Main()
        {
            var host = new HostBuilder()
                .ConfigureFunctionsWorkerDefaults()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json");
                    var builtConfig = config.Build();
                    var secretClient = new SecretClient(
                        new System.Uri($"https://{builtConfig["KeyVaultName"]}.vault.azure.net/"),
                        new DefaultAzureCredential());
                    config.AddAzureKeyVault(secretClient, new KeyVaultSecretManager());
#if DEBUG
                    config.AddJsonFile("appSettings.json");
#endif
                })
                .ConfigureServices((hostContext, services) =>
                {
                    services.AddScoped<IUserContext, SubmissionContext>();

                    services.RegisterDbContext<SmartTRACEContext>();

                    services.AddScoped<IEmailService, EmailService>();

                    services.AddScoped<IPickListReminderHelper, PickListReminderHelper>();

                    services.AddScoped<IReminderService, ReminderService>();
                })
                .Build();

            await host.RunAsync();
        }
    }
}