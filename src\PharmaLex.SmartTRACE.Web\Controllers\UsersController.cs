﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.Caching.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.Authentication.B2C;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.HTMLTags;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    public class UsersController : BaseController
    {
        private readonly IMapper _mapper;
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IUserExport userExport;
        private readonly IAzureAdGraphService graphService;

        public UsersController(IDistributedCacheServiceFactory cache, IMapper mapper, IUserExport userExport, IAzureAdGraphService graphService)
        {
            this.cache = cache;
            this._mapper = mapper;
            this.userExport = userExport;
            this.graphService = graphService;
        }

        [HttpGet, Route("/user/picture")]
        public async Task<IActionResult> Picture()
        {
            return new FileContentResult(await this.graphService.GetUserPicture(this.User.GetEmail()), "image/jpeg");
        }

        [HttpGet, Route("/manage/users/find"), Authorize(Policy = "Editor")]
        public async Task<IActionResult> Find(string term)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var allUsers = await this.cache.CreateMappedEntity<User, UserFindResultModel>().AllAsync();
            List<UserFindResultModel> dbUsers = allUsers.Where(x => x.Email.StartsWith(term, StringComparison.InvariantCultureIgnoreCase) ||
                                                                 (x.FamilyName?.StartsWith(term, StringComparison.InvariantCultureIgnoreCase) ?? false)).ToList();
            List<Microsoft.Graph.Models.User> graphUsers = await this.graphService.FindUsers(term);

            var graphUsersNotInDb = _mapper.Map<List<UserFindResultModel>>(graphUsers.Where(x => !dbUsers.Any(y => y.Value.ToLower() == x.GetEmail().ToLower())));
            var graphUsersAlreadyInDb = dbUsers.Where(x => graphUsers.Any(y => y.GetEmail().ToLower() == x.Value.ToLower()));
            var users = new List<UserFindResultModel>(graphUsersNotInDb);
            users.AddRange(graphUsersAlreadyInDb);

            return Json(users.OrderBy(x => x.Name));
        }

        [HttpGet, Route("/manage/users"), Authorize(Policy = "UserAdmin")]
        public async Task<IActionResult> Index()
        {
            var users = cache.CreateMappedEntity<User, UserModel>()
                 .Configure(o => o
                    .Include(x => x.UserClaim)
                        .ThenInclude(x => x.Claim)
                     .Include(x => x.UserClient)
                        .ThenInclude(x => x.Client));
            return View(await users.WhereAsync(x => x.UserClaim.Count > 0 || x.UserClient.Count > 0));
        }

        [HttpGet, Route("/manage/users/new"), Authorize(Policy = "UserAdmin")]
        public async Task<IActionResult> New()
        {
            var clients = await cache.CreateMappedEntity<Client, ClientModel>().AllAsync();

            var userClients = new UserClientsModel
            {
                Clients = clients.OrderBy(x => x.Name).ToList(),
                User = new UserModel()
            };

            return View("Edit", userClients);
        }

        [HttpPost, Route("/manage/users/new"), ValidateAntiForgeryToken, Authorize(Policy = "UserAdmin")]
        public async Task<IActionResult> New(UserClientsModel userModel)
        {
            if (this.ModelState.IsValid)
            {
                var users = cache.CreateTrackedEntity<User>();
                User u = await users.FirstOrDefaultAsync(x => x.Email.ToLower() == userModel.User.Email.ToLower());
                if (u == null)
                {
                    var user = _mapper.Map<UserModel, User>(userModel.User);
                    users.Add(user);
                    await users.SaveChangesAsync();
                }
                return Redirect("/manage/users");
            }

            var clients = await cache.CreateMappedEntity<Client, ClientModel>().AllAsync();
            userModel.Clients = clients.OrderBy(x => x.Name).ToList();
            return View("Edit", userModel);
        }

        [HttpGet, Route("/manage/users/edit/{id}"), Authorize(Policy = "UserAdmin")]
        public async Task<IActionResult> Edit(int id)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var users = cache.CreateMappedEntity<User, UserModel>()
                .Configure(o => o
                    .Include(x => x.UserClaim)
                        .ThenInclude(x => x.Claim)
                    .Include(x => x.UserClient)
                        .ThenInclude(x => x.Client));

            var clients = await cache.CreateMappedEntity<Client, ClientModel>().AllAsync();
            var currentUser = await users.FirstOrDefaultAsync(x => x.Id == id);
            var userClients = new UserClientsModel
            {
                Clients = clients.OrderBy(x => x.Name).ToList(),
                User = currentUser
            };

            if (currentUser.UserTypeId == (int)UserType.External)
            {
                return View("../ExternalUsers/EditExternalUser", userClients);
            }

            return View(userClients);
        }

        [HttpPost, Route("/manage/users/edit/{id}"), ValidateAntiForgeryToken, Authorize(Policy = "UserAdmin")]
        public async Task<IActionResult> Edit(int id, UserClientsModel userModel)
        {
            if (this.ModelState.IsValid
                && await this.ValidateClaims(userModel.User)
                && !HtmlTags.CheckHTMLTags(userModel.User.GivenName)
                && !HtmlTags.CheckHTMLTags(userModel.User.FamilyName)
                && !HtmlTags.CheckHTMLTags(userModel.User.Email))
            {
                var users = cache.CreateTrackedEntity<User>()
                     .Configure(o => o
                        .Include(x => x.UserClaim)
                            .ThenInclude(x => x.Claim)
                        .Include(x => x.UserClient)
                            .ThenInclude(x => x.Client));
                User u = await users.FirstOrDefaultAsync(x => x.Id == id);

                if (u.UserTypeId == (int)UserType.External)
                {
                    // Revert unauthorized changes for UserTypeId
                    userModel.User.UserTypeId = u.UserTypeId;
                }

                // Revert unauthorized changes for GivenName, FamilyName and Email
                userModel.User.GivenName = u.GivenName;
                userModel.User.FamilyName = u.FamilyName;
                userModel.User.Email = u.Email;

                if (this.User.Claims.Any(x => x.Type.StartsWith("admin:")))
                {
                    _mapper.Map(userModel.User, u);
                }
                else
                {
                    int[] claims = this.User.Claims.Where(x => x.Type.StartsWith("admin:")).Select(x => Int32.Parse(x.Value)).ToArray();
                    List<UserClaim> claimsToKeep = u.UserClaim.Where(x => !claims.Contains(x.ClaimId)).ToList();
                    _mapper.Map(userModel.User, u);
                    foreach (UserClaim uc in claimsToKeep)
                    {
                        u.UserClaim.Add(uc);
                    }
                }
                await users.SaveChangesAsync();
                return Redirect("/manage/users");
            }

            var clients = await cache.CreateMappedEntity<Client, ClientModel>().AllAsync();
            userModel.Clients = clients.OrderBy(x => x.Name).ToList();
            return View(userModel);
        }

        [HttpPost("/manage/users/export"), Authorize(Policy = "Reader"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Export()
        {
            return File(await this.userExport.Export(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"Users_{DateTime.Now.ToString("yyyy-MM-dd")}.xlsx");
        }

        private async Task<bool> ValidateClaims(UserModel user)
        {
            if (this.User.IsSuperAdmin())
            {
                return true;
            }

            var dbClaims = await cache.CreateEntity<Claim>().AllAsync();
            var userClaims = dbClaims.Where(x => user.Claims.Contains(x.Id)).ToList();

            var adminUserClaims = this.User.Claims.Where(x => x.Type.StartsWith("admin:"));

            if (adminUserClaims.Count() > 0)
            {
                return true;
            }

            List<string> allowedClaims = new List<string>();

            foreach (var item in adminUserClaims)
            {
                if (ClaimsHierarchy.GetClaims().ContainsKey(item.Type))
                {
                    allowedClaims.AddRange(ClaimsHierarchy.GetClaims()[item.Type]);
                }
            }

            if (userClaims.Select(x => $"{x.ClaimType}:{x.Name}").Except(allowedClaims).Any())
            {
                throw new UnauthorizedAccessException("The logged in user does not have permission to set the attempted claim(s)");
            }

            return true;
        }
    }
}
