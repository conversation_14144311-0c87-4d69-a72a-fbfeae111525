﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using NSubstitute;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Services
{
    public class UserServiceTests
    {
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IMapper _mapper;
        private readonly IAzureAdB2CGraphService _graphService;
        private readonly IOidcService _oidcService;
        private readonly IHttpContextAccessor _context;
        private readonly IConfiguration _configuration;
        UserService userService;
        public UserServiceTests()
        {
            this._cache = Substitute.For<IDistributedCacheServiceFactory>();
            this._mapper = Substitute.For<IMapper>();
            this._graphService = Substitute.For<IAzureAdB2CGraphService>();
            this._oidcService = Substitute.For<IOidcService>();
            this._configuration = Substitute.For<IConfiguration>();
            this._context = Substitute.For<IHttpContextAccessor>();
            userService = new UserService(_cache, _mapper, _graphService, _oidcService, _context, _configuration);
        }
        #region ProcessExternalUser(UserClientsModel userModel)
        [Fact]
        public async Task ProcessExternalUser_Returns_False()
        {
            //Arange
            UserClientsModel userModel = new UserClientsModel()
            { Clients = new List<ClientModel>() { new ClientModel { Name = "XYZ" } }, User = new UserModel() { Email = "<EMAIL>" } };
            User user = new User();
            user.Email = "<EMAIL>";
            Task<User> TaskUser = Task.FromResult(user);
            var userCache = Substitute.For<ITrackedEntityCacheServiceProxy<User>>();
            _cache.CreateTrackedEntity<User>()
                .Configure(Arg.Any<Func<IIncludable<User>, IIncludable<User>>>()).Returns(userCache);
            userCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<User, bool>>>()).ReturnsForAnyArgs(TaskUser);
            //Act
            var result = await userService.ProcessExternalUser(userModel);
            //Assert
            Assert.False(result);
        }
        [Fact]
        public async Task ProcessExternalUser_Returns_True()
        {
            //Arrange
            UserClientsModel userModel = new UserClientsModel()
            { Clients = new List<ClientModel>() { new ClientModel { Name = "XYZ" } }, User = new UserModel() { Email = "<EMAIL>" } };
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(
                [
                   new KeyValuePair<string, string?>("SendGrid", "test-Key"),
                   new KeyValuePair<string, string?>("emailsSendingEnabled", "true"),
                   new KeyValuePair<string, string?>("AppSettings:SubmissionRequestStateChangedTemplateId", "thempid"),
                   new KeyValuePair<string, string?>("AppSettings:SmartPHLEXAdminEmail","<EMAIL>")
                ])
                .Build();
            userService = new UserService(_cache, _mapper, _graphService, _oidcService, _context, configuration);
            //Act
            var result = await userService.ProcessExternalUser(userModel);
            //Assert
            Assert.True(result);
        }
        [Fact]
        public async Task ProcessExternalUser_ExistingUser_Returns_True()
        {
            //Arrange
            UserClientsModel userModel = new UserClientsModel()
            { Clients = new List<ClientModel>() { new ClientModel { Name = "XYZ" } }, User = new UserModel() { Email = "<EMAIL>" } };
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(
                [
                   new KeyValuePair<string, string?>("SendGrid", "test-Key"),
                   new KeyValuePair<string, string?>("emailsSendingEnabled", "true"),
                   new KeyValuePair<string, string?>("AppSettings:SubmissionRequestStateChangedTemplateId", "thempid"),
                   new KeyValuePair<string, string?>("AppSettings:SmartPHLEXAdminEmail","<EMAIL>")
                ])
                .Build();
            _graphService.UserExists(Arg.Is("<EMAIL>")).Returns(true);
            userService = new UserService(_cache, _mapper, _graphService, _oidcService, _context, configuration);
            //Act
            var result = await userService.ProcessExternalUser(userModel);
            //Assert
            Assert.True(result);
        }
        #endregion

        #region ProcessSignupInvitationResend(UserModel userModel)
        [Fact]
        public async Task ProcessSignupInvitationResend_Returns_InvitationEmailLink()
        {
            //Arange
            var userModel = new UserModel()
            { 
                Email = "<EMAIL>",
                GivenName = "GivenName",
                FamilyName = "FamilyName"
            };
            var user = new User()
            {
                Email = "<EMAIL>",
                GivenName = "GivenName",
                FamilyName = "FamilyName"
            };
            Task<User> TaskUser = Task.FromResult(user);
            var userCache = Substitute.For<ITrackedEntityCacheServiceProxy<User>>();
            _cache.CreateTrackedEntity<User>().Returns(userCache);
            userCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<User, bool>>>()).ReturnsForAnyArgs(TaskUser);
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(
                [
                   new KeyValuePair<string, string?>("SendGrid", "test-Key"),
                   new KeyValuePair<string, string?>("emailsSendingEnabled", "true"),
                   new KeyValuePair<string, string?>("AppSettings:SubmissionRequestStateChangedTemplateId", "thempid"),
                   new KeyValuePair<string, string?>("AppSettings:SmartPHLEXAdminEmail","<EMAIL>")
                ])
                .Build();
            string signupInvitation = "https://decisions-dev.smartphlex.com/signup-invitation?id_token_hint=eyJhbGciOiJSUzI1NiIsImtpZCI6IkUxQkEzNTQxQkEyNTUzQjlCQzQwOTI3RjEzNTlGMjY0RTE3MjRENjEiLCJ4NXQiOiI0Ym8xUWJvbFU3bThRSkpfRTFueVpPRnlUV0UiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.noKWVSbZC3FRxvXekQaplQMaTgwTZZ3j-CJ84undWeVXkIx2xpKwq-Gr1iVmCjmEGyfAScSE4M8ukif26kLBHR4LrAWg1FW6bYHsg3UuGr0S5Q1LPXiJmuGq5FlzhrywzAbXPlIuhfGOYBPA4bMjfx55k7mrurIHkUf251xb4Gl_Lhvzseg-iWozGBkeTWOTGlpxB_XLoscOmqOwCWm8J1G3xoN9SrMroh3P0ZzNvZjLkTPWqccRPQLZWVZw3HoNiN1-OGGdLw88A6DQaVLNmNFS0v4pC_KEL1lNO5qu0LI4xfsmhZPhiezZ7skPSgmnfjA4XL1lFoOFNxMRsZ36wQ";
            _oidcService.GetSignupInvitationLink("signup-invitation",
                new Dictionary<string, string>
                {
                        { "displayName", $"{userModel.GivenName} {userModel.FamilyName}" },
                        { "givenName", userModel.GivenName },
                        { "surname", userModel.FamilyName },
                        { "email", userModel.Email }
                }).ReturnsForAnyArgs(signupInvitation);
            userService = new UserService(_cache, _mapper, _graphService, _oidcService, _context, configuration);

            //Act
            var result = await userService.ProcessSignupInvitationResend(userModel);

            //Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);
            Assert.Equal(signupInvitation, result);
        }

        [Fact]
        public async Task ProcessSignupInvitationResend_Returns_EmptyLink()
        {
            //Arange
            var userModel = new UserModel()
            {
                Email = "<EMAIL>",
                GivenName = "GivenName",
                FamilyName = "FamilyName"
            };
            var user = new User()
            {
                Email = "<EMAIL>",
                GivenName = "GivenName",
                FamilyName = "FamilyName"
            };
            Task<User> TaskUser = Task.FromResult(user);
            var userCache = Substitute.For<ITrackedEntityCacheServiceProxy<User>>();
            _cache.CreateTrackedEntity<User>().Returns(userCache);
            userCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<User, bool>>>()).ReturnsForAnyArgs(TaskUser);
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(
                [
                   new KeyValuePair<string, string?>("SendGrid", "test-Key"),
                   new KeyValuePair<string, string?>("emailsSendingEnabled", "true"),
                   new KeyValuePair<string, string?>("AppSettings:SubmissionRequestStateChangedTemplateId", "thempid"),
                   new KeyValuePair<string, string?>("AppSettings:SmartPHLEXAdminEmail","<EMAIL>")
                ])
                .Build();
            userService = new UserService(_cache, _mapper, _graphService, _oidcService, _context, configuration);

            //Act
            var result = await userService.ProcessSignupInvitationResend(userModel);

            //Assert
            Assert.Empty(result);
        }

        #endregion
    }
}