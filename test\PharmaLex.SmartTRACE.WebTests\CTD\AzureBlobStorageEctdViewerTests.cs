﻿using AutoMapper;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using NSubstitute;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.CTD;
using PharmaLex.SmartTRACE.Web.Models.Ich.AU;
using PharmaLex.SmartTRACE.Web.Models.Ich.AU.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.CH.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.CH32;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32;
using PharmaLex.SmartTRACE.Web.Models.Ich.Eu.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.US;
using PharmaLex.SmartTRACE.Web.Models.Ich.US.Models;
using PharmaLex.SmartTRACE.Web.Storage;
using System.Reflection;

namespace PharmaLex.SmartTRACE.WebTests.CTD
{
    public class AzureBlobStorageEctdViewerTests
    {
        readonly ISubmissionBlobContainer blobContainer;
        readonly IMapper mapper;
        public AzureBlobStorageEctdViewerTests()
        {
            var configuration = new MapperConfiguration(cfg => {
                cfg.AddProfile<LeafMappingProfile>();
                cfg.AddProfile<CtdSectionBaseMappingProfile>();
                cfg.AddProfile<Web.Models.Ich.Eu.Models.CtdEnvelopeMappingProfile>();
                cfg.AddProfile<AuEnvelopeModelProfile>();
                cfg.AddProfile<ChEnvelopModelProfile>();
                cfg.AddProfile<AuEnvelopeModelProfile>();
                cfg.AddProfile<Web.Models.Ich.US.Models.CtdEnvelopeMappingProfile>();
                cfg.AddProfile<TestMappingProfile>();
                
            });
            mapper = configuration.CreateMapper();

            blobContainer = Substitute.For<ISubmissionBlobContainer>();
        }

        [Fact]
        public async Task Check_GetSubmissions_success()
        {
            var sut = GetSut("eu", out string seqLoc);
            var subs = await sut.GetSubmissions(seqLoc);
            Assert.NotEmpty(subs);
        }
        [Theory]
        [InlineData("eu")]
        [InlineData("us")]
        [InlineData("au")]
        [InlineData("ch")]
        public async Task Check_BuildTree_without_submission_success(string country)
        {
            var sut = GetSut(country, out string seqLoc);
            var tree = await sut.BuildTree(seqLoc);
            Assert.NotEmpty(tree);
        }

        [Theory]
        [InlineData("")]
        [InlineData("0000/")]
        [InlineData("0000")]
        [InlineData("all")]
        public async Task Check_BuildTree_with_submission_parameter_success(string submission)
        {
            var sut = GetSut("eu", out string seqLoc);
            var subs = new List<string>();
            if(!string.IsNullOrEmpty(submission))
                subs = new List<string> { submission };
            var tree = await sut.BuildTree(seqLoc, subs);
            Assert.NotEmpty(tree);
        }
        #region Helper Methods and Maper profile class
        private AzureBlobStorageEctdViewer GetSut(string country, out string basePath)
        {
            UpdateBlobClient(blobContainer, country, out basePath);

            var ectdProvider = new EctdProvider(blobContainer);
            var reportfileReader = new EctdStudyReportFileReader(blobContainer);
            var studyProcessor = new EctdStudyReportProcessor(reportfileReader, mapper);
            return new AzureBlobStorageEctdViewer(mapper, ectdProvider, blobContainer, studyProcessor);
        }

        private static void UpdateBlobClient(ISubmissionBlobContainer blobContainer, string country, out string basePath)
        {
            #region Get basepath
            string submission = country == "us" ? "0001" : "0000";
            var rootFolder = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? "";
            basePath = $"{rootFolder}/TestSubmissionFiles/unittestsub{country}";
            #endregion

            #region index.xml
            var blobClient = Substitute.For<BlobClient>();
            var indexPath = $"{basePath}/{submission}/index.xml";
            blobClient.OpenReadAsync().ReturnsForAnyArgs(Task.FromResult(File.OpenRead(indexPath) as Stream));
            blobContainer.GetBlobClientAsync(Arg.Is<string>(x => x.Contains("index.xml"))).Returns(blobClient);
            #endregion

            #region regional.xml and stf.xml
            UpdateM1BlobClient(blobContainer, country, basePath, submission);
            if (country == "us")
                UpdateStudyBlobClient(blobContainer, basePath, submission);
            #endregion

            #region GetSequence and GetDirectories
            if (country == "eu")
            {
                var blobItems = new List<BlobItem> { BlobsModelFactory.BlobItem("testurl.test.net/test") }.AsEnumerable();
                blobContainer.GetSequences(basePath).Returns(Task.FromResult(new List<string> { submission, $"a.zip/{submission}-workingdocuments" }));
                blobContainer.ListDirectoriesAsync($"{basePath}/a.zip/{submission}-workingdocuments").Returns(Task.FromResult(new List<(string prefix, string uri)> { ("test", "test.azure.com/test") }.AsEnumerable()));
                blobContainer.ListBlobsAsync($"{basePath}/a.zip/{submission}-workingdocuments").Returns(Task.FromResult(blobItems));
            }
            else
                blobContainer.GetSequences(basePath).Returns(Task.FromResult(new List<string> { submission }));
            #endregion
        }
        private static void UpdateM1BlobClient(ISubmissionBlobContainer blobContainer, string country, string basePath, string submission)
        {
            var blobClient = Substitute.For<BlobClient>();
            var rFile = $"{country}-regional.xml";
            var indexPath = $"{basePath}/{submission}/m1/{country}/{rFile}";
            
            blobClient.OpenReadAsync().ReturnsForAnyArgs(Task.FromResult(File.OpenRead(indexPath) as Stream));
            blobContainer.GetBlobClientAsync(Arg.Is<string>(x => x.Contains(rFile))).Returns(blobClient);
        }
        private static void UpdateStudyBlobClient(ISubmissionBlobContainer blobContainer, string basePath, string submission)
        {
            string studyPath = $"{basePath}/{submission}/m4/42-stud-rep/421-pharmacol/4211-prim-pd/teststudyf/stf-tsfa.xml";
            var blobClient = Substitute.For<BlobClient>();
            blobClient.OpenReadAsync().ReturnsForAnyArgs(Task.FromResult(File.OpenRead(studyPath) as Stream));
            blobContainer.GetBlobClientAsync(Arg.Is<string>(x => x.Contains("stf-tsfa.xml"))).Returns(blobClient);
        }
        public class TestMappingProfile : Profile
        {
            public TestMappingProfile()
            {
                this.CreateMap<UsAdmin, UsEnvelopeModel>()
                    .ForMember(d => d.Contacts, s => s.MapFrom(x => x.Contacts));
            }
        }
        #endregion

    }


}
