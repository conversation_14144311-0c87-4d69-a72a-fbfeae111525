﻿CREATE TRIGGER [dbo].[Document_Insert] ON [dbo].[Document]
FOR INSERT AS
INSERT INTO [Audit].[Document_Audit]
SELECT 'I', [Id], [Name], [DocumentTypeId], [Version], [SubmissionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Document_Update] ON [dbo].[Document]
FOR UPDATE AS
INSERT INTO [Audit].[Document_Audit]
SELECT 'U',  [Id], [Name], [DocumentTypeId], [Version], [SubmissionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Document_Delete] ON [dbo].[Document]
FOR DELETE AS
INSERT INTO [Audit].[Document_Audit]
SELECT 'D', [Id], [Name], [DocumentTypeId], [Version], [SubmissionId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO