﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class AddInitialSentToEmail : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "InitialSentToEmail",
                schema: "Audit",
                table: "SubmissionResource_Audit",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "InitialSentToEmail",
                table: "SubmissionResource",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.SqlFileExec("0003-AddInitialSentToEmail-UpdateSubmissionResourceTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InitialSentToEmail",
                schema: "Audit",
                table: "SubmissionResource_Audit");

            migrationBuilder.DropColumn(
                name: "InitialSentToEmail",
                table: "SubmissionResource");
        }
    }
}
