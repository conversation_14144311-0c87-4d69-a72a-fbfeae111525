﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class RegulatoryAuthority : EntityBase
    {
        public RegulatoryAuthority()
        {
            RegulatoryAuthorityCountry = new HashSet<RegulatoryAuthorityCountry>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public int Market { get; set; }
        public string Url { get; set; }
        public bool NationalAuthority { get; set; }
        public string Acronym { get; set; }

        public virtual ICollection<RegulatoryAuthorityCountry> RegulatoryAuthorityCountry { get; set; }
    }
}
