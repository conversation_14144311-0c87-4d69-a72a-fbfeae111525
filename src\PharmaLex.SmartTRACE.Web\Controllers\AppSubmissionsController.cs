﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.Helpers;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Helpers.Extensions;
using PharmaLex.SmartTRACE.Web.HTMLTags;
using PharmaLex.SmartTRACE.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    public class AppSubmissionsController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper _mapper;
        private readonly INotificationService notificationService;
        private readonly ISubmissionExport subExport;
        private readonly ISubmissionService submissionService;

        public AppSubmissionsController(IDistributedCacheServiceFactory cache,
                                        IMapper mapper,
                                        INotificationService notificationService,
                                        ISubmissionExport subExport,
                                        ISubmissionService submissionService)
        {
            this.cache = cache;
            this._mapper = mapper;
            this.notificationService = notificationService;
            this.subExport = subExport;
            this.submissionService = submissionService;
        }

        [HttpGet, Route("/app-submissions"), Authorize(Policy = "Reader")]
        public async Task<IActionResult> Index(
           [FromQuery] int skip = 0,
           [FromQuery] int take = 25,
           [FromQuery] string sort = null,
           [FromQuery] string[] filters = null)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var submissions = await this.submissionService.GetPagedSubmissionsAsync(this.User, skip, take, new SubmissionFilterModel(), sort);
            return View(submissions);
        }

        [HttpGet, Route("/paged-app-submissions"), Authorize(Policy = "Reader")]
        public async Task<IActionResult> IndexPagedSubmissions(
           [FromQuery] int skip = 0,
           [FromQuery] int take = 25,
           [FromQuery] string sort = null,
           [FromQuery] string[] filters = null)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var model = new SubmissionFilterModel();
            var submissions = await this.submissionService.GetPagedSubmissionsAsync(this.User, skip, take, model.FromFilters(filters), sort);
            return Json(submissions);
        }

        [HttpGet, Route("/submissions/edit/{id}")]
        [AuthorizeMultiple("Editor;ExternalEditor", false)]
        public async Task<IActionResult> Edit(int id)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var submissionModel = await LoadSubmissionData(id);
            if (submissionModel == null)
            {
                return Forbid();
            }
            if ((SubmissionLifeCycleState)submissionModel.Submission.LifecycleStateId == SubmissionLifeCycleState.Archived &&
                this.User.HasClaim(x => x.Type == "application:Reader"))
            {
                return Redirect($"/submissions/view/{id}");
            }
            return View("EditSubmission", submissionModel);

        }

        [HttpPost, Route("/submissions/edit/{id}"), ValidateAntiForgeryToken, Authorize(Policy = "Editor")]
        public async Task<IActionResult> SaveEdit(int id, EditSubmissionViewModel model, CancellationToken cancellationToken)
        {
            if (!this.ModelState.IsValid || 
                (HtmlTags.CheckHTMLTags(model.SubmissionResource.Comments)) ||
                (HtmlTags.CheckHTMLTags(model.Submission.Comments)) ||
                (HtmlTags.CheckHTMLTags(model.Submission.Description)))
            {
                return this.BadRequest(this.ModelState);
            }
            if (!cancellationToken.IsCancellationRequested)
            {
                var submissionCache = cache.CreateTrackedEntity<Submission>();
                var submission = await submissionCache.FirstOrDefaultAsync(x => x.Id == id);
                _mapper.Map(model.Submission, submission);
                await submissionCache.SaveChangesAsync();

                var subResourceCache = cache.CreateTrackedEntity<SubmissionResource>();
                var submissionResource = await subResourceCache.FirstOrDefaultAsync(x => x.SubmissionId == submission.Id);
                _mapper.Map(model.SubmissionResource, submissionResource);
                await subResourceCache.SaveChangesAsync();

                await this.UpdateSubmissionCountries(model, submission.Id);

                await this.UpdateSubmissionPublishers(model, submissionResource.Id);

                this.AddConfirmationNotification($"<em>{submission.UniqueId}</em> updated");

                return await this.Edit(model.Submission.Id);
            }

            return null;
        }

        [HttpPost, Route("/submissions/next-state/{id}"), Authorize(Policy = "Editor")]
        public async Task<IActionResult> NextState(int id, EditSubmissionViewModel model)
        {
            if (!this.ModelState.IsValid || 
                (HtmlTags.CheckHTMLTags(model.SubmissionResource.Comments)) ||
                (HtmlTags.CheckHTMLTags(model.Submission.Comments)) || 
                (HtmlTags.CheckHTMLTags(model.Submission.Description)))
            {
                return this.BadRequest(this.ModelState);
            }
            var submissionCache = cache.CreateTrackedEntity<Submission>().Configure(o => o.Include(x => x.Application));
            var submission = await submissionCache.FirstOrDefaultAsync(x => x.Id == id);
            _mapper.Map(model.Submission, submission);
            await submissionCache.SaveChangesAsync();

            var subResourceCache = cache.CreateTrackedEntity<SubmissionResource>()
                .Configure(o => o
                    .Include(x => x.Submission)
                    .Include(x => x.SubmissionPublisher)
                        .ThenInclude(x => x.Publisher));
            var submissionResource = await subResourceCache.FirstOrDefaultAsync(x => x.SubmissionId == id);
            _mapper.Map(model.SubmissionResource, submissionResource);
            submissionResource.Submission.PreviousLifecycleStateId = submissionResource.Submission.LifecycleStateId;
            submissionResource.Submission.LifecycleStateId = (int)((SubmissionLifeCycleState)model.Submission.LifecycleStateId).Next();
            await subResourceCache.SaveChangesAsync();

            await this.UpdateSubmissionPublishers(model, submissionResource.Id);

            await this.UpdateSubmissionCountries(model, submission.Id);

            model.Submission.SubmissionType = (await PicklistHelper.ExtractPicklist(cache, model.Submission.SubmissionTypeId))?.Name;

            await this.NotifyUser(submission, submissionResource, model.Submission, model.Product, model.Client.Name);

            if ((SubmissionLifeCycleState)submissionResource.Submission.LifecycleStateId == SubmissionLifeCycleState.WithdrawnFromHA ||
                ((SubmissionLifeCycleState)submissionResource.Submission.LifecycleStateId == SubmissionLifeCycleState.Archived &&
                this.User.HasClaim(x => x.Type == "application:Editor")))
            {
                return Redirect($"/submissions/view/{submissionResource.Submission.Id}");
            }
            return Redirect($"/submissions/edit/{submissionResource.Submission.Id}");
        }

        [HttpPost, Route("/submissions/previous-state/{id}"), Authorize(Policy = "Editor")]
        public async Task<IActionResult> PreviousState(int id, EditSubmissionViewModel model)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var submissionCache = cache.CreateTrackedEntity<Submission>().Configure(o => o.Include(x => x.Application));
            var submission = await submissionCache.FirstOrDefaultAsync(x => x.Id == id);
            _mapper.Map(model.Submission, submission);
            submission.LifecycleStateId = (int)((SubmissionLifeCycleState)model.Submission.LifecycleStateId).Previous();
            submission.PreviousLifecycleStateId = submission.LifecycleStateId - 1;
            await submissionCache.SaveChangesAsync();

            var subResourceCache = cache.CreateTrackedEntity<SubmissionResource>()
               .Configure(o => o
                   .Include(x => x.Submission)
                   .Include(x => x.SubmissionPublisher)
                       .ThenInclude(x => x.Publisher));
            var submissionResource = await subResourceCache.FirstOrDefaultAsync(x => x.SubmissionId == id);
            _mapper.Map(model.SubmissionResource, submissionResource);
            await subResourceCache.SaveChangesAsync();

            await this.UpdateSubmissionPublishers(model, submissionResource.Id);

            await this.UpdateSubmissionCountries(model, submission.Id);

            model.Submission.SubmissionType = (await PicklistHelper.ExtractPicklist(cache, model.Submission.SubmissionTypeId))?.Name;

            await this.NotifyUser(submission, submissionResource, model.Submission, model.Product, model.Client.Name);
            return Redirect($"/submissions/edit/{submission.Id}");
        }

        [HttpPost, Route("/submissions/final-state/{id}"), Authorize(Policy = "Editor")]
        public async Task<IActionResult> FinalState(int id, [FromBody] DateTime withdrawalDate)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var submissionCache = cache.CreateTrackedEntity<Submission>();
            var submission = await submissionCache.FirstOrDefaultAsync(x => x.Id == id);
            submission.WithdrawalDate = withdrawalDate;
            await submissionCache.SaveChangesAsync();

            var subResourceCache = cache.CreateTrackedEntity<SubmissionResource>().Configure(o => o.Include(x => x.Submission));
            var submissionResource = await subResourceCache.FirstOrDefaultAsync(x => x.SubmissionId == id);
            submissionResource.Submission.PreviousLifecycleStateId = submissionResource.Submission.LifecycleStateId;
            submissionResource.Submission.LifecycleStateId = (int)SubmissionLifeCycleState.WithdrawnFromHA;
            await subResourceCache.SaveChangesAsync();
            return NoContent();
        }

        [HttpPost, Route("/submissions/obsolete-state/{id}"), Authorize(Policy = "BusinessAdmin")]
        public async Task<IActionResult> MoveToObsoleteState(int id)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var submissionCache = cache.CreateTrackedEntity<Submission>();
            var submission = await submissionCache.FirstOrDefaultAsync(x => x.Id == id);
            submission.PreviousLifecycleStateId = submission.LifecycleStateId;
            submission.LifecycleStateId = (int)SubmissionLifeCycleState.Obsolete;
            await submissionCache.SaveChangesAsync();
            return Ok();
        }

        [HttpGet, Route("/submissions/view/{id}"), Authorize(Policy = "Reader")]
        public async Task<IActionResult> View(int id)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var submissionModel = await LoadSubmissionData(id);
            if (submissionModel == null)
            {
                return Forbid();
            }
            return View("ViewSubmission", submissionModel);
        }

        [HttpPost("/submissions/export"), Authorize(Policy = "Reader")]
        public async Task<IActionResult> Export(SubmissionFilterModel model)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            return File(await this.subExport.Export(User, model), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"Submissions_{DateTime.Now.ToString("yyyy-MM-dd")}.xlsx");
        }

        [HttpPost("/submissions/{id}/document")]
        [AuthorizeMultiple("Editor;ExternalEditor", false)]
        public async Task<IActionResult> SaveDocument([FromBody] DocumentModel model)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var documentCache = cache.CreateTrackedEntity<Document>();
            var document = _mapper.Map<DocumentModel, Document>(model);
            documentCache.Add(document);
            await documentCache.SaveChangesAsync();
            return Json(document);
        }

        [HttpDelete("/submissions/{id}/document/{documentId}")]
        [AuthorizeMultiple("Editor;ExternalEditor", false)]
        public async Task<IActionResult> DeleteDocument(int id, int documentId)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var documentCache = cache.CreateTrackedEntity<Document>();
            var document = await documentCache.FirstOrDefaultAsync(x => x.Id == documentId);
            documentCache.Remove(document);
            await documentCache.SaveChangesAsync();
            return NoContent();
        }

        [HttpGet("/submissions/{id}/{fileName}/version")]
        [AuthorizeMultiple("Editor;ExternalEditor", false)]
        public async Task<IActionResult> GetLatestFileVersion(int id, string fileName, [FromQuery] string documentTypeId)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var documentCache = cache.CreateMappedEntity<Document, DocumentModel>();
            var docWithSameNames = await documentCache.WhereAsync(x => x.Name == fileName && x.DocumentTypeId == int.Parse(documentTypeId) && x.SubmissionId == id);
            int maxVersion = 0;
            if (docWithSameNames.Count > 0)
            {
                maxVersion = docWithSameNames.Max(x => x.Version);
            }

            return Json(maxVersion);
        }

        [HttpPatch, Route("/submissions/{id}/submission-dossier"), Authorize(Policy = "Editor")]
        public async Task<IActionResult> SaveDossierFormat(int id, [FromBody] string dossierFormatId)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            if (!string.IsNullOrEmpty(dossierFormatId))
            {
                var submissionCache = cache.CreateTrackedEntity<Submission>();
                var submission = await submissionCache.FirstOrDefaultAsync(x => x.Id == id);
                submission.DossierFormatId = int.Parse(dossierFormatId);
                await submissionCache.SaveChangesAsync();
            }
            return Ok();
        }

        private async Task<EditSubmissionViewModel> LoadSubmissionData(int submissionId)
        {
            var appProductCache = cache.CreateEntity<ApplicationProduct>()
                .Configure(o => o
                    .Include(x => x.Product)
                    .Include(x => x.Application));

            var subPublisherCache = cache.CreateEntity<SubmissionPublisher>()
               .Configure(o => o
                   .Include(x => x.Publisher));

            var subCountryCache = cache.CreateEntity<SubmissionCountry>()
               .Configure(o => o
                   .Include(x => x.Country)
                        .ThenInclude(x => x.Region));

            var appCountryCache = cache.CreateEntity<ApplicationCountry>()
                .Configure(o => o
                        .Include(x => x.Country));

            var submissionCache = cache.CreateEntity<Submission>()
               .Configure(o => o
                   .Include(x => x.Project)
                        .ThenInclude(x => x.Client)
                   .Include(x => x.Document)
                   .Include(x => x.SubmissionResource));

            var existingSubmission = await submissionCache.FirstOrDefaultAsync(x => x.Id == submissionId);

            var submissionCountries = await subCountryCache.WhereAsync(x => x.SubmissionId == submissionId);

            var applicationCountries = await appCountryCache.WhereAsync(x => x.ApplicationId == existingSubmission.ApplicationId);

            var appProducts = await appProductCache.WhereAsync(x => x.ApplicationId == existingSubmission.ApplicationId);

            var submissionPublishers = await subPublisherCache.WhereAsync(x => x.SubmissionResourceId == existingSubmission.SubmissionResource.Id);

            var clientIds = (await this.cache.CreateEntity<UserClient>().WhereAsync(x => x.UserId == this.CurrentUserId)).Select(x => x.ClientId).ToList();

            if (!clientIds.Contains(existingSubmission.Project.ClientId))
            {
                return null;
            }

            var countries = _mapper.Map<IList<CountryModel>>(submissionCountries.Select(x => x.Country));
            var appCountries = _mapper.Map<IList<CountryModel>>(applicationCountries.Select(x => x.Country));
            var submissionClientId = existingSubmission.Project.ClientId;
            var application = appProducts.Select(x => x.Application).FirstOrDefault();

            var submissionModel = new EditSubmissionViewModel()
            {
                Application = _mapper.Map<ApplicationModel>(application),
                Submission = _mapper.Map<SubmissionModel>(existingSubmission),
                Project = _mapper.Map<ProjectModel>(existingSubmission.Project),
                SubmissionResource = _mapper.Map<SubmissionResourceModel>(existingSubmission.SubmissionResource),
                Client = _mapper.Map<ClientModel>(existingSubmission.Project.Client),
                Projects = await cache.CreateMappedEntity<Project, ProjectModel>().WhereAsync(x => x.ClientId == submissionClientId),
                Picklists = await PicklistHelper.GetPicklistDataBasedOnCountry(cache, countries),
                AllCountries = countries.OrderBy(x => x.Name).ToList(),
                ApplicationCountries = appCountries.OrderBy(x => x.Name).ToList(),
                InvalidFields = RequiredFieldsHelper.GetRequiredFields((SubmissionLifeCycleState)existingSubmission.LifecycleStateId).ToList()
            };

            foreach (var country in submissionCountries)
            {
                submissionModel.Submission.CountriesIds.Add(country.CountryId);
            }

            submissionModel.Submission.DisplayCountries = string.Join(", ", countries.Select(x => x.Name).Distinct().OrderBy(x => x).ToList());
            submissionModel.Submission.SubmissionDeliveryDetails = (await PicklistHelper.ExtractPicklist(cache, existingSubmission.DeliveryDetailsId))?.Name;
            submissionModel.Submission.DossierFormat = (await PicklistHelper.ExtractPicklist(cache, existingSubmission.DossierFormatId))?.Name;
            submissionModel.Submission.SubmissionType = (await PicklistHelper.ExtractPicklist(cache, existingSubmission.SubmissionTypeId))?.Name;
            submissionModel.Submission.SubmissionUnit = (await PicklistHelper.ExtractPicklist(cache, existingSubmission.SubmissionUnitId))?.Name;
            submissionModel.Submission.SubmissionMode = (await PicklistHelper.ExtractPicklist(cache, existingSubmission.SubmissionModeId))?.Name;
            submissionModel.Submission.RegionId = submissionCountries.Select(x => x.Country).FirstOrDefault()?.RegionId ?? 0;
            submissionModel.SubmissionResource.Priority = (await PicklistHelper.ExtractPicklist(cache, existingSubmission.SubmissionResource.PriorityId))?.Name;
            submissionModel.SubmissionResource.EstimatedSize = (await PicklistHelper.ExtractPicklist(cache, existingSubmission.SubmissionResource.EstimatedSizeId))?.Name;
            submissionModel.SubmissionResource.PublishingTeam = (await PicklistHelper.ExtractPicklist(cache, existingSubmission.SubmissionResource.PublishingTeamId))?.Name;
            submissionModel.SubmissionResource.Publishers = _mapper.Map<IList<UserFindResultModel>>(submissionPublishers.Select(x => x.Publisher));
            submissionModel.SubmissionResource.DisplayPublishers = string.Join(", ", submissionModel.SubmissionResource.Publishers.Select(x => x.Name).Distinct().OrderBy(x => x).ToList());
            submissionModel.Application.ApplicationType = (await PicklistHelper.ExtractPicklist(cache, application?.ApplicationTypeId))?.Name;
            submissionModel.Application.ProcedureType = (await PicklistHelper.ExtractPicklist(cache, application?.ProcedureTypeId))?.Name;
            submissionModel.Client.ContractOwner = (await PicklistHelper.ExtractPicklist(cache, submissionModel.Client.ContractOwnerId))?.Name;
            submissionModel.Product = string.Join(" | ", appProducts.Select(x => x.Product)
                                                                   .Select(async x => $"{x.Name}, {(await PicklistHelper.ExtractPicklist(cache, x.DosageFormId)).Name}, {x.Strength}")
                                                                   .Select(x => x.GetAwaiter().GetResult())
                                                                   .ToList());

            foreach (var document in submissionModel.Submission.Documents)
            {
                var sourceDocument = await cache.CreateEntity<Document>()
                                                    .FirstOrDefaultAsync(x => x.Name == document.Name &&
                                                                              x.Version == document.Version &&
                                                                              x.SubmissionId == submissionModel.Submission.Id &&
                                                                              x.DocumentTypeId == (int)DocumentType.Source);

                if (sourceDocument != null)
                {
                    var files = await cache.CreateEntity<DocshifterDocumentFile>().WhereAsync(x => x.DocumentId == sourceDocument.Id);
                    document.FilesCount = files.Count();
                    if (document.DocumentTypeId == (int)DocumentType.SubmissionReady)
                    {
                        document.FilesCount = files.Count(x => x.StateId == (int)DocumentFileState.Processed);
                    }
                }
            }

            var isActualDateNotRequired = existingSubmission.LifecycleStateId >= (int)SubmissionLifeCycleState.ApprovedForSubmission &&
                                       submissionModel.Submission.SubmissionDeliveryDetails == "Publish & Send to Client";

            if (isActualDateNotRequired)
            {
                submissionModel.InvalidFields.Remove("Submission.ActualSubmissionDate");
            }

            return submissionModel;
        }

        private async Task UpdateSubmissionPublishers(EditSubmissionViewModel model, int submissionResourceId)
        {
            var subPublisherCache = cache.CreateTrackedEntity<SubmissionPublisher>();
            var subPublishers = (await subPublisherCache.WhereAsync(x => x.SubmissionResourceId == submissionResourceId)).ToList();
            var missingPublishers = model.SubmissionResource.Publishers.Where(x => !subPublishers.Select(y => y.PublisherId).Contains(x.Id)).ToList();

            foreach (var missingPublisher in missingPublishers)
            {
                var users = cache.CreateTrackedEntity<User>();
                User user = await users.FirstOrDefaultAsync(x => x.Email.ToLower() == missingPublisher.Email.ToLower());
                if (user == null)
                {
                    user = _mapper.Map<User>(missingPublisher);
                    users.Add(user);
                    await users.SaveChangesAsync();
                }

                subPublisherCache.Add(new SubmissionPublisher
                {
                    SubmissionResourceId = submissionResourceId,
                    PublisherId = user.Id
                });
            }

            var deletedPublishers = subPublishers.Where(x => !model.SubmissionResource.Publishers.Select(y => y.Id).Contains(x.PublisherId)).ToList();

            foreach (var deletedPublisher in deletedPublishers)
            {
                subPublisherCache.Remove(deletedPublisher);
            }

            await subPublisherCache.SaveChangesAsync();
        }

        private async Task UpdateSubmissionCountries(EditSubmissionViewModel model, int submissionId)
        {
            var subCountryCache = cache.CreateTrackedEntity<SubmissionCountry>();
            var subCountries = (await subCountryCache.WhereAsync(x => x.SubmissionId == submissionId)).ToList();
            var missingCountries = model.Submission.CountriesIds.Where(x => !subCountries.Select(y => y.CountryId).Contains(x)).ToList();

            foreach (var missingCountry in missingCountries)
            {
                subCountryCache.Add(new SubmissionCountry
                {
                    CountryId = missingCountry,
                    SubmissionId = submissionId
                });
            }

            var deletedAppCountries = subCountries.Where(x => !model.Submission.CountriesIds.Select(y => y).Contains(x.CountryId)).ToList();

            foreach (var deletedAppCountry in deletedAppCountries)
            {
                subCountryCache.Remove(deletedAppCountry);
            }

            await subCountryCache.SaveChangesAsync();
        }

        private async Task NotifyUser(Submission submission, SubmissionResource submissionResource, SubmissionModel model, string product, string client)
        {
            var createdByUser = await cache.CreateMappedEntity<User, UserModel>().FirstOrDefaultAsync(x => x.Email.ToLower() == submission.CreatedBy.ToLower());
            var submissionResourceModel = await cache.CreateMappedEntity<SubmissionResource, SubmissionResourceModel>().FirstOrDefaultAsync(x => x.SubmissionId == submission.Id);
            var subPublisherCache = cache.CreateEntity<SubmissionPublisher>().Configure(o => o.Include(x => x.Publisher));
            var subPublishers = await subPublisherCache.WhereAsync(x => x.SubmissionResourceId == submissionResourceModel.Id);
            model.SubmissionResource = submissionResourceModel;
            model.SubmissionResource.DisplayPublishers = string.Join(", ", subPublishers.Select(x => x.Publisher).Select(x => $"{x.GivenName} {x.FamilyName} {(x.Email)}").Distinct().OrderBy(x => x).ToList());
            model.LifecycleStateId = submission.LifecycleStateId;
            var currentUser = User;

            if (submissionResource.RegulatoryLead == submissionResource.PublishingLead &&
                submissionResource.SubmissionPublisher.Select(x => x.Publisher).All(x => submissionResource.RegulatoryLead.Contains(x.Email)) &&
                submissionResource.RegulatoryLead.Contains(currentUser.Identity.Name))
            {
                return;
            }
            else if (submissionResource.RegulatoryLead == submissionResource.PublishingLead &&
                     submissionResource.SubmissionPublisher.Select(x => x.Publisher).All(x => submissionResource.RegulatoryLead.Contains(x.Email)) &&
                     !submissionResource.RegulatoryLead.Contains(currentUser.Identity.Name))
            {
                await this.notificationService.SendNotification(submissionResource.RegulatoryLead, model, submission.Application.ApplicationNumber, createdByUser.DisplayFullName, this.Request, product, client);
                return;
            }

            if ((SubmissionLifeCycleState)submission.LifecycleStateId == SubmissionLifeCycleState.Planned)
            {
                if (submissionResource.RegulatoryLead == submissionResource.PublishingLead)
                {
                    if (!submissionResource.RegulatoryLead.Contains(currentUser.Identity.Name))
                    {
                        await this.notificationService.SendNotification(submissionResource.RegulatoryLead, model, submission.Application.ApplicationNumber, createdByUser.DisplayFullName, this.Request, product, client);
                    }
                    foreach (var publisher in submissionResource.SubmissionPublisher.Select(x => x.Publisher))
                    {
                        var existingPublisher = await cache.CreateMappedEntity<User, UserModel>().FirstOrDefaultAsync(x => x.Id == publisher.Id);
                        if (existingPublisher.DisplayNameAndEmail != submissionResource.RegulatoryLead && !existingPublisher.DisplayNameAndEmail.Contains(currentUser.Identity.Name))
                        {
                            await this.notificationService.SendNotification(existingPublisher.DisplayNameAndEmail, model, submission.Application.ApplicationNumber, createdByUser.DisplayFullName, this.Request, product, client);
                        }
                    }
                }

                if (submissionResource.RegulatoryLead != submissionResource.PublishingLead)
                {
                    if (!submissionResource.RegulatoryLead.Contains(currentUser.Identity.Name))
                    {
                        await this.notificationService.SendNotification(submissionResource.RegulatoryLead, model, submission.Application.ApplicationNumber, createdByUser.DisplayFullName, this.Request, product, client, model.SubmissionResource.DisplayPublishers);
                    }
                    if (!submissionResource.PublishingLead.Contains(currentUser.Identity.Name))
                    {
                        await this.notificationService.SendNotification(submissionResource.PublishingLead, model, submission.Application.ApplicationNumber, createdByUser.DisplayFullName, this.Request, product, client);
                    }
                    foreach (var publisher in submissionResource.SubmissionPublisher.Select(x => x.Publisher))
                    {
                        var existingPublisher = await cache.CreateMappedEntity<User, UserModel>().FirstOrDefaultAsync(x => x.Id == publisher.Id);
                        if (existingPublisher.DisplayNameAndEmail != submissionResource.RegulatoryLead &&
                            existingPublisher.DisplayNameAndEmail != submissionResource.PublishingLead &&
                            !existingPublisher.DisplayNameAndEmail.Contains(currentUser.Identity.Name))
                        {
                            await this.notificationService.SendNotification(existingPublisher.DisplayNameAndEmail, model, submission.Application.ApplicationNumber, createdByUser.DisplayFullName, this.Request, product, client);
                        }
                    }
                }
            }

            if (((SubmissionLifeCycleState)submission.LifecycleStateId == SubmissionLifeCycleState.QCReview ||
               (SubmissionLifeCycleState)submission.LifecycleStateId == SubmissionLifeCycleState.Submitted ||
               (SubmissionLifeCycleState)submission.LifecycleStateId == SubmissionLifeCycleState.Archived) &&
               !submissionResource.RegulatoryLead.Contains(currentUser.Identity.Name))
            {
                await this.notificationService.SendNotification(submissionResource.RegulatoryLead, model, submission.Application.ApplicationNumber, createdByUser.DisplayFullName, this.Request, product, client);
            }

            if ((SubmissionLifeCycleState)submission.LifecycleStateId == SubmissionLifeCycleState.InProgress ||
                (SubmissionLifeCycleState)submission.LifecycleStateId == SubmissionLifeCycleState.ReadyForPublishing ||
                (SubmissionLifeCycleState)submission.LifecycleStateId == SubmissionLifeCycleState.ApprovedForSubmission)
            {
                if (!submissionResource.PublishingLead.Contains(currentUser.Identity.Name))
                {
                    await this.notificationService.SendNotification(submissionResource.PublishingLead, model, submission.Application.ApplicationNumber, createdByUser.DisplayFullName, this.Request, product, client);
                }
                foreach (var publisher in submissionResource.SubmissionPublisher.Select(x => x.Publisher))
                {
                    var existingPublisher = await cache.CreateMappedEntity<User, UserModel>().FirstOrDefaultAsync(x => x.Id == publisher.Id);
                    if (existingPublisher.DisplayNameAndEmail != submissionResource.PublishingLead &&
                       !existingPublisher.DisplayNameAndEmail.Contains(currentUser.Identity.Name))
                    {
                        await this.notificationService.SendNotification(existingPublisher.DisplayNameAndEmail, model, submission.Application.ApplicationNumber, createdByUser.DisplayFullName, this.Request, product, client);
                    }
                }
            }
        }
    }
}
