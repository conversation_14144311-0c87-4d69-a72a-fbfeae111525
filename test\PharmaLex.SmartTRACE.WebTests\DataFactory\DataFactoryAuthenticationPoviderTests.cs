﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;
using NSubstitute;
using PharmaLex.SmartTRACE.Web.DataFactory;
using Microsoft.Extensions.Configuration;
using Azure.Identity;

namespace PharmaLex.SmartTRACE.WebTests.DataFactory
{
    public class DataFactoryAuthenticationPoviderTests
    {
        readonly AzureAdOptions adOptions;
        public DataFactoryAuthenticationPoviderTests()
        {
            adOptions = new AzureAdOptions
            {
                Instance = "test",
                TenantId = "9750fb76-dfdc-458a-91c6-703e8eb103ff"
            };
                 
        }

        [Fact]
        public void Check_CreateClientCrdential()
        {
            IConfiguration config = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    { "DataFactoryPipeline:Instance", adOptions.Instance},
                    { "DataFactoryPipeline:TenantId", adOptions.TenantId}
                }).Build();
            IDataFactoryAuthenticationPovider sut = new DataFactoryAuthenticationPovider(config);
            var actual = sut.CreateClientCrdential();
            Assert.NotNull(actual);
            Assert.IsType<DefaultAzureCredential>(actual);
        }
    }
}
