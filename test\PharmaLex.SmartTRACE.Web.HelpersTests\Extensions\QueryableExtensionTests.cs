﻿using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Web.Helpers.Extensions;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Extensions
{
    public class QueryableExtensionTests
    {
        private class TestEntity : EntityBase
        {
            public string Name { get; set; }
            public int Id { get; set; }
        }

        [Fact]
        public void FilterItems_FiltersAndOrdersEntitiesCorrectly()
        {
            // Arrange
            var entities = new List<TestEntity>
            {
                new TestEntity { Id = 1, Name = "Client1" },
                new TestEntity { Id = 2, Name = "Client2" },
                new TestEntity { Id = 3, Name = "Client3" },
                new TestEntity { Id = 4, Name = "Client4" }
            }.AsQueryable();

            Expression<Func<TestEntity, bool>> expression = e => e.Id > 1;
            Func<IQueryable<TestEntity>, IOrderedQueryable<TestEntity>> orderby = q => q.OrderBy(e => e.Name);

            // Act
            var result = entities.FilterItems(expression, orderby, skip: 1, take: 2).ToList();

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Equal(3, result[0].Id); // Client3
            Assert.Equal(4, result[1].Id); // Client4
        }

        [Fact]
        public void FilterItems_SkipsAndTakesEntitiesCorrectly()
        {
            // Arrange
            var entities = new List<TestEntity>
            {
                new TestEntity { Id = 1, Name = "Client1" },
                new TestEntity { Id = 2, Name = "Client2" },
                new TestEntity { Id = 3, Name = "Client3" },
                new TestEntity { Id = 4, Name = "Client4" }
            }.AsQueryable();

            // Act
            var result = entities.FilterItems(null, null, skip: 1, take: 2).ToList();

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Equal(2, result[0].Id); // Client2
            Assert.Equal(3, result[1].Id); // Client3
        }

        [Fact]
        public void FilterItems_NoFilterNoOrderBy_ReturnsAllEntities()
        {
            // Arrange
            var entities = new List<TestEntity>
            {
                new TestEntity { Id = 1, Name = "Client1" },
                new TestEntity { Id = 2, Name = "Client2" },
                new TestEntity { Id = 3, Name = "Client3" },
                new TestEntity { Id = 4, Name = "Client4" }
            }.AsQueryable();

            // Act
            var result = entities.FilterItems(null, null).ToList();

            // Assert
            Assert.Equal(4, result.Count);
            Assert.Equal(1, result[0].Id); // Client1
            Assert.Equal(2, result[1].Id); // Client2
            Assert.Equal(3, result[2].Id); // Client3
            Assert.Equal(4, result[3].Id); // Client4
        }
    }
}
