﻿@model IEnumerable<ClientModel>
@using PharmaLex.Caching.Data
@using PharmaLex.SmartTRACE.Entities
@using PharmaLex.SmartTRACE.Entities.Enums
@inject IDistributedCacheServiceFactory cache

@{
    var contractOwners = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                                  .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.ContractOwner);
}
<div id="clients" class="manage-container">
    <header class="manage-header">
        <h2>Manage Clients</h2>
        <a id="exportButton" class="button icon-button-download">Export</a>
        <a class="button icon-button-add" href="/clients/new">Add client</a>
    </header>
    <filtered-table :items="clients" :columns="columns" :filters="filters" :link="link"></filtered-table>
    <form id="exportForm" style="display: none;" method="post" action="/clients/export">
        @Html.AntiForgeryToken()
    </form>
</div>

@section Scripts {
    <script type="text/javascript">

    var pageConfig = {
        appElement: '#clients',
        data() {
            return {
                link: '/clients/edit/',
                clients: @Html.Raw(Model.ToJson()),
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            type: 'text'
                        },
                        {
                            dataKey: 'contractOwner',
                            sortKey: 'contractOwner',
                            header: 'Contract Owner',
                            type: 'text'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'name',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'contractOwner',
                        options: @Html.Raw(contractOwners.ToJson()),
                        filterCollection: 'contractOwner',
                        display: 'id',
                        type: 'select',
                        header: 'Filter By Contract Owner',
                        fn: v => p => p.contractOwner === v,
                        convert: v => v
                    },
                ]
            };
        },
        mounted() {
            document.getElementById('exportButton').addEventListener('click', (e) => {
                e.preventDefault();
                document.getElementById('exportForm').submit();
            });
        }
    };

    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}