﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NPOI.OpenXmlFormats.Wordprocessing;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.RemindersApp.Models;
using PharmaLex.SmartTRACE.RemindersApp.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.RemindersApp
{
    public class RemindersTimer
    {
        private readonly IReminderService _reminderService;

        public RemindersTimer(IReminderService reminderService)
        {
            _reminderService = reminderService;
        }

        [Function("RemindersTimer")]
        public async Task Run([TimerTrigger("0 0 2 * * *"/*, RunOnStartup = true*/)] TimerInfo timer, FunctionContext context)
        {
            var logger = context.GetLogger("RemindersTIck");
            logger.LogInformation($"Exectuion started, Invocation Id: {context.InvocationId} - {DateTime.Now}");

            await _reminderService.SendPlannedSubmissionDateReminders(logger);
            await _reminderService.SendPlannedDispatchDateReminders(logger);
        }
    }
}
