﻿using NPOI.SS.Formula.Functions;
using PharmaLex.SmartTRACE.Web.Helpers.Extensions;
using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Extensions
{
    public class ApplicationFilterModelExtensionsTests
    {
        [Fact]
        public void FromFilters_ValidFilters_ShouldReturnModelWithCorrectValues()
        {
            // Arrange
            var model = new ApplicationFilterModel();
            string[] filters = {
            "clientname=>Astra",
            "applicationnumber=>PE435",
            "product=>Cosmos",
            "applicationtype=>Drug",
            "region=>European",
            "country=>Belgium",
            "medicinalproductdomain=>clinical",
            "lifecyclestate=>planned",
            "proceduretype=>test"

            };

            // Act
            var result = model.FromFilters(filters);

            // Assert
            Assert.Equal("Astra", result.ClientName);
            Assert.Equal("PE435", result.ApplicationNumber);
            Assert.Equal("Cosmos", result.Product);
            Assert.Equal("Drug", result.ApplicationType);
            Assert.Equal("European", result.Region);
            Assert.Equal("Belgium", result.Country);
            Assert.Equal("clinical", result.MedicinalProductDomain);
            Assert.Equal("planned", result.LifecycleState);
            Assert.Equal("test", result.ProcedureType);
        }

        [Fact]
        public void FromFilters_EmptyFilters_ShouldReturnEmptyModel()
        {
            // Arrange
            var model = new ApplicationFilterModel();
            string[] filters = Array.Empty<string>();

            // Act
            var result = model.FromFilters(filters);

            // Assert
            Assert.Null(result.ClientName);
            Assert.Null(result.ApplicationNumber);
            Assert.Null(result.Product);
            Assert.Null(result.ApplicationType);
            Assert.Null(result.Region);
            Assert.Null(result.Country);
            Assert.Null(result.MedicinalProductDomain);
            Assert.Null(result.LifecycleState);
            Assert.Null(result.ProcedureType);
        }

        [Fact]
        public void FromFilters_NullFilters_ShouldReturnEmptyModel()
        {
            // Arrange
            var model = new ApplicationFilterModel();
            string[] filters = null;

            // Act
            var result = model.FromFilters(filters);

            // Assert
            Assert.Null(result.ClientName);
            Assert.Null(result.ApplicationNumber);
            Assert.Null(result.Product);
            Assert.Null(result.ApplicationType);
            Assert.Null(result.Region);
            Assert.Null(result.Country);
            Assert.Null(result.MedicinalProductDomain);
            Assert.Null(result.LifecycleState);
            Assert.Null(result.ProcedureType);
        }

        [Fact]
        public void FromFilters_InvalidFilter_ShouldSkipInvalidFilter()
        {
            // Arrange
            var model = new ApplicationFilterModel();
            string[] filters = {
            "invalidfilter",
            "clientname=>InvalidClient"
        };

            // Act
            var result = model.FromFilters(filters);

            // Assert
            Assert.Equal("InvalidClient", result.ClientName);
            Assert.Null(result.ApplicationNumber);
            Assert.Null(result.Product);
            Assert.Null(result.ApplicationType);
            Assert.Null(result.Region);
            Assert.Null(result.Country);
            Assert.Null(result.MedicinalProductDomain);
            Assert.Null(result.LifecycleState);
            Assert.Null(result.ProcedureType);
        }
    }
}
