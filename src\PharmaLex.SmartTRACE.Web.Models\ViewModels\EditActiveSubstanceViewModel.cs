﻿using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class EditActiveSubstanceViewModel
    {
        public EditActiveSubstanceViewModel()
        {
            this.Clients = new List<ClientModel>();
        }
        public bool IsProductAssigned { get; set; }
        public ActiveSubstanceModel ActiveSubstance { get; set; }

        public IList<ClientModel> Clients { get; set; }
    }
}
