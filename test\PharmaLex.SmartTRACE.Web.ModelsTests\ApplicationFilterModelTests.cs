﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class ApplicationFilterModelTests
    {
        [Fact]
        public void ApplicationFilterModel_Get_SetValue()
        {
            //Arrange
            var model = new ApplicationFilterModel();
            model.ApplicationNumber = "123";
            model.ApplicationType = "test";
            model.ClientName= "test";
            model.Country = "test";
            model.MedicinalProductDomain = "test";
            model.ProcedureType = "test";
            model.Product = "product";
            model.Region= "test";
            model.LifecycleState = "test";
            //Assert
            Assert.NotNull(model);
        }
    }
}
