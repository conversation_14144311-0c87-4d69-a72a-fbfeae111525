﻿using NSubstitute;
using PharmaLex.SmartTRACE.Web;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Builder;

namespace PharmaLex.SmartTRACE.WebTests
{
    public class ContentConfigureOptionsTests
    {
        readonly ContentConfigureOptions sut;
        readonly IWebHostEnvironment hostEnv;
        public ContentConfigureOptionsTests()
        {
            hostEnv = Substitute.For<IWebHostEnvironment>();
            sut = new ContentConfigureOptions(hostEnv);
        }

        [Theory]
        [InlineData(true), InlineData(false)]
        public void Check_PostConfigure_Success(bool isNull)
        {
            var fileOptBuilder = new StaticFileOptions();
            fileOptBuilder.ContentTypeProvider = isNull ? null : new Microsoft.AspNetCore.StaticFiles.FileExtensionContentTypeProvider();
            fileOptBuilder.FileProvider = isNull ? null : hostEnv.WebRootFileProvider; 
            var actExp = Record.Exception(() => sut.PostConfigure("", fileOptBuilder));
            Assert.Null(actExp);
        }
        [Fact]
        public void Check_PostConfigure_Throws_exception()
        {
            var fileOptBuilder = new StaticFileOptions();
            hostEnv.WebRootFileProvider = null;
            fileOptBuilder.ContentTypeProvider = null;
            fileOptBuilder.FileProvider = null; 
            var actExp = Assert.Throws<InvalidOperationException>(() => sut.PostConfigure("", fileOptBuilder));
            Assert.Equal("Missing FileProvider.", actExp.Message);
        }
    }
}
