﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class Country : EntityBase
    {
        public Country()
        {
            RegulatoryAuthorityCountry = new HashSet<RegulatoryAuthorityCountry>();
            ApplicationCountry = new HashSet<ApplicationCountry>();
            SubmissionCountry = new HashSet<SubmissionCountry>();
            PicklistDataCountry = new HashSet<PicklistDataCountry>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string TwoLetterCode { get; set; }
        public int RegionId { get; set; }

        public virtual Region Region { get; set; }
        public virtual ICollection<RegulatoryAuthorityCountry> RegulatoryAuthorityCountry { get; set; }
        public virtual ICollection<ApplicationCountry> ApplicationCountry { get; set; }
        public virtual ICollection<SubmissionCountry> SubmissionCountry { get; set; }
        public virtual ICollection<PicklistDataCountry> PicklistDataCountry { get; set; }
    }
}
