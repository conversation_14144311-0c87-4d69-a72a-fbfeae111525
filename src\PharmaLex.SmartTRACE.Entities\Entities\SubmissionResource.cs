﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class SubmissionResource : EntityBase
    {
        public SubmissionResource()
        {
            SubmissionPublisher = new HashSet<SubmissionPublisher>();
        }
        public int Id { get; set; }
        public int? PriorityId { get; set; }
        public int? EstimatedSizeId { get; set; }
        public decimal? EstimatedHours { get; set; }
        public string RegulatoryLead { get; set; }
        public string PublishingLead { get; set; }
        public int? PublishingTeamId { get; set; }
        public string InitialSentToEmail { get; set; }
        public string Comments { get; set; }
        public int SubmissionId { get; set; }

        public virtual Submission Submission { get; set; }
        public virtual ICollection<SubmissionPublisher> SubmissionPublisher { get; set; }
    }
}
