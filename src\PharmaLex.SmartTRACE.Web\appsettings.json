{
  "Logging": {
    "LogLevel": {
      "Default": "Warning"
    }
  },
  "AzureAdB2C": {
    "Instance": "https://smartphlexb2c.b2clogin.com",
    "ClientId": "53f4fe97-71df-48ad-9bd9-3c6555487872",
    "Domain": "smartphlexb2c.onmicrosoft.com",
    "SignedOutCallbackPath": "/signout/B2C_1_signin",
    "SignUpSignInPolicyId": "B2C_1_signup_signin_plx",
    "ResetPasswordPolicyId": "b2c_1_password_reset",
    "EditProfilePolicyId": "b2c_1_profile_edit",
    "CallbackPath": "/signin-oidc"
  },
  "AzureAdB2CPolicy": {
    "Tenant": "smartphlexb2c",
    "TenantId": "f8b90da3-e024-492c-800e-1b793397f942",
    "ClientId": "53f4fe97-71df-48ad-9bd9-3c6555487872",
    "SigningCertThumbprint": "E1BA3541BA2553B9BC40927F1359F264E1724D61",
    "LinkExpiresAfterDays": 2,
    "Policies": [
      {
        "PolicyId": "B2C_1A_signup_invitation",
        "CallbackPath": "/signin-oidc-invite",
        "ApplicationCallbackPath": "/signup-invitation"
      },
      {
        "PolicyId": "B2C_1_signin_local",
        "CallbackPath": "/signin-local"
      },
      {
        "PolicyId": "B2C_1_signin_federated",
        "CallbackPath": "/signin-federated"
      }
    ],
    "Domains": [
      [
        "pharmalex.com",
        "yespharmaservices.onmicrosoft.com"
      ]
    ]
  },
  "AzureAdGraph": {
    "ClientId": "e014e4a5-078f-4095-9d8f-6e07dd8ed25d",
    "Domain": "yes-services.eu",
    "TenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77"
  },
  "AzureAdB2CGraph": {
    "ClientId": "5c76d17d-764a-4e8e-bef7-c39afd6fb3bd",
    "Domain": "smartphlexb2c.onmicrosoft.com",
    //"ClientSecret": ""
  },
  "DataFactoryPipeline": {
    "Instance": "https://management.azure.com/",
    "Subscription": "8f05cf92-4915-4f15-9950-74d44cde4bba",
    "ResourceGroupName": "rg-str-dev-eun",
    "FactoryName": "str-dev-adf-eun",
    "UnpackPipelineName": "Unpack",
    "CopyPipelineName": "Copy_Public_Private",
    "UploadPath": "Uploaded",
    "UnpackPath": "Unpacked",
    "AllSequencesPath": "All",
    "DocumentsPath": "Documents",
    "TenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77"
  },
  "AppSettings": {
    "BuildInfo": "Build:0000, Branch:local",
    "BuildNumber": "local",
    "SystemAdminEmail": "<EMAIL>",
    "SmartPHLEXAdminEmail": "<EMAIL>",
    "SubmissionRequestStateChangedTemplateId": "d-a41e0dbca59242cc867f386cb387f1ce",
    "ExternalUserLoginEmailTemplateId": "d-a2c0c5021d764ae68ed8af50f52bb048",
    "ExternalUserSignUpEmailTemplateId": "d-a07f826a31ab4b22b086e6e6fd1d18a0",
    "SubmissionSourceDocumentsUploadedTemplateId": "d-e2ed0dde33884c29b0a4023ed15cd25c",
    "SmartTraceConfigurableEmail": "<EMAIL>",
    "HomeScreen": "/dashboard",
    "MaxTime": "100",
    "ZenDeskURL": "https://cencoragcs.zendesk.com/hc/en-gb"
  },
  "DashboardLinks": {
    "SystemAccessLink": "https://yespharmaservices.sharepoint.com/sites/RegOpsServices/Lists/Access%20Request%20new/NewForm.aspx?Source=https%3a//yespharmaservices.sharepoint.com/sites/RegOpsServices/Lists/Access%2520Request%2520new/overview.aspx",
    "TraceEmailLink": "mailto:<EMAIL>",
    "LocalBusinessAdministratorLink": "https://teams.microsoft.com/l/file/7803106C-18F7-40EF-90AF-DE12A4D4A4EE?tenantId=ff9ac3ce-3c41-41c3-b556-e1b32a662fed&fileType=docx&objectUrl=https%3A%2F%2Fyespharmaservices.sharepoint.com%2Fsites%2FDep-Global-Regulatory-Affairs%2FShared%20Documents%2FRegulatory%20Informatics%20and%20Operations%2FRegulatory%20System%2FSmartTRACE%2FProcess%20Docs%2FLocal%20SmartTRACE%20Business%20Administrators.docx&baseUrl=https%3A%2F%2Fyespharmaservices.sharepoint.com%2Fsites%2FDep-Global-Regulatory-Affairs&serviceName=teams&threadId=19:d36f3a51bd4c4db9af8dc73d33cba5b6@thread.tacv2&groupId=0c5f1260-200d-4c63-b6cf-9d1036d8ef55"
  },
  "SessionTimeout": {
    "IdleTimeoutMinutes": 20,
    "WarningTimeoutMinutes": 5
  },
  "Static": {
    "App": "smarttrace",
    "Env": "dev",
    "Cdn": "https://phcgvcdn-endpoint.azureedge.net",
    "Version": "v1.40.0",
    "Container": "content"
  },
  "ConnectionStrings": {
    "default": "Server=(local); Database=smarttrace-dev; Trusted_connection=true; MultipleActiveResultSets=True;TrustServerCertificate=True"
  },
  "SqlOptions": {
    "EnableDetailedErrors": true,
    "TenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77"
  },
  "AzureStorage": {
    "Account": "strdevsharedeun",
    "PubAccount": "strdevpubliceun",
    "Container": "smarttrace",
    "TenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77",
    "Url": "https://strdevsharedeun.blob.core.windows.net/smarttrace"
  },
  "KeyVaultName": "str-dev-kv-eun",
  "Migrations": {
    "Assembly": "PharmaLex.SmartTRACE.Data"
  },
  "AllowedHosts": "*",
  "emailsSendingEnabled": "true"
}
