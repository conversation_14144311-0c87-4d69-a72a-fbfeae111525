﻿using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers.Builders;
using PharmaLex.SmartTRACE.Web.Helpers.Constants;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Builders
{
    public class ExpressionBuilderTests
    {
        private static Dictionary<string, Expression<Func<Submission, object>>> sortExpressions =
        new()
        {
                    { TableFilterConstants.ClientName, x => x.Project.Client.Name }
        };

        [Theory]
        [InlineData(TableFilterConstants.ClientName + "=>asc")]
        [InlineData(" ")]
        [InlineData("desc=>asc")]
        public void BuildSortExpression_ReturnsData(string? sort)
        {
            var predicate = ExpressionBuilder<Submission>.BuildSortExpression(sort, sortExpressions, x => x.UniqueId);
            bool result = predicate.Target.Equals("isAsc");
            Assert.False(result);

        }
    }
}
