﻿using Azure.Identity;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Identity.Client;
using Microsoft.Identity.Web;
using System;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.DataFactory
{
    public interface IDataFactoryAuthenticationPovider
    {
         DefaultAzureCredential CreateClientCrdential();
    }
    public class DataFactoryAuthenticationPovider : IDataFactoryAuthenticationPovider
    {
        private readonly string tenantId;

        public DataFactoryAuthenticationPovider(IConfiguration configuration)
        {
            tenantId = configuration.GetValue<string>("DataFactoryPipeline:TenantId");
        }
       
        public DefaultAzureCredential CreateClientCrdential()
        {
            var options = new DefaultAzureCredentialOptions { TenantId = tenantId};
            var defaultAzureCredential = new DefaultAzureCredential(options);
            return defaultAzureCredential;
        }
    }
}
