﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    /// <inheritdoc />
    public partial class AddUserEmailLink : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "InvitationEmailLink",
                schema: "Audit",
                table: "User_Audit",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "InvitationEmailLink",
                table: "User",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.SqlFileExec("0028-AddUserEmailLink-AlterUserTriggers.sql");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InvitationEmailLink",
                schema: "Audit",
                table: "User_Audit");

            migrationBuilder.DropColumn(
                name: "InvitationEmailLink",
                table: "User");
        }
    }
}
