﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class ProductModelTests
    {
        [Fact]
        public void ProductModel_Get_SetValue()
        {
            //Arrange
            var model = new ProductModel();
            model.Id = 1;
            model.Name = "Test";
            model.DosageFormId = 1;
            model.LifecycleStateId = 1;
            model.DosageForm = "DosageForm";
            model.Strength = "test";
            model.LifecycleState = "test";
            model.ClientId = 1;
            model.ClientName = "Test";
            model.ActiveSubstances=new List<ActiveSubstanceModel>();
            model.HasError = true;
            model.ErrorMessage = "error";
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void ProductMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new ProductMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
    }
}
