﻿@model RegionModel

<div id="region" class="manage-container">
    <header class="manage-header">
        <h3>@Model.Name Region</h3>
        <a href="/regions" class="button secondary icon-button-back">Back to Region list</a>
    </header>
    <form id="region-form" method="post">
        <div class="form-col">
            <label for="Name">Name*</label>
            <input asp-for="Name" type="text" required/>
            <div id="validationMessage" style="color:red;padding:10px">
            </div>
            <label for="Abbreviation">Abbreviation</label>
            <input asp-for="Abbreviation" type="text" />
        </div>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/regions">Cancel</a><button class="icon-button-save">Save</button>
        </div>
    </form>

    @if (Model.Id != 0)
    {
        <br />
        <h3>Countries</h3>
        <filtered-table :items="countries" :columns="columns" :filters="filters" :link="link" addurl="/countries/new/@Model.Id"></filtered-table>
    }
</div>
<script src="@Cdn.GetUrl("lib/jquery/dist/jquery.min.js")"></script>
<script>
    $(document).ready(function () {
        
            $('#region-form').submit(function () {
            var inputValue = $('#Name').val();           
            var unmatchedChars = findUnmatchedCharacters(inputValue);
            if (unmatchedChars._value.length > 0) {
                event.preventDefault();
                $('#validationMessage').text("Name contains invalid characters: " + unmatchedChars._value.join(' '));
                return false;
            }
            $('#validationMessage').text('');
            return true;
        });
        $('#Name').change(function () {
            $('#validationMessage').text('');
        });
    });
</script>
@section Scripts {
    <script type="text/javascript">
     var pageConfig = {
        appElement: '#region',
        data() {
            return {
                link: '/countries/edit/',
                countries: @Html.Raw(Model.Countries.ToJson()),
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            type: 'text'
                        },
                        {
                            dataKey: 'twoLetterCode',
                            sortKey: 'twoLetterCode',
                            header: 'Two Letter Code',
                            type: 'text'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'name',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'twoLetterCode',
                        options: [],
                        type: 'search',
                        header: 'Search Code',
                        fn: v => p => p.twoLetterCode.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    }
                ]
            };
        }
    };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}
