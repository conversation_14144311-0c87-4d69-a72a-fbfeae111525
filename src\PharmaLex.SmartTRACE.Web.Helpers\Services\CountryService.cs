﻿using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Helpers.Services;

public class CountryService(IDistributedCacheServiceFactory cache) : ICountryService
{
    public async Task<bool> IsCountryNameDuplicate(string countryName)
    {
        var countryCache = cache.CreateEntity<Country>();
        var countryResult = await countryCache.WhereAsync(p => p.Name == countryName);
        return countryResult.Count != 0;
    }
}
