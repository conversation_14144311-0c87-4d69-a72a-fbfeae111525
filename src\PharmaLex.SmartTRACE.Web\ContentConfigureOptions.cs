﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Options;
using System;

namespace PharmaLex.SmartTRACE.Web
{
    public class ContentConfigureOptions : IPostConfigureOptions<StaticFileOptions>
    {
        private readonly IWebHostEnvironment environment;
        public ContentConfigureOptions(IWebHostEnvironment environment)
        {
            this.environment = environment;
        }

        public void PostConfigure(string name, StaticFileOptions options)
        {
            //Basic initialization in case the options weren't initialized by any other component
            options.ContentTypeProvider = options.ContentTypeProvider ?? new FileExtensionContentTypeProvider();
                if (options.FileProvider == null && environment.WebRootFileProvider == null)
                {
                    throw new InvalidOperationException("Missing FileProvider.");
                }

                options.FileProvider = options.FileProvider ?? environment.WebRootFileProvider;

                var basePath = "wwwroot";

                var filesProvider = new ManifestEmbeddedFileProvider(GetType().Assembly, basePath);
                options.FileProvider = new CompositeFileProvider(options.FileProvider, filesProvider);
            }
    }
}
