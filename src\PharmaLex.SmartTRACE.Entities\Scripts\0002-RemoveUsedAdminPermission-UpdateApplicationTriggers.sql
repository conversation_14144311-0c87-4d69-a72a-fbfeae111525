﻿ALTER TRIGGER [dbo].[Application_Insert] ON [dbo].[Application]
FOR INSERT AS
INSERT INTO [Audit].[Application_Audit]
SELECT 'I', [Id], [ApplicationNumber], [ApplicationTypeId], [MedicinalProductDomainId], [MedicinalProductTypeId], [ProcedureTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

ALTER TRIGGER [dbo].[Application_Update] ON [dbo].[Application]
FOR UPDATE AS
INSERT INTO [Audit].[Application_Audit]
SELECT 'U', [Id], [ApplicationNumber], [ApplicationTypeId], [MedicinalProductDomainId], [MedicinalProductTypeId], [ProcedureTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

ALTER TRIGGER [dbo].[Application_Delete] ON [dbo].[Application]
FOR DELETE AS
INSERT INTO [Audit].[Application_Audit]
SELECT 'D', [Id], [ApplicationNumber], [ApplicationTypeId], [MedicinalProductDomainId], [MedicinalProductTypeId], [ProcedureTypeId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO