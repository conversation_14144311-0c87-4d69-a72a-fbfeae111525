﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class EditClientViewModelTests
    {
        [Fact]
        public void EditClientViewModel_Get_SetValue()
        {
            //Arrange
            var model = new EditClientViewModel();
            model.Client=new ClientModel();
            model.Project=new ProjectModel();
            model.Products=new List<ProductModel>();
            model.Projects= new List<ProjectModel>();
            model.AllContractOwners = new List<PicklistDataModel>();
            //Assert
            Assert.NotNull(model);


        }
    }
}
