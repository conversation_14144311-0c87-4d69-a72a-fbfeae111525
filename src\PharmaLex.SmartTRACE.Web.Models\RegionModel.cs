﻿using AutoMapper;
using PharmaLex.SmartTRACE.Entities;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class RegionModel: IModel
    {
        public int Id { get; set; }

        [Required, StringLength(128)]
        public string Name { get; set; }

        [StringLength(8)]
        public string Abbreviation { get; set; }

        public IList<CountryModel> Countries { get; set; }
    }

    public class RegionMappingProfile : Profile
    {
        public RegionMappingProfile()
        {
            this.CreateMap<Region, RegionModel>().ReverseMap();

        }
    }
}
