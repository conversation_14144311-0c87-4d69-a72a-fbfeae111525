html {
    font-size: 16px;
}

body, button, .button, select, input[type=date], input[type=email], input[type=number], input[type=password], input[type=search], input[type=tel], input[type=text], input[type=url], textarea {
    font-size: .8125rem;
    font-family: var(--main-font);
}

.page-header {
    height: 3.125rem;
}

.manage-header {
    align-items: center;
}

.manage-header h3 {
    margin-bottom: 0;
}

h2 {
    font-size: 1.25rem;
}

h4 {
    font-size: .875rem;
}

h5 {
    font-weight: normal;
}

/* navbar */

.main-nav a, .user-nav a {
    font-weight: 400;
}
.main-nav li.active  > a {
    font-weight: 600;
}

.main-nav span.profile-picture, .user-nav span.profile-picture {
    font-size: 1.5rem;
    font-weight: normal;
}

.main-mega-menu-container {
    position: absolute;
    left: 28px;
}

.main-mega-menu-container .main-sub-nav {
    background: #fff;
    min-width: 60rem;
}

.main-mega-menu-container .main-sub-nav>li ul {
    list-style: none;
}

.main-mega-menu-container .main-sub-nav>li ul li {
    padding: 0.5rem;
    border: none;
}

.main-mega-menu-container .main-sub-nav>li, .main-mega-menu-container .main-sub-nav>li:last-child {
    border: none;
}

.main-mega-menu-container .main-sub-nav>li.selected {
    background-color: var(--grey-50);
}

.account-sub-nav li:last-child, .main-sub-nav li:last-child {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}

.main-mega-menu-container a {
    width: 100%;
}

/* Dashboard */

.welcome-details {
    font-size: .875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.dashboard-text {
    color: var(--main-theme-color);
}

.dashboard-container {
    text-align: left;
    padding: 25px 50px;
}

.dashboard {
    margin-top: 2rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 1rem
}

.dashboard-welcome {
    width: 70%;
}

.submissions-dashboard {
    width: 25%;
}

.dashboard-tile {
    border: 1px solid var(--grey-100);
    position: relative;
    margin-top: 8%;
    border-radius: var(--radius-small);
}

    .dashboard-tile header {
        background-color: var(--main-theme-color);
        color: #fff;
        font-weight: bold;
        padding: 1rem;
        width: 100%;
        text-align: center;
        border-top-right-radius: var(--radius-small);
        border-top-left-radius: var(--radius-small);
    }

.dashboard-content {
    margin: 0.75rem;
    overflow-y: auto;
    overflow-x: hidden;
}

.dashboard-tile th {
    background-color: transparent;
    border-top: none;
    border-bottom: 1px solid #aaa;
    font-weight: bold;
    color: #333;
    padding-left: 0;
    margin-top: 20px;
}

.dashboard-tile td {
    border-top: none;
    border-bottom: 1px solid #aaa;
    padding-left: 0;
}

.dashboard-content dl {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.dashboard-text {
    color: var(--main-theme-color);
}

h1.welcome-message {
    text-align: left;
}

/* forms */

.form-col h5 {
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--grey-100);
}

.user-form {
    gap: 2rem;
    margin-top: 0;
}

.user-form .form-col {
    padding: 0px;
}

.user-form .form-col .claims-col {
    margin-top: 1rem;
}

.user-form .form-col-two-third {
    width: 60%;
}

/* buttons */

.icon-button-download:before {
    top: 0px;
}

/* drag drop */

.uppy-DragDrop-label, .uppy-DragDrop-note, .uppy-DragDrop-arrow {
    color: var(--text);
}

.table-filter-item {
    list-style: none;
    text-align: left;
    text-transform: none;
    cursor: pointer;
    font-weight: initial;
    padding: 5px;
    margin: 0;
    color: #000;
}

/* switch */

.switch-container {
    display: flex !important;
    margin-top: 0.2rem;
    align-items: center;
    pointer-events: none;
}

.switch-container label {
    margin: 0;
}

.switch-container label.switch {
    cursor: pointer;
    text-indent: -9999px;
    width: 58px;
    height: 28px;
    display: inline-block !important;
    background-color: var(--status-red);
    color: var(--status-red);
    border-radius: 20px;
    position: relative;
    margin: 0 0.5rem 0 0;
    pointer-events: all; 
}

.switch-container label.switch:after {
    font-family: MaterialIconsOutlined, Arial, Helvetica, Verdana, sans-serif;
    font-size: 1rem;
    content: 'close';
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 3px;
    left: 4px;
    width: 22px;
    height: 22px;
    background-color: #fff;
    border-radius: 50%;
    transition: 0.3s;
    text-indent: 0;
}

.switch-container input.switch:checked + label {
    background: var(--status-green);
    color: var(--status-green);
}

.switch-container input.switch:checked + label:after {
    left: 10px;
    transform: translateX(100%);
    content: 'check'
}

/* tabs */

.tab-link {
    font-size: 1rem;
}

/* lists */

ul {
    list-style-image: none;
}

/* submissions */

.workflow-step-done {
    border: 1px solid var(--blue-900);
    background-color: var(--blue-900);
}

.dataTables_wrapper {
    position: relative;
}
