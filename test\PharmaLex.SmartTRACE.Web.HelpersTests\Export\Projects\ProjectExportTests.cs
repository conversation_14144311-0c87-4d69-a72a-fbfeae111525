﻿using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Export.Projects
{
    public class ProjectExportTests
    {
        private readonly IDistributedCacheServiceFactory cache;

        public ProjectExportTests()
        {
            cache = Substitute.For<IDistributedCacheServiceFactory>();
        }

        [Fact]
        public async Task Export_Projects_ReturnsData()
        {
            // Arrange
            List<ProjectModel> projects = new List<ProjectModel>() { new ProjectModel() { Id = 8, ClientId = 7, ClientName = "abc", Code = "876" } };

            var ac = Substitute.For<IMappedEntityCacheServiceProxy<Project, ProjectModel>>();
           cache.CreateMappedEntity<Project, ProjectModel>()
              .Configure(Arg.Any<Func<IIncludable<Project>, IIncludable<Project>>>()).Returns(ac);
            ac.AllAsync().Returns(Task.FromResult(projects));

            // Act
            ProjectExport projExport = new ProjectExport(cache);
            var result = await projExport.Export();

            Assert.NotNull(result);
            // Assert

        }
        [Fact]
        public async Task Export_NoProjects_Returns_HeaderRow()
        {
            // Arrange
            List<ProjectModel> projects = new List<ProjectModel>() {  };

            var ac = Substitute.For<IMappedEntityCacheServiceProxy<Project, ProjectModel>>();
            cache.CreateMappedEntity<Project, ProjectModel>()
               .Configure(Arg.Any<Func<IIncludable<Project>, IIncludable<Project>>>()).Returns(ac);
            ac.AllAsync().Returns(Task.FromResult(projects));

            // Act
            ProjectExport projExport = new ProjectExport(cache);
            var result = await projExport.Export();

            Assert.NotNull(result);
            // Assert

        }
    }
}
