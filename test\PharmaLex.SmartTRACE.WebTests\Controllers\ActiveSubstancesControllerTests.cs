﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Caching.Distributed;
using NSubstitute;
using NuGet.Protocol;
using PharmaLex.Caching;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Collections.Concurrent;
using System.Linq.Expressions;
using System.Security.Claims;
using System.Text;
using NSubstitute.ExceptionExtensions;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics.CodeAnalysis;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    [ExcludeFromCodeCoverage]
    public class ActiveSubstancesControllerTests
    {
        private readonly SmartTRACEContext dbCtx;
        private readonly ActiveSubstancesController controller;
        private readonly IMapper mapper;
        private const string UniqueErrorMessage = "An active substance with the same name already exists to this client.";
        public ActiveSubstancesControllerTests()
        {
            #region TestData and config
            var config = TestHelpers.GetConfiguration();
            var dbCtxReslover = TestHelpers.GetPlxDbContextResolver();
            dbCtx = (SmartTRACEContext)dbCtxReslover.Context;
            if (!dbCtx.Product.Any())
                dbCtx.Product.Add(new Product { Name = "Test product", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            if (!dbCtx.Client.Any())
                dbCtx.Client.Add(new Client { Id = 100002, Name = "Test", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            if (!dbCtx.ActiveSubstance.Any() && !dbCtx.ActiveSubstanceAudit.Any())
            {
                dbCtx.AddRange(new List<ActiveSubstance> {
                    new ActiveSubstance { ClientId = 100002, Name = "Test Substance 1", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                    new ActiveSubstance { ClientId = 100002, Name = "Test Substance 2", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" }
                    });
            }
            if (!dbCtx.ActiveSubstanceProduct.Any())
                dbCtx.ActiveSubstanceProduct.Add(new ActiveSubstanceProduct { ActiveSubstanceId = 1, ProductId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.SaveChanges();
            #endregion

            #region Fake Httpcontext with identity
            IHttpContextAccessor httpContextAccessor = new HttpContextAccessor();
            var context = new DefaultHttpContext();
            context.User = new ClaimsPrincipal(new ClaimsIdentity(new List<System.Security.Claims.Claim> { new System.Security.Claims.Claim("emails", "<EMAIL>") }));
            httpContextAccessor.HttpContext = context;
            var userCtx = new PlxUserContext(httpContextAccessor);
            #endregion

            #region Fake Cache
            var cacheKey = config.GetSection("Static:App").Value + "|" + config.GetSection("Static:Env").Value + "|CacheDependencyMap";
            var ds = new ConcurrentDictionary<string, List<string>>(
               new List<KeyValuePair<string, List<string>>> { new KeyValuePair<string, List<string>>("", new List<string> { "" }) }).ToJson();
            var fakeCache = Substitute.For<IDistributedCache>();
            fakeCache.Get(cacheKey).Returns(Encoding.UTF8.GetBytes(ds));
            #endregion

            #region Fake repofactory and Distributed cache serviice
            var repoFactory = new RepositoryFactory(dbCtxReslover, userCtx);
            
            var cacheService = new DistributedCacheService(fakeCache, config);
            mapper = new MapperConfiguration(
                cfg => { 
                    cfg.AddProfile<ActiveSubstanceMappingProfile>();
                    cfg.AddProfile<ClientMappingProfile>();
            }).CreateMapper();
            var distributedCacheServiceFactory = new DistributedCacheServiceFactory(repoFactory, cacheService, mapper, config);
            #endregion

            #region controller and Tempdata
            controller = new ActiveSubstancesController(distributedCacheServiceFactory, mapper);

            var tempData = new TempDataDictionary(context, Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            controller.TempData = tempData;
            #endregion
        }

        #region Index
        [Fact]
        public async Task Index_Returns_ActiveSubstanceModel()
        {
            // Act
            var result = await controller.Index();
            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var viewData = Assert.IsAssignableFrom<IList<ActiveSubstanceModel>>(viewResult.Model);
            Assert.NotEmpty(viewData);
        }
        #endregion

        #region New & Edit
        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task New_Edit_Get_ReturnsViewResult(bool isEdit)
        { // Arrange
            string ViewName = "EditActiveSubstance";
            // Act
            IActionResult? result = null;
            if(isEdit) 
                result = await controller.Edit(1);
            else
                result = await controller.New();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult.Model);
            Assert.Equal(ViewName, viewResult.ViewName);

        }
        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task New_Edit_Get_Invalid_Data_ReturnsViewResult(bool isEdit)
        { // Arrange
            controller.ModelState.AddModelError("id", "ErrorMessage");
            // Act
            IActionResult? result = null;
            if (isEdit)
                result = await controller.Edit(1);
            else
                result = await controller.New();

            // Assert
            if (isEdit)
            {
                var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
                Assert.NotNull(badRequestResult);
                Assert.Equal(400, badRequestResult.StatusCode);
            }
            else
            {
                var viewResult = Assert.IsType<ViewResult>(result);
                Assert.NotNull(viewResult.Model);
            }

        }
        #endregion
        #region SaveEdit & SaveNew
        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task Save_New_Edit_With_ValidData_ThrowsException(bool isEdit)
        {
            // Arrange
            var fc = Substitute.For<IDistributedCacheServiceFactory>();
            var substanceCache = Substitute.For<ITrackedEntityCacheServiceProxy<ActiveSubstance>>();
            var subProdCache = Substitute.For<ITrackedEntityCacheServiceProxy<ActiveSubstanceProduct>>();
            fc.CreateTrackedEntity<ActiveSubstance>().Returns(substanceCache);
            fc.CreateTrackedEntity<ActiveSubstanceProduct>().Returns(subProdCache);
            List<ActiveSubstance> activeSubstances =
                [
                new ActiveSubstance { Id=1,Name="A", ClientId=11},
                new ActiveSubstance { Id=2,Name="b", ClientId=12},
                new ActiveSubstance { Id=3,Name="c", ClientId=14}
                ];
            substanceCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<ActiveSubstance, bool>>>()).Returns(activeSubstances[0]);
            substanceCache.SaveChangesAsync().Throws(new DbUpdateException(UniqueErrorMessage));
            var delController = new ActiveSubstancesController(fc, mapper);
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            delController.TempData = tempData;
            var model = new ActiveSubstanceModel { ClientId = 1, Name = "Test Substance 2" };

            EditActiveSubstanceViewModel editsubstanceModel = new EditActiveSubstanceViewModel { ActiveSubstance = model };
            // Act
            IActionResult? result = null;
            if (!isEdit)
                result = await delController.SaveNew(editsubstanceModel);
            else
                result = await delController.SaveEdit(1, editsubstanceModel);
            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var actual = Assert.IsType<EditActiveSubstanceViewModel>(viewResult.Model);
            Assert.NotNull(actual);
            Assert.True(actual.ActiveSubstance.HasDuplicate);
            Assert.Equal(UniqueErrorMessage, actual.ActiveSubstance.ErrorMessage);
        }
        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task Save_New_Edit_With_ValidData(bool isEdit)
        {
            // Arrange
            var model = new ActiveSubstanceModel { Id = isEdit ? 1 : 4, ClientId = 1, Name = "Test Substance 2" };

            EditActiveSubstanceViewModel editsubstanceModel = new EditActiveSubstanceViewModel();
            editsubstanceModel.ActiveSubstance = model;
            editsubstanceModel.IsProductAssigned = true;
            editsubstanceModel.Clients = GetClientModel();
            // Act
            IActionResult? result = null;
            if (isEdit)
                result = await controller.SaveEdit(1, editsubstanceModel);
            else
                result = await controller.SaveNew(editsubstanceModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectResult>(result);
            var ResposeUrl = "/active-substances";
            Assert.NotNull(redirectResult);
            Assert.Equal(redirectResult.Url, ResposeUrl);

        }
        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task Save_New_Edit_With_InValidData(bool isEdit)
        {
            //Arrange
            var model = new ActiveSubstanceModel { ClientId = 1, Name = "Test Substance 3" };
            EditActiveSubstanceViewModel editsubstanceModel = new EditActiveSubstanceViewModel();
            editsubstanceModel.ActiveSubstance = model;
            editsubstanceModel.IsProductAssigned = true;
            editsubstanceModel.Clients = GetClientModel();
            string ViewName = "EditActiveSubstance";
            controller.ModelState.AddModelError("SessionName", "Required");
            //Act
            IActionResult? result = null;
            if (isEdit)
                result = await controller.SaveEdit(1, editsubstanceModel);
            else
                result = await controller.SaveNew(editsubstanceModel);
            // Assert
            var actual = Assert.IsType<ViewResult>(result);
            Assert.NotNull(actual);
            Assert.Equal(actual.ViewName, ViewName);
        }

        [Theory]
        [InlineData(true), InlineData(false)]
        public async Task Save_New_Edit_With_InValidActiveSubstanceName(bool isEdit)
        {
            //Arrange
            var model = new ActiveSubstanceModel { ClientId = 1, Name = "<div>Test Substance 3</div>" };
            EditActiveSubstanceViewModel editsubstanceModel = new EditActiveSubstanceViewModel();
            editsubstanceModel.ActiveSubstance = model;
            editsubstanceModel.IsProductAssigned = true;
            editsubstanceModel.Clients = GetClientModel();
            string ViewName = "EditActiveSubstance";
            //Act
            IActionResult? result = null;
            if (isEdit)
                result = await controller.SaveEdit(1, editsubstanceModel);
            else
                result = await controller.SaveNew(editsubstanceModel);
            // Assert
            var actual = Assert.IsType<ViewResult>(result);
            Assert.NotNull(actual);
            Assert.Equal(actual.ViewName, ViewName);
        }
        #endregion

        #region ChangeState
        [Fact]
        public async Task ChangeState_ReturnsOkResult()
        {
            int id = 1;
            var result = await controller.ChangeState(1, id.ToString());
            var objResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, objResult?.StatusCode);

        }
        [Fact]
        public async Task ChangeState_Invalid()
        {
            // Arrange
            int id = 10;
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.ChangeState(1, id.ToString()) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region Delete
        [Fact]
        public async Task Delete_Invalid()
        {
            // Arrange
            int id = 10;
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.Delete(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        [Fact]
        public async Task Deletew_ReturnsViewResult()
        {
            // Arrange
            var Id = 1;
            // Act
            var result = await controller.Delete(Id);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal("DeleteActiveSubstance", viewResult.ViewName);
        }
        #endregion
        #region ConfirmedDelete
        [Theory]
        [InlineData(true),InlineData(false)]
        public async Task ConfirmedDelete_With_ValidData(bool isNull)
        {
            // Arrange
            var id = 1;
            var ResposeUrl = "/active-substances";
            var fc = Substitute.For<IDistributedCacheServiceFactory>();
            var substanceCache = Substitute.For<ITrackedEntityCacheServiceProxy<ActiveSubstance>>();
            var subProdCache = Substitute.For<ITrackedEntityCacheServiceProxy<ActiveSubstanceProduct>>();
            fc.CreateTrackedEntity<ActiveSubstance>().Returns(substanceCache);
            fc.CreateTrackedEntity<ActiveSubstanceProduct>().Returns(subProdCache);
            List<ActiveSubstance> activeSubstances =
                [
                new ActiveSubstance { Id=1,Name="A", ClientId=11},
                new ActiveSubstance { Id=2,Name="b", ClientId=12},
                new ActiveSubstance { Id=3,Name="c", ClientId=14}
                ];
            substanceCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<ActiveSubstance, bool>>>()).Returns(activeSubstances[0]);
            if (!isNull)
                subProdCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<ActiveSubstanceProduct, bool>>>()).Returns(new ActiveSubstanceProduct { Id = 1 });
            
            var delController = new ActiveSubstancesController(fc, mapper);
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            delController.TempData = tempData;
            // Act
            var result = await delController.ConfirmedDelete(id);
            // Assert
            var redirectResult = Assert.IsType<RedirectResult>(result);
            Assert.NotNull(redirectResult);
            Assert.Equal(redirectResult.Url, ResposeUrl);
        }
        [Fact]
        public async Task ConfirmedDelete_Invalid()
        {
            // Arrange
            int id = 10;
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.ConfirmedDelete(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region Helpers
        private List<ClientModel> GetClientModel()
        {
            return dbCtx.Client.Select(x => new ClientModel { Id = x.Id, Name = x.Name, ContractOwner = $"{x.ContractOwnerId}" }).ToList();
        }
        #endregion
    }
}
