﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class EditApplicationViewModelTests
    {
        [Fact]
        public void EditApplicationViewModel_Get_SetValue()
        {
            //Arrange
            var model = new EditApplicationViewModel();
            model.Application=new ApplicationModel();
            model.ConcernedMemberStatesRequired = true;
            model.Picklists = new List<PicklistDataModel>();
            model.Products=new List<ProductModel>();
            model.Clients=new List<ClientModel>();
            model.AllCountries=new List<CountryModel>();    
            model.ActiveSubstances=new List<ActiveSubstanceModel>();
            //Assert
            Assert.NotNull(model);
        }
    }
}
