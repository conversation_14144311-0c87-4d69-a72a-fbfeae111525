﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using NSubstitute;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Services
{
    public class NotificationServiceTests
    {
        public SubmissionModel submodel = new SubmissionModel()
        { Id = 1, SubmissionType = "type", LifecycleStateId = 2, ApplicationId = 1, CountriesIds = [1, 2, 3], SubmissionResource = new SubmissionResourceModel() { RegulatoryLead = "test{group{test{structT\r\n(email33)\r\n", InitialSentToEmail = "initialemail" } };
         private readonly HttpRequest Request;

        public NotificationServiceTests()
        {
            Request = Substitute.For<HttpRequest>();
        }
        [Fact]
        public void SendNotificationWithInitialSentToEmail_CompletesAction()
        {  //Arrange
            string user = "test{group{test{structT\r\n(email001)\r\n";
            string application = "app1";
            string createdByUser = "<EMAIL>";
            string product = "prod";
            string client = "client";
            submodel.SubmissionResource.InitialSentToEmail = user;
            var configuration = new ConfigurationBuilder()
               .AddInMemoryCollection(
               [
                   new KeyValuePair<string, string?>("SendGrid", "test-Key"),
                   new KeyValuePair<string, string?>("emailsSendingEnabled", "true"),
                   new KeyValuePair<string, string?>("AppSettings:SubmissionRequestStateChangedTemplateId", "thempid"),
                   new KeyValuePair<string, string?>("AppSettings:SmartPHLEXAdminEmail","<EMAIL>")

                   ])
               .Build();

            var NotificationService = new NotificationService(configuration);

            //Act
            var result = NotificationService.SendNotification(user, submodel, application, createdByUser, Request, product, client, null);
            //Assert
            Assert.NotNull(result);
            Assert.Equal("WaitingForActivation", result.Status.ToString());


        }
        [Fact]
        public void SendNotification_CompletesAction()
        {  //Arrange
            string user = "test{group{test{structT\r\n(email001)\r\n";
            string application = "app1";
            string createdByUser = "<EMAIL>";
            string product = "prod";
            string client = "client";

            var configuration = new ConfigurationBuilder()
               .AddInMemoryCollection(
               [
                   new KeyValuePair<string, string?>("SendGrid", "test-Key"),
                   new KeyValuePair<string, string?>("emailsSendingEnabled", "true"),
                   new KeyValuePair<string, string?>("AppSettings:SubmissionRequestStateChangedTemplateId", "thempid"),
                   new KeyValuePair<string, string?>("AppSettings:SmartPHLEXAdminEmail","<EMAIL>")

                   ])
               .Build();
            Request.Scheme.Returns("http");
            var NotificationService = new NotificationService(configuration);

            //Act
            var result = NotificationService.SendNotification(user, submodel, application, createdByUser, Request, product, client, null);

            //Assert
            Assert.NotNull(result);


        }
        [Fact]
        public void SendNotification_CompletesAction_UserGroups()
        {  //Arrange
            Request.Scheme.Returns("http");
            string user = "<EMAIL>";
            string application = "app1";
            string createdByUser = "<EMAIL>";
            string product = "prod";
            string client = "client";

            var configuration = new ConfigurationBuilder()
               .AddInMemoryCollection(
               [
                   new KeyValuePair<string, string?>("SendGrid", "test-Key"),
                   new KeyValuePair<string, string?>("emailsSendingEnabled", "true"),
                   new KeyValuePair<string, string?>("AppSettings:SubmissionSourceDocumentsUploadedTemplateId", "thempid"),
                   new KeyValuePair<string, string?>("AppSettings:SmartPHLEXAdminEmail","<EMAIL>")

                   ])
               .Build();

            var NotificationService = new NotificationService(configuration);

            //Act
            var res = NotificationService.SendNotification(user, submodel, application, createdByUser, Request, product, client, null);
            //Assert
            Assert.NotNull(res);


        }
        [Fact]
        public void SendNotification_For_DocumentModel_CompletesAction()
        {  //Arrange
            string user = "test \r\n{group \r\n{test \r\n{structT \r\n(email001)\r\n";
            DocumentModel documentModel = new DocumentModel()
            { Id = 1, Version = 1 };
            var configuration = new ConfigurationBuilder()
               .AddInMemoryCollection(
               [
                   new KeyValuePair<string, string?>("SendGrid", "test-Key"),
                   new KeyValuePair<string, string?>("emailsSendingEnabled", "true"),
                   new KeyValuePair<string, string?>("AppSettings:SubmissionRequestStateChangedTemplateId", "thempid"),
                   new KeyValuePair<string, string?>("AppSettings:SmartPHLEXAdminEmail","<EMAIL>")

                   ])
               .Build();

            NotificationService NotificationService = new(configuration);

            //Act
            var result = NotificationService.SendNotification(user, submodel, documentModel, Request);

            //Assert
            Assert.NotNull(result);


        }
        [Fact]
        public void SendNotification_emailsSendingDisabled_CompletesAction()
        {  //Arrange
            Request.Scheme.Returns("http");
            string user = "<EMAIL>";
            string application = "app1";
            string createdByUser = "<EMAIL>";
            string product = "prod";
            string client = "client";

            var configuration = new ConfigurationBuilder()
               .AddInMemoryCollection(
               [
                   new KeyValuePair<string, string?>("SendGrid", "test-Key"),
                   new KeyValuePair<string, string?>("emailsSendingEnabled", "false"),
                   new KeyValuePair<string, string?>("AppSettings:SubmissionSourceDocumentsUploadedTemplateId", "thempid"),
                   new KeyValuePair<string, string?>("AppSettings:SmartPHLEXAdminEmail","<EMAIL>")

                   ])
               .Build();

            var NotificationService = new NotificationService(configuration);

            //Act
            var res = NotificationService.SendNotification(user, submodel, application, createdByUser, Request, product, client, null);

            //Assert
            Assert.NotNull(res);
            Assert.Equal("RanToCompletion",res.Status.ToString());

        }
    }
}
