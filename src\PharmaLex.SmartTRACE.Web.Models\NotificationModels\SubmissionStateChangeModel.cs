﻿using Newtonsoft.Json;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class SubmissionStateChangeModel : BaseNotificationModel
    {
        [JsonProperty("application")]
        public string Application { get; set; }

        [JsonProperty("state")]
        public string LifecycleState { get; set; }

        [<PERSON><PERSON><PERSON>roper<PERSON>("requestorName")]
        public string RequestorName { get; set; }

        [J<PERSON><PERSON>roper<PERSON>("submissionType")]
        public string SubmissionType { get; set; }

        [JsonProperty("regulatoryLead")]
        public string RegulatoryLead { get; set; }

        [<PERSON>son<PERSON>roperty("initialSentToEmail")]
        public string InitialSentToEmail { get; set; }

        [<PERSON><PERSON><PERSON>roper<PERSON>("publishers")]
        public string Publishers { get; set; }

        [<PERSON><PERSON><PERSON>roper<PERSON>("client")]
        public string Client { get; set; }

        [JsonProperty("product")]
        public string Product { get; set; }
    }
}
