﻿using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using PharmaLex.Office;
using PharmaLex.SmartTRACE.Web.Models;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using PharmaLex.SmartTRACE.Web.Helpers.Export.Submissions;
using Newtonsoft.Json;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public class SubmissionExport : ExcelWriter, ISubmissionExport
    {
        private readonly ISubmissionService submissionService;

        public SubmissionExport(ISubmissionService submissionService)
        {
            this.submissionService = submissionService;
        }

        public async Task<byte[]> Export(ClaimsPrincipal user, SubmissionFilterModel model)
        {
            Thread.CurrentThread.CurrentCulture = new CultureInfo("en-Us");
            (IWorkbook workbook, IDictionary<string, XSSFCellStyle> styles) = this.CreateWorkbook();
            ISheet exSheet = workbook.CreateSheet("Submissions");
            int rowIndex = 0;
            ColorTranslator.ToOle(Color.White);
            styles.Add("bluegrey2-header", CreateHeaderStyle(workbook, new XSSFColor(IndexedColors.White), new XSSFColor(new byte[] { (byte)109, (byte)167, (byte)213 })));
            styles.Add("bluegrey1-header", CreateHeaderStyle(workbook, new XSSFColor(IndexedColors.White), new XSSFColor(new byte[] { (byte)76, (byte)118, (byte)152 })));
            styles.Add("greyblue2-header", CreateHeaderStyle(workbook, new XSSFColor(IndexedColors.White), new XSSFColor(new byte[] { (byte)81, (byte)92, (byte)101 })));
            styles.Add("greyblue1-header", CreateHeaderStyle(workbook, new XSSFColor(IndexedColors.White), new XSSFColor(new byte[] { (byte)122, (byte)139, (byte)152 })));

            model.Columns = JsonConvert.DeserializeObject<List<SubmissionColumnsExportModel>>(model.ExportColumns);

            var generalTabColumnNames = model.Columns.Where(x => x.ColumnType == SubmissionColumnType.generalColumn.ToString()).Select(x => x.ColumnName).ToList();
            var clientDetailsTabColumnNames = model.Columns.Where(x => x.ColumnType == SubmissionColumnType.clientDetailsColumn.ToString()).Select(x => x.ColumnName).ToList();
            var datesTabColumnNames = model.Columns.Where(x => x.ColumnType == SubmissionColumnType.datesColumn.ToString()).Select(x => x.ColumnName).ToList();
            var resourcesTabColumnNames = model.Columns.Where(x => x.ColumnType == SubmissionColumnType.resourcesColumn.ToString()).Select(x => x.ColumnName).ToList();
            var documentTabColumnNames = model.Columns.Where(x => x.ColumnType == SubmissionColumnType.documentColumn.ToString()).Select(x => x.ColumnName).ToList();

            var firstTab = generalTabColumnNames.Count;
            var secondTab = firstTab + clientDetailsTabColumnNames.Count;
            var thirdTab = secondTab + datesTabColumnNames.Count;
            var fourthTab = thirdTab + resourcesTabColumnNames.Count;

            IRow header = exSheet.CreateRow(rowIndex++);
            header.CreateCells(0, styles["header"], generalTabColumnNames.ToArray());
            header.CreateCells(firstTab, styles["bluegrey2-header"], clientDetailsTabColumnNames.ToArray());
            header.CreateCells(secondTab, styles["bluegrey1-header"], datesTabColumnNames.ToArray());
            header.CreateCells(thirdTab, styles["greyblue2-header"], resourcesTabColumnNames.ToArray());
            header.CreateCells(fourthTab, styles["greyblue1-header"], documentTabColumnNames.ToArray());

            await AddSubmissionData(styles, exSheet, rowIndex, user, workbook.CreateDataFormat(), model);

            var allColumnsCount = generalTabColumnNames.Count + clientDetailsTabColumnNames.Count + datesTabColumnNames.Count +
                                  resourcesTabColumnNames.Count + documentTabColumnNames.Count;

            exSheet.AutoSizeColumns(0, allColumnsCount);
            exSheet.SetAutoFilter(new CellRangeAddress(0, 0, 0, allColumnsCount - 1));
            var countriesIndex = generalTabColumnNames.FindIndex(x => x == "Countries");
            var subCommentsIndex = generalTabColumnNames.FindIndex(x => x == "Submission Comments");
            if (countriesIndex > -1)
            {
                exSheet.SetColumnWidth(countriesIndex, 20 * 1000);
            }
            if (subCommentsIndex > -1)
            {
                exSheet.SetColumnWidth(subCommentsIndex, 20 * 1000);
            }

            return workbook.ToByteArray();
        }

        private async Task AddSubmissionData(IDictionary<string, XSSFCellStyle> styles, 
                                             ISheet exSheet, 
                                             int rowIndex, 
                                             ClaimsPrincipal user, 
                                             IDataFormat dataFormatCustom, 
                                             SubmissionFilterModel model)
        {
            var allSubmissions = await submissionService.GetAllSubmissionData(user, model);

            if (!string.IsNullOrEmpty(model.DisplayPublishers))
            {
                allSubmissions = allSubmissions.Where(x => x.DisplayPublishers.Contains(model.DisplayPublishers, StringComparison.InvariantCultureIgnoreCase)).ToList();
            }

            foreach (var sub in allSubmissions)
            {
                List<object> submissionProps = new List<object>();

                foreach (var columnType in Enum.GetValues(typeof(SubmissionColumnType)))
                {
                    foreach (var column in model.Columns.Where(x => x.ColumnType == columnType.ToString()))
                    {
                        if (column.ColumnType == SubmissionColumnType.resourcesColumn.ToString() && column.ColumnName != "Publishers")
                        {
                            submissionProps.Add(sub.SubmissionResource.GetType().GetProperty(SubmissionExportColumns.ColumnsValueDict[column.ColumnName]).GetValue(sub.SubmissionResource, null));
                        }
                        else
                        {
                            submissionProps.Add(sub.GetType().GetProperty(SubmissionExportColumns.ColumnsValueDict[column.ColumnName]).GetValue(sub, null));
                        }
                    }
                }
                

                var row = exSheet.CreateRow(rowIndex++);
                for (int i = 0; i < submissionProps.Count; i++)
                {
                    if (submissionProps[i] is DateTime date)
                    {
                        row.CreateCell(i, date, styles["wrapped"])
                            .CellStyle
                            .DataFormat = dataFormatCustom.GetFormat("dd MMMM yyyy");
                    }
                    else
                    {
                        row.CreateCell(i, submissionProps[i]?.ToString(), styles["wrapped"]);
                    }
                }
            }
        }
    }
}
