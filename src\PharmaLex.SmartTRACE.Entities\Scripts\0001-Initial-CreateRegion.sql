﻿CREATE TRIGGER [dbo].[Region_Insert] ON [dbo].[Region]
FOR INSERT AS
INSERT INTO [Audit].[Region_Audit]
SELECT 'I', [Id], [Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Region_Update] ON [dbo].[Region]
FOR UPDATE AS
INSERT INTO [Audit].[Region_Audit]
SELECT 'U', [Id], [Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Region_Delete] ON [dbo].[Region]
FOR DELETE AS
INSERT INTO [Audit].[Region_Audit]
SELECT 'D', [Id], [Name], [Abbreviation], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO