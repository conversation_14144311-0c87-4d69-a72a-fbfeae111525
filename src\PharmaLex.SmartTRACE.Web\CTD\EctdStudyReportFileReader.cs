﻿using PharmaLex.SmartTRACE.Web.Models.Ich.StudyReport;
using PharmaLex.SmartTRACE.Web.Storage;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.CTD
{
    public interface IEctdStudyReportFileReader
    {
        Task<study> ReadFile(string fullFilePath);
    }

    public class EctdStudyReportFileReader : IEctdStudyReportFileReader
    {
        private readonly ISubmissionBlobContainer blobContainer;

        public EctdStudyReportFileReader(ISubmissionBlobContainer blobContainer)
        {
            this.blobContainer = blobContainer;
        }

        public async Task<study> ReadFile(string fullFilePath)
        {
            using (Stream stream = await this.GetFileStream(fullFilePath))
            {
                try
                {
                    XDocument doc = await XDocument.LoadAsync(stream, LoadOptions.None, new System.Threading.CancellationToken());
                    XmlSerializer serializer = new XmlSerializer(typeof(study));
                    object output = serializer.Deserialize(doc.CreateReader());

                    return (study)output;
                }
                catch
                {
                    return null;
                }
            }
        }

        private async Task<Stream> GetFileStream(string fullFilePath)
        {
            var file = await this.blobContainer.GetBlobClientAsync(fullFilePath);
            return await file.OpenReadAsync();
        }
    }
}
