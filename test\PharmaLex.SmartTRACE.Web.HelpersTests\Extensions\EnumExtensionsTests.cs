﻿using PharmaLex.SmartTRACE.Web.Helpers.Extensions;
using System.ComponentModel;


namespace PharmaLex.SmartTRACE.Web.HelpersTests.Extensions
{
    public class EnumExtensionsTests
    {
        [Fact]
        public void GetValueFromDescription_ValidDescription_ReturnsEnumValue()
        {
            // Arrange
            string description = "First Value";

            // Act
            var result = EnumExtensions.GetValueFromDescription<TestEnum>(description);

            // Assert
            Assert.Equal(TestEnum.First, result);
        }

        [Fact]
        public void GetValueFromDescription_ValidEnumName_ReturnsEnumValue()
        {
            // Arrange
            string description = "Third";

            // Act
            var result = EnumExtensions.GetValueFromDescription<TestEnum>(description);

            // Assert
            Assert.Equal(TestEnum.Third, result);
        }

        [Fact]
        public void GetValueFromDescription_InvalidDescription_ReturnsDefault()
        {
            // Arrange
            string description = "Invalid Value";

            // Act
            var result = EnumExtensions.GetValueFromDescription<TestEnum>(description);

            // Assert
            Assert.Equal(default(TestEnum), result);
        }
        public enum TestEnum
        {
            [Description("First Value")]
            First,

            [Description("Second Value")]
            Second,

            Third // No description attribute
        }
    }
}
