﻿CREATE TRIGGER [dbo].[SubmissionCountry_Insert] ON [dbo].[SubmissionCountry]
FOR INSERT AS
INSERT INTO [Audit].[SubmissionCountry_Audit]
SELECT 'I', [SubmissionId], [CountryId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[SubmissionCountry_Update] ON [dbo].[SubmissionCountry]
FOR UPDATE AS
INSERT INTO [Audit].[SubmissionCountry_Audit]
SELECT 'U', [SubmissionId], [CountryId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[SubmissionCountry_Delete] ON [dbo].[SubmissionCountry]
FOR DELETE AS
INSERT INTO [Audit].[SubmissionCountry_Audit]
SELECT 'D', [SubmissionId], [CountryId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO