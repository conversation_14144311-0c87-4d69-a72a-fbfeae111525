﻿using PharmaLex.SmartTRACE.Web.Helpers.Constants;
using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.Helpers.Extensions
{
    public static class ApplicationFilterModelExtensions
    {
        public static ApplicationFilterModel FromFilters(this ApplicationFilterModel model, string[] filters)
        {
            foreach (var filter in filters ?? System.Array.Empty<string>())
            {
                var splitFilter = filter.Split("=>");
                var filterName = splitFilter[0];
                var filterValue = splitFilter[^1];

                if (!string.IsNullOrWhiteSpace(filterName) && !string.IsNullOrWhiteSpace(filterValue))
                {
                    switch (filterName.ToLower())
                    {
                        case TableFilterConstants.ClientName:
                            model.ClientName = filterValue;
                            break;
                        case TableFilterConstants.ApplicationNumber:
                            model.ApplicationNumber = filterValue;
                            break;
                        case TableFilterConstants.Product:
                            model.Product = filterValue;
                            break;
                        case TableFilterConstants.ApplicationType:
                            model.ApplicationType = filterValue;
                            break;
                        case TableFilterConstants.Region:
                            model.Region = filterValue;
                            break;
                        case TableFilterConstants.Country:
                            model.Country = filterValue;
                            break;
                        case TableFilterConstants.MedicinalProductDomain:
                            model.MedicinalProductDomain = filterValue;
                            break;
                        case TableFilterConstants.LifecycleState:
                            model.LifecycleState = filterValue;
                            break;
                        case TableFilterConstants.ProcedureType:
                            model.ProcedureType = filterValue;
                            break;
                    }
                }
            }
            return model;
        }
    }
}
