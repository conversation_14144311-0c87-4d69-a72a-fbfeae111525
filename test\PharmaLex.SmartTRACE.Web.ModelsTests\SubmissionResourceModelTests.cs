﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class SubmissionResourceModelTests
    {
        [Fact]
        public void SubmissionRequestFormModel_Get_SetValue()
        {
            //Arrange
            var model = new SubmissionResourceModel();
            model.Id = 1;
            model.Priority = "Test";
            model.PriorityId = 1;
            model.EstimatedHours = 0;
            model.RegulatoryLead = "Test123";
            model.PublishingLead = "Test";
            model.PublishingTeam = "Test";
            model.Comments = "Test";
            model.PublishingTeamId = 1;
            model.DisplayPublishers = "Test";
            model.EstimatedSizeId = 1;
            model.Comments = "Test";
            model.EstimatedSize = "large";
            model.SubmissionId = 1;
            model.InitialSentToEmail = "Test";
            model.Publishers = new List<UserFindResultModel>();
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void SubmissionResourceMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new SubmissionResourceMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
    }
}
