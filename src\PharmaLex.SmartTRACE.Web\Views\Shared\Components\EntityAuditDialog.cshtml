﻿<script type="text/x-template" id="entity-audit-template">
    <div class="dialog-surface" v-if="show" v-on:click.stop="close">
        <div class="dialog-container" v-on:click.stop>
            <i class="icon-cancel-circled dialog-closer" v-on:click.stop="close"></i>
            <div class="dialog-content audit-history-dialog-content">
                <h5>{{entityName}} - audit history</h5>
                <div class="audit-history-loader" v-if="records.length === 0"><img src="/images/loaders/spinner-75.svg" /></div>
                <template v-for="ar in records">
                    <section :class="['audit-record', ar.modified.length > 0 ? 'has-details' : '', ar.show ? 'open' : '']" v-on:click="ar.show = !ar.show">
                        <div class="audit-record-details">
                            <strong>{{ar.action}}</strong> by <strong>{{ar.actor}}</strong> on <strong>{{ar.time}}</strong>
                            <table v-if="ar.show">
                                <tr>
                                    <th>Field</th>
                                    <th>Before</th>
                                    <th>After</th>
                                </tr>
                                <tr v-for="m in ar.modified">
                                    <td>{{m.name}}</td>
                                    <td>{{m.before}}</td>
                                    <td>{{m.after}}</td>
                                </tr>
                            </table>
                        </div>
                    </section>
                </template>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
    vueApp.component('entity-audit-dialog', {
        template: '#entity-audit-template',
        data() {
            return {
                show: false,
                records: []
            }
        },
        props: {
            entityName: String,
            url: String
        },
        methods: {
            showTrail() {
                this.show = true;
                if (this.records.length == 0) {
                    fetch(this.url, {
                        method: 'GET',
                        credentials: 'same-origin'
                    }).then(r => r.json()).then(r => {
                        this.records = r;
                    });
                }
            },
            close() {
                this.show = false;
            }
        }
    });
</script>