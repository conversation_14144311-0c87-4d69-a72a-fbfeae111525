﻿using PharmaLex.SmartTRACE.Web.Storage;

namespace PharmaLex.SmartTRACE.Web.CTD
{
    public interface IEctdProvider
    {
        public IEctdFileReader Reader { get; }
    }
    public class EctdProvider : IEctdProvider
    {
        private readonly ISubmissionBlobContainer blobContainer;

        public EctdProvider(ISubmissionBlobContainer blobContainer)
        {
            this.blobContainer = blobContainer;
        }

        public IEctdFileReader Reader => new AzureBlobStorageEctdFileReader(this.blobContainer);
    }
}
