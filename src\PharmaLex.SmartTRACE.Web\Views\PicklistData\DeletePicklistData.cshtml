﻿@model PicklistDataModel
@using PharmaLex.SmartTRACE.Entities.Enums
@{
    ViewData["Title"] = "Delete picklist";
    var picklistType = ((PicklistType)Model.PicklistTypeId).GetDescription();
}

<div class="manage-container">
    <header class="manage-header">
        <h2>Delete <em>@Model.Name</em></h2>
        <a class="button secondary icon-button-back" href="/data/edit/@Model.Id">Back</a>
    </header>
    <form method="post">
        <div class="form-col">
            <p>Warning: Deleting is permanent and once deleted the @picklistType cannot be restored.</p>
            <p>&nbsp;</p>
            <p>Are you sure you want to delete the @picklistType '<strong>@Model.Name</strong>'?</p>
        </div>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/data/edit/@Model.Id">Cancel</a>
            <button class="delete-button icon-button-delete">Delete</button>
        </div>
    </form>
</div>