﻿using PharmaLex.SmartTRACE.Entities.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public static class RequiredFieldsHelper
    {
        private static Dictionary<SubmissionLifeCycleState, IEnumerable<string>> requiredFields;

        static RequiredFieldsHelper()
        {
            requiredFields = new Dictionary<SubmissionLifeCycleState, IEnumerable<string>>();
            InitRequiredFields();
        }

        public static IEnumerable<string> GetRequiredFields(SubmissionLifeCycleState state) =>
            requiredFields[state];

        private static void InitRequiredFields()
        {
            InitDraft();
            InitPlanned();
            InitInProgress();
            InitReadyForPublishing();
            InitQCReview();
            InitApprovedForSubmission();
            InitSubmitted();
            InitArchived();
            InitWithdrawn();
            InitObsolete();
        }

        private static void InitObsolete() => requiredFields.Add(SubmissionLifeCycleState.Obsolete, ArchivedFields());

        private static void InitWithdrawn() => requiredFields.Add(SubmissionLifeCycleState.WithdrawnFromHA, ArchivedFields());

        private static void InitArchived() => requiredFields.Add(SubmissionLifeCycleState.Archived, ArchivedFields());

        private static void InitSubmitted() => requiredFields.Add(SubmissionLifeCycleState.Submitted, SubmittedFields());

        private static void InitApprovedForSubmission() => requiredFields.Add(SubmissionLifeCycleState.ApprovedForSubmission, ApprovedForSubmissionFields());

        private static void InitQCReview() => requiredFields.Add(SubmissionLifeCycleState.QCReview, ReadyForPublishingAndQCReviewFields());

        private static void InitReadyForPublishing() => requiredFields.Add(SubmissionLifeCycleState.ReadyForPublishing, ReadyForPublishingAndQCReviewFields());

        private static void InitInProgress() => requiredFields.Add(SubmissionLifeCycleState.InProgress, InProgressFields());

        private static void InitPlanned() => requiredFields.Add(SubmissionLifeCycleState.Planned, PlannedFields());
 
        private static void InitDraft() => requiredFields.Add(SubmissionLifeCycleState.Draft, DraftFields());

        private static IEnumerable<string> DraftFields() =>
            new List<string>
            {
                "Application.ApplicationNumber", "Submission.PlannedSubmissionDate", "Submission.SubmissionTypeId", "Submission.PlannedDispatchDate",
                "SubmissionResource.EstimatedSizeId", "SubmissionResource.EstimatedHours", "SubmissionResource.RegulatoryLead", "SubmissionResource.PublishingTeamId",
                "SubmissionResource.PublishingLead", "SubmissionResource.Publisher", "Submission.ProjectId"
            };

        private static IEnumerable<string> PlannedFields() =>
            DraftFields().Concat(new List<string> { "Submission.SubmissionUnitId", "Submission.SubmissionModeId", "Submission.Description" });

        private static IEnumerable<string> InProgressFields() =>
            PlannedFields().Concat(new List<string> { "Submission.DeliveryDetailsId", "Submission.RelatedSequenceNumber" });


        private static IEnumerable<string> ReadyForPublishingAndQCReviewFields() =>
            InProgressFields().Concat(new List<string> { "Submission.SequenceNumber", "Submission.DossierFormatId" });

        private static IEnumerable<string> ApprovedForSubmissionFields() =>
            ReadyForPublishingAndQCReviewFields().Concat(new List<string> { "Submission.ActualDispatchDate", "Submission.ActualSubmissionDate", 
                                                                            "Submission.CespNumber", "Submission.DocubridgeVersionId" });
        
        private static IEnumerable<string> SubmittedFields() =>
            ApprovedForSubmissionFields().Concat(new List<string> { "Submission.DossierArchive" });

        private static IEnumerable<string> ArchivedFields() =>
          SubmittedFields().Concat(new List<string> { "Submission.WithdrawalDate" });
    }
}
