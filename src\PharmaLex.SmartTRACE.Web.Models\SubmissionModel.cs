﻿using AutoMapper;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using PharmaLex.Helpers;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class SubmissionModel: IModel
    {
        public SubmissionModel()
        {
            this.CountriesIds = new List<int>();
        }

        public int Id { get; set; }
        [MaxLength(32)]
        public string UniqueId { get; set; }

        public int? DeliveryDetailsId { get; set; }
        public string SubmissionDeliveryDetails { get; set; }

        [MaxLength(10)]
        public string SequenceNumber { get; set; }

        [MaxLength(10)]
        public string RelatedSequenceNumber { get; set; }

        public int? DossierFormatId { get; set; }
        public string DossierFormat { get; set; }

        [MaxLength(10)]
        public string SerialNumber { get; set; }

        [MaxLength(50)]
        public string ReferenceNumber { get; set; }

        public int SubmissionTypeId { get; set; }
        public string SubmissionType { get; set; }

        public int? SubmissionUnitId { get; set; }
        public string SubmissionUnit { get; set; }

        public int? SubmissionModeId { get; set; }
        public string SubmissionMode { get; set; }

        public string Description { get; set; }
        public string Comments { get; set; }

        public int LifecycleStateId { get; set; }
        public string LifecycleState { get; set; }

        public int PreviousLifecycleStateId { get; set; }

        public DateTime? HealthAuthorityDueDate { get; set; }

        public DateTime? AuthoringDeadline { get; set; }

        public DateTime? PlannedDispatchDate { get; set; }

        public DateTime? ActualDispatchDate { get; set; }

        public DateTime PlannedSubmissionDate { get; set; }

        public DateTime? ActualSubmissionDate { get; set; }

        public string CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; }

        [MaxLength(128)]
        public string CespNumber { get; set; }

        public DateTime? WithdrawalDate { get; set; }
        public string DisplayRegulatoryLead { get; set; }
        public string DisplayPublishingLead { get; set; }
        public string DisplayCountries { get; set; }

        public string SourceDocumentsLocation { get; set; }
        public string ArchivedDocumentsLocation { get; set; }

        public string DossierName { get; set; }

        [MaxLength(33)]
        public string DocubridgeVersionId { get; set; }

        public int ApplicationId { get; set; }
        public int? ProjectId { get; set; }
        public IList<int> CountriesIds { get; set; }
        public int RegionId { get; set; }
        public SubmissionResourceModel SubmissionResource { get; set; }
        public IList<DocumentModel> Documents { get; set; }
    }

    public class SubmissionLifecycleStateList : NamedEntityModel
    {
        public bool Selected { get; set; }
    }

    public class PicklistTypeList : NamedEntityModel
    {
    }

    public class SubmissionMappingProfile : Profile
    {
        public SubmissionMappingProfile()
        {
            this.CreateMap<Submission, SubmissionModel>()
                .ForMember(d => d.LifecycleState, s => s.MapFrom(x => ((SubmissionLifeCycleState)x.LifecycleStateId).GetDescription()))
                .ForMember(d => d.Documents, s => s.MapFrom(x => x.Document));
            this.CreateMap<SubmissionModel, Submission>()
                 .ForMember(d => d.Document, s => s.MapFrom(x => x.Documents))
                .ForMember(d => d.CreatedDate, s => s.Ignore())
                .ForMember(d => d.CreatedBy, s => s.Ignore());
            this.CreateMap<Submission, EditSubmissionViewModel>()
                .ForMember(d => d.Submission, s => s.MapFrom(x => x));
            this.CreateMap<SubmissionLifeCycleState, SubmissionLifecycleStateList>()
                .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
                .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));
            this.CreateMap<PicklistType, PicklistTypeList>()
                .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
                .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));
            this.CreateMap<Submission, SubmissionViewModel>();
        }
    }
}
