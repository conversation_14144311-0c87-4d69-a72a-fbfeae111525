﻿// Configure bundling and minification for the project.
// More info at https://go.microsoft.com/fwlink/?LinkId=808241
[
  //{
  //  "outputFileName": "../../lib/PharmaLex.Shared.Web/wwwroot/css/site.min.css",
  //  "inputFiles": [
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/css/materialdesignicons.css",
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/css/reset.css",
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/css/fonts.css",
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/css/screen.css",
  //    "wwwroot/css/smarttrace.css"
  //  ]
  //},
  //{
  //  "outputFileName": "../../lib/PharmaLex.Shared.Web/wwwroot/js/map-bundle.min.js",
  //  "inputFiles": [
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/data/countries.topo.js",
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/lib/d3/d3.v4.js",
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/lib/d3/topojson.js",
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/js/map.js"
  //  ]
  //},
  //{
  //  "outputFileName": "../../lib/PharmaLex.Shared.Web/wwwroot/lib/qjuery/dist/jquery.min.js",
  //  "inputFiles": [
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/lib/qjuery/dist/jquery.js"
  //  ]
  //},
  //{
  //  "outputFileName": "../../lib/PharmaLex.Shared.Web/wwwroot/js/datatables-custom.min.js",
  //  "inputFiles": [
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/lib/datatables/datatables.min.js",
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/lib/moment/moment.min.js",
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/lib/datatables/datatables-moment.js",
  //    "../../lib/PharmaLex.Shared.Web/wwwroot/js/datatables.ext.js"
  //  ],
  //  "minify": {
  //    "enabled": true,
  //    "renameLocals": true
  //  },
  //  "sourceMap": false
  //}
]
