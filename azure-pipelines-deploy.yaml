parameters:
  - name: packageLocation
    type: string
    default: 'drop/PharmaLex.SmartTRACE.Web.zip'
  - name: packageFaLocation
    type: string
    default: 'drop/PharmaLex.SmartTRACE.RemindersApp.zip'
  

pool: 'pv-pool'

trigger: none

resources:
  pipelines:
    - pipeline: build-pipeline
      source: SmartTrace.Build
      project: SmartTrace
      trigger:
        branches:
          include:
            - develop

stages:
  - template: ./azure-pipelines-template.yaml
    parameters:
      packageLocation: ${{ parameters.packageLocation }}
      packageFaLocation: ${{ parameters.packageFaLocation }}