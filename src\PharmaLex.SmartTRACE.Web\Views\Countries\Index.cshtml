﻿@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@model IEnumerable<CountryModel>

<div id="countries" class="manage-container">
    <header class="manage-header">
        <h2>Manage Countries</h2>
    </header>
    <filtered-table :items="countries" :columns="columns" :filters="filters" :link="link"></filtered-table>
</div>

@section Scripts {
    <script type="text/javascript">

    var pageConfig = {
        appElement: '#countries',
        data() {
            return {
                link: '',
                countries: @Html.Raw(JsonConvert.SerializeObject(Model, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })),
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            type: 'text'
                        },
                        {
                            dataKey: 'twoLetterCode',
                            sortKey: 'twoLetterCode',
                            header: 'Two Letter Code',
                            type: 'text'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'name',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'twoLetterCode',
                        options: [],
                        type: 'search',
                        header: 'Search Code',
                        fn: v => p => p.twoLetterCode.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    }
                ]
            };
        }
    };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}