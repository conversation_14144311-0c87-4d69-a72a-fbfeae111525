﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class ApplicationModelTests
    {
        [Fact]
        public void ApplicationModel_Get_SetValue()
        {
            //Arrange
            var model = new ApplicationModel();
            model.Id = 1;
            model.UniqueId = "1";
            model.ApplicationNumber = "1";
            model.ProcedureNumber = "1";
            model.MedicinalProductDomainId = 1;
            model.MedicinalProductTypeId = 1;
            model.ApplicationTypeId = 1;
            model.ApplicationType = "1";
            model.ProcedureTypeId = 1;
            model.ProcedureType = "1";
            model.Comments = "test";
            model.LifecycleStateId = 1;
            model.LifecycleState = "initial";
            model.ClientId = 1;
            model.ProjectId = 1;
            model.CountryId = 1;
            model.CountriesIds = new List<int>();
            model.Submissions = new List<SubmissionModel>();
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void ApplicationLifecycleStateList_Get_SetValue()
        {
            //Arrange
            var model = new ApplicationLifecycleStateList();
            model.Selected = true;
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void ApplicationMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new ApplicationMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
    }
}
