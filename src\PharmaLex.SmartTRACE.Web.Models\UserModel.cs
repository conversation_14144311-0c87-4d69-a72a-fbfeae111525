﻿using AutoMapper;
using PharmaLex.Authentication.B2C;
using PharmaLex.Helpers;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class UserModel : IModel
    {
        public UserModel()
        {
            this.Claims = new List<int>();
            this.DisplayClaims = new List<string>();
            this.Clients = new List<int>();
        }

        public int Id { get; set; }
        [Required, StringLength(256)]
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Email field.")]
        public string Email { get; set; }
        [Required, StringLength(512)]
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Given name field.")]
        public string GivenName { get; set; }
        [Required, StringLength(512)]
        [RegularExpression(@"^[^<>]*$", ErrorMessage = "Invalid characters in the Family name field.")]
        public string FamilyName { get; set; }
        public List<int> Claims { get; set; }
        public List<string> DisplayClaims { get; set; }
        public string DisplayClaimsText { get; set; }
        public List<int> Clients { get; set; }
        public string DisplayClientsText { get; set; }
        public int UserTypeId { get; set; }
        public string DisplayUserType { get; set; }
        public bool AutoAccessClients { get; set; }
        public string InvitationEmailLink { get; set; }
        public DateTime? LastLoginDate { get; set; }

        public string DisplayNameAndEmail
        {
            get { return this.Id > 0 ? $"{this.GivenName} {this.FamilyName} ({this.Email})" : ""; }
        }

        public string DisplayFullName { get; set; }
    }

    public class UserFindResultModel
    {
        public int Id { get; set; }
        public string GivenName { get; set; }
        public string FamilyName { get; set; }
        public string Name { get; set; }
        public string Value { get; set; }
        public string Email { get; set; }
        public string InvitationEmailLink { get; set; }
        public List<int> Claims { get; set; }
    }

    public class UserTypeList : NamedEntityModel, IModel
    {
    }

    public class UserMappingProfile : Profile
    {
        public UserMappingProfile()
        {
            this.CreateMap<UserClaim, int>().ConvertUsing(x => x.ClaimId);
            this.CreateMap<User, UserModel>()
                .ForMember(d => d.DisplayFullName, s => s.MapFrom(x => $"{x.GivenName} {x.FamilyName}"))
                .ForMember(d => d.Claims, s => s.MapFrom(x => x.UserClaim))
                .ForMember(d => d.Clients, s => s.MapFrom(x => x.UserClient.Select(uc => uc.ClientId)))
                .ForMember(d => d.DisplayClaims, s => s.MapFrom(x => x.UserClaim.Select(x => x.Claim.Name)))
                .ForMember(d => d.DisplayClaimsText, s => s.MapFrom(x => string.Join(", ", x.UserClaim.Select(x => x.Claim.Name))))
                .ForMember(d => d.DisplayClientsText, s => s.MapFrom(x => string.Join(", ", x.UserClient.OrderBy(x => x.Client.Name).Select(x => x.Client.Name))))
                .ForMember(d => d.DisplayUserType, s => s.MapFrom(x => ((UserType)x.UserTypeId).GetDescription()));
            this.CreateMap<User, UserFindResultModel>()
                .ForMember(d => d.Claims, s => s.MapFrom(x => x.UserClaim));
            this.CreateMap<UserModel, User>().AfterMap((m, u) =>
            {
                u.UserClient = m.Clients.Select(clientId => new UserClient
                {
                    UserId = u.Id,
                    ClientId = clientId,
                }).ToList();
                foreach (int id in m.Claims)
                {
                    if (!u.UserClaim.Any(x => x.ClaimId == id))
                    {
                        u.UserClaim.Add(new UserClaim
                        {
                            ClaimId = id,
                            UserId = u.Id
                        });
                    }
                }
                foreach (int id in u.UserClaim.Where(x => !m.Claims.Contains(x.ClaimId)).Select(x => x.ClaimId).ToList())
                {
                    u.UserClaim.Remove(u.UserClaim.First(x => x.ClaimId == id));
                }
            });

            this.CreateMap<User, UserFindResultModel>()
                .ForMember(um => um.Name, u => u.MapFrom(x => $"{x.GivenName} {x.FamilyName} ({x.Email})"))
                .ForMember(um => um.Value, u => u.MapFrom("Email"))
                .ForMember(d => d.Claims, s => s.MapFrom(x => x.UserClaim));
            this.CreateMap<UserFindResultModel, User>();
            this.CreateMap<Microsoft.Graph.Models.User, UserFindResultModel>()
                .ForMember(um => um.Name, u => u.MapFrom(x => $"{x.GivenName} {x.Surname} ({x.GetEmail()})"))
                .ForMember(um => um.GivenName, u => u.MapFrom(x => x.GivenName))
                .ForMember(um => um.Value, u => u.MapFrom("UserPrincipalName"))
                .ForMember(um => um.FamilyName, u => u.MapFrom("Surname"))
                .ForMember(um => um.Email, u => u.MapFrom(x => x.GetEmail()))
                .ForMember(um => um.Id, u => u.MapFrom(x => 0));
            this.CreateMap<UserType, UserTypeList>()
                .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
                .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));
        }
    }
}
