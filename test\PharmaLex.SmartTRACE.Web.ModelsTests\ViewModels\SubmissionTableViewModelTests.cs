﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class SubmissionTableViewModelTests
    {
        [Fact]
        public void SubmissionTableViewModel_Get_SetValue()
        {
            //Arrange
            var model = new SubmissionTableViewModel();
            model.Id = 1;
            model.ApplicationNumber = "1";
            model.Description="Description";
            model.ClientName = "client";
            model.DisplayPublishers = "test";
            model.RegulatoryLead = "nitish";
            model.SequenceNumber = "001";
            model.SubmissionMode = "initial";
            model.SubmissionUnit = "1";
            model.SubmissionType= "1";
            model.LifecycleState = "initial";
            model.DossierFormat = "dossier";
            //Assert
            Assert.NotNull(model);
        }
    }
}
