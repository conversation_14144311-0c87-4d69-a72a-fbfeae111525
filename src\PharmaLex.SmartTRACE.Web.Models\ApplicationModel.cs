﻿using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class ApplicationModel : IModel
    {
        public ApplicationModel()
        {
            Submissions = new List<SubmissionModel>();
            CountriesIds = new List<int>();
            ProductsIds = new List<int>();
        }

        public int Id { get; set; }
        public string UniqueId { get; set; }

        [Required, MaxLength(32)]
        public string ApplicationNumber { get; set; }
        [MaxLength(32)]
        public string ProcedureNumber { get; set; }

        [Required]
        public int MedicinalProductDomainId { get; set; }

        [Required]
        public int MedicinalProductTypeId { get; set; }

        [Required]
        public int ApplicationTypeId { get; set; }
        public string ApplicationType { get; set; }

        [Required]
        public int ProcedureTypeId { get; set; }
        public string ProcedureType { get; set; }

        [MaxLength(250)]
        public string Comments { get; set; }

        public int LifecycleStateId { get; set; }
        public string LifecycleState { get; set; }

        [Required]
        public int ClientId { get; set; }
        public int ProjectId { get; set; }
        public int? CountryId { get; set; }
        public IList<int> CountriesIds { get; set; }
        public IList<int> ProductsIds { get; set; }
        public IList<SubmissionModel> Submissions { get; set; }
    }

    public class ApplicationLifecycleStateList : NamedEntityModel
    {
        public bool Selected { get; set; }
    }

    public class ApplicationMappingProfile : Profile
    {
        public ApplicationMappingProfile()
        {
            this.CreateMap<Application, ApplicationModel>()
                .ForMember(d => d.Submissions, s => s.MapFrom(x => x.Submission));
            this.CreateMap<ApplicationModel, Application>();
            this.CreateMap<Application, ApplicationViewModel>();
            this.CreateMap<Application, EditApplicationViewModel>()
                .ForMember(d => d.Application, s => s.MapFrom(x => x));
            this.CreateMap<CommonLifecycleState, ApplicationLifecycleStateList>()
                .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
                .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));
        }
    }
}
