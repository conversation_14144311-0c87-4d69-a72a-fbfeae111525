﻿using Microsoft.AspNetCore.Mvc.Rendering;
using PharmaLex.SmartTRACE.Web.Models;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Helpers.Extensions
{
    public static class NamedEntityModelExtensions
    {
        public static IEnumerable<SelectListItem> ToSelectList(this IEnumerable<NamedEntityModel> models)
        {
            return models.Select(x => new SelectListItem
            {
                Text = x.Name,
                Value = x.Id.ToString()
            });
        }
    }
}
