﻿@model PharmaLex.SmartTRACE.Web.Models.ErrorViewModel
@{
    ViewData["Title"] = "Ooops!";
}

<div class="manage-container">
    <header class="manage-header">
        <h2>Ooops!</h2>
        <a href="/home" class="button secondary icon-button-back">Main Page</a>
    </header>

    <p>Something unexpected happened and we were unable to complete your request. The error has been logged but a system administrator has not been notified.</p>
    <p>&nbsp;</p>
    @if (!String.IsNullOrEmpty(Model.NotifyEmail))
    {
        <p>
            If you repeatedly experience the same issue please
            <a id="letUsKnow" class="button icon-mail" style="margin:0;" href="mailto:@(Model.NotifyEmail)?subject=@Model.AppName Error@(Model.ShowRequestId ? $"&body=Request ID: {Model.RequestId}" : "")">let us know</a>
            providing as much detail as possible about the error e.g. data being managed, steps to reproduce etc.
        </p>
    }
</div>
@section Scripts {
    @if (!string.IsNullOrEmpty(Model.NotifyEmail))
    {
        <script type="text/javascript">
        (function () {
            let letUsKnowLink = document.getElementById('letUsKnow');
            let contactLink = document.getElementById('contactLink');

            if (contactLink) {
                letUsKnowLink.setAttribute('href', 'javascript:;');

                letUsKnowLink.addEventListener('click', () => {
                    contactForm.config.subject = '@Model.AppName Error';
                    contactForm.config.title = 'Error notification';
                    contactForm.config.body = '@(Model.ShowRequestId ? $"\\n\\n\\n___________________________________\\nRequest ID: {Model.RequestId}" : "")'

                    contactLink.click();
                });
            }
        })();
        </script>
    }
}