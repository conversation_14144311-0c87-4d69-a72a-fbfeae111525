﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using System.Threading.Tasks;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.DataAccess;
using System.Collections.Generic;
using System.Linq;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Entities.Enums;
using Microsoft.AspNetCore.Authorization;
using System;
using PharmaLex.Helpers;
using PharmaLex.SmartTRACE.Web.Helpers.Extensions;
using PharmaLex.SmartTRACE.Web.Models.ViewModels;
using System.Web;
using PharmaLex.SmartTRACE.Web.HTMLTags;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    public class ApplicationsController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper _mapper;
        private readonly IApplicationService service;
        private readonly IApplicationExport appExport;
        private readonly IAuthorizationService authorizationService;

        public ApplicationsController(IDistributedCacheServiceFactory cache,
                                      IMapper mapper,
                                      IApplicationService service,
                                      IApplicationExport applicationExport,
                                      IAuthorizationService authorizationService)
        {
            this.cache = cache;
            this._mapper = mapper;
            this.service = service;
            this.appExport = applicationExport;
            this.authorizationService = authorizationService;
        }

        [HttpGet, Route("/applications"), Authorize(Policy = "Reader")]
        public async Task<IActionResult> Index(
        [FromQuery] int skip = 0,
        [FromQuery] int take = 25,
        [FromQuery] string sort = null,
        [FromQuery] string[] filters = null)
        {
            ApiPagedListResult<ApplicationViewModel> applications = new ApiPagedListResult<ApplicationViewModel>(Array.Empty<ApplicationViewModel>(), 0, 0, 0, 0);
            if (ModelState.IsValid)
                applications = await this.service.GetPagedApplicationsAsync(this.User, skip, take, new ApplicationFilterModel(), sort);
            return View(applications);
        }

        [HttpGet, Route("/paged-applications"), Authorize(Policy = "Reader")]
        public async Task<IActionResult> IndexPagedApplications(
           [FromQuery] int skip = 0,
           [FromQuery] int take = 25,
           [FromQuery] string sort = null,
           [FromQuery] string[] filters = null)
        {
            ApiPagedListResult<ApplicationViewModel> applications = new ApiPagedListResult<ApplicationViewModel>(Array.Empty<ApplicationViewModel>(), 0, 0, 0, 0);
            var model = new ApplicationFilterModel();
            if (ModelState.IsValid)
                applications = await this.service.GetPagedApplicationsAsync(this.User, skip, take, model.FromFilters(filters), sort);
            return Json(applications);
        }

        [HttpGet, Route("/applications/new"), Authorize(Policy = "BusinessAdmin")]
        public async Task<IActionResult> New()
        {
            (IList<PicklistDataModel> allPicklists, IList<ProductModel> allProducts,
             IList<CountryModel> allCountries, IList<ActiveSubstanceModel> allActiveSubstances) = await PrepareInitialData();

            var model = new EditApplicationViewModel()
            {
                Application = new ApplicationModel()
                {
                    CountryId = 0,
                    LifecycleStateId = (int)CommonLifecycleState.Active
                },
                Picklists = allPicklists,
                Products = allProducts,
                Clients = await LoadClients(),
                AllCountries = allCountries,
                ActiveSubstances = allActiveSubstances
            };
            return View("EditApplication", model);
        }

        [HttpPost, Route("/applications/new"), ValidateAntiForgeryToken, Authorize(Policy = "BusinessAdmin")]
        public async Task<IActionResult> New(EditApplicationViewModel model)
        {
            (IList<PicklistDataModel> allPicklists, IList<ProductModel> allProducts,
             IList<CountryModel> allCountries, IList<ActiveSubstanceModel> allActiveSubstances) = await PrepareInitialData();

            if (!this.ModelState.IsValid)
            {
                model.Picklists = allPicklists;
                model.Products = allProducts;
                model.AllCountries = allCountries;
                model.Clients = await LoadClients();
                model.ActiveSubstances = allActiveSubstances;
                return View("EditApplication", model);
            }

            var application = await service.InsertApplication(model, allPicklists);

            this.AddConfirmationNotification($"<em>{application.ApplicationNumber}</em> created");
            return Redirect($"/applications/view/{application.Id}");
        }

        [HttpGet, Route("/applications/edit/{id}"), Authorize(Policy = "BusinessAdmin")]
        public async Task<IActionResult> Edit(int id)
        {
            if(!ModelState.IsValid)
                return View("EditApplication", new EditApplicationViewModel());
            (IList<PicklistDataModel> allPicklists, IList<ProductModel> allProducts,
             IList<CountryModel> allCountries, IList<ActiveSubstanceModel> allActiveSubstances) = await PrepareInitialData();

            var appCache = cache.CreateMappedEntity<Application, EditApplicationViewModel>()
                .Configure(o => o
                        .Include(x => x.Submission)
                            .ThenInclude(x => x.SubmissionResource));

            var appCountryCache = cache.CreateEntity<ApplicationCountry>();

            var appProductCache = cache.CreateEntity<ApplicationProduct>()
                .Configure(o => o
                    .Include(x => x.Product));

            var appCountries = await appCountryCache.WhereAsync(x => x.ApplicationId == id);

            var clientsId = (await this.cache.CreateEntity<UserClient>().WhereAsync(x => x.UserId == this.CurrentUserId)).Select(x => x.ClientId).ToList();

            var applicationProduct = await appProductCache.FirstOrDefaultAsync(x => x.ApplicationId == id);

            var currentApplication = await appCache.FirstOrDefaultAsync(x => x.Id == id);

            if (!clientsId.Contains(applicationProduct.Product.ClientId))
            {
                return Forbid();
            }

            currentApplication.Application.LifecycleState = ((CommonLifecycleState)currentApplication.Application.LifecycleStateId).GetDescription();

            var procedureTypes = allPicklists.Where(x => x.PicklistTypeId == (int)PicklistType.ProcedureType);
            currentApplication.Application.ProcedureType = procedureTypes.FirstOrDefault(x => x.Id == currentApplication.Application.ProcedureTypeId)?.Name;

            switch (currentApplication.Application.ProcedureType)
            {
                case var type when type == "National Procedure" || type == "GCC National Procedure":
                    currentApplication.Application.CountryId = appCountries.Find(x => x.AuthorityRoleId == null)?.CountryId;
                    break;
                case var type when type == "Decentralised Procedure" || type == "Mutual Recognition Procedure" || type == "ASMF Worksharing Procedure":
                    currentApplication.Application.CountryId = appCountries.Find(x => x.AuthorityRoleId == (int)AuthorityRoles.ReferenceMemberState)?.CountryId;
                    foreach (var appCountry in appCountries.Where(x => x.AuthorityRoleId == (int)AuthorityRoles.ConcernedMemberState))
                    {
                        currentApplication.Application.CountriesIds.Add(appCountry.CountryId);
                    }
                    if (currentApplication.Application.CountriesIds.Count == 0)
                    {
                        currentApplication.ConcernedMemberStatesRequired = true;
                    }
                    break;
                default:
                    foreach (var appCountry in appCountries)
                    {
                        currentApplication.Application.CountriesIds.Add(appCountry.CountryId);
                    }
                    currentApplication.Application.CountryId = 0;
                    break;
            }

            var appProducts = await appProductCache.WhereAsync(x => x.ApplicationId == id && x.Product.LifecycleStateId != (int)CommonLifecycleState.Obsolete);

            foreach (var appProduct in appProducts)
            {
                currentApplication.Application.ProductsIds.Add(appProduct.ProductId);
            }

            currentApplication.Application.ClientId = allProducts.FirstOrDefault(x => x.Id == appProducts.FirstOrDefault()?.ProductId)?.ClientId ?? 0;

            foreach (var sub in currentApplication.Application.Submissions)
            {
                sub.DossierFormat = (await PicklistHelper.ExtractPicklist(cache, sub.DossierFormatId))?.Name;
                sub.SubmissionType = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionTypeId))?.Name;
                sub.SubmissionUnit = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionUnitId))?.Name;
                sub.SubmissionMode = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionModeId))?.Name;
                sub.DisplayRegulatoryLead = sub.SubmissionResource.RegulatoryLead;
                sub.DisplayPublishingLead = sub.SubmissionResource.PublishingLead;
            }
            currentApplication.Picklists = allPicklists;
            currentApplication.Products = allProducts;
            currentApplication.AllCountries = allCountries;
            currentApplication.Clients = await LoadClients();
            currentApplication.ActiveSubstances = allActiveSubstances;

            return View("EditApplication", currentApplication);
        }

        [HttpPost, Route("/applications/edit/{id}"), ValidateAntiForgeryToken, Authorize(Policy = "BusinessAdmin")]
        public async Task<IActionResult> Edit(int id, EditApplicationViewModel model)
        {
            if (!this.ModelState.IsValid || HtmlTags.CheckHTMLTags(model.Application.ApplicationNumber))
            {                
                (IList<PicklistDataModel> allPicklists, IList<ProductModel> allProducts,
                IList<CountryModel> allCountries, IList<ActiveSubstanceModel> allActiveSubstances) = await PrepareInitialData();
                model.Picklists = allPicklists;
                model.Products = allProducts;
                model.AllCountries = allCountries;
                model.Clients = await LoadClients();
                model.ActiveSubstances = allActiveSubstances;
                return this.BadRequest(this.ModelState);
            }

            var procedureTypes = await cache.CreateMappedEntity<PicklistData, PicklistDataModel>().WhereAsync(x => x.PicklistTypeId == (int)PicklistType.ProcedureType);

            var application = await service.UpdateApplication(model, procedureTypes);

            this.AddConfirmationNotification($"<em>{application.ApplicationNumber}</em> updated");

            return Redirect($"/applications/view/{id}");
        }

        [HttpGet, Route("/applications/view/{id}"), Authorize(Policy = "Reader")]
        public async Task<IActionResult> View(int id)
        {
            if(!this.ModelState.IsValid)
                return View("ViewApplication", new ApplicationViewModel());
            var appProductCache = cache.CreateEntity<ApplicationProduct>()
                .Configure(o => o
                    .Include(x => x.Application)
                    .Include(x => x.Product)
                        .ThenInclude(x => x.Client));

            var activeSubstanceProductCache = cache.CreateEntity<ActiveSubstanceProduct>()
                .Configure(o => o
                    .Include(x => x.ActiveSubstance));

            var applicationCountryCache = cache.CreateEntity<ApplicationCountry>()
                .Configure(o => o
                    .Include(x => x.Country));

            var submissionCache = cache.CreateEntity<Submission>()
                .Configure(o => o
                    .Include(x => x.SubmissionResource)
                    .Include(x => x.SubmissionCountry));

            var clientsId = (await this.cache.CreateEntity<UserClient>().WhereAsync(x => x.UserId == this.CurrentUserId)).Select(x => x.ClientId).ToList();

            var applicationProducts = await appProductCache.WhereAsync(x => x.ApplicationId == id);
            var currentApplication = applicationProducts.GroupBy(x => x.ApplicationId)
                                                 .Select(x => new
                                                 {
                                                     Application = x.Select(x => x.Application).First(),
                                                     Products = x.Select(p => p.Product)
                                                                 .ToList()
                                                 })
                                                 .FirstOrDefault();
            var productIds = currentApplication?.Products.Select(x => x.Id).ToList();

            var activeSubstanceProducts = await activeSubstanceProductCache.WhereAsync(x => productIds.Contains(x.ProductId));
            var submissions = await submissionCache.WhereAsync(x => x.ApplicationId == currentApplication.Application.Id);
            var applicationCountries = await applicationCountryCache.WhereAsync(x => x.ApplicationId == currentApplication.Application.Id);

            if (!clientsId.Contains(currentApplication.Products[0].ClientId))
            {
                return Forbid();
            }

            var application = _mapper.Map<ApplicationViewModel>(currentApplication.Application);

            application.ApplicationType = (await PicklistHelper.ExtractPicklist(cache, currentApplication.Application.ApplicationTypeId))?.Name;
            application.MedicinalProductDomain = (await PicklistHelper.ExtractPicklist(cache, currentApplication.Application.MedicinalProductDomainId))?.Name;
            application.MedicinalProductType = (await PicklistHelper.ExtractPicklist(cache, currentApplication.Application.MedicinalProductTypeId))?.Name;
            application.LifecycleState = ((CommonLifecycleState)application.LifecycleStateId).GetDescription();

            var procedureTypes = await cache.CreateMappedEntity<PicklistData, PicklistDataModel>().WhereAsync(x => x.PicklistTypeId == (int)PicklistType.ProcedureType);
            application.ProcedureType = procedureTypes.Find(x => x.Id == currentApplication.Application.ProcedureTypeId)?.Name;
            var countries = applicationCountries.Select(x => x.Country);

            switch (application.ProcedureType)
            {
                case var type when type == "National Procedure" || type == "GCC National Procedure":
                    application.Country = countries.FirstOrDefault()?.Name;
                    break;
                case var type when type == "Decentralised Procedure" || type == "Mutual Recognition Procedure" || type == "ASMF Worksharing Procedure":
                    application.ReferenceMemberState = countries.FirstOrDefault(c => c.Id == applicationCountries.Find(x => x.AuthorityRoleId == (int)AuthorityRoles.ReferenceMemberState)?.CountryId)?.Name;
                    application.ConcernedMemberState = string.Join(", ", countries
                        .Where(x => applicationCountries
                            .Where(a => a.AuthorityRoleId == (int)AuthorityRoles.ConcernedMemberState)
                            .Select(c => c.CountryId)
                            .Contains(x.Id))
                        .Select(x => x.Name)
                        .Distinct()
                        .OrderBy(x => x)
                        .ToList());
                    break;
                default:
                    application.Country = string.Join(", ", countries.Select(x => x.Name).Distinct().OrderBy(x => x).ToList());
                    break;
            }

            var activeSubstances = activeSubstanceProducts.Where(x => productIds.Contains(x.ProductId)).Select(x => x.ActiveSubstance);

            application.Product = string.Join(" | ", applicationProducts.Select(x => x.Product)
                                                                  .Select(async x => $"{x.Name}, {(await PicklistHelper.ExtractPicklist(cache, x.DosageFormId)).Name}, {x.Strength}")
                                                                  .Select(x => x.GetAwaiter().GetResult())
                                                                  .ToList());

            application.ClientName = applicationProducts.FirstOrDefault()?.Product.Client.Name;
            application.ClientId = applicationProducts.FirstOrDefault()?.Product.Client.Id ?? 0;

            application.ActiveSubstances = string.Join(" | ", activeSubstances.Select(x => x.Name).Distinct().OrderBy(x => x).ToList());

            if (!(await authorizationService.AuthorizeAsync(User, "Admin")).Succeeded)
            {
                submissions = submissions.Where(x => x.LifecycleStateId != (int)SubmissionLifeCycleState.Obsolete).ToList();
            }

            foreach (var sub in submissions)
            {
                var submission = _mapper.Map<SubmissionModel>(sub);
                submission.DossierFormat = (await PicklistHelper.ExtractPicklist(cache, sub.DossierFormatId))?.Name;
                submission.SubmissionType = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionTypeId))?.Name;
                submission.SubmissionUnit = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionUnitId))?.Name;
                submission.SubmissionMode = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionModeId))?.Name;
                submission.DisplayRegulatoryLead = sub.SubmissionResource.RegulatoryLead;
                submission.DisplayPublishingLead = sub.SubmissionResource.PublishingLead;
                submission.Documents = await cache.CreateMappedEntity<Document, DocumentModel>().WhereAsync(x => x.SubmissionId == sub.Id);
                submission.CountriesIds = sub.SubmissionCountry.Select(x => x.CountryId).ToList();
                application.Submissions.Add(submission);
            }

            return View("ViewApplication", application);
        }

        [HttpPost("/applications/export"), Authorize(Policy = "Reader"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Export(ApplicationFilterModel model)
        {
            if (!ModelState.IsValid)
                return Json("Export Error");
            return File(await this.appExport.Export(User, model), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"Applications_{DateTime.Now.ToString("yyyy-MM-dd")}.xlsx");
        }

        [HttpPost("/applications/state/{id}"), Authorize(Policy = "BusinessAdmin"), ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangeState(int id, [FromBody] string stateId)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);
            var applicationCache = cache.CreateTrackedEntity<Application>();
            var application = await applicationCache.FirstOrDefaultAsync(x => x.Id == id);
            application.LifecycleStateId = int.Parse(stateId);
            await applicationCache.SaveChangesAsync();
            return Ok(application.LifecycleStateId);
        }

        private async Task<(IList<PicklistDataModel>, IList<ProductModel>, IList<CountryModel>, IList<ActiveSubstanceModel>)> PrepareInitialData()
        {
            var allPicklists = await cache.CreateMappedEntity<PicklistData, PicklistDataModel>().AllAsync();
            var allProducts = await cache.CreateMappedEntity<Product, ProductModel>().WhereAsync(x => x.LifecycleStateId != (int)CommonLifecycleState.Obsolete);
            var allCountries = (await cache.CreateMappedEntity<Country, CountryModel>().AllAsync()).OrderBy(x => x.Name).ToList();
            var allActiveSubstances = await cache.CreateMappedEntity<ActiveSubstance, ActiveSubstanceModel>().AllAsync();
            foreach (var product in allProducts)
            {
                product.DosageForm = (await PicklistHelper.ExtractPicklist(cache, product.DosageFormId)).Name;
            }

            return (allPicklists, allProducts, allCountries, allActiveSubstances);
        }

        private async Task<IList<ClientModel>> LoadClients()
        {
            var clientsId = (await this.cache.CreateEntity<UserClient>().WhereAsync(x => x.UserId == this.CurrentUserId)).Select(x => x.ClientId).ToList();
            var allClients = await cache.CreateMappedEntity<Client, ClientModel>().WhereAsync(x => clientsId.Contains(x.Id));
            return allClients;
        }
    }
}
