﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class SubmissionFilterModelTests
    {
        [Fact]
        public void SubmissionFilterModel_Get_SetValue()
        {
            //Arrange
            var model = new SubmissionFilterModel();
            model.Description = "Test";
            model.DisplayPublishers = "Test";
            model.ApplicationNumber = "Test123";
            model.RegulatoryLead = "Test";
            model.DossierFormat = "Test";
            model.LifecycleState = "Test";
            model.SequenceNumber = "001";
            model.SubmissionMode = "Test";
            model.SubmissionType = "Test";
            model.SubmissionUnit = "Test";
            model.ClientName = "Test";
            model.ExportColumns = "Test";
            model.Columns = new List<SubmissionColumnsExportModel>();
            //Assert
            Assert.NotNull(model);
        }
    }
}
