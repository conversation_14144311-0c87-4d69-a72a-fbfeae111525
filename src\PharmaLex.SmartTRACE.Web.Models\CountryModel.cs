﻿using AutoMapper;
using PharmaLex.SmartTRACE.Entities;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class CountryModel: IModel
    {
        public int Id { get; set; }

        [Required, StringLength(128)]
        public string Name { get; set; }

        [StringLength(128)]
        public string TwoLetterCode { get; set; }

        public int RegionId { get; set; }

        public bool HasError { get; set; }

        public string ErrorMessage { get; set; }
    }

    public class CountryMappingProfile : Profile
    {
        public CountryMappingProfile()
        {
            this.CreateMap<Country, CountryModel>().ReverseMap();
        }
    }
}
