﻿using PharmaLex.DataAccess;

namespace PharmaLex.SmartTRACE.Entities.Audit
{
    public partial class SubmissionResourceAudit: AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }

        public int? Id { get; set; }
        public int? PriorityId { get; set; }
        public int? EstimatedSizeId { get; set; }
        public decimal? EstimatedHours { get; set; }
        public string RegulatoryLead { get; set; }
        public string PublishingLead { get; set; }
        public int? PublishingTeamId { get; set; }
        public string InitialSentToEmail { get; set; }
        public string Comments { get; set; }
        public int? SubmissionId { get; set; }
    }
}
