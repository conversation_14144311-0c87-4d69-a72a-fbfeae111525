﻿<script type="text/x-template" id="document-template">
    <div v-if="config.viewMode" class="document-version-row">
        <div class="upload-info" v-if="selectedDocument.showUploadInfo">
            <div v-if="selectedDocument.filesCount !== 0"><span>Number of files:</span> {{selectedDocument.filesCount}} </div>
            <div :class="{'hidden': this.config.userExternalEditor }"><span>Uploaded by:</span> {{selectedDocument.createdBy}} </div>
            <div><span>Uploaded date/time (UTC):</span> {{selectedDocument.createdDate}}</div>
        </div>
        <div class="version-info">
            <div v-if="!document.map(x => x.documentTypeId).includes(config.dossierDocumentTypeId)">Version:</div>
            <div class="select-wrapper version-view-child" v-if="!document.map(x => x.documentTypeId).includes(config.dossierDocumentTypeId)">
                <select v-model="selectedDocument" v-on:change="onVersionChange()">
                    <option v-for="(d, index) in document" :key="index" v-bind:value="d">v. {{ d.version }}</option>
                </select>
            </div>
            <div class="buttons-child">
                <a :href="'/submissions/' + config.submissionId + '/dossier/' + selectedDocument.name + '?clientId=' + config.clientId + '&applicationId=' + config.applicationId"
                   class="button icon-button-view" v-if="selectedDocument.documentTypeId === config.dossierDocumentTypeId && config.showViewButton" title="View">View</a>
                <button type="button" class="button icon-download" v-on:click="downloadDocument()" title='Download' v-show="showDownloadButton()">Download</button>
            </div>
        </div>
    </div>
    <div v-else="!config.viewMode" class="version-container">
        <div class="select-wrapper version-child" v-if="!document.map(x => x.documentTypeId).includes(config.dossierDocumentTypeId)">
            <select v-model="selectedDocument">
                <option v-for="(d, index) in document" :key="index" v-bind:value="d">v. {{ d.version }}</option>
            </select>
        </div>
        <div class="buttons-child">
            <button type="button" name="removeDossier" class="button icon-button-delete" title='Remove'
                    v-show="showRemoveButton()"
                    v-on:click="removeDocument()">
                Remove
            </button>
            <a :href="'/submissions/' + config.submissionId + '/dossier/' + selectedDocument.name + '?clientId=' + config.clientId + '&applicationId=' + config.applicationId"
               class="button icon-button-view" v-if="selectedDocument.documentTypeId === config.dossierDocumentTypeId && config.showViewButton" title="View">View</a>
            <button type="button" class="button icon-download" v-on:click="downloadDocument()" title='Download' v-show="showDownloadButton()">Download</button>
        </div>
    </div>
</script>

<script type="text/javascript">
    vueApp.component('document', {
        template: '#document-template',
        data() {
            return {
                selectedDocument: {},
                showUploadInfo: true,
                options: { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric', hour12: false }
            };
        },
        props: {
            config: {
                type: Object,
                default: {}
            },
            document: {
                type: Array,
                default: []
            }
        },
        watch: {
            document() {
                this.selectedDocument = this.document[this.document.length - 1];
                if (this.selectedDocument.createdBy === 'update script') {
                    this.selectedDocument.showUploadInfo = false;
                }
            }
        },
        methods: {
            showRemoveButton() {
                switch (this.selectedDocument.documentTypeId) {
                    case 1:
                        return this.config.userAdmin || this.config.userExternalEditor || (this.config.userEditor && !this.config.isArchiveStage);
                        break;
                    case 2:
                    case 3:
                        return this.config.userAdmin || (this.config.userEditor && !this.config.isArchiveStage);
                        break;
                    case 4:
                        return this.config.userAdmin || this.config.userEditor;
                        break;
                    default:
                        return false;
                        break;
                }
            },
            showDownloadButton() {
                if(this.config.userEditor || this.config.isPrivilegedExternalEditorUser)
                {
                    return true;
                }
                else if (this.selectedDocument.documentTypeId === this.config.dossierDocumentTypeId && this.config.userExternalEditor && !this.config.isPrivilegedExternalEditorUser) {
                    return false;
                }

                return true;
            },
            removeDocument() {
                this.$emit('selected-document', this.selectedDocument);
            },
            downloadDocument() {
                var url = `/submissions/${this.config.submissionUniqueId}/download/${this.selectedDocument.name}?clientId=${this.config.clientId}&applicationId=${this.config.applicationId}&documentTypeId=${this.selectedDocument.documentTypeId}&version=${this.selectedDocument.version}`
                if (this.selectedDocument.documentTypeId === this.config.dossierDocumentTypeId) {
                    url = `/submissions/${this.config.submissionUniqueId}/download/${this.selectedDocument.name}?clientId=${this.config.clientId}&applicationId=${this.config.applicationId}`
                }
                fetch(url, this.getFetchOptions('GET'))
                    .then(result => result.blob())
                    .then(result => {
                        var url = window.URL.createObjectURL(result);
                        const a = document.createElement('a');
                        a.style.display = 'none';
                        a.href = url;
                        a.download = this.selectedDocument.name;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                    })
                    .catch(_ => {
                        plx.toast.show(`Could not download dossier`, 2, 'failed', null, 2000);
                    });
            },
            onVersionChange() {
                if (this.selectedDocument.createdBy === 'update script') {
                    this.selectedDocument.createdBy = 'Admin';
                }
                this.selectedDocument.createdDate = new Date(this.selectedDocument.createdDate).toLocaleString('default', this.options);
            },
            getFetchOptions: function (method, body) {
                return {
                    method: method,
                    credentials: 'same-origin',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: body
                };
            }
        },
        created() {
            this.selectedDocument = this.document[this.document.length - 1];
            this.selectedDocument.createdDate = new Date(this.selectedDocument.createdDate).toLocaleString('default', this.options);
        }
    });
</script>