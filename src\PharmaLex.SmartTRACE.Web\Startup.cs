﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.UI;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.Helpers;
using PharmaLex.SmartTRACE.Data.Persistance.Repository;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Authentication;
using PharmaLex.SmartTRACE.Web.CTD;
using PharmaLex.SmartTRACE.Web.DataFactory;
using PharmaLex.SmartTRACE.Web.HealthCheck;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Helpers.Services;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Storage;
using System;

namespace PharmaLex.SmartTRACE.Web
{
    public class Startup
    {
        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        public Startup(IConfiguration configuration, IWebHostEnvironment environment)
        {
            Configuration = configuration;
            this.Environment = environment;
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddScoped<IUserContext, PlxUserContext>();

            services.RegisterDbContext<SmartTRACEContext>();

            services.AddScoped<IDistributedCacheServiceFactory, DistributedCacheServiceFactory>();
            services.AddSingleton<IDistributedCacheService, DistributedCacheService>();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddSingleton<ITicketReceivedCallback, AddClaimsCallback>();

            services.AddScoped<ISubmissionBlobStorage, SubmissionBlobStorage>();
            services.AddScoped<ISubmissionPubBlobStorage, SubmissionPubBlobStorage>();
            services.AddScoped<ISubmissionBlobContainer, SubmissionBlobContainer>();
            services.AddScoped<ISubmissionPubBlobContainer, SubmissionPubBlobContainer>();
            services.Configure<StorageSettings>(Configuration.GetSection("AzureStorage"));

            services.AddScoped<IAzureDataFactoryManagementClient, AzureDataFactoryManagementClient>();
            services.AddScoped<IDataFactoryAuthenticationPovider, DataFactoryAuthenticationPovider>();
            services.Configure<DataFactoryPipelineSettings>(Configuration.GetSection("DataFactoryPipeline"));

            services.AddScoped<IEctdViewer, AzureBlobStorageEctdViewer>();
            services.AddScoped<IEctdProvider, EctdProvider>();
            services.AddScoped<IEctdStudyReportFileReader, EctdStudyReportFileReader>();
            services.AddScoped<IEctdStudyReportProcessor, EctdStudyReportProcessor>();

            services.AddScoped<INotificationService, NotificationService>();
            services.AddScoped<IApplicationService, ApplicationService>();
            services.AddScoped<IAppSubmissionsRepository, AppSubmissionsRepository>();
            services.AddScoped<IApplicationRepository, ApplicationRepository>();
            services.AddScoped<ISubmissionService, SubmissionService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IApplicationExport, ApplicationExport>();
            services.AddScoped<ISubmissionExport, SubmissionExport>();
            services.AddScoped<IClientExport, ClientExport>();
            services.AddScoped<IProductExport, ProductExport>();
            services.AddScoped<IProjectExport, ProjectExport>();
            services.AddScoped<IUserExport, UserExport>();
            services.AddScoped<IPicklistDataService, PicklistDataService>();
            services.AddScoped<ICountryService, CountryService>();

            services.AddAuthorization(options =>
            {
                options.FallbackPolicy = new AuthorizationPolicyBuilder()
                  .RequireAuthenticatedUser()
                  .Build();
            }).AddPolicies();

            services
               .AddOptions()
               .Configure<AzureAdGraphOptions>(this.Configuration.GetSection("AzureAdGraph"))
               .Configure<AzureAdB2CGraphOptions>(this.Configuration.GetSection("AzureAdB2CGraph"))
               .Configure<CookiePolicyOptions>(options =>
               {
                   options.CheckConsentNeeded = context => true;
                   options.MinimumSameSitePolicy = SameSiteMode.Unspecified;
                   options.HandleSameSiteCookieCompatibility();
                   options.HttpOnly = Microsoft.AspNetCore.CookiePolicy.HttpOnlyPolicy.Always;
                   options.Secure = CookieSecurePolicy.Always;
               })
               .ConfigureMicrosoftItentityWebAuthentication(Configuration)
               .Configure<CookieAuthenticationOptions>(IdentityConstants.ApplicationScheme, options =>
               {
                   options.Cookie.HttpOnly = true;
                   options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
               })
               .ConfigureCustomAuthentication(Configuration)
               .ConfigureHealthChecks(Configuration);

            services
                .AddSession(options =>
                {
                    options.Cookie.HttpOnly = true;
                    options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
                    options.Cookie.IsEssential = true;
                })
                .AddControllersWithViews(options =>
                {
                    options.Filters.Add(new AutoValidateAntiforgeryTokenAttribute());
                })
                .AddMicrosoftIdentityUI()
                .AddAzureAdB2CAuthenticationUI()
                .AddRazorRuntimeCompilation();

            services.AddAntiforgery(options =>
            {
                options.Cookie.HttpOnly = true;
                options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
            });

            services.AddHsts(options =>
            {
                options.Preload = true;
                options.IncludeSubDomains = true;
                options.MaxAge = TimeSpan.FromDays(365);
            });

            if (this.Environment.IsDevelopment())
            {
                services.ConfigureOptions<ContentConfigureOptions>();
            }

            services.AddAutoMapper(typeof(UserModel).Assembly);
            services.AddTransient<IGraphClientProvider<AzureAdB2CGraphOptions>, AzureAdB2CGraphClientProvider>();
            services.AddTransient<IGraphClientProvider<AzureAdGraphOptions>, AzureAdGraphClientProvider>();
            services.AddScoped<IAzureAdB2CGraphService, AzureAdB2CGraphService>();
            services.AddScoped<IAzureAdGraphService, AzureAdGraphService>();
            services.AddSingleton<AppSettingsHelper>();
            services.AddSingleton<VersionCdnHelper>();

            JsonConvert.DefaultSettings = () => new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment environment)
        {
            if (environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");
                app.UseHttpsRedirection();
                app.UseHsts();
            }

            app.UseCookiePolicy(
                new CookiePolicyOptions
                {
                    Secure = CookieSecurePolicy.Always,
                    HttpOnly = Microsoft.AspNetCore.CookiePolicy.HttpOnlyPolicy.Always
                }
            );

            app.Use(async (context, next) =>
            {
                if (!context.Response.Headers.ContainsKey("Content-Security-Policy"))
                {
                    var cdn = Configuration.GetValue<string>("Static:Cdn");

                    context.Response.Headers.Append("Content-Security-Policy", $"default-src 'self'; "
                        + $"connect-src 'self' " + (environment.IsDevelopment() ? "wss: ws: http:" : "https:") + "; "
                        + $"script-src 'self' 'unsafe-inline' 'unsafe-eval' {cdn}; "
                        + $"style-src 'self' 'unsafe-inline' {cdn}; "
                        + $"img-src 'self' data: {cdn}; "
                        + $"font-src 'self' {cdn}; "
                        + $"manifest-src 'self' {cdn}; "
                        + "frame-ancestors 'self'; "
                        + "object-src 'none';");
                }

                if (!context.Response.Headers.ContainsKey("X-Frame-Options"))
                {
                    context.Response.Headers.Append("X-Frame-Options", "SAMEORIGIN");
                }

                await next();
            });

            app.UseStaticFiles();

            app.UseStaticFiles(new StaticFileOptions()
            {
                ServeUnknownFileTypes = true
            });

            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseHealthChecks("/health-status");

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapCustomHealthChecks().AllowAnonymous();
                endpoints.MapDefaultControllerRoute();
                endpoints.MapRazorPages();
            });
        }
    }
}
