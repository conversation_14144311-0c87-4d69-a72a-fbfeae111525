﻿@model ListPicklistDataViewModel
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@using PharmaLex.SmartTRACE.Entities.Enums
@{
    var picklistType = ((PicklistType)Model.PicklistTypeId).GetDescription();
}

<div id="picklistData" class="manage-container">
    <header class="manage-header">
        <h2>@picklistType</h2>
        <a class="button icon-button-add" href="/data/new/@Model.PicklistTypeId">Add @picklistType</a>
    </header>
    <filtered-table :items="picklistData" :columns="columns" :filters="filters" :link="link" :no-records-message="'@($"No \\'{picklistType}\\' records found")'"></filtered-table>
</div>

@section Scripts {
    <script type="text/javascript">

    var pageConfig = {
        appElement: '#picklistData',
        data() {
            return {
                link: '/data/edit/',
                picklistData: @Html.Raw(JsonConvert.SerializeObject(Model.PicklistData, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })),
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            type: 'text'
                        },
                        {
                            dataKey: 'country',
                            sortKey: 'country',
                            header: 'Country',
                            type: 'text'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'name',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    }
                ]
            };
        }
    };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}