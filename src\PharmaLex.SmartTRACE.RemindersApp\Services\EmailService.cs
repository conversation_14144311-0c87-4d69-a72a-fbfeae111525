﻿using Microsoft.Extensions.Configuration;
using PharmaLex.SmartTRACE.RemindersApp.Models;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Net;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.RemindersApp.Services
{
    public class EmailService : IEmailService
    {
        private readonly string _sendGridApiKey;
        private readonly string _senderEmail;
        private readonly bool _isemailsSendingEnabled;

        private readonly IConfiguration _configuration;

        public EmailService(IConfiguration configuration)
        {
            _configuration = configuration;
            _isemailsSendingEnabled = _configuration.GetValue<bool>("emailsSendingEnabled");
            _sendGridApiKey = _configuration.GetValue<string>("SendGrid");
            _senderEmail = _configuration.GetValue<string>("SystemAdminEmail");
        }

        public async Task<(bool, HttpStatusCode)> SendAsync(NotificationModel model, string templateId)
        {
            if (_isemailsSendingEnabled)
            {
                var client = new SendGridClient(new SendGridClientOptions { ApiKey = _sendGridApiKey, HttpErrorAsException = true });

                var from = new EmailAddress(_senderEmail);

                var to = new EmailAddress(model.To);

                SendGridMessage msg = new SendGridMessage();
                msg.SetTemplateId(templateId);
                msg.SetTemplateData(model);
                msg.SetFrom(from);
                msg.AddTo(to);

                Response response = await client.SendEmailAsync(msg).ConfigureAwait(false);
                return (response.IsSuccessStatusCode, response.StatusCode);
            }
            return (true, HttpStatusCode.OK);
        }
    }
}
