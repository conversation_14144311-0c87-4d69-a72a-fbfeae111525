﻿@model EditSubmissionViewModel
@using PharmaLex.Caching.Data
@using PharmaLex.SmartTRACE.Entities
@using PharmaLex.SmartTRACE.Entities.Enums
@using Microsoft.AspNetCore.Authorization

@inject IAuthorizationService AuthorizationService
@inject IDistributedCacheServiceFactory cache
@inject AutoMapper.IMapper mapper
@{
    var submissionTypeList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.SubmissionType).OrderBy(x => x.Name);
    var submissionUnitList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.SubmissionUnit).OrderBy(x => x.Name);
    var submissionModeList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.SubmissionMode).OrderBy(x => x.Name);
    var dossierFormatList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.DossierFormat).OrderBy(x => x.Name);
    var euRegion = await cache.CreateMappedEntity<Region, RegionModel>().FirstOrDefaultAsync(x => x.Name == "European Union");
    var euCountriesIds = (await cache.CreateMappedEntity<Country, CountryModel>().WhereAsync(x => x.RegionId == euRegion.Id)).Select(x => x.Id);
    var usCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "United States of America");
    var ukCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "United Kingdom");
    var swissCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "Switzerland");
    var auCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "Australia");
    var isAdminUser = (await AuthorizationService.AuthorizeAsync(User, "Admin")).Succeeded;
    var isEditorUser = (await AuthorizationService.AuthorizeAsync(User, "Editor")).Succeeded;
    var isExternalEditorUser = (await AuthorizationService.AuthorizeAsync(User, "ExternalEditor")).Succeeded;
    var isPrivilegedExternalEditorUser = (await AuthorizationService.AuthorizeAsync(User, "PrivilegedExternalEditor")).Succeeded;
    var documentType = mapper.Map<IEnumerable<DocumentTypeList>>(Enum.GetValues(typeof(DocumentType)));
}

<div id="viewSubmission" class="manage-container" v-cloak>
    @Html.AntiForgeryToken()
    <workflow-progress :id="id" :current-step="currentStage" :final-submission-state="finalSubmissionStateId" :last-active-step="lastActiveStep" :show-step="showStep"></workflow-progress>
    <header class="manage-header">
        <h3>Submission Details</h3>
        @if (Model.Submission.LifecycleStateId != (int)SubmissionLifeCycleState.WithdrawnFromHA &&
        Model.Application.LifecycleStateId == (int)CommonLifecycleState.Active &&
        (((await AuthorizationService.AuthorizeAsync(User, "ExternalEditor")).Succeeded &&
        Model.Submission.LifecycleStateId != (int)SubmissionLifeCycleState.Submitted &&
        Model.Submission.LifecycleStateId != (int)SubmissionLifeCycleState.Archived) ||
        ((await AuthorizationService.AuthorizeAsync(User, "Editor")).Succeeded &&
        Model.Submission.LifecycleStateId != (int)SubmissionLifeCycleState.Obsolete)) ||
        (await AuthorizationService.AuthorizeAsync(User, "BusinessAdmin")).Succeeded &&
        Model.Application.LifecycleStateId == (int)CommonLifecycleState.Withdrawn)
        {
            <a href="/submissions/edit/@Model.Submission.Id" class="button icon-button-edit">Edit</a>
        }
        <a href="/app-submissions" class="button secondary icon-button-list">View All Submissions</a>
        <a href="/applications/view/@Model.Submission.ApplicationId" class="button secondary icon-button-back">Back to Application</a>
        @if ((SubmissionLifeCycleState)Model.Submission.LifecycleStateId == SubmissionLifeCycleState.Archived &&
        (await AuthorizationService.AuthorizeAsync(User, "BusinessAdmin")).Succeeded)
        {
            <button id="nextState" class="button icon-button-next">Move to @(((SubmissionLifeCycleState)(Model.Submission.LifecycleStateId + 1)).GetDescription())</button>
        }
        @if ((await AuthorizationService.AuthorizeAsync(User, "BusinessAdmin")).Succeeded &&
        (SubmissionLifeCycleState)Model.Submission.LifecycleStateId != SubmissionLifeCycleState.Obsolete)
        {
            <button name="proceedToObsolete" class="button secondary icon-button-next" v-on:click.once="moveToObsolete()">Move to @(SubmissionLifeCycleState.Obsolete.GetDescription())</button>
        }
    </header>
    <div class="submission-additional-content">
        <h4>{{model.product}} ({{model.client.name}})</h4>
        <h4>
            {{model.application.applicationType}}<span v-if="model.application.applicationType != null">,</span>
            {{model.application.applicationNumber}}<span v-if="model.application.procedureType != null">,</span>
            {{model.application.procedureType}}
        </h4>
    </div>
    <div class="tab-container">
        <div v-for="tab in tabs" class="tab-link" v-on:click="tabClick($event, tab.id)">{{tab.name}}</div>
    </div>
    <div id="general-info" class="tabcontent form-col">
        <div class="submission-view">
            @if (Model.Submission.LifecycleStateId > (int)SubmissionLifeCycleState.Draft)
            {
                <span>Unique Id:</span>
                <span>{{model.submission.uniqueId}}</span>
            }
            <span>Application Number:</span>
            <span>{{model.application.applicationNumber}}</span>
            <span>Countries:</span>
            <span>{{model.submission.displayCountries}}</span>
            <span>Submission Delivery Details:</span>
            <span v-if="model.submission.submissionDeliveryDetails">{{model.submission.submissionDeliveryDetails}}</span>
            <span v-if="!model.submission.submissionDeliveryDetails">-</span>
            <span v-if="submissionTypes.length !== 0">Submission Type:</span>
            <span v-if="model.submission.submissionType">{{model.submission.submissionType}}</span>
            <span v-if="!model.submission.submissionType && submissionTypes.length !== 0">-</span>
            <span v-if="submissionUnits.length !== 0">Submission Unit:</span>
            <span v-if="model.submission.submissionUnit">{{model.submission.submissionUnit}}</span>
            <span v-if="!model.submission.submissionUnit && submissionUnits.length !== 0">-</span>
            <span v-if="submissionModes.length !== 0">Submission Mode:</span>
            <span v-if="model.submission.submissionMode">{{model.submission.submissionMode}}</span>
            <span v-if="!model.submission.submissionMode && submissionModes.length !== 0">-</span>
            <span>Dossier Format:</span>
            <span v-if="model.submission.dossierFormat">{{model.submission.dossierFormat}}</span>
            <span v-if="!model.submission.dossierFormat">-</span>
            <span>Sequence Number:</span>
            <span v-if="model.submission.sequenceNumber">{{model.submission.sequenceNumber}}</span>
            <span v-if="!model.submission.sequenceNumber">-</span>
            <span>Related Sequence Number:</span>
            <span v-if="model.submission.relatedSequenceNumber">{{model.submission.relatedSequenceNumber}}</span>
            <span v-if="!model.submission.relatedSequenceNumber">-</span>
            <span v-if="showSerialNumberField()">Serial Number:</span>
            <span v-if="model.submission.serialNumber && showSerialNumberField()">{{model.submission.serialNumber}}</span>
            <span v-if="!model.submission.serialNumber && showSerialNumberField()">-</span>
            <span>Description:</span>
            <span v-if="model.submission.description">{{model.submission.description}}</span>
            <span v-if="!model.submission.description">-</span>
            <span>Comments:</span>
            <span v-if="model.submission.comments">{{model.submission.comments}}</span>
            <span v-if="!model.submission.comments">-</span>
        </div>
    </div>

    <div id="client-details" class="tabcontent form-col">
        <div class="submission-view">
            <span>Client Name:</span>
            <span>{{model.client.name}}</span>
            <span>Project Name:</span>
            <span>{{model.project.name}}</span>
            <span>Project Code:</span>
            <span>{{model.project.code}}</span>
            <span>Opportunity Number:</span>
            <span>{{model.project.opportunityNumber}}</span>
            <span>Contract Owner:</span>
            <span>{{model.client.contractOwner}}</span>
        </div>
    </div>

    <div id="dates" class="tabcontent form-col">
        <div class="submission-view">
            @if (Model.Submission.LifecycleStateId > (int)SubmissionLifeCycleState.Draft)
            {
                @if (!isExternalEditorUser)
                {
                    <span>Created By:</span>
                    <span>{{model.submission.createdBy}}</span>
                }
                <span>Created Date:</span>
                <span>{{formatDateString(model.submission.createdDate)}}</span>
            }
            <span>Health Authority Due Date:</span>
            <span v-if="model.submission.healthAuthorityDueDate">{{formatDateString(model.submission.healthAuthorityDueDate)}}</span>
            <span v-if="!model.submission.healthAuthorityDueDate">-</span>
            <span>Deadline for Last Document:</span>
            <span v-if="model.submission.authoringDeadline">{{formatDateString(model.submission.authoringDeadline)}}</span>
            <span v-if="!model.submission.authoringDeadline">-</span>
            <span>Planned Dispatch:</span>
            <span>{{formatDateString(model.submission.plannedDispatchDate)}}</span>
            @if (Model.Submission.LifecycleStateId > (int)SubmissionLifeCycleState.QCReview)
            {
                <span>Actual Dispatch:</span>
                <span v-if="model.submission.actualDispatchDate">{{formatDateString(model.submission.actualDispatchDate)}}</span>
                <span v-if="!model.submission.actualDispatchDate">-</span>
            }
            <span>Planned Submission:</span>
            <span>{{formatDateString(model.submission.plannedSubmissionDate)}}</span>

            @if (Model.Submission.LifecycleStateId > (int)SubmissionLifeCycleState.QCReview)
            {
                <span>Actual Submission:</span>
                <span v-if="model.submission.actualSubmissionDate">{{formatDateString(model.submission.actualSubmissionDate)}}</span>
                <span v-if="!model.submission.actualSubmissionDate">-</span>
                <span>docuBridge Version Id:</span>
                <span v-if="model.submission.docubridgeVersionId">{{model.submission.docubridgeVersionId}}</span>
                <span v-if="!model.submission.docubridgeVersionId">-</span>
                <span>Portal/Gateway Reference Number:</span>
                <span v-if="model.submission.cespNumber">{{model.submission.cespNumber}}</span>
                <span v-if="!model.submission.cespNumber">-</span>
            }
            @if (Model.Submission.LifecycleStateId == (int)SubmissionLifeCycleState.WithdrawnFromHA)
            {
                <span>Withdrawn:</span>
                <span>{{formatDateString(model.submission.withdrawalDate)}}</span>
            }
        </div>
    </div>

    <div id="resources" class="tabcontent form-col">
        <div class="submission-view">
            <span>Priority:</span>
            <span v-if="model.submissionResource.priority">{{model.submissionResource.priority}}</span>
            <span v-if="!model.submissionResource.priority">-</span>
            <span>Estimated Size:</span>
            <span v-if="model.submissionResource.estimatedSize">{{model.submissionResource.estimatedSize}}</span>
            <span v-if="!model.submissionResource.estimatedSize">-</span>
            <span>Estimated Hours:</span>
            <span v-if="model.submissionResource.estimatedHours">{{model.submissionResource.estimatedHours}}</span>
            <span v-if="!model.submissionResource.estimatedHours">-</span>
            <span>Publishing Lead:</span>
            <span v-if="model.submissionResource.publishingLead">{{model.submissionResource.publishingLead}}</span>
            <span v-if="!model.submissionResource.publishingLead">-</span>
            <span>Regulatory Lead:</span>
            <span>{{model.submissionResource.regulatoryLead}}</span>
            <span>Publishing Team:</span>
            <span v-if="model.submissionResource.publishingTeam">{{model.submissionResource.publishingTeam}}</span>
            <span v-if="!model.submissionResource.publishingTeam">-</span>
            <span>Publishers:</span>
            <span v-if="model.submissionResource.displayPublishers">{{model.submissionResource.displayPublishers}}</span>
            <span v-if="!model.submissionResource.displayPublishers">-</span>
            <span>Submission Request Notification Delivered To:</span>
            <span class="request-email" v-if="model.submissionResource.initialSentToEmail">{{model.submissionResource.initialSentToEmail}}</span>
            <span class="request-email" v-if="!model.submissionResource.initialSentToEmail">-</span>
            <span>Resources Comments:</span>
            <span v-if="model.submissionResource.comments">{{model.submissionResource.comments}}</span>
            <span v-if="!model.submissionResource.comments">-</span>
        </div>
    </div>

    <div id="dossier-details" class="tabcontent form-col">
        <div class="submission-view">
            @if (Model.Submission.SourceDocumentsLocation != null)
            {
                <span>Location of Source Documents:</span>
                <span v-if="model.submission.sourceDocumentsLocation">{{model.submission.sourceDocumentsLocation}}</span>
                <span v-if="!model.submission.sourceDocumentsLocation">-</span>
            }
            @if (Model.Submission.LifecycleStateId > (int)SubmissionLifeCycleState.ApprovedForSubmission)
            {
                @if (Model.Submission.ArchivedDocumentsLocation != null)
                {
                    <span>Location of Archived Dossier:</span>
                    <span v-if="model.submission.archivedDocumentsLocation">{{model.submission.archivedDocumentsLocation}}</span>
                    <span v-if="!model.submission.archivedDocumentsLocation">-</span>
                }
            }
        </div>
        @if (Model.Submission.Documents.Any())
        {
            <div class="document-view-list">
                <div class="document-list" v-for="(document, index) in groupBy(submission.documents, 'documentTypeId')" :key="index">
                    <i :class="[{ 'icon-right-dir': false, 'icon-down-dir': true}, 'document-list-title']">
                        <span style="font-weight:bold;">
                            {{ documentType(index) }}
                        </span>
                    </i>

                    <div v-for="(doc, name) in  groupBy(document, 'name')" :key="name" class="document-list document-view-name">
                        <i :class="['expand-document', { 'icon-right-dir': hide(name, index), 'icon-down-dir': !hide(name, index) }]" v-on:click="expand(name, index)">
                            {{ name }}
                        </i>
                        <document :config="documentConfig" v-bind:document="doc" v-on:selected-document="onSelectedDocument($event)" v-on:download-document="onDownloadDocument($event)" :class="[{hidden: hide(name, index)}]"></document>
                    </div>
                </div>
            </div>
        }
    </div>

    <withdrawn-state :config="config"></withdrawn-state>
    <yes-no-dialog :config="obsolеteStateConfig" v-on:button-pressed="onProceedToObsolete"></yes-no-dialog>
</div>

@section Scripts {
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        var pageConfig = {
            appElement: '#viewSubmission',
            data() {
                return {
                    config: {
                        id: @Model.Submission.Id,
                    },
                    documentConfig: {
                        viewMode: true
                    },
                    obsolеteStateConfig: {
                        id: @Model.Submission.Id,
                        message: 'Do you want to move to Obsolete?',
                        noButton: '',
                        yesButton: 'Move to Obsolete',
                        elementName: 'proceedToObsolete',
                        buttonClass: 'icon-button-next',
                        fontSize: 'text-medium-helper'
                    },
                    minDate: new Date().toISOString().substring(0, 10),
                    id: @Model.Submission.Id,
                    currentStage: @Model.Submission.LifecycleStateId,
                    lastActiveStep: @Model.Submission.PreviousLifecycleStateId,
                    showStep: @Html.Raw(isAdminUser.ToString().ToLower()),
                    invalidFields: @Html.Raw(Json.Serialize(Model.InvalidFields)),
                    model: @Html.Raw(Json.Serialize(Model)),
                    tabs: [
                        { id: 'general-info', name: 'General Info' },
                        { id: 'client-details', name: 'Client Details' },
                        { id: 'dates', name: 'Dates' },
                        { id: 'resources', name: 'Resources' },
                        { id: 'dossier-details', name: 'Submission Document/Dossier Details' }],
                    submissionTypes: @Html.Raw(Json.Serialize(submissionTypeList)),
                    submissionUnits: @Html.Raw(Json.Serialize(submissionUnitList)),
                    submissionModes: @Html.Raw(Json.Serialize(submissionModeList)),
                    dossierFormats: @Html.Raw(Json.Serialize(dossierFormatList)),
                    submission: @Html.Raw(Json.Serialize(Model.Submission)),
                    application: @Html.Raw(Json.Serialize(Model.Application)),
                    client: @Html.Raw(Json.Serialize(Model.Client)),
                    euRegion: @Html.Raw(Json.Serialize(euRegion)),
                    euCountriesIds: @Html.Raw(Json.Serialize(euCountriesIds)),
                    usCountry: @Html.Raw(Json.Serialize(usCountry)),
                    nonEuUkCountry: @Html.Raw(Json.Serialize(ukCountry)),
                    swissCountry: @Html.Raw(Json.Serialize(swissCountry)),
                    auCountry: @Html.Raw(Json.Serialize(auCountry)),
                    dossierDocumentTypeId: @Html.Raw((int)DocumentType.Dossier),
                    documentTypes: @Html.Raw(Json.Serialize(documentType)),
                    expandedType: 0,
                    expandedDocumentName: '',
                    userEditor: @Html.Raw(Json.Serialize(isEditorUser)),
                    userAdmin: @Html.Raw(Json.Serialize(isAdminUser)),
                    userExternalEditor: @Html.Raw(Json.Serialize(isExternalEditorUser)),
                    isPrivilegedExternalEditorUser: @Html.Raw(Json.Serialize(isPrivilegedExternalEditorUser)),
                    withdrawnAppTypeId: @Html.Raw((int)CommonLifecycleState.Withdrawn),
                    lifeCycleState: '@Html.Raw(((SubmissionLifeCycleState)Model.Submission.LifecycleStateId).GetDescription())',
                }
            },
            computed: {
                finalSubmissionStateId() {
                    return this.showStep ? @Html.Raw((int)SubmissionLifeCycleState.Obsolete) : @Html.Raw((int)SubmissionLifeCycleState.WithdrawnFromHA);
                },
            },
            methods: {
                tabClick(event, link) {
                    let i, tabcontent, tablinks;
                    tabcontent = document.getElementsByClassName("tabcontent");
                    for (i = 0; i < tabcontent.length; i++) {
                        tabcontent[i].style.display = "none";
                    }
                    tablinks = document.getElementsByClassName("tab-link");
                    for (i = 0; i < tablinks.length; i++) {
                        tablinks[i].className = tablinks[i].className.replace(" active", "");
                    }
                    document.getElementById(link).style.display = "block";
                    event.currentTarget.className += " active";
                },
                formatDateString(d) {
                    if (d) {
                        return new Intl.DateTimeFormat('default', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                        }).format(new Date(d));
                    }
                    // return null to show empty date, not default
                    return null;
                },
                showSerialNumberField() {
                    return this.submission.displayCountries.includes('United States of America');
                },
                documentType(index) {
                    return this.documentTypes.find(x => x.id === +index).name;
                },
                expand(name, index) {
                    if (this.expandedDocumentName == name && this.expandedType == index) {
                        this.expandedDocumentName = "";
                        this.expandedType = 0;
                    }
                    else {
                        this.expandedDocumentName = name;
                        this.expandedType = index;
                    }
                },
                hide(name, index) {
                    return !(this.expandedDocumentName == name && this.expandedType == index);
                },
                showViewDossierButton() {
                    return (this.submission.countriesIds.some(x => this.euCountriesIds.includes(x)) ||
                        this.submission.countriesIds.includes(this.usCountry.id) ||
                        this.submission.countriesIds.includes(this.nonEuUkCountry.id) ||
                        this.submission.countriesIds.includes(this.swissCountry.id) ||
                        this.submission.countriesIds.includes(this.auCountry.id)) &&
                        this.submission.dossierFormatId === this.dossierFormats.find(x => x.name === 'eCTD')?.id;
                },
                isApplicationWithdrawn() {
                    return this.submission.application.lifecycleStateId === this.withdrawnAppTypeId;
                },
                moveToObsolete() {
                    this.obsolеteStateConfig.noButton = 'Stay on ' + this.lifeCycleState;
                },
                onProceedToObsolete(yesButtonPressed) {
                    if (yesButtonPressed) {
                        fetch(`/submissions/obsolete-state/${this.submission.id}`, {
                            method: 'POST',
                            credentials: 'same-origin',
                            headers: {
                                'Content-Type': 'application/json', 
                                'RequestVerificationToken': token
                            }
                        }).then(result => {
                            if (result.ok) {
                                document.location.href = '/submissions/view/' + this.submission.id;
                            }
                        });
                    }
                },
                getFetchOptions(method, body) {
                    return {
                        method: method,
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': token
                        },
                        body: body
                    };
                },
                groupBy(xs, key) {
                    return xs.reduce(function (rv, x) {
                        (rv[x[key]] = rv[x[key]] || []).push(x);
                        return rv;
                    }, {});
                }
            },
            mounted() {
                let currentTab = document.getElementsByClassName('tab-link');
                currentTab[0].className += " active";
                document.getElementById(this.tabs[0].id).style.display = "block";

                if (this.userExternalEditor) {
                    this.tabs = this.tabs.filter(t => t.id != 'client-details');
                    this.tabs = this.tabs.filter(t => t.id != 'resources');
                }
            },
            created() {
                this.documentConfig.clientId = this.model.client.id;
                this.documentConfig.applicationId = this.model.application.id;
                this.documentConfig.submissionUniqueId = this.model.submission.uniqueId;
                this.documentConfig.submissionId = this.model.submission.id;
                this.documentConfig.dossierDocumentTypeId = this.dossierDocumentTypeId;
                this.documentConfig.showViewButton = this.showViewDossierButton();
                this.documentConfig.userAdmin = this.userAdmin;
                this.documentConfig.userEditor = this.userEditor;
                this.documentConfig.userExternalEditor = this.userExternalEditor;
                this.documentConfig.isPrivilegedExternalEditorUser = this.isPrivilegedExternalEditorUser;
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/WorkflowProgress" />
    <partial name="Components/WithdrawnStateDialog" />
    <partial name="Components/Document" />
    <partial name="Components/YesNoDialog" />
}
