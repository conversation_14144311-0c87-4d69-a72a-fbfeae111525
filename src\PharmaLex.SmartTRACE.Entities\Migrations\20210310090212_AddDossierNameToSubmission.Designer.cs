﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using PharmaLex.SmartTRACE.Entities;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    [DbContext(typeof(SmartTRACEContext))]
    [Migration("20210310090212_AddDossierNameToSubmission")]
    partial class AddDossierNameToSubmission
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .UseIdentityColumns()
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("ProductVersion", "5.0.0-rc.1.20451.13");

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.ActiveSubstance", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("Name", "ClientId")
                        .IsUnique()
                        .HasDatabaseName("UC_ActiveSubstance_Name_ClientId")
                        .HasFilter("[Name] IS NOT NULL");

                    b.ToTable("ActiveSubstance");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.ActiveSubstanceProduct", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("ActiveSubstanceId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ActiveSubstanceId");

                    b.HasIndex("ProductId", "ActiveSubstanceId")
                        .IsUnique()
                        .HasDatabaseName("UC_ActiveSubstanceProduct_ProductId_ActiveSubstanceId");

                    b.ToTable("ActiveSubstanceProduct");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Application", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("ApplicationNumber")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<int>("ApplicationTypeId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("MedicinalProductDomainId")
                        .HasColumnType("int");

                    b.Property<int?>("MedicinalProductTypeId")
                        .HasColumnType("int");

                    b.Property<int?>("ProcedureTypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Application");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.ApplicationCountry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("ApplicationId")
                        .HasColumnType("int");

                    b.Property<int?>("AuthorityRoleId")
                        .HasColumnType("int");

                    b.Property<int>("CountryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("ApplicationId", "CountryId")
                        .IsUnique()
                        .HasDatabaseName("UC_ApplicationCountry_ApplicationId_CountryId");

                    b.ToTable("ApplicationCountry");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.ApplicationProduct", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("ApplicationId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("ApplicationId", "ProductId")
                        .IsUnique()
                        .HasDatabaseName("UC_ApplicationProduct_ApplicationId_ProductId");

                    b.ToTable("ApplicationProduct");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.ActiveSubstanceAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("AuditId");

                    b.ToTable("ActiveSubstance_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.ActiveSubstanceProductAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int?>("ActiveSubstanceId")
                        .HasColumnType("int");

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("ActiveSubstanceProduct_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.ApplicationAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("ApplicationNumber")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<int?>("ApplicationTypeId")
                        .HasColumnType("int");

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("MedicinalProductDomainId")
                        .HasColumnType("int");

                    b.Property<int?>("MedicinalProductTypeId")
                        .HasColumnType("int");

                    b.Property<int?>("ProcedureTypeId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("Application_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.ApplicationCountryAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int?>("ApplicationId")
                        .HasColumnType("int");

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<int?>("AuthorityRoleId")
                        .HasColumnType("int");

                    b.Property<int?>("CountryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("AuditId");

                    b.ToTable("ApplicationCountry_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.ApplicationProductAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int?>("ApplicationId")
                        .HasColumnType("int");

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("ApplicationProduct_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.ClaimAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("ClaimType")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("AuditId");

                    b.ToTable("Claim_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.ClientAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<int>("ContractOwnerId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.HasKey("AuditId");

                    b.ToTable("Client_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.CountryAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<int?>("RegionId")
                        .HasColumnType("int");

                    b.Property<string>("TwoLetterCode")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AuditId");

                    b.ToTable("Country_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.CtdSectionAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("CtdModuleId")
                        .HasColumnType("int");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Number")
                        .HasMaxLength(16)
                        .IsUnicode(false)
                        .HasColumnType("varchar(16)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("AuditId");

                    b.ToTable("CtdSection_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.PicklistDataAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("PicklistTypeId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("PicklistData_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.PicklistDataCountryAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<int?>("CountryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("PicklistDataId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("PicklistDataCountry_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.ProductAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("DosageFormId")
                        .HasColumnType("int");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Strength")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.HasKey("AuditId");

                    b.ToTable("Product_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.ProjectAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("OpportunityNumber")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.HasKey("AuditId");

                    b.ToTable("Project_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.RegionAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("Abbreviation")
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)");

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.HasKey("AuditId");

                    b.ToTable("Region_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.RegulatoryAuthorityAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("Acronym")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Market")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("NationalAuthority")
                        .HasColumnType("bit");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AuditId");

                    b.ToTable("RegulatoryAuthority_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.RegulatoryAuthorityCountryAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<int?>("CountryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("RegulatoryAuthorityId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("RegulatoryAuthorityCountry_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.SubmissionAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<DateTime?>("ActualDispatchDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ActualSubmissionDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("ApplicationId")
                        .HasColumnType("int");

                    b.Property<string>("ArchivedDocumentsLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<DateTime?>("AuthoringDeadline")
                        .HasColumnType("datetime");

                    b.Property<string>("CespNumber")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("DeliveryDetailsId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("DossierFormatId")
                        .HasColumnType("int");

                    b.Property<string>("DossierName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("HealthAuthorityDueDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("LifecycleStateId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PlannedDispatchDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("PlannedSubmissionDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("PreviousLifecycleStateId")
                        .HasColumnType("int");

                    b.Property<int?>("ProjectId")
                        .HasColumnType("int");

                    b.Property<string>("ReferenceNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("RelatedSequenceNumber")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("SequenceNumber")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("SourceDocumentsLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SubmissionModeId")
                        .HasColumnType("int");

                    b.Property<int?>("SubmissionTypeId")
                        .HasColumnType("int");

                    b.Property<int?>("SubmissionUnitId")
                        .HasColumnType("int");

                    b.Property<string>("UniqueId")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime?>("WithdrawalDate")
                        .HasColumnType("datetime");

                    b.HasKey("AuditId");

                    b.ToTable("Submission_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.SubmissionCountryAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<int?>("CountryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("SubmissionId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("SubmissionCountry_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.SubmissionPublisherAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("PublisherId")
                        .HasColumnType("int");

                    b.Property<int?>("SubmissionResourceId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("SubmissionPublisher_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.SubmissionResourceAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int?>("AllocatedHours")
                        .HasColumnType("int");

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("EstimatedSizeId")
                        .HasColumnType("int");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<string>("InitialSentToEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("PriorityId")
                        .HasColumnType("int");

                    b.Property<string>("PublishingLead")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("PublishingTeamId")
                        .HasColumnType("int");

                    b.Property<string>("RegulatoryLead")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("SubmissionId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("SubmissionResource_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.UserAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("FamilyName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("GivenName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<int?>("Id")
                        .HasColumnType("int");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("AuditId");

                    b.ToTable("User_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Audit.UserClaimAudit", b =>
                {
                    b.Property<int>("AuditId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AuditAction")
                        .HasMaxLength(1)
                        .IsUnicode(false)
                        .HasColumnType("char(1)")
                        .IsFixedLength(true);

                    b.Property<int?>("ClaimId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("AuditId");

                    b.ToTable("UserClaim_Audit", "Audit");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Claim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("ClaimType")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("Claim");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Client", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("ContractOwnerId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("UC_Client_Name")
                        .HasFilter("[Name] IS NOT NULL");

                    b.ToTable("Client");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Country", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<int>("RegionId")
                        .HasColumnType("int");

                    b.Property<string>("TwoLetterCode")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("UC_Country_Name")
                        .HasFilter("[Name] IS NOT NULL");

                    b.HasIndex("RegionId");

                    b.ToTable("Country");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.CtdSection", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("CtdModuleId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasMaxLength(16)
                        .IsUnicode(false)
                        .HasColumnType("varchar(16)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("Number")
                        .IsUnique()
                        .HasDatabaseName("UX_CtdSection_Number");

                    b.HasIndex("ParentId");

                    b.ToTable("CtdSection");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.PicklistData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("PicklistTypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("PicklistData");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.PicklistDataCountry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("CountryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("PicklistDataId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("PicklistDataId", "CountryId")
                        .IsUnique()
                        .HasDatabaseName("UC_PicklistDataCountry_PicklistDataId_CountryId");

                    b.ToTable("PicklistDataCountry");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("DosageFormId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Strength")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("Name", "ClientId", "Strength", "DosageFormId")
                        .IsUnique()
                        .HasDatabaseName("UC_Product_Name_ClientId_Strength_DosageFormId")
                        .HasFilter("[Name] IS NOT NULL AND [Strength] IS NOT NULL");

                    b.ToTable("Product");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Project", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("OpportunityNumber")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.ToTable("Project");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Region", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("Abbreviation")
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.HasKey("Id");

                    b.ToTable("Region");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.RegulatoryAuthority", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("Acronym")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("Market")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("NationalAuthority")
                        .HasColumnType("bit");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("RegulatoryAuthority");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.RegulatoryAuthorityCountry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("CountryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("RegulatoryAuthorityId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RegulatoryAuthorityId");

                    b.HasIndex("CountryId", "RegulatoryAuthorityId")
                        .IsUnique()
                        .HasDatabaseName("UC_RegulatoryAuthorityCountry_CountryId_RegulatoryAuthorityId");

                    b.ToTable("RegulatoryAuthorityCountry");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Submission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<DateTime?>("ActualDispatchDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ActualSubmissionDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ApplicationId")
                        .HasColumnType("int");

                    b.Property<string>("ArchivedDocumentsLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("AuthoringDeadline")
                        .HasColumnType("datetime");

                    b.Property<string>("CespNumber")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("DeliveryDetailsId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("DossierFormatId")
                        .HasColumnType("int");

                    b.Property<string>("DossierName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("HealthAuthorityDueDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("LifecycleStateId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PlannedDispatchDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("PlannedSubmissionDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("PreviousLifecycleStateId")
                        .HasColumnType("int");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<string>("ReferenceNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("RelatedSequenceNumber")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("SequenceNumber")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("SourceDocumentsLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SubmissionModeId")
                        .HasColumnType("int");

                    b.Property<int>("SubmissionTypeId")
                        .HasColumnType("int");

                    b.Property<int?>("SubmissionUnitId")
                        .HasColumnType("int");

                    b.Property<string>("UniqueId")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime?>("WithdrawalDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationId");

                    b.HasIndex("ProjectId");

                    b.ToTable("Submission");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.SubmissionCountry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("CountryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("SubmissionId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("SubmissionId", "CountryId")
                        .IsUnique()
                        .HasDatabaseName("UC_SubmissionCountry_SubmissionId_CountryId");

                    b.ToTable("SubmissionCountry");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.SubmissionPublisher", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("PublisherId")
                        .HasColumnType("int");

                    b.Property<int>("SubmissionResourceId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PublisherId");

                    b.HasIndex("SubmissionResourceId", "PublisherId")
                        .IsUnique()
                        .HasDatabaseName("UC_SubmissionPublisher_SubmissionResourceId_PublisherId");

                    b.ToTable("SubmissionPublisher");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.SubmissionResource", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int?>("AllocatedHours")
                        .HasColumnType("int");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("EstimatedSizeId")
                        .HasColumnType("int");

                    b.Property<string>("InitialSentToEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("PriorityId")
                        .HasColumnType("int");

                    b.Property<string>("PublishingLead")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("PublishingTeamId")
                        .HasColumnType("int");

                    b.Property<string>("RegulatoryLead")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("SubmissionId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SubmissionId")
                        .IsUnique();

                    b.ToTable("SubmissionResource");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("FamilyName")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("GivenName")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.ToTable("User");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.UserClaim", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("ClaimId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("UserId", "ClaimId");

                    b.HasIndex("ClaimId");

                    b.ToTable("UserClaim");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.ActiveSubstance", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Client", "Client")
                        .WithMany("ActiveSubstance")
                        .HasForeignKey("ClientId")
                        .HasConstraintName("FK_ActiveSubstance_Client")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.ActiveSubstanceProduct", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.ActiveSubstance", "ActiveSubstance")
                        .WithMany("ActiveSubstanceProduct")
                        .HasForeignKey("ActiveSubstanceId")
                        .HasConstraintName("FK_ActiveSubstanceProduct_ActiveSubstance")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.SmartTRACE.Entities.Product", "Product")
                        .WithMany("ActiveSubstanceProduct")
                        .HasForeignKey("ProductId")
                        .HasConstraintName("FK_ActiveSubstanceProduct_Product")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ActiveSubstance");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.ApplicationCountry", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Application", "Application")
                        .WithMany("ApplicationCountry")
                        .HasForeignKey("ApplicationId")
                        .HasConstraintName("FK_ApplicationCountry_Application")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.SmartTRACE.Entities.Country", "Country")
                        .WithMany("ApplicationCountry")
                        .HasForeignKey("CountryId")
                        .HasConstraintName("FK_ApplicationCountry_Country")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Application");

                    b.Navigation("Country");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.ApplicationProduct", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Application", "Application")
                        .WithMany("ApplicationProduct")
                        .HasForeignKey("ApplicationId")
                        .HasConstraintName("FK_ApplicationProduct_Application")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.SmartTRACE.Entities.Product", "Product")
                        .WithMany("ApplicationProduct")
                        .HasForeignKey("ProductId")
                        .HasConstraintName("FK_ApplicationProduct_Product")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Application");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Country", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Region", "Region")
                        .WithMany("Country")
                        .HasForeignKey("RegionId")
                        .HasConstraintName("FK_Country_Region")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Region");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.CtdSection", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.CtdSection", "Parent")
                        .WithMany("ChildCtdSection")
                        .HasForeignKey("ParentId")
                        .HasConstraintName("FK_CtdSection_CtdSection");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.PicklistDataCountry", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Country", "Country")
                        .WithMany("PicklistDataCountry")
                        .HasForeignKey("CountryId")
                        .HasConstraintName("FK_PicklistDataCountry_Product")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.SmartTRACE.Entities.PicklistData", "PicklistData")
                        .WithMany("PicklistDataCountry")
                        .HasForeignKey("PicklistDataId")
                        .HasConstraintName("FK_PicklistDataCountry_PicklistData")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");

                    b.Navigation("PicklistData");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Product", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Client", "Client")
                        .WithMany("Product")
                        .HasForeignKey("ClientId")
                        .HasConstraintName("FK_Product_Client")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Project", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Client", "Client")
                        .WithMany("Project")
                        .HasForeignKey("ClientId")
                        .HasConstraintName("FK_Project_Client")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.RegulatoryAuthorityCountry", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Country", "Country")
                        .WithMany("RegulatoryAuthorityCountry")
                        .HasForeignKey("CountryId")
                        .HasConstraintName("FK_RegulatoryAuthorityCountry_Country")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.SmartTRACE.Entities.RegulatoryAuthority", "RegulatoryAuthority")
                        .WithMany("RegulatoryAuthorityCountry")
                        .HasForeignKey("RegulatoryAuthorityId")
                        .HasConstraintName("FK_RegulatoryAuthorityCountry_RegulatoryAuthority")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");

                    b.Navigation("RegulatoryAuthority");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Submission", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Application", "Application")
                        .WithMany("Submission")
                        .HasForeignKey("ApplicationId")
                        .HasConstraintName("FK_Submission_Application")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.SmartTRACE.Entities.Project", "Project")
                        .WithMany("Submission")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Application");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.SubmissionCountry", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Country", "Country")
                        .WithMany("SubmissionCountry")
                        .HasForeignKey("CountryId")
                        .HasConstraintName("FK_SubmissionCountry_Country")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.SmartTRACE.Entities.Submission", "Submission")
                        .WithMany("SubmissionCountry")
                        .HasForeignKey("SubmissionId")
                        .HasConstraintName("FK_SubmissionCountry_Submission")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.SubmissionPublisher", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.User", "Publisher")
                        .WithMany("SubmissionPublisher")
                        .HasForeignKey("PublisherId")
                        .HasConstraintName("FK_SubmissionPublisher_Publisher")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.SmartTRACE.Entities.SubmissionResource", "SubmissionResource")
                        .WithMany("SubmissionPublisher")
                        .HasForeignKey("SubmissionResourceId")
                        .HasConstraintName("FK_SubmissionResource_SubmissionResource")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Publisher");

                    b.Navigation("SubmissionResource");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.SubmissionResource", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Submission", "Submission")
                        .WithOne("SubmissionResource")
                        .HasForeignKey("PharmaLex.SmartTRACE.Entities.SubmissionResource", "SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.UserClaim", b =>
                {
                    b.HasOne("PharmaLex.SmartTRACE.Entities.Claim", "Claim")
                        .WithMany("UserClaim")
                        .HasForeignKey("ClaimId")
                        .HasConstraintName("FK_UserClaim_Claim")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.SmartTRACE.Entities.User", "User")
                        .WithMany("UserClaim")
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_UserClaim_User")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Claim");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.ActiveSubstance", b =>
                {
                    b.Navigation("ActiveSubstanceProduct");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Application", b =>
                {
                    b.Navigation("ApplicationCountry");

                    b.Navigation("ApplicationProduct");

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Claim", b =>
                {
                    b.Navigation("UserClaim");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Client", b =>
                {
                    b.Navigation("ActiveSubstance");

                    b.Navigation("Product");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Country", b =>
                {
                    b.Navigation("ApplicationCountry");

                    b.Navigation("PicklistDataCountry");

                    b.Navigation("RegulatoryAuthorityCountry");

                    b.Navigation("SubmissionCountry");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.CtdSection", b =>
                {
                    b.Navigation("ChildCtdSection");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.PicklistData", b =>
                {
                    b.Navigation("PicklistDataCountry");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Product", b =>
                {
                    b.Navigation("ActiveSubstanceProduct");

                    b.Navigation("ApplicationProduct");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Project", b =>
                {
                    b.Navigation("Submission");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Region", b =>
                {
                    b.Navigation("Country");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.RegulatoryAuthority", b =>
                {
                    b.Navigation("RegulatoryAuthorityCountry");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.Submission", b =>
                {
                    b.Navigation("SubmissionCountry");

                    b.Navigation("SubmissionResource");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.SubmissionResource", b =>
                {
                    b.Navigation("SubmissionPublisher");
                });

            modelBuilder.Entity("PharmaLex.SmartTRACE.Entities.User", b =>
                {
                    b.Navigation("SubmissionPublisher");

                    b.Navigation("UserClaim");
                });
#pragma warning restore 612, 618
        }
    }
}
