﻿@model ProjectModel

<div class="manage-container">
    <header class="manage-header">
        <h3>@Model.Name Project</h3>
        <a href="/projects" class="button secondary icon-button-list">View All Projects</a>
        <a href="/clients/edit/@Model.ClientId" class="button secondary icon-button-back">Back to Client</a>
    </header>
    <form method="post">
        <div class="form-col">
            <label for="Name">Name*</label>
            <input asp-for="Name" type="text" required />
            <label for="Code">Code*</label>
            <input asp-for="Code" type="text" required />
            <label for="OpportunityNumber">Opportunity Number*</label>
            <input asp-for="OpportunityNumber" type="text" required />
            <div class="buttons">
                <a class="button secondary icon-button-cancel" href="/clients/edit/@Model.ClientId">Cancel</a><button class="icon-button-save">Save</button>
            </div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="ClientId" />
        </div>
    </form>
</div>