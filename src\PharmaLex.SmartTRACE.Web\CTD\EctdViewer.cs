﻿using AutoMapper;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models.Ich;
using PharmaLex.SmartTRACE.Web.Models.Ich.AU.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.CH.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32;
using PharmaLex.SmartTRACE.Web.Models.Ich.Eu.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.US.Models;
using PharmaLex.SmartTRACE.Web.Storage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.CTD
{
    public interface IEctdViewer
    {
        Task<List<CtdNodeModel>> BuildTree(string sequenceLocation, List<string> submissions = null, bool recursive = false);
    }

    public class AzureBlobStorageEctdViewer : EctdViewerBase
    {
        public AzureBlobStorageEctdViewer(
            IMapper mapper,
            IEctdProvider ectdProvider,
            ISubmissionBlobContainer submissionBlobContainer,
            IEctdStudyReportProcessor ectdStudyReportProcessor)
            : base(mapper, ectdProvider, submissionBlobContainer, ectdStudyReportProcessor)
        { }
    }
    public abstract class EctdViewerBase : IEctdViewer
    {
        private readonly IMapper mapper;
        private readonly IEctdProvider ectdProvider;
        private readonly ISubmissionBlobContainer submissionBlobContainer;
        private readonly IEctdStudyReportProcessor ectdStudyReportProcessor;

        public EctdViewerBase(
            IMapper mapper,
            IEctdProvider ectdProvider,
            ISubmissionBlobContainer submissionBlobContainer,
            IEctdStudyReportProcessor ectdStudyReportProcessor)
        {
            this.mapper = mapper;
            this.ectdProvider = ectdProvider;
            this.submissionBlobContainer = submissionBlobContainer;
            this.ectdStudyReportProcessor = ectdStudyReportProcessor;
        }

        public Task<List<string>> GetSubmissions(string sequenceLocation)
        {
            return this.submissionBlobContainer.GetSequences(sequenceLocation);
        }

        public async Task<List<CtdNodeModel>> BuildTree(string sequenceLocation, List<string> submissions = null, bool recursive = false)
        {
            bool fullRead = submissions == null || submissions.Count == 0 ||
                (submissions.Count == 1 && submissions[0].ToLower() == "all");
            if (fullRead)
            {
                submissions = await this.submissionBlobContainer.GetSequences(sequenceLocation, recursive);
            }
            else
            {
                var sub = await this.submissionBlobContainer.GetSequences(sequenceLocation, recursive);
                var intersectedSubmissions = sub.Intersect(submissions).ToList();

                if (intersectedSubmissions.Count == 0)
                {
                    var finalSubmissions = new List<string>();
                    intersectedSubmissions = sub.Intersect(submissions.Select(x => x.Split('/').First())).ToList();
                    foreach (var intersectSub in intersectedSubmissions)
                    {
                        finalSubmissions.AddRange(submissions.Where(x => x.StartsWith(intersectSub)));
                    }

                    submissions = finalSubmissions;
                }
                else
                {
                    submissions = intersectedSubmissions;
                }
            }

            List<List<CtdNodeModel>> sequence = new List<List<CtdNodeModel>>();
            foreach (string submission in submissions.OrderBy(x => x))
            {
                if (submission.EndsWith("-workingdocuments"))
                {
                    //read documents and add them as leaf
                    sequence.Add(await ReadDocumentsAsync(sequenceLocation, submission));
                }

                if (!submission.EndsWith("-workingdocuments"))
                {
                    sequence.Add(await this.ReadSubmission(sequenceLocation, submission, fullRead));
                }
            }

            return this.LayerSubmissions(sequence.Where(x => x != null).ToList(), fullRead);
        }

        private List<CtdNodeModel> LayerSubmissions(List<List<CtdNodeModel>> orderedSubmissions, bool fullRead)
        {
            List<CtdNodeModel> latestSubmission = orderedSubmissions.Last();

            var olderSubmissions = orderedSubmissions.GetRange(0, orderedSubmissions.Count - 1);
            olderSubmissions.Reverse();

            List<CtdNodeModel> layered = latestSubmission;
            foreach (List<CtdNodeModel> previousSumission in olderSubmissions)
            {
                layered = this.Layer(latestSubmission, previousSumission);
            }

            if (fullRead)
            {
                List<M1DataModel> orderedEnvelopes = olderSubmissions.Select(x => x[0].Envelope[0]).OrderBy(x => x.Submission).ToList();
                orderedEnvelopes.Add(layered[0].Envelope[0]);
                layered[0].Envelope = orderedEnvelopes;
            }

            return layered;
        }

        private List<CtdNodeModel> Layer(List<CtdNodeModel> latest, List<CtdNodeModel> previous)
        {
            foreach (CtdNodeModel latestNode in latest)
            {
                CtdNodeModel previousNode = previous.FirstOrDefault(x => x.Name == latestNode.Name);
                if (previousNode != null)
                {
                    var notNewLatestDocuments = latestNode.Nodes.Where(x => x.op != "New");

                    foreach (LeafModel latestDocument in notNewLatestDocuments)
                    {
                        string modifiedFileId = latestDocument.ModifiedFile.Substring(latestDocument.ModifiedFile.LastIndexOf('#') + 1);
                        var previousDocument = previousNode.Nodes.FirstOrDefault(x => x.Id == modifiedFileId);
                        if (previousDocument != null)
                        {
                            latestDocument.Historical.Add(previousDocument);

                            if (previousDocument.op != "New")
                            {
                                latestDocument.ModifiedFile = previousDocument.ModifiedFile;
                            }
                        }
                    }

                    latestNode.ChildNodes = this.Layer(latestNode.ChildNodes, previousNode.ChildNodes);

                    var previousDocuments = previousNode.Nodes.Where(x => !notNewLatestDocuments.Any(y => y.Historical.Select(z => z.Id).Contains(x.Id)));
                    latestNode.Nodes.AddRange(previousDocuments);
                    latestNode.Nodes = latestNode.Nodes.OrderBy(x => x.Submission).ThenBy(x => x.text).ToList();
                }
            }

            var onlyInPrevious = previous.Where(x => !latest.Any(y => y.Name == x.Name));

            if (onlyInPrevious.Any())
            {
                foreach (var onlyInPreviousNode in onlyInPrevious)
                {
                    var docName = onlyInPreviousNode.Name;
                    if (docName != null && docName.Contains("workingdocuments"))
                    {
                        foreach (var pp in onlyInPrevious)
                        {
                            pp.SortOrder = int.MaxValue;
                        }
                    }
                }

                latest.AddRange(onlyInPrevious);
            }

            return latest.OrderBy(x => x.SortOrder).ToList();
        }

        private async Task<List<CtdNodeModel>> ReadSubmission(string sequenceLocation, string submission, bool fullRead)
        {
            string submissionLocation = $"{sequenceLocation}/{submission}/index.xml";
            IEctdModule module;
            IEctdFileReader reader;

            reader = this.ectdProvider.Reader;
            module = await reader.ReadFile(submissionLocation);

            List<CtdNodeModel> tree = (await this.BuildCtdTree(module.Content, module, sequenceLocation, submission, string.Empty, reader.SchemaName, reader.DtdVersion)).ToList();

            string m1RelativePath = (module as ectd).m1administrativeinformationandprescribinginformation?.leaf.FirstOrDefault()?.href.Trim('/');
            if (!string.IsNullOrEmpty(m1RelativePath))
            {
                string m1Location = $"{sequenceLocation}/{submission}/{m1RelativePath}";
                reader = this.ectdProvider.Reader;
                module = await reader.ReadFile(m1Location);

                IEctdModule1 m1 = module as IEctdModule1;

                string subRelativePath = string.Join('/', m1RelativePath.Split('/').Take(m1RelativePath.Split('/').Length - 1));
                IEnumerable<CtdNodeModel> m1Tree = await this.BuildCtdTree(m1.Content, module, sequenceLocation, submission, subRelativePath, reader.SchemaName, reader.DtdVersion);

                if (fullRead)
                {
                    tree[0].Envelope = new List<M1DataModel> {
                        new M1DataModel
                        {
                            Submission = submission,
                            Display = submission,
                            DtdVersion = m1.DtdVersion,
                            Region = m1.Region,
                            List = this.GetModule1Data(m1.Data, reader.SchemaName),
                        }
                    };
                }

                tree[0].ChildNodes.AddRange(m1Tree);
            }

            return tree;
        }

        private async Task<List<CtdNodeModel>> ReadDocumentsAsync(string sequenceLocation, string submission)
        {
            var documentFolderPath = $"{sequenceLocation}/{submission}";
            var tree = new List<CtdNodeModel>();

            var submissionName = submission;

            if (submission.Contains(".zip/"))
            {
                submissionName = submission.Split(".zip/").Last();
            }

            var node = new CtdNodeModel(submission, submissionName);
            tree.Add(node);

            await BuildDocumentTree(node, submission, documentFolderPath);
            return tree;
        }

        private async Task BuildDocumentTree(CtdNodeModel node, string submission, string path)
        {
            var directories = await this.submissionBlobContainer.ListDirectoriesAsync(path);
            foreach (var directory in directories)
            {
                var directoryName = directory.prefix.Replace(path, "").Trim('/');
                var childNode = new CtdNodeModel(submission, directoryName);
                node.ChildNodes.Add(childNode);

                await this.BuildDocumentTree(childNode, submission, directory.prefix);
            }

            var documents = await this.submissionBlobContainer.ListBlobsAsync(path);
            var documentNodes = documents.Select(d => d.ToLeafModel(submission)).ToList();
            node.Nodes = documentNodes;
        }

        private List<IModule1DataModel> GetModule1Data(List<EctdModule1Data> data, string schemaName)
        {
            if (schemaName == "eu-backbone")
            {
                return mapper.Map<List<EuEnvelopeModel>>(data).Select(x => x as IModule1DataModel).ToList();
            }

            if (schemaName == "fda-regional")
            {
                return mapper.Map<List<UsEnvelopeModel>>(data).Select(x => x as IModule1DataModel).ToList();
            }

            if (schemaName == "ch-backbone")
            {
                return mapper.Map<List<ChEnvelopeModel>>(data).Select(x => x as IModule1DataModel).ToList();
            }

            if (schemaName == "tga_ectd")
            {
                return mapper.Map<List<AuEnvelopeModel>>(data).Select(x => x as IModule1DataModel).ToList();
            }

            return new List<IModule1DataModel>();
        }

        private async Task<IEnumerable<CtdNodeModel>> BuildCtdTree(IEnumerable<CtdSectionBase> nodes, IEctdModule module, string sequenceLocation, string submission, string path, string schemaName, string dtdVersion)
        {
            List<CtdNodeModel> ctds = new List<CtdNodeModel>();
            foreach (CtdSectionBase ctd in nodes)
            {
                if (ctd == null) continue;

                CtdNodeModel node = this.mapper.Map<CtdNodeModel>(ctd);

                var nodeDocuments = ctd.Documents.Where(x => x is Ileaf)
                    .Cast<Ileaf>()
                    .Where(x => x.href == null || !x.href.EndsWith("xml"))
                    .Select(leaf =>
                    {
                        leaf.Submission = submission;
                        leaf.Path = path;

                        return leaf;
                    });

                node.Nodes = this.mapper.Map<List<LeafModel>>(nodeDocuments);
                ctds.Add(node);
                node.ChildNodes = (await this.BuildCtdTree(ctd.ChildNodes, module, sequenceLocation, submission, path, schemaName, dtdVersion)).ToList();

                var documentNodes = await this.ectdStudyReportProcessor.Process(module, ctd, sequenceLocation, submission, path, schemaName);
                node.ChildNodes.AddRange(documentNodes);
            }

            return ctds.OrderBy(x => x.SortOrder);
        }
    }
}
