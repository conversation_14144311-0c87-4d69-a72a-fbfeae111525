﻿ALTER TRIGGER [dbo].[User_Insert] ON [dbo].[User]
FOR INSERT AS
INSERT INTO [Audit].[User_Audit]
SELECT 'I', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [UserTypeId], [AutoAccessClients] FROM [Inserted]
GO

ALTER TRIGGER [dbo].[User_Update] ON [dbo].[User]
FOR UPDATE AS
INSERT INTO [Audit].[User_Audit]
SELECT 'U', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [UserTypeId], [AutoAccessClients] FROM [Inserted]
GO

ALTER TRIGGER [dbo].[User_Delete] ON [dbo].[User]
FOR DELETE AS
INSERT INTO [Audit].[User_Audit]
SELECT 'D', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()), [UserTypeId], [AutoAccessClients] FROM [Deleted]
GO