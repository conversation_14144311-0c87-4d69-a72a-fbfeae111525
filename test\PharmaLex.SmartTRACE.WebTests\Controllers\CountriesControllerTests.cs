﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.EntityFrameworkCore;
using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Helpers.Constants;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class CountriesControllerTests
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly CountriesController countriesController;
        private readonly IMapper mapper;
        private readonly ICountryService countryService;

        public CountriesControllerTests()
        {
            mapper = Substitute.For<IMapper>();
            cache = Substitute.For<IDistributedCacheServiceFactory>();
            countryService = Substitute.For<ICountryService>();
            countriesController = new CountriesController(cache, mapper,countryService);
        }
        #region static methods
        private static List<CountryModel> GetCountryModel()
        {
            return new List<CountryModel>
            {
                new CountryModel { Id=1,RegionId=1, Name="country1"},
                new CountryModel { Id=2,RegionId=2, Name="country2"},
                new CountryModel { Id=3,RegionId=3, Name="country3"}
            };
        }
        private static List<Country> GetCountry()
        {
            return new List<Country>
            {
                new Country { Id=1,RegionId=1, Name="country1"},
                new Country { Id=2,RegionId=2, Name="country2"},
                new Country { Id=3,RegionId=3, Name="country3"}
            };
        }
        #endregion

        #region Index
        [Fact]
        public async Task Index_Returns_CountryList()
        {
            // Arrange
            var mappedCache = Substitute.For<IMappedEntityCacheServiceProxy<Country, CountryModel>>();
            var Countries = GetCountryModel();
            cache.CreateMappedEntity<Country, CountryModel>().AllAsync().Returns(Countries);

            // Act
            var result = await countriesController.Index();
            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsAssignableFrom<IEnumerable<CountryModel>>(viewResult.ViewData.Model);
            Assert.NotNull(viewResult);
            Assert.Equal(Countries, model);
        }
        #endregion

        #region Method New Return Country Model
        [Fact]
        public void New_Returns_ViewResult_With_CountryModel()
        {
            // Arrange
            var viewName = "EditCountry";

            // Act
            var result = countriesController.New(1);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.IsAssignableFrom<CountryModel>(viewResult.ViewData.Model);
            Assert.Equal(viewName, viewResult.ViewName);
        }
        #endregion

        #region Action Method New(int Id) for invalid
        [Fact]
        public async Task New_Invalid()
        {
            // Arrange
            int id = 10;
            var controller = new RegionsController(cache, mapper);
            countriesController.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result =  countriesController.New(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region  SaveNew with valid (id,  model)
        [Fact]
        public async Task Save_New_with_ValidData()
        {
            // Arrange
            var regionId = 1;
            var countryCache = Substitute.For<ITrackedEntityCacheServiceProxy<Country>>();
            cache.CreateTrackedEntity<Country>().Returns(countryCache);
            List<Country> Country =
               [
                   new Country { Id = 1, RegionId=1, Name="country1" },
                    new Country { Id = 2, RegionId=2, Name="country2" },
                ];
            mapper
               .Map<Country>(Arg.Any<CountryModel>())
                   .Returns(callInfo =>
                   {
                       var countryModel = callInfo.Arg<CountryModel>();
                       return Country.FirstOrDefault();
                   });

            var model = GetCountryModel()[0];
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            countriesController.TempData = tempData;

            // Act
            var result = await countriesController.SaveNew(regionId, model);

            // Assert
            var viewResult = Assert.IsType<RedirectResult>(result);
            var redirectResult = viewResult as RedirectResult;
            var ResposeUrl = "/regions/edit/1";
            Assert.NotNull(redirectResult);
            Assert.Equal(redirectResult.Url, ResposeUrl);

        }
        #endregion

        #region  New(save) with invalid (id,  model)
        [Fact]
        public async Task Save_New_with_InValidData()
        {
            //Arrange
            var regionId = 1;
            CountryModel countryModel = GetCountryModel()[0];
            countriesController.ModelState.AddModelError("SessionName", "Required");
            //Act
            var result = await countriesController.SaveNew(regionId, countryModel) as BadRequestObjectResult;
            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region  SaveNew DuplicateName (id,  model)
        [Fact]
        public async Task SaveNew_WhenCountryNameIsDuplicate_ReturnsViewWithError()
        {
            // Arrange
            var model = new CountryModel { Name = "DuplicateCountry" };
            countryService.IsCountryNameDuplicate("DuplicateCountry").Returns(true);

            // Act
            var result = await countriesController.SaveNew(1, model);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal("EditCountry", viewResult.ViewName);
            Assert.Same(model, viewResult.Model);
            Assert.True(model.HasError);
            Assert.Equal(ErrorMessages.UniqueErrorMessage, model.ErrorMessage);
        }
        #endregion

        #region Edit Returns EditProject Model
        [Fact]
        public async Task Edit_Returns_EditCountryModel()
        {
            // Arrange
            var Id = 1;
            var countryCache = Substitute.For<IEntityCacheServiceProxy<Country>>();
            cache.CreateEntity<Country>().Returns(countryCache);
            countryCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(GetCountry()[0]);
            List<CountryModel> CountryModel = new List<CountryModel>
              {
                   new CountryModel { Id = 1, RegionId=1, Name="country1" },
                    new CountryModel { Id = 2, RegionId=2, Name="country2" },
                 };
            mapper.Map<CountryModel>(Arg.Any<Country>())
                  .Returns(callInfo =>
                  {
                      var crModel = callInfo.Arg<Country>();
                      return CountryModel.FirstOrDefault();
                  });
            // Act
            var result = await countriesController.Edit(Id);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal("EditCountry", viewResult.ViewName);
        }
        #endregion

        #region Action Method Edit(int Id) for invalid
        [Fact]
        public async Task Edit_Invalid()
        {
            // Arrange
            int id = 10;
            var controller = new RegionsController(cache, mapper);
            countriesController.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await countriesController.Edit(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region  Edit save with valid (id,  model)
        [Fact]
        public async Task Edit_Save_With_ValidData()
        {
            // Arrange
            var countryCache = Substitute.For<ITrackedEntityCacheServiceProxy<Country>>();
            cache.CreateTrackedEntity<Country>().Returns(countryCache);

            List<Country> Country =
                [
                    new Country { Id = 1, RegionId=1, Name="country1" },
                    new Country { Id = 2, RegionId=2, Name="country2" },
                ];
            countryCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(Country[0]);
            mapper
               .Map<CountryModel, Country>(Arg.Any<CountryModel>())
               .Returns(callInfo =>
               {
                   var countryModel = callInfo.Arg<CountryModel>();
                   return Country.FirstOrDefault();
               });

            var model = GetCountryModel()[0];
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            countriesController.TempData = tempData;

            // Act
            var result = await countriesController.SaveEdit(model.Id, model);

            // Assert
            var viewResult = Assert.IsType<RedirectResult>(result);
            var redirectResult = viewResult as RedirectResult;
            var ResposeUrl = $"/regions/edit/{model.RegionId}";
            Assert.NotNull(redirectResult);
            Assert.Equal(redirectResult.Url, ResposeUrl);
        }
        #endregion

        #region Edit save with invalid model
        [Fact]
        public async Task Edit_Save_With_InValidData()
        {
            //Arrange
            CountryModel countryModel = GetCountryModel()[0];
            string ViewName = "EditCountry";
            countriesController.ModelState.AddModelError("SessionName", "Required");

            //Act
            var result = await countriesController.SaveEdit(countryModel.Id, countryModel) as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.ViewName);
            Assert.Equal(result.ViewName, ViewName);
        }
        #endregion

        #region  EditSave DuplicateName (id,  model)
        [Fact]
        public async Task SaveEdit_WhenCountryNameIsDuplicate_ReturnsViewWithError()
        {
            // Arrange
            var model = new CountryModel { Name = "DuplicateCountry" };
            countryService.IsCountryNameDuplicate("DuplicateCountry").Returns(true);

            // Act
            var result = await countriesController.SaveEdit(1, model);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal("EditCountry", viewResult.ViewName);
            Assert.Same(model, viewResult.Model);
            Assert.True(model.HasError);
            Assert.Equal(ErrorMessages.UniqueErrorMessage, model.ErrorMessage);
        }
        #endregion

        #region Delete Get (id)
        [Fact]
        public async Task Delete()
        {
            // Arrange
            var Id = 1;
            var countryCache = Substitute.For<IEntityCacheServiceProxy<Country>>();
            cache.CreateEntity<Country>().Returns(countryCache);
            countryCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(GetCountry()[0]);

            // Act
            var result = await countriesController.Delete(Id);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal("DeleteCountry", viewResult.ViewName);
        }
        #endregion

        #region Action Method Delete(int Id) for invalid
        [Fact]
        public async Task Delete_Invalid()
        {
            // Arrange
            int id = 10;
            var controller = new RegionsController(cache, mapper);
            countriesController.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await countriesController.Delete(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion

        #region  Delete with valid (id)
        [Fact]
        public async Task Delete_With_ValidData()
        {
            // Arrange
            var id = 1;
            var ResposeUrl = "/regions/edit/";
            var countryCache = Substitute.For<ITrackedEntityCacheServiceProxy<Country>>();
            cache.CreateTrackedEntity<Country>().Returns(countryCache);
            List<Country> Country =
                [
                    new Country { Id = 1, RegionId=1, Name="country1" },
                    new Country { Id = 2, RegionId=2, Name="country2" },
                ];
            countryCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(Country[0]);
            countryCache.Remove(Country[0]);
            var model = GetCountryModel()[0];
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            countriesController.TempData = tempData;
            var expectedResponse = new ObjectResult(null);
            // Act
            var result = await countriesController.ConfirmedDelete(model.Id);
            // Assert
            var viewResult = Assert.IsType<RedirectResult>(result);
            var redirectResult = viewResult as RedirectResult;
            var resp = result as RedirectResult;
            Assert.NotNull(redirectResult);
            Assert.Equal(resp?.Url, ResposeUrl + id);
        }
        #endregion

        #region Action Method ConfirmDelete(int Id) for invalid
        [Fact]
        public async Task ConfirmDelete_Invalid()
        {
            // Arrange
            int id = 10;
            var controller = new RegionsController(cache, mapper);
            countriesController.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await countriesController.ConfirmedDelete(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
    }
}
