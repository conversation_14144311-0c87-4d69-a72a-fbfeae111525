﻿@model IEnumerable<CacheViewModel>
@{
    ViewData["Title"] = "Cache";
}

<div id="cache" class="manage-container">
    <header class="manage-header">
        <h2>Cache</h2>
        <div class="controls">
            <form id="deleteForm" method="post" action="/cache/flush">
                @Html.AntiForgeryToken()
                <input type="submit" value="Flush All Cached Data" class="button" />
            </form>
        </div>
    </header>
    <filtered-table :items="cacheItems"
                    :columns="columns"
                    :filters="filters"
                    :link="link">
    </filtered-table>
</div>

@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#cache',
            data() {
                return {
                    link: '/cache/edit/',
                    cacheItems: @Html.Raw(Json.Serialize(Model)),
                    columns: {
                        idKey: 'value',
                        config: [
                            {
                                dataKey: 'value',
                                sortKey: 'value',
                                header: 'Dependency',
                                type: 'text',
                            },
                            {
                                dataKey: 'key',
                                sortKey: 'key',
                                header: 'Key',
                                type: 'text',
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'value',
                            options: [],
                            type: 'search',
                            header: 'Search Key',
                            fn: v => p => p.value.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ]
                };
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}