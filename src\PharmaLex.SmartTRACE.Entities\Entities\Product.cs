﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class Product : EntityBase
    {
        public Product()
        {
            ActiveSubstanceProduct = new HashSet<ActiveSubstanceProduct>();
            ApplicationProduct = new HashSet<ApplicationProduct>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public int DosageFormId { get; set; }
        public string Strength { get; set; }
        public int ClientId { get; set; }
        public int LifecycleStateId { get; set; }

        public virtual Client Client { get; set; }
        public virtual ICollection<ActiveSubstanceProduct> ActiveSubstanceProduct { get; set; }
        public virtual ICollection<ApplicationProduct> ApplicationProduct { get; set; }
    }
}
