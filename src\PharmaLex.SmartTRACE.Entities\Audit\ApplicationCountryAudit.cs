﻿using PharmaLex.DataAccess;

namespace PharmaLex.SmartTRACE.Entities.Audit
{
    public partial class ApplicationCountryAudit: AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }

        public int? ApplicationId { get; set; }
        public int? CountryId { get; set; }
        public int? AuthorityRoleId { get; set; }
    }
}
