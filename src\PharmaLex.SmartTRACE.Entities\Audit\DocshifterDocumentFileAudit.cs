﻿using PharmaLex.DataAccess;

namespace PharmaLex.SmartTRACE.Entities.Audit
{
    public partial class DocshifterDocumentFileAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }
        public int Id { get; set; }
        public string FileName { get; set; }
        public int StateId { get; set; }
        public int DocumentId { get; set; }
    }
}
