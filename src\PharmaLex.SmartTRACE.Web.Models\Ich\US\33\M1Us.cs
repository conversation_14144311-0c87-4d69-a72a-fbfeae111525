﻿using System;
using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.eCTD32.US33
{
    [Serializable()]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class leaf
    {
        public leaf()
        {
            this.type = "simple";
        }
        public title title { get; set; }
        [XmlElement("link-text")]
        public linktext linktext { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        [XmlAttribute("application-version")]
        public string applicationversion { get; set; }
        [XmlAttribute()]
        public string version { get; set; }
        [XmlAttribute("font-library")]
        public string fontlibrary { get; set; }
        [XmlAttribute()]
        public leafOperation operation { get; set; }
        [XmlAttribute("modified-file")]
        public string modifiedfile { get; set; }
        [XmlAttribute()]
        public string checksum { get; set; }
        [XmlAttribute("checksum-type")]
        public string checksumtype { get; set; }
        [XmlAttribute()]
        public string keywords { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string type { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string role { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string href { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefShow show { get; set; }
        [XmlIgnore()]
        public bool showSpecified { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefActuate actuate { get; set; }
        [XmlIgnore()]
        public bool actuateSpecified { get; set; }
   
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class title
    {
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        [XmlText()]
        public string Value { get; set; }
    }

    [Serializable]
    [XmlRoot("link-text", Namespace = "", IsNullable = false)]
    public partial class linktext
    {
        [XmlElement("xref")]
        public xref[] Items { get; set; }
        [XmlText()]
        public string[] Text { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class xref
    {
        public xref()
        {
            type = "simple";
        }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string type { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string role { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string title { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string href { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefShow show { get; set; }
        [XmlIgnore()]
        public bool showSpecified { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefActuate actuate { get; set; }
        [XmlIgnore()]
        public bool actuateSpecified { get; set; }
    }

    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3c.org/1999/xlink")]

    public enum xrefShow
    {
        embed,
        none,
        other,
        @new,
        replace,
    }

    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3c.org/1999/xlink")]

    public enum xrefActuate
    {
        onLoad,
        none,
        other,
        onRequest,
    }

    [Serializable]
    public enum leafOperation
    {
        delete,
        @new,
        append,
        replace,
    }

    [Serializable]
    [XmlRoot("node-extension", Namespace = "", IsNullable = false)]
    public partial class nodeextension
    {
        public title title { get; set; }
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class admin
    {
        [XmlElement("applicant-info")]
        public applicantinfo applicantinfo { get; set; }
        [XmlArray("application-set")]
        [XmlArrayItem("application", IsNullable = false)]
        public application[] applicationset { get; set; }
    }

    [Serializable]
    [XmlRoot("applicant-info", Namespace = "", IsNullable = false)]
    public partial class applicantinfo
    {
        public string id { get; set; }
        [XmlElement("company-name")]
        public string companyname { get; set; }
        [XmlElement("submission-description")]
        public string submissiondescription { get; set; }
        [XmlArray("applicant-contacts")]
        [XmlArrayItem("applicant-contact", IsNullable = false)]
        public applicantcontact[] applicantcontacts { get; set; }
    }

    [Serializable]
    [XmlRoot("applicant-contact", Namespace = "", IsNullable = false)]
    public partial class applicantcontact
    {
        [XmlElement("applicant-contact-name")]
        public applicantcontactname applicantcontactname { get; set; }
        [XmlArrayItem("telephone", IsNullable = false)]
        public telephone[] telephones { get; set; }
        [XmlArrayItem("email", IsNullable = false)]
        public string[] emails { get; set; }
    }

    [Serializable]
    [XmlRoot("applicant-contact-name", Namespace = "", IsNullable = false)]
    public partial class applicantcontactname
    {
        [XmlAttribute("applicant-contact-type")]
        public string applicantcontacttype { get; set; }
        [XmlText()]
        public string Value { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class telephone
    {
        [XmlAttribute("telephone-number-type")]
        public string telephonenumbertype { get; set; }
        [XmlText()]
        public string Value { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class application
    {
        [XmlElement("application-information")]
        public applicationinformation applicationinformation { get; set; }
        [XmlElement("submission-information")]
        public submissioninformation submissioninformation { get; set; }
        [XmlAttribute("application-containing-files")]
        public applicationApplicationcontainingfiles applicationcontainingfiles { get; set; }
    }

    [Serializable]
    [XmlRoot("application-information", Namespace = "", IsNullable = false)]
    public partial class applicationinformation
    {
        [XmlElement("application-number")]
        public applicationnumber applicationnumber { get; set; }
        [XmlElement("cross-reference-application-number")]
        public crossreferenceapplicationnumber[] crossreferenceapplicationnumber { get; set; }
    }

    [Serializable]
    [XmlRoot("application-number", Namespace = "", IsNullable = false)]
    public partial class applicationnumber
    {
        [XmlAttribute("application-type")]
        public string applicationtype { get; set; }
        [XmlText()]
        public string Value { get; set; }
    }

    [Serializable]
    [XmlRoot("cross-reference-application-number", Namespace = "", IsNullable = false)]
    public partial class crossreferenceapplicationnumber
    {
        [XmlAttribute("application-type")]
        public string applicationtype { get; set; }
        [XmlText()]
        public string Value { get; set; }
    }

    [Serializable]
    [XmlRoot("submission-information", Namespace = "", IsNullable = false)]
    public partial class submissioninformation
    {
        [XmlElement("submission-id")]
        public submissionid submissionid { get; set; }
        [XmlElement("sequence-number")]
        public sequencenumber sequencenumber { get; set; }
        public form form { get; set; }
    }

    [Serializable]
    [XmlRoot("submission-id", Namespace = "", IsNullable = false)]
    public partial class submissionid
    {
        [XmlAttribute("submission-type")]
        public string submissiontype { get; set; }
        [XmlAttribute("supplement-effective-date-type")]
        public string supplementeffectivedatetype { get; set; }
        [XmlText()]
        public string Value { get; set; }
    }

    [Serializable]
    [XmlRoot("sequence-number", Namespace = "", IsNullable = false)]
    public partial class sequencenumber
    {
        [XmlAttribute("submission-sub-type")]
        public string submissionsubtype { get; set; }
        [XmlText()]
        public string Value { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class form
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute("form-type")]
        public string formtype { get; set; }
    }

    [Serializable]
    public enum applicationApplicationcontainingfiles
    {
        @false,
        @true,
    }

    [Serializable]
    [XmlRoot("applicant-contacts", Namespace = "", IsNullable = false)]
    public partial class applicantcontacts
    {
        [XmlElement("applicant-contact")]
        public applicantcontact[] applicantcontact { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class telephones
    {
        [XmlElement("telephone")]
        public telephone[] telephone { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class emails
    {
        [XmlElement("email")]
        public string[] email { get; set; }
    }

    [Serializable]
    [XmlRoot("application-set", Namespace = "", IsNullable = false)]
    public partial class applicationset
    {
        [XmlElement("application")]
        public application[] application { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-regional", Namespace = "", IsNullable = false)]
    public partial class m1regional
    {
        [XmlArray("m1-1-forms")]
        [XmlArrayItem("form", IsNullable = false)]
        public form[] m11forms { get; set; }
        [XmlElement("m1-2-cover-letters")]
        public m12coverletters m12coverletters { get; set; }
        [XmlElement("m1-3-administrative-information")]
        public m13administrativeinformation m13administrativeinformation { get; set; }
        [XmlElement("m1-4-references")]
        public m14references m14references { get; set; }
        [XmlElement("m1-5-application-status")]
        public m15applicationstatus m15applicationstatus { get; set; }
        [XmlElement("m1-6-meetings")]
        public m16meetings m16meetings { get; set; }
        [XmlElement("m1-7-fast-track")]
        public m17fasttrack m17fasttrack { get; set; }
        [XmlElement("m1-8-special-protocol-assessment-request")]
        public m18specialprotocolassessmentrequest m18specialprotocolassessmentrequest { get; set; }
        [XmlElement("m1-9-pediatric-administrative-information")]
        public m19pediatricadministrativeinformation m19pediatricadministrativeinformation { get; set; }
        [XmlElement("m1-10-dispute-resolution")]
        public m110disputeresolution m110disputeresolution { get; set; }
        [XmlElement("m1-11-information-amendment-information-not-covered-under-modules-2-to-5")]
        public m111informationamendmentinformationnotcoveredundermodules2to5 m111informationamendmentinformationnotcoveredundermodules2to5 { get; set; }
        [XmlElement("m1-12-other-correspondence")]
        public m112othercorrespondence m112othercorrespondence { get; set; }
        [XmlElement("m1-13-annual-report")]
        public m113annualreport m113annualreport { get; set; }
        [XmlElement("m1-14-labeling")]
        public m114labeling m114labeling { get; set; }
        [XmlElement("m1-15-promotional-material")]
        public m115promotionalmaterial m115promotionalmaterial { get; set; }
        [XmlElement("m1-16-risk-management-plan")]
        public m116riskmanagementplan m116riskmanagementplan { get; set; }
        [XmlElement("m1-17-postmarketing-studies")]
        public m117postmarketingstudies m117postmarketingstudies { get; set; }
        [XmlElement("m1-18-proprietary-names")]
        public m118proprietarynames m118proprietarynames { get; set; }
        [XmlElement("m1-19-pre-eua-and-eua")]
        public m119preeuaandeua m119preeuaandeua { get; set; }
        [XmlElement("m1-20-general-investigational-plan-for-initial-ind")]
        public m120generalinvestigationalplanforinitialind m120generalinvestigationalplanforinitialind { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-cover-letters", Namespace = "", IsNullable = false)]
    public partial class m12coverletters
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-administrative-information", Namespace = "", IsNullable = false)]
    public partial class m13administrativeinformation
    {
        [XmlElement("m1-3-1-contact-sponsor-applicant-information")]
        public m131contactsponsorapplicantinformation[] m131contactsponsorapplicantinformation { get; set; }
        [XmlElement("m1-3-2-field-copy-certification")]
        public m132fieldcopycertification[] m132fieldcopycertification { get; set; }
        [XmlElement("m1-3-3-debarment-certification")]
        public m133debarmentcertification[] m133debarmentcertification { get; set; }
        [XmlElement("m1-3-4-financial-certification-and-disclosure")]
        public m134financialcertificationanddisclosure[] m134financialcertificationanddisclosure { get; set; }
        [XmlElement("m1-3-5-patent-and-exclusivity")]
        public m135patentandexclusivity[] m135patentandexclusivity { get; set; }
        [XmlElement("m1-3-6-tropical-disease-priority-review-voucher")]
        public m136tropicaldiseasepriorityreviewvoucher[] m136tropicaldiseasepriorityreviewvoucher { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-contact-sponsor-applicant-information", Namespace = "", IsNullable = false)]
    public partial class m131contactsponsorapplicantinformation
    {
        [XmlElement("m1-3-1-1-change-of-address-or-corporate-name")]
        public m1311changeofaddressorcorporatename[] m1311changeofaddressorcorporatename { get; set; }
        [XmlElement("m1-3-1-2-change-in-contact-agent")]
        public m1312changeincontactagent[] m1312changeincontactagent { get; set; }
        [XmlElement("m1-3-1-3-change-in-sponsor")]
        public m1313changeinsponsor[] m1313changeinsponsor { get; set; }
        [XmlElement("m1-3-1-4-transfer-of-obligation")]
        public m1314transferofobligation[] m1314transferofobligation { get; set; }
        [XmlElement("m1-3-1-5-change-in-ownership-of-an-application-or-reissuance-of-license")]
        public m1315changeinownershipofanapplicationorreissuanceoflicense[] m1315changeinownershipofanapplicationorreissuanceoflicense { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-1-change-of-address-or-corporate-name", Namespace = "", IsNullable = false)]
    public partial class m1311changeofaddressorcorporatename
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-2-change-in-contact-agent", Namespace = "", IsNullable = false)]
    public partial class m1312changeincontactagent
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-3-change-in-sponsor", Namespace = "", IsNullable = false)]
    public partial class m1313changeinsponsor
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-4-transfer-of-obligation", Namespace = "", IsNullable = false)]
    public partial class m1314transferofobligation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-5-change-in-ownership-of-an-application-or-reissuance-of-license", Namespace = "", IsNullable = false)]
    public partial class m1315changeinownershipofanapplicationorreissuanceoflicense
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-2-field-copy-certification", Namespace = "", IsNullable = false)]
    public partial class m132fieldcopycertification
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-3-debarment-certification", Namespace = "", IsNullable = false)]
    public partial class m133debarmentcertification
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-4-financial-certification-and-disclosure", Namespace = "", IsNullable = false)]
    public partial class m134financialcertificationanddisclosure
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-5-patent-and-exclusivity", Namespace = "", IsNullable = false)]
    public partial class m135patentandexclusivity
    {
        [XmlElement("m1-3-5-1-patent-information")]
        public m1351patentinformation[] m1351patentinformation { get; set; }
        [XmlElement("m1-3-5-2-patent-certification")]
        public m1352patentcertification[] m1352patentcertification { get; set; }
        [XmlElement("m1-3-5-3-exclusivity-claim")]
        public m1353exclusivityclaim[] m1353exclusivityclaim { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-5-1-patent-information", Namespace = "", IsNullable = false)]
    public partial class m1351patentinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-5-2-patent-certification", Namespace = "", IsNullable = false)]
    public partial class m1352patentcertification
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-5-3-exclusivity-claim", Namespace = "", IsNullable = false)]
    public partial class m1353exclusivityclaim
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-6-tropical-disease-priority-review-voucher", Namespace = "", IsNullable = false)]
    public partial class m136tropicaldiseasepriorityreviewvoucher
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-references", Namespace = "", IsNullable = false)]
    public partial class m14references
    {
        [XmlElement("m1-4-1-letter-of-authorization")]
        public m141letterofauthorization[] m141letterofauthorization { get; set; }
        [XmlElement("m1-4-2-statement-of-right-of-reference")]
        public m142statementofrightofreference[] m142statementofrightofreference { get; set; }
        [XmlElement("m1-4-3-list-of-authorized-persons-to-incorporate-by-reference")]
        public m143listofauthorizedpersonstoincorporatebyreference[] m143listofauthorizedpersonstoincorporatebyreference { get; set; }
        [XmlElement("m1-4-4-cross-reference-to-previously-submitted-information")]
        public m144crossreferencetopreviouslysubmittedinformation[] m144crossreferencetopreviouslysubmittedinformation { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-1-letter-of-authorization", Namespace = "", IsNullable = false)]
    public partial class m141letterofauthorization
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-2-statement-of-right-of-reference", Namespace = "", IsNullable = false)]
    public partial class m142statementofrightofreference
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-3-list-of-authorized-persons-to-incorporate-by-reference", Namespace = "", IsNullable = false)]
    public partial class m143listofauthorizedpersonstoincorporatebyreference
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-4-cross-reference-to-previously-submitted-information", Namespace = "", IsNullable = false)]
    public partial class m144crossreferencetopreviouslysubmittedinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-application-status", Namespace = "", IsNullable = false)]
    public partial class m15applicationstatus
    {
        [XmlElement("m1-5-1-withdrawal-of-an-ind")]
        public m151withdrawalofanind[] m151withdrawalofanind { get; set; }
        [XmlElement("m1-5-2-inactivation-request")]
        public m152inactivationrequest[] m152inactivationrequest { get; set; }
        [XmlElement("m1-5-3-reactivation-request")]
        public m153reactivationrequest[] m153reactivationrequest { get; set; }
        [XmlElement("m1-5-4-reinstatement-request")]
        public m154reinstatementrequest[] m154reinstatementrequest { get; set; }
        [XmlElement("m1-5-5-withdrawal-of-an-unapproved-bla-nda-anda-or-supplement")]
        public m155withdrawalofanunapprovedblandaandaorsupplement[] m155withdrawalofanunapprovedblandaandaorsupplement { get; set; }
        [XmlElement("m1-5-6-withdrawal-of-listed-drug")]
        public m156withdrawaloflisteddrug[] m156withdrawaloflisteddrug { get; set; }
        [XmlElement("m1-5-7-withdrawal-of-approval-of-an-application-or-revocation-of-license")]
        public m157withdrawalofapprovalofanapplicationorrevocationoflicense[] m157withdrawalofapprovalofanapplicationorrevocationoflicense { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-1-withdrawal-of-an-ind", Namespace = "", IsNullable = false)]
    public partial class m151withdrawalofanind
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-2-inactivation-request", Namespace = "", IsNullable = false)]
    public partial class m152inactivationrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-3-reactivation-request", Namespace = "", IsNullable = false)]
    public partial class m153reactivationrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-4-reinstatement-request", Namespace = "", IsNullable = false)]
    public partial class m154reinstatementrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-5-withdrawal-of-an-unapproved-bla-nda-anda-or-supplement", Namespace = "", IsNullable = false)]
    public partial class m155withdrawalofanunapprovedblandaandaorsupplement
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-6-withdrawal-of-listed-drug", Namespace = "", IsNullable = false)]
    public partial class m156withdrawaloflisteddrug
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-7-withdrawal-of-approval-of-an-application-or-revocation-of-license", Namespace = "", IsNullable = false)]
    public partial class m157withdrawalofapprovalofanapplicationorrevocationoflicense
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-meetings", Namespace = "", IsNullable = false)]
    public partial class m16meetings
    {
        [XmlElement("m1-6-1-meeting-request")]
        public m161meetingrequest[] m161meetingrequest { get; set; }
        [XmlElement("m1-6-2-meeting-background-materials")]
        public m162meetingbackgroundmaterials[] m162meetingbackgroundmaterials { get; set; }
        [XmlElement("m1-6-3-correspondence-regarding-meetings")]
        public m163correspondenceregardingmeetings[] m163correspondenceregardingmeetings { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-1-meeting-request", Namespace = "", IsNullable = false)]
    public partial class m161meetingrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-2-meeting-background-materials", Namespace = "", IsNullable = false)]
    public partial class m162meetingbackgroundmaterials
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-3-correspondence-regarding-meetings", Namespace = "", IsNullable = false)]
    public partial class m163correspondenceregardingmeetings
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-fast-track", Namespace = "", IsNullable = false)]
    public partial class m17fasttrack
    {
        [XmlElement("m1-7-1-fast-track-designation-request")]
        public m171fasttrackdesignationrequest[] m171fasttrackdesignationrequest { get; set; }
        [XmlElement("m1-7-2-fast-track-designation-withdrawal-request")]
        public m172fasttrackdesignationwithdrawalrequest[] m172fasttrackdesignationwithdrawalrequest { get; set; }
        [XmlElement("m1-7-3-rolling-review-request")]
        public m173rollingreviewrequest[] m173rollingreviewrequest { get; set; }
        [XmlElement("m1-7-4-correspondence-regarding-fast-track-rolling-review")]
        public m174correspondenceregardingfasttrackrollingreview[] m174correspondenceregardingfasttrackrollingreview { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-1-fast-track-designation-request", Namespace = "", IsNullable = false)]
    public partial class m171fasttrackdesignationrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-2-fast-track-designation-withdrawal-request", Namespace = "", IsNullable = false)]
    public partial class m172fasttrackdesignationwithdrawalrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-3-rolling-review-request", Namespace = "", IsNullable = false)]
    public partial class m173rollingreviewrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-4-correspondence-regarding-fast-track-rolling-review", Namespace = "", IsNullable = false)]
    public partial class m174correspondenceregardingfasttrackrollingreview
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-special-protocol-assessment-request", Namespace = "", IsNullable = false)]
    public partial class m18specialprotocolassessmentrequest
    {
        [XmlElement("m1-8-1-clinical-study")]
        public m181clinicalstudy[] m181clinicalstudy { get; set; }
        [XmlElement("m1-8-2-carcinogenicity-study")]
        public m182carcinogenicitystudy[] m182carcinogenicitystudy { get; set; }
        [XmlElement("m1-8-3-stability-study")]
        public m183stabilitystudy[] m183stabilitystudy { get; set; }
        [XmlElement("m1-8-4-animal-efficacy-study-for-approval-under-the-animal-rule")]
        public m184animalefficacystudyforapprovalundertheanimalrule[] m184animalefficacystudyforapprovalundertheanimalrule { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-1-clinical-study", Namespace = "", IsNullable = false)]
    public partial class m181clinicalstudy
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-2-carcinogenicity-study", Namespace = "", IsNullable = false)]
    public partial class m182carcinogenicitystudy
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-3-stability-study", Namespace = "", IsNullable = false)]
    public partial class m183stabilitystudy
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-4-animal-efficacy-study-for-approval-under-the-animal-rule", Namespace = "", IsNullable = false)]
    public partial class m184animalefficacystudyforapprovalundertheanimalrule
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-pediatric-administrative-information", Namespace = "", IsNullable = false)]
    public partial class m19pediatricadministrativeinformation
    {
        [XmlElement("m1-9-1-request-for-waiver-of-pediatric-studies")]
        public m191requestforwaiverofpediatricstudies[] m191requestforwaiverofpediatricstudies { get; set; }
        [XmlElement("m1-9-2-request-for-deferral-of-pediatric-studies")]
        public m192requestfordeferralofpediatricstudies[] m192requestfordeferralofpediatricstudies { get; set; }
        [XmlElement("m1-9-3-request-for-pediatric-exclusivity-determination")]
        public m193requestforpediatricexclusivitydetermination[] m193requestforpediatricexclusivitydetermination { get; set; }
        [XmlElement("m1-9-4-proposed-pediatric-study-request-and-amendments")]
        public m194proposedpediatricstudyrequestandamendments[] m194proposedpediatricstudyrequestandamendments { get; set; }
        [XmlElement("m1-9-6-other-correspondence-regarding-pediatric-exclusivity-or-study-plans")]
        public m196othercorrespondenceregardingpediatricexclusivityorstudyplans[] m196othercorrespondenceregardingpediatricexclusivityorstudyplans { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-1-request-for-waiver-of-pediatric-studies", Namespace = "", IsNullable = false)]
    public partial class m191requestforwaiverofpediatricstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-2-request-for-deferral-of-pediatric-studies", Namespace = "", IsNullable = false)]
    public partial class m192requestfordeferralofpediatricstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-3-request-for-pediatric-exclusivity-determination", Namespace = "", IsNullable = false)]
    public partial class m193requestforpediatricexclusivitydetermination
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-4-proposed-pediatric-study-request-and-amendments", Namespace = "", IsNullable = false)]
    public partial class m194proposedpediatricstudyrequestandamendments
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-6-other-correspondence-regarding-pediatric-exclusivity-or-study-plans", Namespace = "", IsNullable = false)]
    public partial class m196othercorrespondenceregardingpediatricexclusivityorstudyplans
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-10-dispute-resolution", Namespace = "", IsNullable = false)]
    public partial class m110disputeresolution
    {
        [XmlElement("m1-10-1-request-for-dispute-resolution")]
        public m1101requestfordisputeresolution[] m1101requestfordisputeresolution { get; set; }
        [XmlElement("m1-10-2-correspondence-related-to-dispute-resolution")]
        public m1102correspondencerelatedtodisputeresolution[] m1102correspondencerelatedtodisputeresolution { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-10-1-request-for-dispute-resolution", Namespace = "", IsNullable = false)]
    public partial class m1101requestfordisputeresolution
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-10-2-correspondence-related-to-dispute-resolution", Namespace = "", IsNullable = false)]
    public partial class m1102correspondencerelatedtodisputeresolution
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-information-amendment-information-not-covered-under-modules-2-to-5", Namespace = "", IsNullable = false)]
    public partial class m111informationamendmentinformationnotcoveredundermodules2to5
    {
        [XmlElement("m1-11-1-quality-information-amendment")]
        public m1111qualityinformationamendment[] m1111qualityinformationamendment { get; set; }
        [XmlElement("m1-11-2-nonclinical-information-amendment")]
        public m1112nonclinicalinformationamendment[] m1112nonclinicalinformationamendment { get; set; }
        [XmlElement("m1-11-3-clinical-information-amendment")]
        public m1113clinicalinformationamendment[] m1113clinicalinformationamendment { get; set; }
        [XmlElement("m1-11-4-multiple-module-information-amendment")]
        public m1114multiplemoduleinformationamendment[] m1114multiplemoduleinformationamendment { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-1-quality-information-amendment", Namespace = "", IsNullable = false)]
    public partial class m1111qualityinformationamendment
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-2-nonclinical-information-amendment", Namespace = "", IsNullable = false)]
    public partial class m1112nonclinicalinformationamendment
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-3-clinical-information-amendment", Namespace = "", IsNullable = false)]
    public partial class m1113clinicalinformationamendment
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-4-multiple-module-information-amendment", Namespace = "", IsNullable = false)]
    public partial class m1114multiplemoduleinformationamendment
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-other-correspondence", Namespace = "", IsNullable = false)]
    public partial class m112othercorrespondence
    {
        [XmlElement("m1-12-1-pre-ind-correspondence")]
        public m1121preindcorrespondence[] m1121preindcorrespondence { get; set; }
        [XmlElement("m1-12-2-request-to-charge-for-clinical-trial")]
        public m1122requesttochargeforclinicaltrial[] m1122requesttochargeforclinicaltrial { get; set; }
        [XmlElement("m1-12-3-request-to-charge-for-expanded-access")]
        public m1123requesttochargeforexpandedaccess[] m1123requesttochargeforexpandedaccess { get; set; }
        [XmlElement("m1-12-4-request-for-comments-and-advice")]
        public m1124requestforcommentsandadvice[] m1124requestforcommentsandadvice { get; set; }
        [XmlElement("m1-12-5-request-for-a-waiver")]
        public m1125requestforawaiver[] m1125requestforawaiver { get; set; }
        [XmlElement("m1-12-6-exception-from-informed-consent-for-emergency-research")]
        public m1126exceptionfrominformedconsentforemergencyresearch[] m1126exceptionfrominformedconsentforemergencyresearch { get; set; }
        [XmlElement("m1-12-7-public-disclosure-statement-for-exception-from-informed-consent-for-emerg" +
            "ency-research")]
        public m1127publicdisclosurestatementforexceptionfrominformedconsentforemergencyresearch[] m1127publicdisclosurestatementforexceptionfrominformedconsentforemergencyresearch { get; set; }
        [XmlElement("m1-12-8-correspondence-regarding-exception-from-informed-consent-for-emergency-re" +
            "search")]
        public m1128correspondenceregardingexceptionfrominformedconsentforemergencyresearch[] m1128correspondenceregardingexceptionfrominformedconsentforemergencyresearch { get; set; }
        [XmlElement("m1-12-9-notification-of-discontinuation-of-clinical-trial")]
        public m1129notificationofdiscontinuationofclinicaltrial[] m1129notificationofdiscontinuationofclinicaltrial { get; set; }
        [XmlElement("m1-12-10-generic-drug-enforcement-act-statement")]
        public m11210genericdrugenforcementactstatement[] m11210genericdrugenforcementactstatement { get; set; }
        [XmlElement("m1-12-11-anda-basis-for-submission-statement")]
        public m11211andabasisforsubmissionstatement[] m11211andabasisforsubmissionstatement { get; set; }
        [XmlElement("m1-12-12-comparison-of-generic-drug-and-reference-listed-drug")]
        public m11212comparisonofgenericdrugandreferencelisteddrug[] m11212comparisonofgenericdrugandreferencelisteddrug { get; set; }
        [XmlElement("m1-12-13-request-for-waiver-for-in-vivo-studies")]
        public m11213requestforwaiverforinvivostudies[] m11213requestforwaiverforinvivostudies { get; set; }
        [XmlElement("m1-12-14-environmental-analysis")]
        public m11214environmentalanalysis[] m11214environmentalanalysis { get; set; }
        [XmlElement("m1-12-15-request-for-waiver-of-in-vivo-bioavailability-studies")]
        public m11215requestforwaiverofinvivobioavailabilitystudies[] m11215requestforwaiverofinvivobioavailabilitystudies { get; set; }
        [XmlElement("m1-12-16-field-alert-reports")]
        public m11216fieldalertreports[] m11216fieldalertreports { get; set; }
        [XmlElement("m1-12-17-orphan-drug-designation")]
        public m11217orphandrugdesignation[] m11217orphandrugdesignation { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-1-pre-ind-correspondence", Namespace = "", IsNullable = false)]
    public partial class m1121preindcorrespondence
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-2-request-to-charge-for-clinical-trial", Namespace = "", IsNullable = false)]
    public partial class m1122requesttochargeforclinicaltrial
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-3-request-to-charge-for-expanded-access", Namespace = "", IsNullable = false)]
    public partial class m1123requesttochargeforexpandedaccess
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-4-request-for-comments-and-advice", Namespace = "", IsNullable = false)]
    public partial class m1124requestforcommentsandadvice
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-5-request-for-a-waiver", Namespace = "", IsNullable = false)]
    public partial class m1125requestforawaiver
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-6-exception-from-informed-consent-for-emergency-research", Namespace = "", IsNullable = false)]
    public partial class m1126exceptionfrominformedconsentforemergencyresearch
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-7-public-disclosure-statement-for-exception-from-informed-consent-for-emerg" +
        "ency-research", Namespace = "", IsNullable = false)]
    public partial class m1127publicdisclosurestatementforexceptionfrominformedconsentforemergencyresearch
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-8-correspondence-regarding-exception-from-informed-consent-for-emergency-re" +
        "search", Namespace = "", IsNullable = false)]
    public partial class m1128correspondenceregardingexceptionfrominformedconsentforemergencyresearch
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-9-notification-of-discontinuation-of-clinical-trial", Namespace = "", IsNullable = false)]
    public partial class m1129notificationofdiscontinuationofclinicaltrial
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-10-generic-drug-enforcement-act-statement", Namespace = "", IsNullable = false)]
    public partial class m11210genericdrugenforcementactstatement
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-11-anda-basis-for-submission-statement", Namespace = "", IsNullable = false)]
    public partial class m11211andabasisforsubmissionstatement
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-12-comparison-of-generic-drug-and-reference-listed-drug", Namespace = "", IsNullable = false)]
    public partial class m11212comparisonofgenericdrugandreferencelisteddrug
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-13-request-for-waiver-for-in-vivo-studies", Namespace = "", IsNullable = false)]
    public partial class m11213requestforwaiverforinvivostudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-14-environmental-analysis", Namespace = "", IsNullable = false)]
    public partial class m11214environmentalanalysis
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-15-request-for-waiver-of-in-vivo-bioavailability-studies", Namespace = "", IsNullable = false)]
    public partial class m11215requestforwaiverofinvivobioavailabilitystudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-16-field-alert-reports", Namespace = "", IsNullable = false)]
    public partial class m11216fieldalertreports
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-17-orphan-drug-designation", Namespace = "", IsNullable = false)]
    public partial class m11217orphandrugdesignation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-annual-report", Namespace = "", IsNullable = false)]
    public partial class m113annualreport
    {
        [XmlElement("m1-13-1-summary-for-nonclinical-studies")]
        public m1131summaryfornonclinicalstudies[] m1131summaryfornonclinicalstudies { get; set; }
        [XmlElement("m1-13-2-summary-of-clinical-pharmacology-information")]
        public m1132summaryofclinicalpharmacologyinformation[] m1132summaryofclinicalpharmacologyinformation { get; set; }
        [XmlElement("m1-13-3-summary-of-safety-information")]
        public m1133summaryofsafetyinformation[] m1133summaryofsafetyinformation { get; set; }
        [XmlElement("m1-13-4-summary-of-labeling-changes")]
        public m1134summaryoflabelingchanges[] m1134summaryoflabelingchanges { get; set; }
        [XmlElement("m1-13-5-summary-of-manufacturing-changes")]
        public m1135summaryofmanufacturingchanges[] m1135summaryofmanufacturingchanges { get; set; }
        [XmlElement("m1-13-6-summary-of-microbiological-changes")]
        public m1136summaryofmicrobiologicalchanges[] m1136summaryofmicrobiologicalchanges { get; set; }
        [XmlElement("m1-13-7-summary-of-other-significant-new-information")]
        public m1137summaryofothersignificantnewinformation[] m1137summaryofothersignificantnewinformation { get; set; }
        [XmlElement("m1-13-8-individual-study-information")]
        public m1138individualstudyinformation[] m1138individualstudyinformation { get; set; }
        [XmlElement("m1-13-9-general-investigational-plan")]
        public m1139generalinvestigationalplan[] m1139generalinvestigationalplan { get; set; }
        [XmlElement("m1-13-10-foreign-marketing")]
        public m11310foreignmarketing[] m11310foreignmarketing { get; set; }
        [XmlElement("m1-13-11-distribution-data")]
        public m11311distributiondata[] m11311distributiondata { get; set; }
        [XmlElement("m1-13-12-status-of-postmarketing-study-commitments-and-requirements")]
        public m11312statusofpostmarketingstudycommitmentsandrequirements[] m11312statusofpostmarketingstudycommitmentsandrequirements { get; set; }
        [XmlElement("m1-13-13-status-of-other-postmarketing-studies-and-requirements")]
        public m11313statusofotherpostmarketingstudiesandrequirements[] m11313statusofotherpostmarketingstudiesandrequirements { get; set; }
        [XmlElement("m1-13-14-log-of-outstanding-regulatory-business")]
        public m11314logofoutstandingregulatorybusiness[] m11314logofoutstandingregulatorybusiness { get; set; }
        [XmlElement("m1-13-15-development-safety-update-report-dsur")]
        public m11315developmentsafetyupdatereportdsur[] m11315developmentsafetyupdatereportdsur { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-1-summary-for-nonclinical-studies", Namespace = "", IsNullable = false)]
    public partial class m1131summaryfornonclinicalstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-2-summary-of-clinical-pharmacology-information", Namespace = "", IsNullable = false)]
    public partial class m1132summaryofclinicalpharmacologyinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-3-summary-of-safety-information", Namespace = "", IsNullable = false)]
    public partial class m1133summaryofsafetyinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-4-summary-of-labeling-changes", Namespace = "", IsNullable = false)]
    public partial class m1134summaryoflabelingchanges
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-5-summary-of-manufacturing-changes", Namespace = "", IsNullable = false)]
    public partial class m1135summaryofmanufacturingchanges
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-6-summary-of-microbiological-changes", Namespace = "", IsNullable = false)]
    public partial class m1136summaryofmicrobiologicalchanges
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-7-summary-of-other-significant-new-information", Namespace = "", IsNullable = false)]
    public partial class m1137summaryofothersignificantnewinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-8-individual-study-information", Namespace = "", IsNullable = false)]
    public partial class m1138individualstudyinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-9-general-investigational-plan", Namespace = "", IsNullable = false)]
    public partial class m1139generalinvestigationalplan
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-10-foreign-marketing", Namespace = "", IsNullable = false)]
    public partial class m11310foreignmarketing
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-11-distribution-data", Namespace = "", IsNullable = false)]
    public partial class m11311distributiondata
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-12-status-of-postmarketing-study-commitments-and-requirements", Namespace = "", IsNullable = false)]
    public partial class m11312statusofpostmarketingstudycommitmentsandrequirements
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-13-status-of-other-postmarketing-studies-and-requirements", Namespace = "", IsNullable = false)]
    public partial class m11313statusofotherpostmarketingstudiesandrequirements
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-14-log-of-outstanding-regulatory-business", Namespace = "", IsNullable = false)]
    public partial class m11314logofoutstandingregulatorybusiness
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-15-development-safety-update-report-dsur", Namespace = "", IsNullable = false)]
    public partial class m11315developmentsafetyupdatereportdsur
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-labeling", Namespace = "", IsNullable = false)]
    public partial class m114labeling
    {
        [XmlElement("m1-14-1-draft-labeling")]
        public m1141draftlabeling[] m1141draftlabeling { get; set; }
        [XmlElement("m1-14-2-final-labeling")]
        public m1142finallabeling[] m1142finallabeling { get; set; }
        [XmlElement("m1-14-3-listed-drug-labeling")]
        public m1143listeddruglabeling[] m1143listeddruglabeling { get; set; }
        [XmlElement("m1-14-4-investigational-drug-labeling")]
        public m1144investigationaldruglabeling[] m1144investigationaldruglabeling { get; set; }
        [XmlElement("m1-14-5-foreign-labeling")]
        public m1145foreignlabeling[] m1145foreignlabeling { get; set; }
        [XmlElement("m1-14-6-product-labeling-for-2253-submissions")]
        public m1146productlabelingfor2253submissions[] m1146productlabelingfor2253submissions { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-draft-labeling", Namespace = "", IsNullable = false)]
    public partial class m1141draftlabeling
    {
        [XmlElement("m1-14-1-1-draft-carton-and-container-labels")]
        public m11411draftcartonandcontainerlabels[] m11411draftcartonandcontainerlabels { get; set; }
        [XmlElement("m1-14-1-2-annotated-draft-labeling-text")]
        public m11412annotateddraftlabelingtext[] m11412annotateddraftlabelingtext { get; set; }
        [XmlElement("m1-14-1-3-draft-labeling-text")]
        public m11413draftlabelingtext[] m11413draftlabelingtext { get; set; }
        [XmlElement("m1-14-1-4-label-comprehension-studies")]
        public m11414labelcomprehensionstudies[] m11414labelcomprehensionstudies { get; set; }
        [XmlElement("m1-14-1-5-labeling-history")]
        public m11415labelinghistory[] m11415labelinghistory { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-1-draft-carton-and-container-labels", Namespace = "", IsNullable = false)]
    public partial class m11411draftcartonandcontainerlabels
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-2-annotated-draft-labeling-text", Namespace = "", IsNullable = false)]
    public partial class m11412annotateddraftlabelingtext
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-3-draft-labeling-text", Namespace = "", IsNullable = false)]
    public partial class m11413draftlabelingtext
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-4-label-comprehension-studies", Namespace = "", IsNullable = false)]
    public partial class m11414labelcomprehensionstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-5-labeling-history", Namespace = "", IsNullable = false)]
    public partial class m11415labelinghistory
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-2-final-labeling", Namespace = "", IsNullable = false)]
    public partial class m1142finallabeling
    {
        [XmlElement("m1-14-2-1-final-carton-or-container-labels")]
        public m11421finalcartonorcontainerlabels[] m11421finalcartonorcontainerlabels { get; set; }
        [XmlElement("m1-14-2-2-final-package-insert-package-inserts-patient-information-medication-gui" +
            "des")]
        public m11422finalpackageinsertpackageinsertspatientinformationmedicationguides[] m11422finalpackageinsertpackageinsertspatientinformationmedicationguides { get; set; }
        [XmlElement("m1-14-2-3-final-labeling-text")]
        public m11423finallabelingtext[] m11423finallabelingtext { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-2-1-final-carton-or-container-labels", Namespace = "", IsNullable = false)]
    public partial class m11421finalcartonorcontainerlabels
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-2-2-final-package-insert-package-inserts-patient-information-medication-gui" +
        "des", Namespace = "", IsNullable = false)]
    public partial class m11422finalpackageinsertpackageinsertspatientinformationmedicationguides
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-2-3-final-labeling-text", Namespace = "", IsNullable = false)]
    public partial class m11423finallabelingtext
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-3-listed-drug-labeling", Namespace = "", IsNullable = false)]
    public partial class m1143listeddruglabeling
    {
        [XmlElement("m1-14-3-1-annotated-comparison-with-listed-drug")]
        public m11431annotatedcomparisonwithlisteddrug[] m11431annotatedcomparisonwithlisteddrug { get; set; }
        [XmlElement("m1-14-3-2-approved-labeling-text-for-listed-drug")]
        public m11432approvedlabelingtextforlisteddrug[] m11432approvedlabelingtextforlisteddrug { get; set; }
        [XmlElement("m1-14-3-3-labeling-text-for-reference-listed-drug")]
        public m11433labelingtextforreferencelisteddrug[] m11433labelingtextforreferencelisteddrug { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-3-1-annotated-comparison-with-listed-drug", Namespace = "", IsNullable = false)]
    public partial class m11431annotatedcomparisonwithlisteddrug
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-3-2-approved-labeling-text-for-listed-drug", Namespace = "", IsNullable = false)]
    public partial class m11432approvedlabelingtextforlisteddrug
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-3-3-labeling-text-for-reference-listed-drug", Namespace = "", IsNullable = false)]
    public partial class m11433labelingtextforreferencelisteddrug
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-4-investigational-drug-labeling", Namespace = "", IsNullable = false)]
    public partial class m1144investigationaldruglabeling
    {
        [XmlElement("m1-14-4-1-investigational-brochure")]
        public m11441investigationalbrochure[] m11441investigationalbrochure { get; set; }
        [XmlElement("m1-14-4-2-investigational-drug-labeling")]
        public m11442investigationaldruglabeling[] m11442investigationaldruglabeling { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-4-1-investigational-brochure", Namespace = "", IsNullable = false)]
    public partial class m11441investigationalbrochure
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-4-2-investigational-drug-labeling", Namespace = "", IsNullable = false)]
    public partial class m11442investigationaldruglabeling
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-5-foreign-labeling", Namespace = "", IsNullable = false)]
    public partial class m1145foreignlabeling
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-6-product-labeling-for-2253-submissions", Namespace = "", IsNullable = false)]
    public partial class m1146productlabelingfor2253submissions
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-promotional-material", Namespace = "", IsNullable = false)]
    public partial class m115promotionalmaterial
    {
        [XmlElement("m1-15-1-correspondence-relating-to-promotional-materials")]
        public m1151correspondencerelatingtopromotionalmaterials m1151correspondencerelatingtopromotionalmaterials { get; set; }
        [XmlElement("m1-15-2-materials")]
        public m1152materials m1152materials { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
        [XmlAttribute("promotional-material-audience-type")]
        public string promotionalmaterialaudiencetype { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-correspondence-relating-to-promotional-materials", Namespace = "", IsNullable = false)]
    public partial class m1151correspondencerelatingtopromotionalmaterials
    {
        [XmlElement("m1-15-1-1-request-for-advisory-comments-on-launch-materials")]
        public m11511requestforadvisorycommentsonlaunchmaterials m11511requestforadvisorycommentsonlaunchmaterials { get; set; }
        [XmlElement("m1-15-1-2-request-for-advisory-comments-on-non-launch-materials")]
        public m11512requestforadvisorycommentsonnonlaunchmaterials m11512requestforadvisorycommentsonnonlaunchmaterials { get; set; }
        [XmlElement("m1-15-1-3-pre-submission-of-launch-promotional-materials-for-accelerated-approval" +
            "-products")]
        public m11513presubmissionoflaunchpromotionalmaterialsforacceleratedapprovalproducts m11513presubmissionoflaunchpromotionalmaterialsforacceleratedapprovalproducts { get; set; }
        [XmlElement("m1-15-1-4-pre-submission-of-non-launch-promotional-materials-for-accelerated-appr" +
            "oval-products")]
        public m11514presubmissionofnonlaunchpromotionalmaterialsforacceleratedapprovalproducts m11514presubmissionofnonlaunchpromotionalmaterialsforacceleratedapprovalproducts { get; set; }
        [XmlElement("m1-15-1-5-pre-dissemination-review-of-television-ads")]
        public m11515predisseminationreviewoftelevisionads m11515predisseminationreviewoftelevisionads { get; set; }
        [XmlElement("m1-15-1-6-response-to-untitled-letter-or-warning-letter")]
        public m11516responsetountitledletterorwarningletter m11516responsetountitledletterorwarningletter { get; set; }
        [XmlElement("m1-15-1-7-response-to-information-request")]
        public m11517responsetoinformationrequest m11517responsetoinformationrequest { get; set; }
        [XmlElement("m1-15-1-8-correspondence-accompanying-materials-previously-missing-or-rejected")]
        public m11518correspondenceaccompanyingmaterialspreviouslymissingorrejected m11518correspondenceaccompanyingmaterialspreviouslymissingorrejected { get; set; }
        [XmlElement("m1-15-1-9-withdrawal-request")]
        public m11519withdrawalrequest m11519withdrawalrequest { get; set; }
        [XmlElement("m1-15-1-10-submission-of-annotated-references")]
        public m115110submissionofannotatedreferences m115110submissionofannotatedreferences { get; set; }
        [XmlElement("m1-15-1-11-general-correspondence")]
        public m115111generalcorrespondence m115111generalcorrespondence { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-1-request-for-advisory-comments-on-launch-materials", Namespace = "", IsNullable = false)]
    public partial class m11511requestforadvisorycommentsonlaunchmaterials
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-2-request-for-advisory-comments-on-non-launch-materials", Namespace = "", IsNullable = false)]
    public partial class m11512requestforadvisorycommentsonnonlaunchmaterials
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-3-pre-submission-of-launch-promotional-materials-for-accelerated-approval" +
        "-products", Namespace = "", IsNullable = false)]
    public partial class m11513presubmissionoflaunchpromotionalmaterialsforacceleratedapprovalproducts
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-4-pre-submission-of-non-launch-promotional-materials-for-accelerated-appr" +
        "oval-products", Namespace = "", IsNullable = false)]
    public partial class m11514presubmissionofnonlaunchpromotionalmaterialsforacceleratedapprovalproducts
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-5-pre-dissemination-review-of-television-ads", Namespace = "", IsNullable = false)]
    public partial class m11515predisseminationreviewoftelevisionads
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-6-response-to-untitled-letter-or-warning-letter", Namespace = "", IsNullable = false)]
    public partial class m11516responsetountitledletterorwarningletter
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-7-response-to-information-request", Namespace = "", IsNullable = false)]
    public partial class m11517responsetoinformationrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-8-correspondence-accompanying-materials-previously-missing-or-rejected", Namespace = "", IsNullable = false)]
    public partial class m11518correspondenceaccompanyingmaterialspreviouslymissingorrejected
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-9-withdrawal-request", Namespace = "", IsNullable = false)]
    public partial class m11519withdrawalrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-10-submission-of-annotated-references", Namespace = "", IsNullable = false)]
    public partial class m115110submissionofannotatedreferences
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-1-11-general-correspondence", Namespace = "", IsNullable = false)]
    public partial class m115111generalcorrespondence
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-2-materials", Namespace = "", IsNullable = false)]
    public partial class m1152materials
    {
        [XmlElement("m1-15-2-1-material")]
        public m11521material[] m11521material { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
        [XmlAttribute("promotional-material-doc-type")]
        public string promotionalmaterialdoctype { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-2-1-material", Namespace = "", IsNullable = false)]
    public partial class m11521material
    {
        [XmlElement("m1-15-2-1-1-clean-version")]
        public m115211cleanversion m115211cleanversion { get; set; }
        [XmlElement("m1-15-2-1-2-annotated-version")]
        public m115212annotatedversion m115212annotatedversion { get; set; }
        [XmlElement("m1-15-2-1-3-annotated-labeling-version")]
        public m115213annotatedlabelingversion m115213annotatedlabelingversion { get; set; }
        [XmlElement("m1-15-2-1-4-annotated-references")]
        public m115214annotatedreferences m115214annotatedreferences { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
        [XmlAttribute("promotional-material-type")]
        public string promotionalmaterialtype { get; set; }
        [XmlAttribute("material-id")]
        public string materialid { get; set; }
        [XmlAttribute("issue-date")]
        public string issuedate { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-2-1-1-clean-version", Namespace = "", IsNullable = false)]
    public partial class m115211cleanversion
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-2-1-2-annotated-version", Namespace = "", IsNullable = false)]
    public partial class m115212annotatedversion
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-2-1-3-annotated-labeling-version", Namespace = "", IsNullable = false)]
    public partial class m115213annotatedlabelingversion
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-2-1-4-annotated-references", Namespace = "", IsNullable = false)]
    public partial class m115214annotatedreferences
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-16-risk-management-plan", Namespace = "", IsNullable = false)]
    public partial class m116riskmanagementplan
    {
        [XmlElement("m1-16-1-risk-management-non-rems")]
        public m1161riskmanagementnonrems m1161riskmanagementnonrems { get; set; }
        [XmlElement("m1-16-2-risk-evaluation-and-mitigation-strategies-rems")]
        public m1162riskevaluationandmitigationstrategiesrems m1162riskevaluationandmitigationstrategiesrems { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-16-1-risk-management-non-rems", Namespace = "", IsNullable = false)]
    public partial class m1161riskmanagementnonrems
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-16-2-risk-evaluation-and-mitigation-strategies-rems", Namespace = "", IsNullable = false)]
    public partial class m1162riskevaluationandmitigationstrategiesrems
    {
        [XmlElement("m1-16-2-1-final-rems")]
        public m11621finalrems m11621finalrems { get; set; }
        [XmlElement("m1-16-2-2-draft-rems")]
        public m11622draftrems m11622draftrems { get; set; }
        [XmlElement("m1-16-2-3-rems-assessment")]
        public m11623remsassessment m11623remsassessment { get; set; }
        [XmlElement("m1-16-2-4-rems-assessment-methodology")]
        public m11624remsassessmentmethodology m11624remsassessmentmethodology { get; set; }
        [XmlElement("m1-16-2-5-rems-correspondence")]
        public m11625remscorrespondence m11625remscorrespondence { get; set; }
        [XmlElement("m1-16-2-6-rems-modification-history")]
        public m11626remsmodificationhistory m11626remsmodificationhistory { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-16-2-1-final-rems", Namespace = "", IsNullable = false)]
    public partial class m11621finalrems
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-16-2-2-draft-rems", Namespace = "", IsNullable = false)]
    public partial class m11622draftrems
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-16-2-3-rems-assessment", Namespace = "", IsNullable = false)]
    public partial class m11623remsassessment
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-16-2-4-rems-assessment-methodology", Namespace = "", IsNullable = false)]
    public partial class m11624remsassessmentmethodology
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-16-2-5-rems-correspondence", Namespace = "", IsNullable = false)]
    public partial class m11625remscorrespondence
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-16-2-6-rems-modification-history", Namespace = "", IsNullable = false)]
    public partial class m11626remsmodificationhistory
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-17-postmarketing-studies", Namespace = "", IsNullable = false)]
    public partial class m117postmarketingstudies
    {
        [XmlElement("m1-17-1-correspondence-regarding-postmarketing-commitments")]
        public m1171correspondenceregardingpostmarketingcommitments[] m1171correspondenceregardingpostmarketingcommitments { get; set; }
        [XmlElement("m1-17-2-correspondence-regarding-postmarketing-requirements")]
        public m1172correspondenceregardingpostmarketingrequirements[] m1172correspondenceregardingpostmarketingrequirements { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-17-1-correspondence-regarding-postmarketing-commitments", Namespace = "", IsNullable = false)]
    public partial class m1171correspondenceregardingpostmarketingcommitments
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-17-2-correspondence-regarding-postmarketing-requirements", Namespace = "", IsNullable = false)]
    public partial class m1172correspondenceregardingpostmarketingrequirements
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-18-proprietary-names", Namespace = "", IsNullable = false)]
    public partial class m118proprietarynames
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-19-pre-eua-and-eua", Namespace = "", IsNullable = false)]
    public partial class m119preeuaandeua
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-20-general-investigational-plan-for-initial-ind", Namespace = "", IsNullable = false)]
    public partial class m120generalinvestigationalplanforinitialind
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-1-forms", Namespace = "", IsNullable = false)]
    public partial class m11forms
    {
        [XmlElement("form")]
        public form[] form { get; set; }
    }

    [Serializable]
    [XmlRoot("fda-regional", Namespace = "http://www.ich.org/fda", IsNullable = false)]
    public partial class fdaregional
    {
        public fdaregional()
        {
            this.dtdversion = "3.3";
        }
        [XmlElement(Namespace = "")]
        public admin admin { get; set; }
        [XmlElement("m1-regional", Namespace = "")]
        public m1regional m1regional { get; set; }

        public string lang { get; set; }
        [XmlAttribute("dtd-version")]
        public string dtdversion { get; set; }
    }
}
