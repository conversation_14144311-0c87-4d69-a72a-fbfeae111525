﻿using Microsoft.AspNetCore.Mvc.ModelBinding;
using NSubstitute;
using PharmaLex.SmartTRACE.Entities;
namespace PharmaLex.SmartTRACE.Web.ModelsTests.ModelBinders
{
    public class DecimalBinderTests
    {
        DecimalBinder decimalBinder;
        ModelBindingContext bindingContext;
        public DecimalBinderTests()
        {
            bindingContext = Substitute.For<ModelBindingContext>();
        }
        [Fact]
        public void BindModelAsync_ReturnTask_Value_IsNull()
        {
            decimalBinder = new DecimalBinder();
            var result = decimalBinder.BindModelAsync(bindingContext);
            Assert.NotNull(result);
            Assert.Equal("RanToCompletion", result.Status.ToString());
        }
        [Fact]
        public void BindModelAsync_ReturnTask_Value_Is_Not_Null()
        {
            decimalBinder = new DecimalBinder();
            var ModelName = new ValueProviderResult("123");
            bindingContext.ValueProvider.GetValue(bindingContext.ModelName).Returns(ModelName);
            var result = decimalBinder.BindModelAsync(bindingContext);
            Assert.NotNull(result);
            Assert.Equal("RanToCompletion", result.Status.ToString());
        }
        [Fact]
        public void BindModelAsync_ReturnTask_Exception()
        {
            decimalBinder = new DecimalBinder();
            var ModelName = new ValueProviderResult("Test");
            bindingContext.ValueProvider.GetValue(bindingContext.ModelName).Returns(ModelName);
            var result = decimalBinder.BindModelAsync(bindingContext);
            Assert.NotNull(result);
            Assert.Equal("RanToCompletion", result.Status.ToString());
        }
    }
}
