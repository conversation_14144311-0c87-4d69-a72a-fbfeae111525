﻿@model PharmaLex.SmartTRACE.Web.Models.UserClientsModel
@using Microsoft.AspNetCore.Authorization
@using PharmaLex.Caching.Data
@using PharmaLex.SmartTRACE.Entities
@using PharmaLex.SmartTRACE.Web.Helpers
@using PharmaLex.SmartTRACE.Entities.Enums
@inject IDistributedCacheServiceFactory cache
@inject IAuthorizationService AuthorizationService
@{
    ViewData["Title"] = "Manage users";

    IEnumerable<Claim> _claims = await cache.CreateEntity<Claim>().WhereAsync(x => x.Name == "ExternalEditor" || x.Name == "PrivilegedExternalEditor");
    var claims = _claims.Select(c => new { Id = c.Id, Name = c.Name });
}

<div id="user" class="manage-container" v-cloak>
    <header class="manage-header">
        <a href="/manage/users" class="button secondary icon-button-back">Back to User list</a>
    </header>
    <form method="post" v-on:submit="onFormSubmit">
        @Html.AntiForgeryToken()
        <div v-bind:class="['form-col form-col-third']">
            <div v-if="!user">
                <h5>Find user</h5>
                <label for="user">Start typing the user's name or email address to select from list of existing users or hit enter to create a new user</label>
                <autocomplete :config="config" v-on:selected="onSelected"></autocomplete>
            </div>
            <div id="details-column" class="form-col" v-if="user">
                <h5>Details</h5>
                <label for="GivenName">First name</label>
                <input name="User.GivenName" v-model="user.givenName" type="text" :readonly="!!user.id" required autocomplete="off" />

                <label for="FamilyName">Last name</label>
                <input name="User.FamilyName" v-model="user.familyName" type="text" :readonly="!!user.id" required autocomplete="off" />

                <label for="Email">Email</label>
                <div class="email-edit">
                    <input id="Email" name="User.Email" v-model="user.email" type="email" v-on:input="checkAvailability" :readonly="!!user.id" required autofocus autocomplete="off" />
                </div>
            </div>

            <div id="claims-column" v-bind:class="['form-col claims-col', {'form-col-disabled': !user}]">
                <h5>Roles</h5>
                <div v-for="claim in displayedClaims" :key="claim.id" class="checkbox-list-item">
                    <input v-bind="bindClaim(claim)" v-on:click="onClaimSelected($event)" />
                    <label :for="'claim-' + claim.id">{{claim.name}}</label>
                </div>
            </div>
        </div>
        <div id="clients-column" :class="['form-col form-col-two-third', {'form-col-disabled': !user}] ">
            <h5>Clients</h5>
            <div class="clients-col">
                <div v-for="client in clients" :key="client.id" class="checkbox-list-item client-item">
                    <input v-bind="bindClient(client)" />
                    <label :for="'client-' + client.id" :title="client.name">{{client.name}}</label>
                </div>
            </div>
        </div>
        <template v-if="user !== null && user.id > 0 && user.userTypeId == 3">
            <div class="form-group">
                <a class="button" v-bind:name="resendButton" v-bind:class="{ secondary: user.lastLoginDate }">Resend Invitation</a>
                <a class="button" v-bind:class="{ secondary: !user.invitationEmailLink || user.lastLoginDate }" v-on:click="copyLink">Copy Invitation Link</a>
            </div>
        </template>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/manage/users">Cancel</a>
            <button v-if="!!user" class="icon-button-save" v-on:click.once>Save</button>
        </div>

        <input type="hidden" asp-for="User.Id" />
        <input type="hidden" asp-for="User.UserTypeId" v-model="user.userTypeId" v-if="user" />
    </form>

    <yes-no-dialog v-if="user !== null && user.id > 0 && !user.lastLoginDate" :config="resendConfig" v-on:button-pressed="confirmSend"></yes-no-dialog>

</div>

@section Scripts {
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        var pageConfig = {
            appElement: '#user',
            data() {
                let model = @Html.Raw(Json.Serialize(Model));
                return {
                    config: {
                        canAddNew: true,
                        searchUrl: `/manage/external-user/find?term={term}`
                    },
                    resendConfig: {
                        message: 'Do you wish to resend the invitation email to this user?',
                        noButton: 'Cancel',
                        yesButton: 'Yes',
                        elementName: 'resend-button',
                        buttonClass: null
                    },
                    model,
                    resendButton: 'resend-button',
                    user: null,
                    timeout: null,
                    newUserModel: { name: '' },
                    clients: @Html.Raw(Json.Serialize(Model.Clients)),
                    previousSelectedClients: [],
                    claims: @Html.Raw(Json.Serialize(claims)),
                    checkedClaims: [],
                    externalUserTypeId: @Html.Raw((int)UserType.External),
                    externalReaderClaim: null
                };
            },
            methods: {
                init(user) {
                    let rx = /(.*)\s\((.*)\)/;
                    let email = (user.email || user.name || '');
                    let matchedResults = (user.email || user.name || '').match(rx);
                    if (matchedResults) {
                        email = matchedResults[2];
                    }
                    let splitNames = (user.email || user.name || '').split('@@')[0].split('.');
                    let givenName = splitNames.splice(0, 1)[0];
                    let familyName = splitNames.join('.');

                    return {
                        id: user.id,
                        email: email,
                        familyName: user.familyName || [(familyName[0] || '').toUpperCase(), familyName.slice(1, familyName.length)].join(''),
                        givenName: user.givenName || [(givenName[0] || '').toUpperCase(), givenName.slice(1, givenName.length)].join(''),
                        userTypeId: this.externalUserTypeId
                    };
                },
                onSelected(user) {
                    if (user.id > 0) {
                        document.location.href = '/manage/users/edit/' + user.id;
                    } else {
                        this.user = this.init(user);
                        this.checkAvailability();
                    }
                },
                removeCheckedClaim(claimName) {
                    let index = this.checkedClaims.indexOf(claimName);
                    if (index > -1) {
                        this.checkedClaims.splice(index, 1);
                    }
                },
                onClaimSelected(claim) {
                    if (claim.currentTarget.checked) {
                        let claimName = claim.currentTarget.labels[0].innerText;
                        let claimId = +claim.currentTarget.id.substring(6, 7);

                        if (!this.user.claims) {
                            this.user.claims = Vue.ref([]);
                        }

                        this.user.claims.push(claimId);
                        this.checkedClaims.push(claimName);
                    }
                    else {
                        let claimName = claim.currentTarget.labels[0].innerText;
                        let claimId = +claim.currentTarget.id.substring(6, 7);

                        this.removeCheckedClaim(claimName);

                        if (this.user.claims) {
                            let index = this.user.claims.indexOf(claimId);
                            if (index !== -1) {
                                this.user.claims.splice(index, 1);
                            }
                        }

                        if (claimName.includes('ExternalEditor')) {
                            document.getElementById('claim-' + this.externalReaderClaim.id).checked = false;
                            let index = this.user.claims.indexOf(this.externalReaderClaim.id);
                            if (index !== -1) {
                                this.user.claims.splice(index, 1);
                            }
                            this.removeCheckedClaim(this.externalReaderClaim.name);
                        }
                    }
                },
                checkAvailability(e) {
                    if (e && !e.target.checkValidity()) {
                        return;
                    }

                    if (this.timeout) {
                        clearTimeout(this.timeout);
                    }
                    let data = new FormData();
                    data.append("email", (this.user ? this.user.email : this.newUserModel.name).trim());
                    this.timeout = setTimeout(() => {
                        fetch('/manage/users/available', {
                            method: 'POST',
                            credentials: 'same-origin',
                            headers: {
                                'RequestVerificationToken': token
                            },
                            body: data
                        })
                            .then(r => r.json())
                            .then(availableUser => {
                                if (availableUser) {
                                    document.location.href = '/manage/users/edit/' + availableUser.id;
                                }
                            })
                            .catch(error => {
                                console.log(error);
                            })
                    }, 500)
                },
                onFormSubmit(e) {
                    if (!this.user) {
                        e.preventDefault();
                    }
                },
                bindClaim(claim) {
                    let binding = {
                        id: `claim-${claim.id}`,
                        value: `${claim.id}`,
                        name: 'User.Claims',
                        type: 'checkbox'
                    }

                    if (this.user && this.user.claims && this.user.claims.includes(claim.id)) {
                        binding.checked = 'checked';
                        if (!this.checkedClaims.includes(claim.name)) {
                            this.checkedClaims.push(claim.name);
                        }
                    }

                    return binding;
                },
                bindClient(client) {
                    let binding = {
                        id: `client-${client.id}`,
                        value: `${client.id}`,
                        name: 'User.Clients',
                        type: 'checkbox'
                    }

                    if (this.user && this.user.clients && this.user.clients.includes(client.id)) {
                        binding.checked = 'checked';
                    }

                    return binding;
                },
                confirmSend(yesButtonPressed) {
                    if (yesButtonPressed) {
                        let parent = this;

                        let url = '/manage/users/resendinvitation';
                        fetch(url, {
                            method: 'POST',
                            credentials: 'same-origin',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': token
                            },
                            body: JSON.stringify({
                                Id: parent.user.id,
                                Email: parent.user.email,
                                GivenName: parent.user.givenName,
                                FamilyName: parent.user.familyName
                            })
                        }).then(response => {
                            response.json().then(data => {
                                plx.toast.show('Email sent successfully', 2, 'confirm', null, 2500, { useIcons: true });
                                parent.user.invitationEmailLink = data;
                            })
                                .catch(error => {
                                    plx.toast.show('Failed to send email.', 2, 'failed', null, 5000, { useIcons: true })
                                    console.log(error);
                                });
                        })
                    }
                },
                copyLink() {
                    if (this.user.invitationEmailLink && !this.user.lastLoginDate) {
                        navigator.clipboard.writeText(this.user.invitationEmailLink);
                    }
                }
            },
            computed: {
                displayedClaims() {
                    if (!this.checkedClaims.includes("ExternalEditor")) {
                        return this.claims.filter(x => x.name !== "PrivilegedExternalEditor");
                    }
                    return this.claims;

                }
            },
            created() {
                if (this.model.user.id > 0) {
                    this.user = this.model.user;
                }

                this.externalReaderClaim = this.claims.find(x => x.name === 'PrivilegedExternalEditor');
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/AutoCompleteList" />
    <partial name="Components/YesNoDialog" />
}
