﻿insert into [dbo].[Claim] (Name, ClaimType, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy)
values ('PrivilegedExternalReader', 'application', GETDATE(), 'update script', GETDATE(), 'update script')

insert into [dbo].[UserClaim] (UserId, ClaimId, CreatedDate, CreatedBy, LastUpdatedDate, LastUpdatedBy)
  select uc.UserId as UserId, 
		 (select top 1 Id from Claim where Name = 'PrivilegedExternalReader') as ClaimId,
		 GETDATE() as CreatedDate,
		 'update script' as CreatedBy,
		 GETDATE() as LastUpdatedDate,
		 'update script' as LastUpdatedBy
 from [dbo].[UserClaim] uc inner join Claim c on c.Id = uc.ClaimId 
 where c.Name = 'ExternalReader'