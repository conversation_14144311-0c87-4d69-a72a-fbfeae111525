﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class AddCommentsToApplication : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Comments",
                schema: "Audit",
                table: "Application_Audit",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Comments",
                table: "Application",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.SqlFileExec("0012-AddCommentsToApplication-UpdateApplicationTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Comments",
                schema: "Audit",
                table: "Application_Audit");

            migrationBuilder.DropColumn(
                name: "Comments",
                table: "Application");
        }
    }
}
