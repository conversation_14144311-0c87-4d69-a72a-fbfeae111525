﻿using AutoMapper;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.Eu.Models
{
    public class EuEnvelopeModel : IModule1DataModel
    {
        public string Country { get; set; }
        public string Identifier { get; set; }
        public string SubmissionUnitType { get; set; }
        public string Applicant { get; set; }
        public CtdSubmission Submission { get; set; }
        public string InventedName { get; set; }
        public string ProcedureTrackingNumber { get; set; }
        public string TrackingNumber { get; set; }
        public string Procedure { get; set; }
        public string AgencyCode { get; set; }
        public string Inn { get; set; }
        public string Sequence { get; set; }
        public string RelatedSequence { get; set; }
        public string SubmissionDescription { get; set; }
    }

    public class CtdEnvelopeMappingProfile : Profile
    {
        public CtdEnvelopeMappingProfile()
        {
            this.CreateMap<EuEnvelope, EuEnvelopeModel>();
        }
    }
}
