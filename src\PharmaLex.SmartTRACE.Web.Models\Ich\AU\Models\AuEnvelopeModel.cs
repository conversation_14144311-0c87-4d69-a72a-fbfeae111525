﻿using AutoMapper;
using System;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.AU.Models
{
    public class AuEnvelopeModel: IModule1DataModel
    {
        public string EsubId { get; set; }

        public string ClientId { get; set; }

        public string[] Aan { get; set; }

        public string[] ProductName { get; set; }

        public string[] ArtgNumber { get; set; }

        public string[] SubmissionNumber { get; set; }

        public string SequenceNumber { get; set; }

        public string RelatedSequenceNumber { get; set; }

        public AuAnnotatedElementType RegActivityLead { get; set; }

        public AuSequenceType[] SequenceType { get; set; }

        public string SubmissionMode { get; set; }

        public AuWorkSharing[] WorkSharing { get; set; }

        public string[] Email { get; set; }
    }

    public class AuAnnotatedElementType
    {
        public string CodeVersion { get; set; }

        public string Code { get; set; }
    }

    public class AuWorkSharing
    {
        public string EsubId { get; set; }

        public string[] SubmissionNumber { get; set; }

        public string SequenceNumber { get; set; }
    }

    public class AuSequenceType
    {
        public DateTime Data { get; set; }

        public string SequenceDescriptionCodeVersion { get; set; }

        public string SequenceDescriptionCode { get; set; }

        public string CodeVersion { get; set; }

        public string Code { get; set; }
    }

    public class AuEnvelopeModelProfile : Profile
    {
        public AuEnvelopeModelProfile()
        {
            this.CreateMap<auenvelope, AuEnvelopeModel>();
            this.CreateMap<auannotatedelementtype, AuAnnotatedElementType>();
            this.CreateMap<auworksharing, AuWorkSharing>();
            this.CreateMap<ausequencetype, AuSequenceType>();
        }
    }
}
