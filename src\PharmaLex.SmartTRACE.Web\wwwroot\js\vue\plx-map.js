﻿Vue.component('plx-map', {
    template: '#plx-map-template',
    data: function () {
        return {
            showList: false,
            selectedCountries: [...this.preselectedCountries],
            copyFromRegion: -1
        };
    },
    props: {
        countries: {
            type: Array,
            required: true
        },
        preselectedCountries: {
            type: Array,
            required: false,
            default: () => []
        },
        country: {
            type: Number,
            required: false,
            default: () => 0
        },
        regions: {
            type: Array,
            required: false,
            default: () => []
        },
        editable: {
            type: Boolean,
            required: false,
            default: () => true
        },
        acumulate: {
            type: Boolean,
            required: false,
            default: () => false
        }
    },
    computed: {
        toggleText() {
            return `Switch to ${this.showList ? 'map' : 'list'} view`;
        }
    },
    methods: {
        onMapRendered() {
            this.fillCountries();
        },
        onCountryListSelectionChanged(e) {
            if (!this.editable) return e.preventDefault();

            this.selectedCountries = [...document.getElementById('countrySelect').selectedOptions].map(o => o.value);
            this.$emit('change', this.selectedCountries);
            this.fillCountries();
        },
        onCountrySelected(id) {
            if (!this.editable) return;

            let index = this.selectedCountries.indexOf(id);
            if (index > -1) {
                this.selectedCountries.splice(index, 1);
                plx.map.resetCountryFill(id);
                this.$emit('change', this.selectedCountries);
            }
            else {
                this.selectedCountries.push(id);
                plx.map.fillCountry(id, 'rgb(109, 167, 213)');
                this.$emit('change', this.selectedCountries);
            }
        },
        onClear() {
            this.selectedCountries = this.country ? [this.country] : [];
            this.copyFromRegion = -1;
            this.$emit('change', this.selectedCountries);
            this.fillCountries();
        },
        onSelectAllCountries() {
            let allCountries = [...countries];
            allCountries.forEach(x => {
                if (!this.selectedCountries.includes(x.id)) {
                    x.selected = true;
                    this.selectedCountries.push(x.id);
                    plx.map.fillCountry(x.id, 'rgb(109, 167, 213)');
                    this.$emit('change', this.selectedCountries);
                }
            });
        },
        onCopyFromRegionChange() {
            if (this.copyFromRegion < 0) return;

            this.selectedCountries = this.acumulate ? this.selectedCountries : this.country ? [this.country] : [];
            let regionCountries = this.regions.find(x => x.id === this.copyFromRegion).countries;

            this.selectedCountries = [...new Set([this.selectedCountries, regionCountries].flat())];
            this.fillCountries();
            this.$emit('change', this.selectedCountries);
        },
        fillCountries() {
            plx.map.resetCountryFill();
            this.selectedCountries.filter(x => x !== this.country).forEach(c => plx.map.fillCountry(c, 'rgb(109, 167, 213)'));
            plx.map.fillCountry(this.country, 'rgb(35,60,76)');
        },
        initMap() {
            plx.map.showZoomReset = false;
            plx.map.highlightCountryOnSelect = false;
            plx.map.init('map-container', null, this.onMapRendered, null, this.onCountrySelected);
            plx.map.render();
        }
    },
    watch: {
        preselectedCountries() {
            this.selectedCountries = [...new Set([this.preselectedCountries, this.country ? [this.country] : []].flat())];
            this.$emit('change', this.selectedCountries);
            this.fillCountries();
        },
        country() {
            if (!this.selectedCountries.includes(this.country)) {
                this.selectedCountries.push(this.country);
                this.$emit('change', this.selectedCountries);
            }

            this.fillCountries();
        }
    },
    mounted() {
        this.initMap();
    }
});