﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class CountryModelTests
    {
        [Fact]
        public void CountryModel_Get_SetValue()
        {
            //Arrange
            var model = new CountryModel();
            model.Id = 1;
            model.Name = "test";
            model.TwoLetterCode = "123";
            model.RegionId = 123;
            model.HasError = false;
            model.ErrorMessage = "test";
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void CountryMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new CountryMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
    }
}
