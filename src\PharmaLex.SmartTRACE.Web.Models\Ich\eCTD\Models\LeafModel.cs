﻿using AutoMapper;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models
{
    public class LeafModel : IModel
    {
        public string text { get; set; }
        public string version { get; set; }
        public string Id { get; set; }
        public string op { get; set; }
        public string href { get; set; }
        public string Submission { get; set; }
        [JsonIgnore]
        public string Path { get; set; }
        [JsonIgnore]
        public string ModifiedFile { get; set; }
        public List<LeafModel> Historical { get; set; } = new List<LeafModel>();
    }

    public class LeafMappingProfile : Profile
    {
        public LeafMappingProfile()
        {
            this.CreateMap<Ileaf, LeafModel>()
                .ForMember(d => d.Id, s => s.MapFrom(x => x.ID))
                .ForMember(d => d.ModifiedFile, s => s.MapFrom(x => x.modifiedfile))
                .ForMember(d => d.href, s => s.MapFrom(x => string.IsNullOrEmpty(x.Path) ? $"{x.Submission}/{x.href}" : $"{x.Submission}/{x.Path}/{x.href}"));
        }
    }
}
