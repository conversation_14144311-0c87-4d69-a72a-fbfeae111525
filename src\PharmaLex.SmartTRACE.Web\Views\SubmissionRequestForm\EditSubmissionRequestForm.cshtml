﻿@model EditSubmissionRequestFormViewModel
@using PharmaLex.SmartTRACE.Entities.Enums;

@inject AutoMapper.IMapper mapper
@{
    var submissionTypeList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.SubmissionType).OrderBy(x => x.Name);
    var submissionUnitList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.SubmissionUnit).OrderBy(x => x.Name);
    var submissionModeList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.SubmissionMode).OrderBy(x => x.Name);
    var estimatedSizeSelectList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.EstimatedSize).OrderBy(x => x.Name).Select(x => new SelectListItem
    {
        Text = $"{x.Name}",
        Value = x.Id.ToString()
    });
    var deliveryDetailsList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.SubmissionDeliveryDetails).OrderBy(x => x.Name).Select(x => new SelectListItem
    {
        Text = $"{x.Name}",
        Value = x.Id.ToString()
    });
    var prioritySelectList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.Priority).OrderBy(x => x.Name).Select(x => new SelectListItem
    {
        Text = $"{x.Name}",
        Value = x.Id.ToString()
    });
    var projectSelectList = Model.Projects.OrderBy(x => x.Name).Select(x => new SelectListItem
    {
        Text = $"{x.Code}",
        Value = x.Id.ToString()
    });
    var picklistType = mapper.Map<IEnumerable<PicklistTypeList>>(Enum.GetValues(typeof(PicklistType)));
}

<div id="submissionForm" class="manage-container">
    <header class="manage-header">
        <h3>Submission Record</h3>
        <a href="/app-submissions" class="button secondary icon-button-list">View All Submissions</a>
        <a href="/applications/view/@Model.SubmissionRequestForm.ApplicationId" class="button secondary icon-button-back">Back to Application</a>
    </header>
    <form id="submission-form" method="post">
        @Html.AntiForgeryToken()
        <div class="form-col form-col-half">
            <h5>General Info</h5>
            <label for="SubmissionRequestForm.ApplicationNumber">Application Number</label>
            <input asp-for="SubmissionRequestForm.ApplicationNumber" type="text" readonly />
            <label for="SubmissionRequestForm.CountryIds" asp-for="SubmissionRequestForm.CountryIds">
                <span>Submission Country</span>
                <i class="icon-info-circled" v-if="memberStateProcedures.includes(submission.procedureType)" 
                   title="Select submission countries to be removed from the list (press and hold Ctrl button on the keyboard оr drag with the mouse for multi-select)."></i>
            </label>
            <div class="multi-select-wrapper">
                <select id="countriesSelectId" asp-for="SubmissionRequestForm.CountryIds" v-model="selectedCountriesId" 
                        :aria-disabled="!memberStateProcedures.includes(submission.procedureType)" multiple>
                    <option v-for="(c, index) in countries" :key="index" v-bind:value="c.id">{{ c.name }}</option>
                </select>
                <i name="deleteCountry" class="hidden"></i>
            </div>
            <div class="submission-content" v-if="memberStateProcedures.includes(submission.procedureType)">
                <a class="button secondary icon-button-cancel" v-on:click="clearSelectedCountries()">Clear</a>
                <button type="button" class="icon-button-delete" v-on:click.once="deleteCountries()" :key="deleteKey">Remove</button>
            </div>
            <label for="SubmissionRequestForm.DeliveryDetailsId">Submission Delivery Details</label>
            <div class="select-wrapper">
                <select asp-for="SubmissionRequestForm.DeliveryDetailsId" asp-items="deliveryDetailsList">
                    <option value="">Select delivery details</option>
                </select>
            </div>
            <label for="SubmissionRequestForm.SequenceNumber">Sequence Number</label>
            <input asp-for="SubmissionRequestForm.SequenceNumber" type="text" />
            <label for="SubmissionRequestForm.SerialNumber" v-if="showSerialNumberField()">Serial Number</label>
            <input asp-for="SubmissionRequestForm.SerialNumber" type="text" v-if="showSerialNumberField()" />
            <label for="SubmissionRequestForm.SubmissionTypeId" class="required-field" v-if="submissionTypes.length != 0">Submission Type*</label>
            <div v-if="submissionTypes.length != 0" class="select-wrapper">
                <select asp-for="SubmissionRequestForm.SubmissionTypeId" required>
                    <option value="">Select submission type</option>
                    <option v-for="(s, index) in submissionTypes" :key="index" v-bind:value="s.id">{{ s.name }}</option>
                </select>
            </div>
            <label for="SubmissionRequestForm.SubmissionUnitId" v-if="submissionUnits.length != 0">Submission Unit</label>
            <div v-if="submissionUnits.length != 0" class="select-wrapper">
                <select asp-for="SubmissionRequestForm.SubmissionUnitId">
                    <option value="">Select submission unit</option>
                    <option v-for="(s, index) in submissionUnits" :key="index" v-bind:value="s.id">{{ s.name }}</option>
                </select>
            </div>
            <label for="SubmissionRequestForm.SubmissionModeId" v-if="submissionModes.length != 0">Submission Mode</label>
            <div v-if="submissionModes.length != 0" class="select-wrapper">
                <select asp-for="SubmissionRequestForm.SubmissionModeId">
                    <option value="">Select submission mode</option>
                    <option v-for="(s, index) in submissionModes" :key="index" v-bind:value="s.id">{{ s.name }}</option>
                </select>
            </div>
            <label for="SubmissionRequestForm.Description">Description</label>
            <textarea asp-for="SubmissionRequestForm.Description"></textarea>
        </div>

        <div class="form-col form-col-half">
            <h5>Additional Info</h5>
            <label for="SubmissionRequestForm.HealthAuthorityDueDate" asp-for="SubmissionRequestForm.HealthAuthorityDueDate">Health Authority Due Date</label>
            <input type="date" asp-for="SubmissionRequestForm.HealthAuthorityDueDate" />
            <label for="SubmissionRequestForm.PlannedDispatchDate" class="required-field">Planned Dispatch*</label>
            <input asp-for="SubmissionRequestForm.PlannedDispatchDate" type="date" :min="minDate" required />
            <label for="SubmissionRequestForm.PlannedSubmissionDate" class="required-field">Planned Submission*</label>
            <input asp-for="SubmissionRequestForm.PlannedSubmissionDate" type="date" :min="minDate" required />
            <label for="SubmissionRequestForm.PriorityId">Priority</label>
            <div class="select-wrapper">
                <select asp-for="SubmissionRequestForm.PriorityId" asp-items="prioritySelectList">
                    <option value="">Select priority</option>
                </select>
            </div>
            <label for="SubmissionRequestForm.EstimatedSizeId" class="required-field">Estimated Size*</label>
            <div class="select-wrapper">
                <select asp-for="SubmissionRequestForm.EstimatedSizeId" asp-items="estimatedSizeSelectList" required>
                    <option value="">Select estimated size</option>
                </select>
            </div>
            <label for="SubmissionRequestForm.Comments">Comments</label>
            <textarea asp-for="SubmissionRequestForm.Comments"></textarea>
            <label for="SubmissionRequestForm.ProjectId" class="required-field">Project code*</label>
            <div class="select-wrapper">
                <select asp-for="SubmissionRequestForm.ProjectId" asp-items="projectSelectList" v-on:change="selectedProject($event)" required>
                    <option value="">Select project code</option>
                </select>
            </div>
            <label for="SubmissionRequestForm.OpportunityNumber">Opportunity Number</label>
            <input asp-for="SubmissionRequestForm.OpportunityNumber" type="text" v-model="opportunityNumber" readonly />
            <label for="selectedLead" class="required-field">Regulatory Lead*</label>
            <autocomplete class="submission-request-autocomplete" :config="config" v-on:selected="selectedLead" :autocompleteoff="true"></autocomplete>
            <div v-if="selectedLeadErrorMessage">
                <span class="required-field"> {{ selectedLeadErrorMessage }}</span>
            </div>
            <label for="fieldActive">Send Notification to RegOps Request Mailbox?</label>
            <label class="switch-container notification-email">
                No
                <input id="fieldActive" type="checkbox" class="switch" v-on:change="sendNotification($event)" v-model="submission.sendNotification" :value="submission.sendNotification" />
                <label for="fieldActive" class="switch"></label>
                Yes
                <span class="tooltiptext">Do you want to send a notification to {{ initialEmail }}?</span>
            </label>
            <autocomplete class="submission-request-autocomplete" v-if="!submission.sendNotification" :config="notificationConfig" v-on:selected="selectedInitialEmail"></autocomplete>

            <input type="hidden" asp-for="SubmissionRequestForm.InitialSentToEmail" v-model="initialSendToEmail" />
            <input type="hidden" asp-for="SubmissionRequestForm.RegulatoryLead" v-model="regulatoryLead" />
            <input type="hidden" asp-for="SubmissionRequestForm.SendNotification" v-model="submission.sendNotification" />
            <input type="hidden" asp-for="SubmissionRequestForm.ApplicationId" />
            <input type="hidden" asp-for="SubmissionRequestForm.ApplicationId" />
            <input type="hidden" asp-for="SubmissionRequestForm.ProjectId" />
            <select asp-for="SubmissionRequestForm.CountryIds" class="hidden" v-model="selectedCountries">
                <option v-for="(sc, index) in selectedCountries" :key="index" v-bind:value="sc" v-bind:id="sc"></option>
            </select>
        </div>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/applications/view/@Model.SubmissionRequestForm.ApplicationId">Cancel</a>
            <button type="button" class="icon-button-save" v-on:click.once="submit" :key="submitKey">Submit</button>
            <button id="submitButtonId" class="hide-button">Submit</button>
        </div>
    </form>
    <yes-no-dialog :config="deleteCountryConfig" v-on:button-pressed="onDeleteCountries"></yes-no-dialog>
</div>
@section Scripts {
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        var pageConfig = {
            appElement: '#submissionForm',
            data() {
                return {
                    minDate: new Date().toISOString().substring(0, 10),
                    config: {
                        showSelectedName: true,
                        canAddNew: false,
                        searchUrl: '/manage/users/find?term={term}',
                        placeholder: 'Type and select the e-mail from the list'
                    },
                    notificationConfig: {
                        showSelectedName: true,
                        canAddNew: false,
                        searchUrl: '/manage/users/find?term={term}',
                        placeholder: 'Type and select the e-mail for a person who should assign publishing resource, or leave blank'
                    },
                    deleteCountryConfig: {
                        noButton: 'Cancel',
                        yesButton: 'Remove',
                        elementName: 'deleteCountry',
                        buttonClass: 'icon-button-delete'

                    },
                    regulatoryLead: null,
                    submission: @Html.Raw(Model.SubmissionRequestForm.ToJson()),
                    countries: @Html.Raw(Model.Countries.ToJson()),
                    selectedCountries: @Html.Raw(Json.Serialize(Model.SubmissionRequestForm.CountryIds)),
                    projects: @Html.Raw(Model.Projects.ToJson()),
                    submissionTypes: @Html.Raw(Json.Serialize(submissionTypeList)),
                    submissionUnits: @Html.Raw(Json.Serialize(submissionUnitList)),
                    submissionModes: @Html.Raw(Json.Serialize(submissionModeList)),
                    picklistTypes: @Html.Raw(Json.Serialize(picklistType)),
                    allPicklists: @Html.Raw(Json.Serialize(Model.Picklists)),
                    opportunityNumber: null,
                    submitKey: 1,
                    deleteKey: 1,
                    initialEmail: '@Html.Raw(Model.SubmissionRequestForm.InitialSentToEmail)',
                    initialSendToEmail: null,
                    memberStateProcedures: ['Decentralised Procedure', 'Mutual Recognition Procedure', 'ASMF Worksharing Procedure'],
                    selectedCountriesId: [],
                    selectedLeadErrorMessage: null
                }
            },
            methods: {
                submit() {
                    document.getElementById("countriesSelectId").removeAttribute("disabled");
                    this.clearSelectedCountries();

                    if (this.regulatoryLead == null) {
                        this.selectedLeadErrorMessage = 'Regulatory lead is not selected';
                    } else {
                        document.getElementById('submitButtonId').click();
                    }

                    setTimeout(() => this.submitKey++, 500);
                },
                selectedLead(selectedLead) {
                    this.regulatoryLead = selectedLead.name;
                    this.selectedLeadErrorMessage = null;
                },
                selectedProject(project) {
                    let selectedProjectId = project.target.value;
                    let selectedProject = this.projects.find(x => x.id === parseInt(selectedProjectId));
                    if (selectedProject) {
                        this.opportunityNumber = selectedProject.opportunityNumber;
                    } else {
                        this.opportunityNumber = null;
                    }
                },
                selectedInitialEmail(selectedEmail) {
                    this.initialSendToEmail = selectedEmail.name;
                },
                updatePicklistValues() {
                    fetch(`/data/update-picklist`, {
                        method: 'POST',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': token
                        },
                        body: JSON.stringify(this.countries)
                    })
                    .then(r => r.json())
                    .then(result => {
                            this.allPicklists = result;
                            this.submissionTypes = this.getPicklistValuesByType('Submission Type');
                            this.submissionUnits = this.getPicklistValuesByType('Submission Unit');
                            this.submissionModes = this.getPicklistValuesByType('Submission Mode');
                            plx.toast.show('Submission type, unit and mode values are updated', 2, 'info', null, 3000);
                    });
                },
                getPicklistValuesByType(type) {
                    let picklistType = this.picklistTypes.find(t => t.name === type);
                    return this.allPicklists.filter(x => x.picklistTypeId === picklistType.id);
                },
                showSerialNumberField() {
                    let country = this.countries.find(x => x.name === 'United States of America');
                    if (country) {
                        return this.selectedCountries.includes(country.id);
                    }
                },
                sendNotification() {
                    if ((!this.submission.sendNotification && this.initialSendToEmail) || this.submission.sendNotification) {
                        this.initialSendToEmail = null;
                    }
                },
                deleteCountries() {
                    const options = Array.from(document.getElementById("countriesSelectId").children);
                    if (this.selectedCountriesId.length === 0) {
                        plx.toast.show('Please select countries for removing', 5, 'warning', null, 2000);
                        this.deleteKey += 1;
                        return;
                    } else if (this.selectedCountriesId.length === options.length) {
                        plx.toast.show('Cannot remove all countries from a submission', 5, 'warning', null, 2000);
                        this.deleteKey += 1;
                        return;
                    }
                    else if (options.length === 1) {
                        plx.toast.show('At least one country should be associated to a submission', 5, 'warning', null, 2000);
                        this.deleteKey += 1;
                        return;
                    }

                    const labels = this.countries.filter(x => this.selectedCountriesId.includes(x.id)).map(x => x.name);

                    this.deleteCountryConfig.message = `Do you want to remove the ${labels.length === 1 ? "country" : "countries"} ${labels.join(', ')}?`;
                    document.getElementsByName(this.deleteCountryConfig.elementName)[0].click();
                },
                onDeleteCountries(yesButtonPressed) {
                    if (yesButtonPressed) {
                        this.removeCountry();
                        this.updatePicklistValues();
                    }
                    this.deleteKey += 1;
                },
                clearSelectedCountries() {
                    Array.from(document.getElementById("countriesSelectId").children).forEach(child => {
                        child.selected = false;
                    });

                    this.selectedCountriesId = Vue.ref([]);
                },
                removeCountry() {
                    this.selectedCountriesId.forEach(elementId => {
                        let element = this.countries.find(x => x.id === elementId);
                        const index = this.countries.indexOf(element);
                        this.countries.splice(index, 1);
                        this.selectedCountries = this.countries.map(x => x.id);
                    })
                },
            },
            mounted() {
                let autocompleteElements = document.getElementsByClassName('autocomplete');
                let regulatoryLeadElement = autocompleteElements[0].firstChild;
                regulatoryLeadElement.setAttribute("required", "true");
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/AutoCompleteList" />
    <partial name="Components/YesNoDialog" />
}