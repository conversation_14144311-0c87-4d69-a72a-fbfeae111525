﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.HTMLTags;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    [Authorize("BusinessAdmin")]
    public class ActiveSubstancesController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper _mapper;
        private const string UniqueErrorMessage = "An active substance with the same name already exists to this client.";

        public ActiveSubstancesController(IDistributedCacheServiceFactory cache, IMapper mapper)
        {
            this.cache = cache;
            this._mapper = mapper;
        }

        [HttpGet, Route("/active-substances")]
        public async Task<IActionResult> Index()
        {
            var substances = await cache.CreateEntity<ActiveSubstance>().Configure(o => o.Include(x => x.Client)).AllAsync();
            var allActiveSubstances = _mapper.Map<IList<ActiveSubstanceModel>>(substances);
            return View(allActiveSubstances);
        }

        [HttpGet, Route("/active-substances/new")]
        public async Task<IActionResult> New()
        {
            return View("EditActiveSubstance", new EditActiveSubstanceViewModel()
            {
                ActiveSubstance = new ActiveSubstanceModel(),
                Clients = await this.cache.CreateMappedEntity<Client, ClientModel>().AllAsync()
            });
        }

        [HttpPost, Route("/active-substances/new"), ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveNew(EditActiveSubstanceViewModel model)
        {
            if (!ModelState.IsValid || HtmlTags.CheckHTMLTags(model.ActiveSubstance.Name))
            {
                model.Clients = await this.cache.CreateMappedEntity<Client, ClientModel>().AllAsync();
                return View("EditActiveSubstance", model);
            }

            try
            {
                var asCache = cache.CreateTrackedEntity<ActiveSubstance>();
                var actsub = _mapper.Map<ActiveSubstance>(model.ActiveSubstance);
                actsub.LifecycleStateId = (int)CommonLifecycleState.Active;
                asCache.Add(actsub);
                await asCache.SaveChangesAsync();
                this.AddConfirmationNotification($"<em>{actsub.Name}</em> created");
                return Redirect("/active-substances");
            }
            catch (DbUpdateException)
            {
                model.ActiveSubstance.HasDuplicate = true;
                model.ActiveSubstance.ErrorMessage = UniqueErrorMessage;
                model.Clients = await this.cache.CreateMappedEntity<Client, ClientModel>().AllAsync();
                return View("EditActiveSubstance", model);
            }
        }

        [HttpGet, Route("/active-substances/edit/{id}")]
        public async Task<IActionResult> Edit(int id)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var currentActiveSubstance = await cache.CreateEntity<ActiveSubstance>()
                                                    .Configure(o => o.
                                                        Include(x => x.ActiveSubstanceProduct))
                                                    .FirstOrDefaultAsync(x => x.Id == id);
            return View("EditActiveSubstance", new EditActiveSubstanceViewModel()
            {
                ActiveSubstance = _mapper.Map<ActiveSubstanceModel>(currentActiveSubstance),
                Clients = await this.cache.CreateMappedEntity<Client, ClientModel>().AllAsync(),
                IsProductAssigned = currentActiveSubstance.ActiveSubstanceProduct.Any()
            });
        }

        [HttpPost, Route("/active-substances/edit/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveEdit(int id, EditActiveSubstanceViewModel model)
        {
            if (!ModelState.IsValid || HtmlTags.CheckHTMLTags(model.ActiveSubstance.Name))
            {
                model.Clients = await this.cache.CreateMappedEntity<Client, ClientModel>().AllAsync();
                return View("EditActiveSubstance", model);
            }

            try
            {
                var asCache = cache.CreateTrackedEntity<ActiveSubstance>();
                var activeSubstance = await asCache.FirstOrDefaultAsync(x => x.Id == model.ActiveSubstance.Id);
                _mapper.Map(model.ActiveSubstance, activeSubstance);
                await asCache.SaveChangesAsync();
                this.AddConfirmationNotification($"<em>{activeSubstance.Name}</em> updated");
                return Redirect("/active-substances");
            }
            catch (DbUpdateException)
            {
                model.ActiveSubstance.HasDuplicate = true;
                model.ActiveSubstance.ErrorMessage = UniqueErrorMessage;
                model.Clients = await this.cache.CreateMappedEntity<Client, ClientModel>().AllAsync();
                return View("EditActiveSubstance", model);
            }
        }

        [HttpGet("/active-substances/delete/{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var activeSubstance = await cache.CreateMappedEntity<ActiveSubstance, ActiveSubstanceModel>()
                                                .FirstOrDefaultAsync(x => x.Id == id);
            return View("DeleteActiveSubstance", activeSubstance);
        }

        [HttpPost("/active-substances/state/{id}"), Authorize(Policy = "BusinessAdmin"), ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangeState(int id, [FromBody] string stateId)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var activeSubstanceCache = cache.CreateTrackedEntity<ActiveSubstance>();
            var activeSubstance = await activeSubstanceCache.FirstOrDefaultAsync(x => x.Id == id);
            activeSubstance.LifecycleStateId = int.Parse(stateId);
            await activeSubstanceCache.SaveChangesAsync();
            return Ok(activeSubstance.LifecycleStateId);
        }

        [HttpPost("/active-substances/delete/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmedDelete(int id)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var asCache = cache.CreateTrackedEntity<ActiveSubstance>();
            var asProductCache = cache.CreateTrackedEntity<ActiveSubstanceProduct>();
            var activeSubstance = await asCache.FirstOrDefaultAsync(x => x.Id == id);
            var activeSubstanceProduct = await asProductCache.FirstOrDefaultAsync(x => x.Id == id);
            if (activeSubstanceProduct != null)
            {
                asProductCache.Remove(activeSubstanceProduct);
            }
            asCache.Remove(activeSubstance);
            await asCache.SaveChangesAsync();
            await asProductCache.SaveChangesAsync();
            this.AddConfirmationNotification($"<em>{activeSubstance.Name}</em> deleted");
            return Redirect("/active-substances");
        }
    }
}
