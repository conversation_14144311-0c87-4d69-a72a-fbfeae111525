﻿using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class EditProductViewModel
    {
        public bool IsApplicationAssigned { get; set; }
        public ProductModel Product { get; set; }
        public IList<ActiveSubstanceModel> ActiveSubstances { get; set; }
        public IList<ClientModel> AllClients { get; set; }
        public IList<PicklistDataModel> Picklists { get; set; }
    }
}
