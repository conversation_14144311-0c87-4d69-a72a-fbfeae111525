﻿using Microsoft.AspNetCore.Http;
using PharmaLex.SmartTRACE.Web.Models;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public interface INotificationService
    {
        Task SendNotification(string user, SubmissionModel submission, string application, string createdByUser, HttpRequest request, string product, string client, string publishers = null);
        Task SendNotification(string user, SubmissionModel submission, DocumentModel document, HttpRequest request);
    }
}
