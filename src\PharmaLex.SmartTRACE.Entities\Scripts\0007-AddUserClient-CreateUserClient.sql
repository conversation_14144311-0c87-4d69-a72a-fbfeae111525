﻿CREATE TRIGGER [dbo].[UserClient_Insert] ON [dbo].[UserClient]
FOR INSERT AS
INSERT INTO [Audit].[UserClient_Audit]
SELECT 'I', [UserId], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[UserClient_Update] ON [dbo].[UserClient]
FOR UPDATE AS
INSERT INTO [Audit].[UserClient_Audit]
SELECT 'U', [UserId], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[UserClient_Delete] ON [dbo].[UserClient]
FOR DELETE AS
INSERT INTO [Audit].[UserClient_Audit]
SELECT 'D', [UserId], [C<PERSON>Id], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO