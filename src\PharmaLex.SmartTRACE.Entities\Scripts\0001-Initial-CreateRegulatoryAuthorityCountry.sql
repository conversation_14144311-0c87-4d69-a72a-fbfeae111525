﻿CREATE TRIGGER [dbo].[RegulatoryAuthorityCountry_Insert] ON [dbo].[RegulatoryAuthorityCountry]
FOR INSERT AS
INSERT INTO [Audit].[RegulatoryAuthorityCountry_Audit]
SELECT 'I', [RegulatoryAuthorityId], [CountryId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[RegulatoryAuthorityCountry_Update] ON [dbo].[RegulatoryAuthorityCountry]
FOR UPDATE AS
INSERT INTO [Audit].[RegulatoryAuthorityCountry_Audit]
SELECT 'U', [RegulatoryAuthorityId], [CountryId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[RegulatoryAuthorityCountry_Delete] ON [dbo].[RegulatoryAuthorityCountry]
FOR DELETE AS
INSERT INTO [Audit].[RegulatoryAuthorityCountry_Audit]
SELECT 'D', [RegulatoryAuthorityId], [CountryId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO