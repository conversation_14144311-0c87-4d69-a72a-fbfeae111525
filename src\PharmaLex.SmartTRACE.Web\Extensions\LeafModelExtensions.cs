﻿using Azure.Storage.Blobs.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models;

namespace PharmaLex.SmartTRACE.Web
{
    public static class LeafModelExtensions
    {
        public static LeafModel ToLeafModel(this BlobItem blob, string submission) => new LeafModel()
        {
            text = blob.Name.Substring(blob.Name.LastIndexOf("/")).Trim('/'),
            href = blob.Name,
            Submission = submission,
            op = "New"
        };
    }
}
