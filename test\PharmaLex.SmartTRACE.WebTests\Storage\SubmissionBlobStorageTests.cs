﻿using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Sas;
using Microsoft.Extensions.Options;
using NSubstitute;
using PharmaLex.SmartTRACE.Web.DataFactory;
using PharmaLex.SmartTRACE.Web.Storage;

namespace PharmaLex.SmartTRACE.WebTests.Storage
{
    public class SubmissionBlobStorageTests
    {
        readonly IAzureDataFactoryManagementClient adfClient;
        readonly IDataFactoryAuthenticationPovider dataFactoryAuthenticationPovider;
        readonly IOptions<DataFactoryPipelineSettings> settings;
        readonly ISubmissionBlobStorage SubmissionBlobStorage;
        readonly SubmissionBlobContainer submissionBlobContainer;
        readonly BlobContainerClient azBlobContainerClient;
        readonly BlobClient azBlobClient;
        public SubmissionBlobStorageTests()
        {
            #region Fake storagesettings and blobServiceClient
            StorageSettings storageSettings = new StorageSettings();
            storageSettings.TenantId = Guid.NewGuid().ToString();
            storageSettings.Url = "url";
            storageSettings.Account = "account";
            storageSettings.ConnectionString = "ConnectionString";
            storageSettings.Container = "Container";
            var settingStorage = Substitute.For<IOptions<StorageSettings>>();
            settingStorage.Value.Returns(storageSettings);
            UserDelegationKey userDelegationKey = Substitute.For<UserDelegationKey>();
            AsyncPageable<BlobHierarchyItem> blobHierarchyItem = Substitute.For<AsyncPageable<BlobHierarchyItem>>();
            Response<UserDelegationKey> response = Substitute.For<Response<UserDelegationKey>>();
            response.Value.Returns(userDelegationKey);
            BlobSasBuilder blobSasBuilder = Substitute.For<BlobSasBuilder>();
            BlobServiceClient blobServiceClient = Substitute.For<BlobServiceClient>();
            azBlobContainerClient = Substitute.For<BlobContainerClient>();
            azBlobClient = Substitute.For<BlobClient>();
            azBlobClient.BlobContainerName.Returns("Contname");
            azBlobClient.Name.Returns("Contname");
            azBlobClient.Uri.Returns(new Uri("http://amee/test/"));
            blobServiceClient.Uri.Returns(new Uri("http://amee/test/"));
            blobServiceClient.GetUserDelegationKey(Arg.Any<DateTimeOffset?>(), Arg.Any<DateTimeOffset>()).ReturnsForAnyArgs(response);
            azBlobClient.GetParentBlobContainerClient().GetParentBlobServiceClient().Returns(blobServiceClient);
            SubmissionBlobStorage = Substitute.For<ISubmissionBlobStorage>();
            #endregion
            #region Settings-Pipeline
            settings = Substitute.For<IOptions<DataFactoryPipelineSettings>>();
            settings.Value.Returns(new DataFactoryPipelineSettings
            {
                AllSequencesPath = "/all",
                DocumentsPath = "/documents",
                FactoryName = "test",
                Instance = "test",
                ResourceGroupName = "test",
                Subscription = "72c56c64-39f3-4fdd-86c7-2c323bfa8679",
                UnpackPath = "test",
                UnpackPipelineName = "test",
                UploadPath = "test"
            });
            dataFactoryAuthenticationPovider = Substitute.For<IDataFactoryAuthenticationPovider>();
            dataFactoryAuthenticationPovider.CreateClientCrdential().Returns(new Azure.Identity.DefaultAzureCredential());
            #endregion

            azBlobContainerClient.GetBlobClient(Arg.Any<string>()).ReturnsForAnyArgs(azBlobClient);
            SubmissionBlobStorage.GetContainerAsync().ReturnsForAnyArgs(azBlobContainerClient);
            (azBlobContainerClient).GetBlobsByHierarchyAsync(BlobTraits.None, BlobStates.None, "", "", default).ReturnsForAnyArgs(blobHierarchyItem);
            adfClient = Substitute.For<IAzureDataFactoryManagementClient>();
            submissionBlobContainer = new SubmissionBlobContainer(SubmissionBlobStorage, adfClient, settings);

        }
        [Theory]
        [InlineData("cid", "11", "231", "2", "0.1")]
        [InlineData("", "11", "231", "2", "0.1")]
        [InlineData("cid", "", "231", "2", "0.1")]
        [InlineData("cid", "11", "", "2", "0.1")]
        [InlineData("cid", "11", "231", "", "0.1")]
        [InlineData("cid", "11", "231", "", "")]
        public void ClearSequenceAsync_CompletesAction(
            string clientId,
            string applicationId,
            string submissionUniqueId,
            string documentTypeId,
            string version)
        {

            adfClient.GetUnpackPath("file.zip").Returns("pathfile");
            adfClient.GetUploadPath("file.zip").Returns("uploadpathfile");
            adfClient.GetUnpackPath("file.zip", "cid", "11", "231", "2", "0.1").Returns("submissionUnpackedDirectoryName");
            adfClient.GetUploadPath("file.zip", "cid", "11", "231", "2", "0.1").Returns("path");

            var result = submissionBlobContainer.ClearSequenceAsync("file.zip", clientId, applicationId, submissionUniqueId, documentTypeId, version);
            Assert.Equal("RanToCompletion", result.Status.ToString());
        }

        [Theory]
        [InlineData("", "cid", "11", "231", "2", "0.1")]
        [InlineData("filename", "", "11", "231", "2", "0.1")]
        [InlineData("filename", "cid", "", "231", "2", "0.1")]
        [InlineData("filename", "cid", "11", "", "2", "0.1")]
        [InlineData("filename", "cid", "11", "231", "", "0.1")]
        [InlineData("filename", "cid", "11", "231", "", "")]
        [InlineData("", "", "", "", "", "")]
        public void GetFileDownloadAsync_WithConditions_CompletesAction(string file,
       string clientId,
       string applicationId,
       string submissionUniqueId,
       string documentTypeId,
       string version)
        {
            //Arrange
            adfClient.GetUnpackPath("file.zip").Returns("pathfile");
            adfClient.GetUploadPath("file.zip").Returns("uploadpathfile");
            adfClient.GetUploadPath("file.zip", "cid", "11", "231", "2", "0.1").Returns("path");

            //Act 
            var result = submissionBlobContainer.GetFileDownloadAsync("file.zip", file, clientId, applicationId, submissionUniqueId, documentTypeId, version);
            Assert.Equal("RanToCompletion", result.Status.ToString());
        }
        [Fact]
        public void GetFileDownloadAsync_CompletesAction()
        {
            //Arrange
            adfClient.GetUnpackPath("file", "clientid", "001").Returns("unpackedDirectoryName");
            //Act 
            var rest = submissionBlobContainer.GetFileDownloadAsync("file", "clientid", "001");
        }
        [Theory]
        [InlineData("filename", "cid", "11", "231", "2", "0.1")]
        [InlineData("filename", "", "11", "231", "2", "0.1")]
        [InlineData("filename", "cid", "", "231", "2", "0.1")]
        [InlineData("filename", "cid", "11", "", "2", "0.1")]
        [InlineData("filename", "cid", "11", "23", "", "0.1")]
        [InlineData("", "", "", "", "", "")]
        public async Task GetFileDownloadAsyncWithData_CompletesAction(string file, string clientId, string applicationId, string submissionUniqueId, string documentTypeId, string version)
        {
            //Arrange
            adfClient.GetUnpackPath("file.zip").Returns("pathfile");
            adfClient.GetUploadPath("file.zip").Returns("uploadpathfile");
            adfClient.GetUploadPath("file.zip", "cid", "11", "231", "2", "0.1").Returns("path");

            //Act and Assert
            var ex = await Assert.ThrowsAsync<ArgumentNullException>(() => submissionBlobContainer.GetFileDownloadLinkAsync("file.zip", file, clientId, applicationId, submissionUniqueId, documentTypeId, version));

        }

        [Fact]
        public async Task GetFileDownloadLinkAsync_ThrowsArgException()
        {
            //Arrange
            adfClient.GetUnpackPath("filename", "clientname", "appid").Returns("unpackedDirectoryName");

            //Act and Assert
            var ex = await Assert.ThrowsAsync<ArgumentNullException>(() => submissionBlobContainer.GetFileDownloadLinkAsync("filename", "clientname", "appid"));

        }
        [Theory]
        [InlineData("cid", "11", "231", "2", "0.1")]
        [InlineData("", "11", "231", "2", "0.1")]
        [InlineData("cid", "", "231", "2", "0.1")]
        [InlineData("cid", "11", "", "2", "0.1")]
        [InlineData("cid", "11", "231", "", "0.1")]
        [InlineData("cid", "11", "231", "", "")]
        [InlineData("", "", "", "", "")]
        public void UnpackSequenceAsync_CompletesAction(
             string clientId,
         string applicationId,
         string submissionUniqueId,
         string documentTypeId,
         string version)
        {

            //Arrange
            adfClient.RunPipelineAsync(Arg.Any<IDictionary<string, object>>(), Arg.Any<string>(), null).ReturnsForAnyArgs(true);
            adfClient.GetUploadPath(null, clientId, applicationId, submissionUniqueId, documentTypeId, version).ReturnsForAnyArgs("dict");
            adfClient.GetUnpackPath(null, clientId, applicationId, submissionUniqueId, documentTypeId, version).ReturnsForAnyArgs("dict");
            adfClient.GetUploadPath(null, clientId, applicationId, submissionUniqueId).ReturnsForAnyArgs("dict");
            adfClient.GetUnpackPath(null, clientId, applicationId).ReturnsForAnyArgs("dict");
            //Act 
            var result = submissionBlobContainer.UnpackSequenceAsync("file", clientId, applicationId, submissionUniqueId, documentTypeId, version);
            Assert.Equal("RanToCompletion", result.Status.ToString());
            Assert.True(result.IsCompleted);
            Assert.True(result?.Result);
        }
        [Theory]
        [InlineData("cid", "11", "231", "2", "0.1")]
        [InlineData("", "11", "231", "2", "0.1")]
        [InlineData("cid", "", "231", "2", "0.1")]
        [InlineData("cid", "11", "", "2", "0.1")]
        [InlineData("cid", "11", "231", "", "0.1")]
        [InlineData("cid", "11", "231", "", "")]
        public void GetSequenceUploadLinkAsync_CompletesAction(
         string clientId,
         string applicationId,
         string submissionUniqueId,
         string documentTypeId,
         string version)
        {

            adfClient.GetUploadPath("file.zip", clientId, applicationId, submissionUniqueId, documentTypeId, version).Returns("pathfile");
            adfClient.GetUploadPath("file.zip", clientId, applicationId, submissionUniqueId).Returns("uploadpathfile");

            var result = submissionBlobContainer.GetSequenceUploadLinkAsync("file.zip", clientId, applicationId, submissionUniqueId, documentTypeId, version);
            Assert.Equal("Faulted", result.Status.ToString());
        }
        [Fact]
        public async Task ListSequencesAsync_CompletesAction()
        {
            List<string> list = new();
            //Arrange
            adfClient.GetUnpackPath().Returns("unpackedDirectoryName");
            //Act 
            var result = await submissionBlobContainer.ListSequencesAsync();
            //Assert
            Assert.Equal(list, result);
        }
        [Theory]
        [InlineData("zip.zip", "cid", "11", "231", "2", "0.1")]
        [InlineData("", "cid", "11", "231", "2", "0.1")]
        [InlineData("zip.zip", "cid", "", "231", "2", "0.1")]
        [InlineData("zip.zip", "cid", "11", "", "2", "0.1")]
        [InlineData("zip.zip", "cid", "11", "231", "", "0.1")]
        [InlineData("zip.zip", "cid", "11", "231", "", "")]
        public void CopySequenceFromPublicToPrivate_CompletesAction(string zipFileName,
         string clientId,
         string applicationId,
         string submissionUniqueId,
         string documentTypeId,
         string version)
        {
            //Arrange
            adfClient.RunPipelineAsync(Arg.Any<IDictionary<string, object>>(), Arg.Any<string>(), null).ReturnsForAnyArgs(true);
            //Act
            var result = submissionBlobContainer.CopySequenceFromPublicToPrivate(zipFileName, clientId, applicationId,
             submissionUniqueId,
             documentTypeId,
             version);
            //Assert 
            Assert.True(result.IsCompleted);
            Assert.Equal("RanToCompletion", result.Status.ToString());
        }
        [Fact]
        public void UpdateSequenceMetadata_CompletesAction()
        {
            string zipFileName = "zip.zip";
            string clientId = "clientid";
            string applicationId = "applicationId";
            string submissionUniqueId = "submissionUniqueId";
            string documentTypeId = "documentTypeId";
            string version = "version";

            //Arrange
            adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version).ReturnsForAnyArgs("path");
            var result = submissionBlobContainer.UpdateSequenceMetadata(zipFileName, clientId, applicationId,
             submissionUniqueId,
             documentTypeId,
             version);
            //Assert 
            Assert.True(result.IsCompleted);
            Assert.Equal("RanToCompletion", result.Status.ToString());
        }
        [Fact]
        public void GetFullFilePath_ReturnsPath()
        {
            string zipFileName = "zip.zip";
            string clientId = "clientid";
            string applicationId = "applicationId";

            //Arrange
            adfClient.GetUnpackPath(zipFileName, clientId, applicationId).ReturnsForAnyArgs("path");
            //Act 
            var result = submissionBlobContainer.GetFullFilePath(zipFileName, "file", clientId, applicationId);
            //Assert 
            Assert.Equal("path/applicationId", result);
        }
        [Fact]
        public void GetFullFilePath_With_Condition_ReturnsPath()
        {
            string zipFileName = "";
            string clientId = "clientid";
            string applicationId = "applicationId";

            //Arrange
            adfClient.GetUnpackPath(zipFileName, clientId, applicationId).ReturnsForAnyArgs("path");
            //Act 
            var result = submissionBlobContainer.GetFullFilePath(zipFileName, "file", clientId, "");
            //Assert 
            Assert.Equal("path", result);
        }
        [Fact]
        public void GetFullFilePath_CompletesAction_ReturnsPath()
        {
            string zipFileName = "file";

            //Arrange
            adfClient.GetUnpackPath(zipFileName).ReturnsForAnyArgs("path");
            //Act 
            var result = submissionBlobContainer.GetFullFilePath(zipFileName, "", "", "filepath");
            //Assert 
            Assert.Equal("path/filepath", result);
        }
        [Fact]
        public void GetFullFilePath_ReturnsPath_ForEmptyValues()
        {
            string zipFileName = "file";

            //Arrange
            adfClient.GetUnpackPath(zipFileName).ReturnsForAnyArgs("path");
            //Act 
            var result = submissionBlobContainer.GetFullFilePath(zipFileName, "", "", "");
            //Assert 
            Assert.Equal("path", result);
        }
        [Fact]
        public void UpdateSequenceMetadata_CompletesAction_ForDossierType()
        {
            string zipFileName = "zip.zip";
            string clientId = "clientid";
            string applicationId = "applicationId";
            string submissionUniqueId = "submissionUniqueId";
            string documentTypeId = "3";
            string version = "version";

            //Arrange
            adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId).ReturnsForAnyArgs("path");
            var result = submissionBlobContainer.UpdateSequenceMetadata(zipFileName, clientId, applicationId,
             submissionUniqueId,
             documentTypeId,
             version);
            //Assert 
            Assert.True(result.IsCompleted);
        }
        [Fact]
        public void GetUploadPath_CompletesAction_ReturnsPath()
        {
            string zipFileName = "zip.zip";
            string clientId = "clientid";
            string applicationId = "applicationId";
            string submissionUniqueId = "submissionUniqueId";
            string documentTypeId = "documentTypeId";
            string version = "version";

            //Arrange
            adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version).ReturnsForAnyArgs("path");
            //Act 
            var result = submissionBlobContainer.GetUploadPath(zipFileName, "file", clientId, applicationId, submissionUniqueId, documentTypeId, version);
            //Assert 
            Assert.Equal("path", result);
        }
        [Fact]
        public void GetSequences_ReturnsSequnceList()
        {
            string zipFileName = "zip.zip";
            //Arrange
            //Act 
            var result = submissionBlobContainer.GetSequences(zipFileName, false);
            //Assert 
            Assert.Equal(new List<string>(), result?.Result);
        }
        [Fact]
        public void GetSequences_ReturnsRecursiveList()
        {
            //Arrange
            string zipFileName = "zip.zip";
            //Act 
            var result = submissionBlobContainer.GetSequences(zipFileName, true);
            //Assert 
            Assert.Equal(new List<string>(), result?.Result);
        }
        [Fact]
        public void GetUnpackPath_CompletesAction_ReturnsPath()
        {
            string zipFileName = "zip.zip";
            string clientId = "clientid";
            string applicationId = "applicationId";
            string submissionUniqueId = "submissionUniqueId";
            string documentTypeId = "documentTypeId";
            string version = "version";

            //Arrange
            adfClient.GetUnpackPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version).ReturnsForAnyArgs("path");
            var result = submissionBlobContainer.GetUnpackPath(zipFileName, "file", clientId, applicationId, submissionUniqueId, documentTypeId, version);
            //Assert 
            Assert.Equal("path", result);
        }
    }
}
