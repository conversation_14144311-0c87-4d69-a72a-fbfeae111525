﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public static class AuthorizationHelper
    {
        public static IServiceCollection AddPolicies(this IServiceCollection services)
        {
            var repoFactory = services.BuildServiceProvider().GetRequiredService<IRepositoryFactory>();
            List<Claim> claims = repoFactory.Create<Claim>().Configure().ToList();

            Action<AuthorizationOptions> configure = o =>
            {
                foreach (Claim c in claims.Where(x => x.ClaimType == "admin"))
                {
                    o.AddPolicy(c.Name, p => p.RequireAssertion(x => x.User.HasClaim(y => $"{c.ClaimType}:{c.Name}" == y.Type || y.Type == "admin:SuperAdmin")));
                }
                o.AddPolicy("Admin", p => p.RequireAssertion(c => c.User.HasClaim(x => ClaimsHierarchy.GetAllAdmins().Contains(x.Type))));

                o.AddPolicy("Editor", p => p.RequireAssertion(c => c.User.HasClaim(x =>
                {
                    if (x.Type.StartsWith("application:") || x.Type.StartsWith("admin:"))
                    {
                        return ClaimsHierarchy.GetAllEditors().Contains(x.Type);
                    }

                    return false;
                })));

                o.AddPolicy("Reader", p => p.RequireAssertion(c => c.User.HasClaim(x => ClaimsHierarchy.GetAllReaders().Contains(x.Type))));

                o.AddPolicy("ExternalEditor", p => p.RequireAssertion(c => c.User.HasClaim(x => ClaimsHierarchy.GetAllExternalEditors().Contains(x.Type))));

                o.AddPolicy("PrivilegedExternalEditor", p => p.RequireAssertion(c => c.User.HasClaim(x => ClaimsHierarchy.GetAllPrivilegedExternalEditor().Contains(x.Type))));
            };
            return services.Configure(configure);
        }
    }
}
