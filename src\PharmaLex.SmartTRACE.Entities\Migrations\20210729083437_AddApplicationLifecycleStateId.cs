﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class AddApplicationLifecycleStateId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "LifecycleStateId",
                schema: "Audit",
                table: "Application_Audit",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "LifecycleStateId",
                table: "Application",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.SqlFileExec("0017-AddApplicationLifecycleStateId-UpdateApplicationTriggers.sql");

            migrationBuilder.SqlFileExec("0017-AddApplicationLifecycleStateId_InsertActiveState.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LifecycleStateId",
                schema: "Audit",
                table: "Application_Audit");

            migrationBuilder.DropColumn(
                name: "LifecycleStateId",
                table: "Application");
        }
    }
}
