﻿@model PharmaLex.SmartTRACE.Web.Models.UserClientsModel
@using System.Web
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@using AutoMapper
@using PharmaLex.SmartTRACE.Entities
@using PharmaLex.Caching.Data
@using PharmaLex.SmartTRACE.Web.Helpers
@using PharmaLex.SmartTRACE.Web.Helpers.Extensions
@using PharmaLex.SmartTRACE.Entities.Enums

@inject IDistributedCacheServiceFactory cache
@inject IMapper mapper
@{
    ViewData["Title"] = "Manage Users";
    var userClaims = this.User.Claims.Where(x => x.Type.StartsWith("admin:") || x.Type.StartsWith("application:")).Select(x => x.Type);
    var allowedClaims = new List<string>();
    foreach (var claim in userClaims)
    {
        allowedClaims.AddRange(ClaimsHierarchy.GetClaims()[claim]);
    }
    IEnumerable<Claim> _claims = (await cache.CreateEntity<Claim>().WhereAsync(x => x.Name != "ExternalEditor"))
        .Where(c => allowedClaims.Contains($"{c.ClaimType}:{c.Name}"));
    var selectedClaims = _claims.Select(c => new { Id = c.Id, Name = c.Name });
    var claims = selectedClaims.Where(x => x.Name.Contains("SuperAdmin")).Concat(selectedClaims.Where(x => x.Name.Contains("Admin") && !x.Name.Contains("SuperAdmin")).OrderBy(x => x.Name))
                    .Concat(selectedClaims.Where(x => !x.Name.Contains("Admin")).OrderBy(x => x.Name));

    var userTypes = mapper.Map<IEnumerable<UserTypeList>>(Enum.GetValues(typeof(UserType))).Where(x => x.Name != UserType.External.ToString()).ToSelectList();
}

<div id="user" class="manage-container" v-cloak>
    <header class="manage-header">
        <h2>@Model.User.DisplayNameAndEmail</h2>
        <a href="/manage/users" class="button secondary icon-button-back">Back to User list</a>
    </header>
    <form id="EditUser-form" method="post" class="user-form">
        <div class="form-col form-col-third">
            <div id="search-column" v-bind:class="['form-col', {'hidden': user}]">
                <h5>Find user</h5>
                <label for="userserachac">Start typing the user's name or email address</label>
                <autocomplete v-if="active" :config="config" v-on:selected="selectedItem"></autocomplete>
            </div>

            <div id="details-column" v-bind:class="['form-col', {'hidden': !user}]" v-if="user !== null">
                <h5>Details</h5>
                <label for="User.GivenName">Given name</label>
                <input asp-for="User.GivenName" :value="user.givenName" type="text" readonly="readonly" />

                <label for="User.FamilyName">Family name</label>
                <input asp-for="User.FamilyName" :value="user.familyName" type="text" readonly="readonly" />
                <label for="User.Email">Email</label>
                <input asp-for="User.Email" :value="user.email ? user.email : user.value" type="text" readonly="readonly" />

                <label for="User.UserTypeId" asp-for="User.UserTypeId">User Type</label>
                <div class="select-wrapper">
                    <select asp-for="User.UserTypeId" asp-items="userTypes" required>
                        <option value="">Select user type</option>
                    </select>
                </div>


                <label for="fieldActive">Auto-update access to newly added clients</label>
                <label class="switch-container">
                    No
                    <input id="fieldActive" type="checkbox" class="switch" v-model="user.autoAccessClients" :value="user.autoAccessClients" />
                    <label for="fieldActive" class="switch"></label>
                    Yes
                </label>
            </div>

            <div id="claims-column" v-bind:class="['form-col claims-col', {'form-col-disabled': !user}]">
                <h5>Roles</h5>
                <div v-for="claim in displayedClaims" :key="claim.id" class="checkbox-list-item">
                    <input v-bind="bindClaim(claim)" v-on:click="onClaimSelected($event)" />
                    <label :for="'claim-' + claim.id">{{claim.name}}</label>
                </div>
            </div>
        </div>

        <div id="clients-column" class="form-col form-col-two-third">
            <h5>Clients</h5>
            <div class="clients-col">
                <div class="checkbox-list-item">
                    <input id="All" type="checkbox" v-on:click="selectAllClients($event)" />
                    <label for="All">Select all</label>
                </div>
                <div v-for="client in clients" :key="client.id" class="checkbox-list-item client-item">
                    <input v-bind="bindClient(client)" v-on:click="onClientSelected($event)" />
                    <label :for="'client-' + client.id" :title="client.name">{{client.name}}</label>
                </div>
            </div>
        </div>
        <input type="hidden" asp-for="User.Id" />
        <input type="hidden" asp-for="User.AutoAccessClients" v-if="user !== null" v-model="user.autoAccessClients" />
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/manage/users">Cancel</a><button class="icon-button-save">Save</button>
        </div>
    </form>
</div>
<script src="@Cdn.GetUrl("lib/jquery/dist/jquery.min.js")"></script>
<script>
    $(document).ready(function () {

        $('#EditUser-form').submit(function () {

            var inputValue1 = $('#User_FamilyName').val();
            var inputValue2 = $('#User_GivenName').val();
            var item = [{ '#User_FamilyName': inputValue1 }, { '#User_GivenName': inputValue2 }];
            $.each(item, function (index, obj) {
                $.each(obj, function (key, value) {
                    var unmatchedChars = findUnmatchedCharacters(value);
                    if (unmatchedChars._value.length > 0) {
                        event.preventDefault();
                        if ($("#validationMessage_" + key.substring(1)).length == 0)
                        {
                            $('<div id = "validationMessage_' + key.substring(1) + '" style = "color:red;padding:10px"> </div>').insertAfter($(key));
                            $('#validationMessage_' + key.substring(1) + '').text("Name contains invalid characters: " + unmatchedChars._value.join(' '));
                            return false;
                        }
                        
                    }
                });
            });
            $('#validationMessage').text('');
            return true;
        });
    });
</script>
@section Scripts {
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#user',
            data() {
                return {
                    config: {
                        canAddNew: false,
                        searchUrl: '/manage/users/find?term={term}',
                        nameattribute: 'userserachac'
                    },
                    user: null,
                    active: true,
                    isModelSet: false,
                    claims: @Html.Raw(Json.Serialize(claims)),
                    clients: @Html.Raw(Json.Serialize(Model.Clients)),
                    checkedClaims: [],
                    previousSelectedClients: [],
                    userAdminClaim: null
                };
            },
            methods: {
                validateForm: function (event) {
                    this.attemptSubmit = true;
                    if (this.missingGivenName || this.missingFamilyName || this.missingEmail) {
                        event.preventDefault();
                    }
                },
                selectedItem(selectedUser) {
                    this.user = selectedUser;
                    this.isModelSet = true;
                    this.user.autoAccessClients = false;
                    /* Id has a value 0, when the selected user does not exist
                     * in the db of the application, but comes from the active directory only*/
                    if (this.user.id > 0) {
                        document.location.href = '/manage/users/edit/' + this.user.id;
                    }
                },
                bindClaim(claim) {
                    let binding = {
                        id: `claim-${claim.id}`,
                        value: `${claim.id}`,
                        name: 'User.Claims',
                        type: 'checkbox'
                    }

                    if (this.user && this.user.claims && this.user.claims.includes(claim.id)) {
                        binding.checked = 'checked';
                        if (!this.checkedClaims.includes(claim.name)) {
                            this.checkedClaims.push(claim.name);
                        }
                    }

                    return binding;
                },
                selectAllClients(value) {
                    Array.from(document.getElementById("clients-column").getElementsByTagName("input"))
                        .forEach(x => {
                            if (x.checked && !this.previousSelectedClients.includes(x.id)) {
                                this.previousSelectedClients.push(x.id);
                            }

                            x.checked = value.currentTarget.checked;
                        });

                    if (!value.currentTarget.checked) {
                        this.previousSelectedClients = Vue.ref([]);
                    }
                },
                deselectAllClients() {
                    Array.from(document.getElementById("clients-column").getElementsByTagName("input"))
                        .forEach(x => {
                            if (!this.previousSelectedClients.includes(x.id)) {
                                x.checked = false;
                            }
                        });
                },
                selectAllButton() {
                    let allCheckbox = document.getElementById('All');
                    allCheckbox.checked = true;
                },
                bindClient(client) {
                    let binding = {
                        id: `client-${client.id}`,
                        value: `${client.id}`,
                        name: 'User.Clients',
                        type: 'checkbox'
                    }

                    if (this.user && this.user.clients && this.user.clients.includes(client.id)) {
                        binding.checked = 'checked';
                    }

                    return binding;
                },
                onClientSelected(client) {
                    if (client.currentTarget.checked) {
                        this.previousSelectedClients.push(client.currentTarget.id);
                        var checkedClients = Array.from(document.getElementById("clients-column").getElementsByTagName("input")).filter(x => x.checked === true);
                        if (checkedClients.length === this.clients.length) {
                            this.selectAllButton();
                        }
                    } else {
                        let index = this.previousSelectedClients.indexOf(client.currentTarget.id);
                        if (index > -1) {
                            this.previousSelectedClients.splice(index, 1);
                        }
                        document.getElementById('All').checked = false;
                    }
                },
                hasClaimsAdmin() {
                    let hasAdmin = false;
                    this.checkedClaims.forEach(claim => {
                        if (claim.toLowerCase().includes('admin')) {
                            hasAdmin = true;
                        }
                    });

                    return hasAdmin;
                },
                removeCheckedClaim(claimName) {
                    let index = this.checkedClaims.indexOf(claimName);
                    if (index > -1) {
                        this.checkedClaims.splice(index, 1);
                    }
                },
                onClaimSelected(claim) {
                    if (claim.currentTarget.checked) {
                        let claimName = claim.currentTarget.labels[0].innerText;
                        let claimId = +claim.currentTarget.id.substring(6, 7);

                        if (!this.user.claims) {
                            this.user.claims = Vue.ref([]);
                        }

                        this.user.claims.push(claimId);
                        this.checkedClaims.push(claimName);
                    }
                    else {
                        let claimName = claim.currentTarget.labels[0].innerText;
                        let claimId = +claim.currentTarget.id.substring(6, 7);

                        this.removeCheckedClaim(claimName);

                        if (this.user.claims) {
                            let index = this.user.claims.indexOf(claimId);
                            if (index !== -1) {
                                this.user.claims.splice(index, 1);
                            }
                        }

                        if (claimName.includes('BusinessAdmin')) {
                            document.getElementById('claim-' + this.userAdminClaim.id).checked = false;
                            let index = this.user.claims.indexOf(this.userAdminClaim.id);
                            if (index !== -1) {
                                this.user.claims.splice(index, 1);
                            }
                            this.removeCheckedClaim(this.userAdminClaim.name);
                        }
                    }
                }
            },
            computed: {
                displayedClaims() {
                    if (!this.checkedClaims.includes("BusinessAdmin")) {
                        return this.claims.filter(x => x.name !== "UserAdmin");
                    }
                    return this.claims;

                }
            },
            created() {
                if (@Model.User.Id !== 0) {
                    var userJson = '@Html.Raw(HttpUtility.JavaScriptStringEncode(JsonConvert.SerializeObject(Model.User, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })))';
                    this.user = JSON.parse(userJson);
                    this.isModelSet = true;
                }

                this.userAdminClaim = this.claims.find(x => x.name === 'UserAdmin');
            },
            mounted() {
                if (this.user && this.user.clients) {
                    if (this.user.clients.length == this.clients.length) {
                        this.selectAllButton();
                    }
                    Array.from(this.user.clients).forEach(x => {
                        this.previousSelectedClients.push(`client-${x}`);
                    });
                }
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/AutoCompleteList" />
}