﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class AddDocumentFileStateId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "StateId",
                schema: "Audit",
                table: "DocshifterDocumentFile_Audit",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "StateId",
                table: "DocshifterDocumentFile",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.SqlFileExec("0022-AddDocumentFileStateId-UpdateDocshifterDocumentFileTriggers.sql");
            migrationBuilder.SqlFileExec("0022-AddDocumentFileStateId-TransferDocumentFileStates.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "StateId",
                schema: "Audit",
                table: "DocshifterDocumentFile_Audit");

            migrationBuilder.DropColumn(
                name: "StateId",
                table: "DocshifterDocumentFile");
        }
    }
}
