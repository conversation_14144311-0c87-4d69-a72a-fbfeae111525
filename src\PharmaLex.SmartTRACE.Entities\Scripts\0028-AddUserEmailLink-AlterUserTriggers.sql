﻿ALTER TRIGGER [dbo].[User_Insert] ON [dbo].[user]
FOR INSERT AS
INSERT INTO [Audit].[User_Audit]
(AuditAction,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [AutoAccessClients], [UserTypeId], [InvitationEmailLink])
SELECT 'I' ,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [AutoAccessClients], [UserTypeId], [InvitationEmailLink] FROM [Inserted]
GO
 
ALTER TRIGGER [dbo].[User_Update] ON [dbo].[user]
FOR UPDATE AS
INSERT INTO [Audit].[User_Audit]
(<PERSON>tAction,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [AutoAccessClients], [UserTypeId], [InvitationEmailLink])
SELECT 'U' ,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [AutoAccessClients], [UserTypeId], [InvitationEmailLink] FROM [Inserted]
GO
 
ALTER TRIGGER [dbo].[User_Delete] ON [dbo].[user]
FOR DELETE AS
INSERT INTO [Audit].[User_Audit]
(AuditAction,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [AutoAccessClients], [UserTypeId], [InvitationEmailLink])
SELECT 'D' ,Id, [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()), [AutoAccessClients], [UserTypeId], [InvitationEmailLink] FROM [Deleted]
GO
 
