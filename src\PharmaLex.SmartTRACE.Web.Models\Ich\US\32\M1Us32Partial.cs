﻿using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32;
using PharmaLex.SmartTRACE.Web.Models.Ich.US;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Models.eCTD32.US32
{
    public partial class leaf : CtdSectionBase, Ileaf
    {
        public string text => this.title.Value;

        public string op
        {
            get
            {
                string opr = this.operation.ToString();
                return $"{ char.ToUpper(opr[0]) }{ opr.Substring(1) }";
            }
        }

        public string Submission { get; set; }
        public string Path { get; set; }
    }

    public partial class title : CtdSectionBase
    {
    }

    public partial class linktext : CtdSectionBase
    {
    }

    public partial class xref : CtdSectionBase
    {
    }

    public partial class nodeextension : CtdSectionBase, Inodeextension
    {
        public override string CtdName => $"{this.title} {this.ID}";
        public override string CtdModule => string.Empty;
        public override string CtdSection => string.Empty;
    }

    public partial class admin : UsAdmin32
    {
        public override UsApplicantInformation Applicant => new UsApplicantInformation
        {
            CompanyName = this.applicantinfo?.companyname,
            Date = DateTime.ParseExact(this.applicantinfo?.dateofsubmission?.date.Value, "yyyymmdd",
                                       System.Globalization.CultureInfo.InvariantCulture).ToString("yyyy-mm-dd"),
            ProductName = string.Join(',', this.productdescription?.prodname.Select(x => x.Value))
        };

        public override UsApplicationInformation Application => new UsApplicationInformation
        {
            SequenceNumber = this.applicationinformation?.submission?.sequencenumber,
            SubmissionType = this.applicationinformation?.submission?.submissiontype.ToString(),
            ApplicationType = this.applicationinformation?.applicationtype.ToString(),
            ApplicationNumber = this.productdescription?.applicationnumber,
            RelatedSequenceNumber = string.Join(',', this.applicationinformation?.submission?.relatedsequencenumber ?? new string[0])  
        };
    }

    public partial class applicantinfo : CtdSectionBase
    {
    }

    public partial class dateofsubmission : CtdSectionBase
    {
    }

    public partial class date : CtdSectionBase
    {
    }

    public partial class productdescription : CtdSectionBase
    {
    }

    public partial class prodname : CtdSectionBase
    {
    }

    public partial class applicationinformation : CtdSectionBase
    {
    }

    public partial class submission : CtdSectionBase
    {
    }

    public partial class m1regional : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m11forms,
                    this.m12coverletters,
                    this.m13administrativeinformation,
                    this.m14references,
                    this.m15applicationstatus,
                    this.m16meetings,
                    this.m17fasttrack,
                    this.m18specialprotocolassessmentrequest,
                    this.m19pediatricadministrativeinformation,
                    this.m110disputeresolution,
                    this.m111informationamendment,
                    this.m112othercorrespondence,
                    this.m113annualreport,
                    this.m114labeling,
                    this.m115promotionalmaterial,
                    this.m116riskmanagementplans
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m11forms : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>()
                {
                    this.m111fdaform1571,
                    this.m112fdaform356h,
                    this.m113fdaform3397,
                    this.m114fdaform2252,
                    this.m115fdaform2253,
                    this.m116fdaform2567
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m111fdaform1571 : CtdSectionBase
    {
        public override string CtdName => "Application form: FDA form 1571";
    }

    public partial class m112fdaform356h : CtdSectionBase
    {
        public override string CtdName => "Application form: FDA form 356h";
    }

    public partial class m113fdaform3397 : CtdSectionBase
    {
        public override string CtdName => "User fee cover sheet: FDA form 3397";
    }

    public partial class m114fdaform2252 : CtdSectionBase
    {
        public override string CtdName => "Annual report transmittal: FDA form 2252";
    }

    public partial class m115fdaform2253 : CtdSectionBase
    {
        public override string CtdName => "Advertisements and promotional labeling transmittal: FDA form 2253";
    }

    public partial class m116fdaform2567 : CtdSectionBase
    {
        public override string CtdName => "Transmittal of Labels and Circulars: FDA form 2567";
    }

    public partial class m12coverletters : CtdSectionBase
    {
    }

    public partial class m13administrativeinformation : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m131applicantinformation != null)
                {
                    nodes.AddRange(this.m131applicantinformation);
                }
                if (this.m132fieldcopycertification != null)
                {
                    nodes.AddRange(this.m132fieldcopycertification);
                }
                if (this.m133debarmentcertification != null)
                {
                    nodes.AddRange(this.m133debarmentcertification);
                }
                if (this.m134financialcertificationdisclosure != null)
                {
                    nodes.AddRange(this.m134financialcertificationdisclosure);
                }
                if (this.m135patentexclusivity != null)
                {
                    nodes.AddRange(this.m135patentexclusivity);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m131applicantinformation : CtdSectionBase
    {
        public override string CtdName => "Contact/sponsor/applicant information";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1311changeofaddressorcorporatename != null)
                {
                    nodes.AddRange(this.m1311changeofaddressorcorporatename);
                }
                if (this.m1312changecontactagent != null)
                {
                    nodes.AddRange(this.m1312changecontactagent);
                }
                if (this.m1313changeinsponsor != null)
                {
                    nodes.AddRange(this.m1313changeinsponsor);
                }
                if (this.m1314transferobligation != null)
                {
                    nodes.AddRange(this.m1314transferobligation);
                }
                if (this.m1315changeapplicationownership != null)
                {
                    nodes.AddRange(this.m1315changeapplicationownership);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m1311changeofaddressorcorporatename : CtdSectionBase
    {
    }

    public partial class m1312changecontactagent : CtdSectionBase
    {
        public override string CtdName => "Change in contact/agent";
    }

    public partial class m1313changeinsponsor : CtdSectionBase
    {
    }

    public partial class m1314transferobligation : CtdSectionBase
    {
        public override string CtdName => "Transfer of obligation";
    }

    public partial class m1315changeapplicationownership : CtdSectionBase
    {
        public override string CtdName => "Change in ownership of an application";
    }

    public partial class m132fieldcopycertification : CtdSectionBase
    {
    }

    public partial class m133debarmentcertification : CtdSectionBase
    {
    }

    public partial class m134financialcertificationdisclosure : CtdSectionBase
    {
    }

    public partial class m135patentexclusivity : CtdSectionBase
    {
        public override string CtdName => "Patent and exclusivity";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1351patentinformation != null)
                {
                    nodes.AddRange(this.m1351patentinformation);
                }
                if (this.m1352patentcertification != null)
                {
                    nodes.AddRange(this.m1352patentcertification);
                }
                if (this.m1353exclusivityrequest != null)
                {
                    nodes.AddRange(this.m1353exclusivityrequest);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m1351patentinformation : CtdSectionBase
    {
    }

    public partial class m1352patentcertification : CtdSectionBase
    {
    }

    public partial class m1353exclusivityrequest : CtdSectionBase
    {
    }

    public partial class m14references : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m141letterauthorization != null)
                {
                    nodes.AddRange(this.m141letterauthorization);
                }
                if (this.m142statementrightreference != null)
                {
                    nodes.AddRange(this.m142statementrightreference);
                }
                if (this.m143listofauthorizedpersonstoincorporatebyreference != null)
                {
                    nodes.AddRange(this.m143listofauthorizedpersonstoincorporatebyreference);
                }
                if (this.m144crossreferenceotherapplications != null)
                {
                    nodes.AddRange(this.m144crossreferenceotherapplications);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m141letterauthorization : CtdSectionBase
    {
        public override string CtdName => "Letter of authorization";
    }

    public partial class m142statementrightreference : CtdSectionBase
    {
        public override string CtdName => "Statement of right of reference";
    }

    public partial class m143listofauthorizedpersonstoincorporatebyreference : CtdSectionBase
    {
    }

    public partial class m144crossreferenceotherapplications : CtdSectionBase
    {
        public override string CtdName => "Cross reference to other applications";
    }

    public partial class m15applicationstatus : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m151withdrawalrequest != null)
                {
                    nodes.AddRange(this.m151withdrawalrequest);
                }
                if (this.m152inactivationrequest != null)
                {
                    nodes.AddRange(this.m152inactivationrequest);
                }
                if (this.m153reactivationrequest != null)
                {
                    nodes.AddRange(this.m153reactivationrequest);
                }
                if (this.m154reinstatementrequest != null)
                {
                    nodes.AddRange(this.m154reinstatementrequest);
                }
                if (this.m155withdrawalunapprovednda != null)
                {
                    nodes.AddRange(this.m155withdrawalunapprovednda);
                }
                if (this.m156withdrawaloflisteddrug != null)
                {
                    nodes.AddRange(this.m156withdrawaloflisteddrug);
                }
                if (this.m157requestwithdrawalapplicationapproval != null)
                {
                    nodes.AddRange(this.m157requestwithdrawalapplicationapproval);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m151withdrawalrequest : CtdSectionBase
    {
    }

    public partial class m152inactivationrequest : CtdSectionBase
    {
    }

    public partial class m153reactivationrequest : CtdSectionBase
    {
    }

    public partial class m154reinstatementrequest : CtdSectionBase
    {
    }
    public partial class m155withdrawalunapprovednda : CtdSectionBase
    {
        public override string CtdName => "Withdrawal of an unapproved NDA";
    }

    public partial class m156withdrawaloflisteddrug : CtdSectionBase
    {
    }

    public partial class m157requestwithdrawalapplicationapproval : CtdSectionBase
    {
        public override string CtdName => "Request for withdrawal of application approval";
    }

    public partial class m16meetings : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m161meetingrequest != null)
                {
                    nodes.AddRange(this.m161meetingrequest);
                }
                if (this.m162meetingbackgroundmaterials != null)
                {
                    nodes.AddRange(this.m162meetingbackgroundmaterials);
                }
                if (this.m163correspondenceregardingmeetings != null)
                {
                    nodes.AddRange(this.m163correspondenceregardingmeetings);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m161meetingrequest : CtdSectionBase
    {
    }

    public partial class m162meetingbackgroundmaterials : CtdSectionBase
    {
    }

    public partial class m163correspondenceregardingmeetings : CtdSectionBase
    {
    }

    public partial class m17fasttrack : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m171fasttrackdesignationrequest != null)
                {
                    nodes.AddRange(this.m171fasttrackdesignationrequest);
                }
                if (this.m172fasttrackdesignationwithdrawalrequest != null)
                {
                    nodes.AddRange(this.m172fasttrackdesignationwithdrawalrequest);
                }
                if (this.m173rollingreviewrequest != null)
                {
                    nodes.AddRange(this.m173rollingreviewrequest);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m171fasttrackdesignationrequest : CtdSectionBase
    {
    }
    public partial class m172fasttrackdesignationwithdrawalrequest : CtdSectionBase
    {
    }

    public partial class m173rollingreviewrequest : CtdSectionBase
    {
    }

    public partial class m18specialprotocolassessmentrequest : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m181clinicalstudy != null)
                {
                    nodes.AddRange(this.m181clinicalstudy);
                }
                if (this.m182carcinogenicitystudy != null)
                {
                    nodes.AddRange(this.m182carcinogenicitystudy);
                }
                if (this.m183stabilitystudy != null)
                {
                    nodes.AddRange(this.m183stabilitystudy);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m181clinicalstudy : CtdSectionBase
    {
    }

    public partial class m182carcinogenicitystudy : CtdSectionBase
    {
    }

    public partial class m183stabilitystudy : CtdSectionBase
    {
    }

    public partial class m19pediatricadministrativeinformation : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m191requestwaiverpediatricstudies != null)
                {
                    nodes.AddRange(this.m191requestwaiverpediatricstudies);
                }
                if (this.m192requestdeferralpediatricstudies != null)
                {
                    nodes.AddRange(this.m192requestdeferralpediatricstudies);
                }
                if (this.m193requestpediatricexclusivitydetermination != null)
                {
                    nodes.AddRange(this.m193requestpediatricexclusivitydetermination);
                }
                if (this.m194proposedpediatricstudyrequestamendments != null)
                {
                    nodes.AddRange(this.m194proposedpediatricstudyrequestamendments);
                }
                if (this.m195proposalwrittenagreement != null)
                {
                    nodes.AddRange(this.m195proposalwrittenagreement);
                } 
                if (this.m196othercorrespondenceregardingpediatricexclusivitystudyplans != null)
                {
                    nodes.AddRange(this.m196othercorrespondenceregardingpediatricexclusivitystudyplans);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m191requestwaiverpediatricstudies : CtdSectionBase
    {
        public override string CtdName => "Request for waiver of pediatric studies";
    }

    public partial class m192requestdeferralpediatricstudies : CtdSectionBase
    {
        public override string CtdName => "Request for defferal of pediatric studies";
    }

    public partial class m193requestpediatricexclusivitydetermination : CtdSectionBase
    {
        public override string CtdName => "Request for pediatric exclusivity determination";
    }

    public partial class m194proposedpediatricstudyrequestamendments : CtdSectionBase
    {
        public override string CtdName => "Proposed pediatric study request and amendments";
    }

    public partial class m195proposalwrittenagreement : CtdSectionBase
    {
        public override string CtdName => "Proposal for written agreement";
    }

    public partial class m196othercorrespondenceregardingpediatricexclusivitystudyplans : CtdSectionBase
    {
        public override string CtdName => "Other correspondence regarding pediatric exclusivity or study plans";
    }

    public partial class m110disputeresolution : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1101requestfordisputeresolution != null)
                {
                    nodes.AddRange(this.m1101requestfordisputeresolution);
                }
                if (this.m1102correspondencerelatedtodisputeresolution != null)
                {
                    nodes.AddRange(this.m1102correspondencerelatedtodisputeresolution);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m1101requestfordisputeresolution : CtdSectionBase
    {
    }

    public partial class m1102correspondencerelatedtodisputeresolution : CtdSectionBase
    {
    }

    public partial class m111informationamendment : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1111qualityinformationamendment != null)
                {
                    nodes.AddRange(this.m1111qualityinformationamendment);
                }
                if (this.m1112safetyinformationamendment != null)
                {
                    nodes.AddRange(this.m1112safetyinformationamendment);
                }
                if (this.m1113efficacyinformationamendment != null)
                {
                    nodes.AddRange(this.m1113efficacyinformationamendment);
                }
                if (this.m1114multiplemoduleinformationamendments != null)
                {
                    nodes.AddRange(this.m1114multiplemoduleinformationamendments);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m1111qualityinformationamendment : CtdSectionBase
    {
    }

    public partial class m1112safetyinformationamendment : CtdSectionBase
    {
    }

    public partial class m1113efficacyinformationamendment : CtdSectionBase
    {
    }

    public partial class m1114multiplemoduleinformationamendments : CtdSectionBase
    {
    }

    public partial class m112othercorrespondence : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1121preindcorrespondence != null)
                {
                    nodes.AddRange(this.m1121preindcorrespondence);
                }
                if (this.m1122requestcharge != null)
                {
                    nodes.AddRange(this.m1122requestcharge);
                }
                if (this.m1123notificationchargingundertreatmentind != null)
                {
                    nodes.AddRange(this.m1123notificationchargingundertreatmentind);
                }
                if (this.m1124requestcommentsadviceind != null)
                {
                    nodes.AddRange(this.m1124requestcommentsadviceind);
                }
                if (this.m1125requestwaiver != null)
                {
                    nodes.AddRange(this.m1125requestwaiver);
                }
                if (this.m1126exemptioninformedconsentemergencyresearch != null)
                {
                    nodes.AddRange(this.m1126exemptioninformedconsentemergencyresearch);
                }
                if (this.m1127publicdisclosurestatementemergencycareresearch != null)
                {
                    nodes.AddRange(this.m1127publicdisclosurestatementemergencycareresearch);
                }
                if (this.m1128correspondenceregardingemergencycareresearch != null)
                {
                    nodes.AddRange(this.m1128correspondenceregardingemergencycareresearch);
                }
                if (this.m1129notificationdiscontinuationclinicaltrial != null)
                {
                    nodes.AddRange(this.m1129notificationdiscontinuationclinicaltrial);
                }
                if (this.m11210genericdrugenforcementactstatement != null)
                {
                    nodes.AddRange(this.m11210genericdrugenforcementactstatement);
                }
                if (this.m11211basissubmissionstatement != null)
                {
                    nodes.AddRange(this.m11211basissubmissionstatement);
                }
                if (this.m11212comparisongenericdrugreferencelisteddrug != null)
                {
                    nodes.AddRange(this.m11212comparisongenericdrugreferencelisteddrug);
                }
                if (this.m11213requestwaiverinvivostudies != null)
                {
                    nodes.AddRange(this.m11213requestwaiverinvivostudies);
                }
                if (this.m11214environmentalanalysis != null)
                {
                    nodes.AddRange(this.m11214environmentalanalysis);
                }
                if (this.m11215requestwaiverinvivobioavailabilitystudies != null)
                {
                    nodes.AddRange(this.m11215requestwaiverinvivobioavailabilitystudies);
                }
                if (this.m11216fieldalertreports != null)
                {
                    nodes.AddRange(this.m11216fieldalertreports);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m1121preindcorrespondence : CtdSectionBase
    {
        public override string CtdName => "Pre IND correspondence";
    }

    public partial class m1122requestcharge : CtdSectionBase
    {
        public override string CtdName => " Request to charge";
    }

    public partial class m1123notificationchargingundertreatmentind : CtdSectionBase
    {
        public override string CtdName => "Notification of charging under treatment IND";
    }

    public partial class m1124requestcommentsadviceind : CtdSectionBase
    {
        public override string CtdName => "Request for comments and advice";
    }

    public partial class m1125requestwaiver : CtdSectionBase
    {
        public override string CtdName => "Request for a waiver";
    }

    public partial class m1126exemptioninformedconsentemergencyresearch : CtdSectionBase
    {
        public override string CtdName => "Exemption from informed consent for research";
    }

    public partial class m1127publicdisclosurestatementemergencycareresearch : CtdSectionBase
    {
        public override string CtdName => "Public disclosure statement for exception from informed consent for research";
    }

    public partial class m1128correspondenceregardingemergencycareresearch : CtdSectionBase
    {
        public override string CtdName => "Correspondence regarding exception from informed consent for research";
    }

    public partial class m1129notificationdiscontinuationclinicaltrial : CtdSectionBase
    {
        public override string CtdName => " Notification of discontinuation of clinical trial";
    }

    public partial class m11210genericdrugenforcementactstatement : CtdSectionBase
    {
    }

    public partial class m11211basissubmissionstatement : CtdSectionBase
    {
        public override string CtdName => "Basis for submission statement";
    }

    public partial class m11212comparisongenericdrugreferencelisteddrug : CtdSectionBase
    {
        public override string CtdName => "Comparison of generic drug and reference listed drug";
    }

    public partial class m11213requestwaiverinvivostudies : CtdSectionBase
    {
        public override string CtdName => "Request for waiver for in vivo studies";
    }

    public partial class m11214environmentalanalysis : CtdSectionBase
    {
    }

    public partial class m11215requestwaiverinvivobioavailabilitystudies : CtdSectionBase
    {
        public override string CtdName => "Request for waiver of in vivo bioavailability studies";
    }

    public partial class m11216fieldalertreports : CtdSectionBase
    {
    }

    public partial class m113annualreport : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1131summarynonclinicalstudies != null)
                {
                    nodes.AddRange(this.m1131summarynonclinicalstudies);
                }
                if (this.m1132summaryclinicalpharmacologyinformation != null)
                {
                    nodes.AddRange(this.m1132summaryclinicalpharmacologyinformation);
                }
                if (this.m1133summarysafetyinformation != null)
                {
                    nodes.AddRange(this.m1133summarysafetyinformation);
                }
                if (this.m1134summarylabelingchanges != null)
                {
                    nodes.AddRange(this.m1134summarylabelingchanges);
                }
                if (this.m1135summarymanufacturingchanges != null)
                {
                    nodes.AddRange(this.m1135summarymanufacturingchanges);
                }
                if (this.m1136summarymicrobiologicalchanges != null)
                {
                    nodes.AddRange(this.m1136summarymicrobiologicalchanges);
                }
                if (this.m1137summaryothersignificantnewinformation != null)
                {
                    nodes.AddRange(this.m1137summaryothersignificantnewinformation);
                }
                if (this.m1138individualstudyinformation != null)
                {
                    nodes.AddRange(this.m1138individualstudyinformation);
                }
                if (this.m1139generalinvestigationalplan != null)
                {
                    nodes.AddRange(this.m1139generalinvestigationalplan);
                }
                if (this.m11310foreignmarketinghistory != null)
                {
                    nodes.AddRange(this.m11310foreignmarketinghistory);
                }
                if (this.m11311distributiondata != null)
                {
                    nodes.AddRange(this.m11311distributiondata);
                }
                if (this.m11312statuspostmarketingstudycommitments != null)
                {
                    nodes.AddRange(this.m11312statuspostmarketingstudycommitments);
                }
                if (this.m11313statusotherpostmarketingstudies != null)
                {
                    nodes.AddRange(this.m11313statusotherpostmarketingstudies);
                }
                if (this.m11314logoutstandingregulatorybusiness != null)
                {
                    nodes.AddRange(this.m11314logoutstandingregulatorybusiness);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m1131summarynonclinicalstudies : CtdSectionBase
    {
        public override string CtdName => "Summary for nonclinical studies";
    }

    public partial class m1132summaryclinicalpharmacologyinformation : CtdSectionBase
    {
        public override string CtdName => "Summary of clinical pharmacology information";
    }

    public partial class m1133summarysafetyinformation : CtdSectionBase
    {
        public override string CtdName => "Summary of safety information";
    }

    public partial class m1134summarylabelingchanges : CtdSectionBase
    {
        public override string CtdName => "Summary of labeling changes";
    }

    public partial class m1135summarymanufacturingchanges : CtdSectionBase
    {
        public override string CtdName => "Summary of manufacturing changes";
    }

    public partial class m1136summarymicrobiologicalchanges : CtdSectionBase
    {
        public override string CtdName => "Summary of microbiological changes";
    }

    public partial class m1137summaryothersignificantnewinformation : CtdSectionBase
    {
        public override string CtdName => "Summary of other significant new information";
    }

    public partial class m1138individualstudyinformation : CtdSectionBase
    {
    }

    public partial class m1139generalinvestigationalplan : CtdSectionBase
    {
    }

    public partial class m11310foreignmarketinghistory : CtdSectionBase
    {
    }

    public partial class m11311distributiondata : CtdSectionBase
    {
    }

    public partial class m11312statuspostmarketingstudycommitments : CtdSectionBase
    {
        public override string CtdName => "Status of postmarketing study commitments";
    }

    public partial class m11313statusotherpostmarketingstudies : CtdSectionBase
    {
        public override string CtdName => "Status of other postmarketing studies";
    }

    public partial class m11314logoutstandingregulatorybusiness : CtdSectionBase
    {
        public override string CtdName => "Log of outstanding regulatory business";
    }

    public partial class m114labeling : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m1141draftlabeling != null)
                {
                    nodes.AddRange(this.m1141draftlabeling);
                }
                if (this.m1142finallabeling != null)
                {
                    nodes.AddRange(this.m1142finallabeling);
                }
                if (this.m1143listeddruglabeling != null)
                {
                    nodes.AddRange(this.m1143listeddruglabeling);
                }
                if (this.m1144investigationaldruglabeling != null)
                {
                    nodes.AddRange(this.m1144investigationaldruglabeling);
                }
                if (this.m1145foreignlabeling != null)
                {
                    nodes.AddRange(this.m1145foreignlabeling);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m1141draftlabeling : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m11411draftcartoncontainerlabels != null)
                {
                    nodes.AddRange(this.m11411draftcartoncontainerlabels);
                }
                if (this.m11412annotateddraftlabelingtext != null)
                {
                    nodes.AddRange(this.m11412annotateddraftlabelingtext);
                }
                if (this.m11413draftlabelingtext != null)
                {
                    nodes.AddRange(this.m11413draftlabelingtext);
                }
                if (this.m11414labelcomprehensionstudies != null)
                {
                    nodes.AddRange(this.m11414labelcomprehensionstudies);
                }
                if (this.m11415labelinghistory != null)
                {
                    nodes.AddRange(this.m11415labelinghistory);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m11411draftcartoncontainerlabels : CtdSectionBase
    {
        public override string CtdName => "Draft carton and container labels";
    }

    public partial class m11412annotateddraftlabelingtext : CtdSectionBase
    {
    }

    public partial class m11413draftlabelingtext : CtdSectionBase
    {
    }

    public partial class m11414labelcomprehensionstudies : CtdSectionBase
    {
    }

    public partial class m11415labelinghistory : CtdSectionBase
    {
    }

    public partial class m1142finallabeling : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m11421finalcartoncontainerlabels != null)
                {
                    nodes.AddRange(this.m11421finalcartoncontainerlabels);
                }
                if (this.m11422finalpackageinsertpackageinserts != null)
                {
                    nodes.AddRange(this.m11422finalpackageinsertpackageinserts);
                }
                if (this.m11423finallabelingtext != null)
                {
                    nodes.AddRange(this.m11423finallabelingtext);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m11421finalcartoncontainerlabels : CtdSectionBase
    {
        public override string CtdName => "Final carton or container labels";
    }

    public partial class m11422finalpackageinsertpackageinserts : CtdSectionBase
    {
        public override string CtdName => " Final package insert (package inserts, patient information, Medication guides)";
    }

    public partial class m11423finallabelingtext : CtdSectionBase
    {
    }

    public partial class m1143listeddruglabeling : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m11431annotatedcomparisonlisteddrug != null)
                {
                    nodes.AddRange(this.m11431annotatedcomparisonlisteddrug);
                }
                if (this.m11432approvedlabelingtextlisteddrug != null)
                {
                    nodes.AddRange(this.m11432approvedlabelingtextlisteddrug);
                }
                if (this.m11433labelingtextreferencelisteddrug != null)
                {
                    nodes.AddRange(this.m11433labelingtextreferencelisteddrug);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m11431annotatedcomparisonlisteddrug : CtdSectionBase
    {
        public override string CtdName => "Annotated comparison with listed drug";
    }

    public partial class m11432approvedlabelingtextlisteddrug : CtdSectionBase
    {
        public override string CtdName => "Approved labeling text for listed drug";
    }

    public partial class m11433labelingtextreferencelisteddrug : CtdSectionBase
    {
        public override string CtdName => "Labeling text for reference listed drug";
    }

    public partial class m1144investigationaldruglabeling : CtdSectionBase
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.m11441investigationalbrochure != null)
                {
                    nodes.AddRange(this.m11441investigationalbrochure);
                }
                if (this.m11442investigationaldruglabel != null)
                {
                    nodes.AddRange(this.m11442investigationaldruglabel);
                }

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m11441investigationalbrochure : CtdSectionBase
    {
    }

    public partial class m11442investigationaldruglabel : CtdSectionBase
    {
        public override string CtdName => "Investigational drug labeling";
    }

    public partial class m1145foreignlabeling : CtdSectionBase
    {
    }

    public partial class m115promotionalmaterial : CtdSectionBase
    {
    }

    public partial class m116riskmanagementplans : CtdSectionBase
    {
    }

    public partial class fdaregional : CtdSectionBase, IEctdModule1
    {
        public List<EctdModule1Data> Data => new List<EctdModule1Data>
        {
            this.admin
        };

        public string DtdVersion => this.dtdversion;

        public string Region => "US";

        public List<CtdSectionBase> Content => this.ChildNodes.FirstOrDefault()?.ChildNodes ?? new List<CtdSectionBase>();

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m1regional
                };

                return nodes.Where(x => x != null).Select(x =>
                {
                    x.Parent = this;
                    return x;
                }).ToList();
            }
        }
    }

}
