﻿using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using PharmaLex.Caching;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.WebTests.Controllers;

public class CacheControllerTests
{
    private readonly IDistributedCacheService mockCacheService;
    private readonly CacheController controller;

    public CacheControllerTests()
    {
        mockCacheService = Substitute.For<IDistributedCacheService>();
        controller = new CacheController(mockCacheService);
    }

    [Fact]
    public void Cache_GetAllCacheItems_ReturnsCacheModelInView()
    {
        // Arrange
        var cacheData = new Dictionary<string, List<string>>
        {
            { "dep1", new List<string> { "key1", "key2" } },
            { "dep2", new List<string> { "key3" } }
        };
        mockCacheService.GetAll().Returns(cacheData);

        // Act
        var result = controller.Cache();

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        var model = Assert.IsType<List<CacheViewModel>>(viewResult.Model);
        Assert.Equal(3, model.Count);
        Assert.Contains(model, x => x.Key == "dep1" && x.Value == "key1");
        Assert.Contains(model, x => x.Key == "dep1" && x.Value == "key2");
        Assert.Contains(model, x => x.Key == "dep2" && x.Value == "key3");
    }

    [Fact]
    public async Task Edit_OpenCacheValue_ReturnsViewWithCacheValue()
    {
        // Arrange
        string key = "testKey";
        string value = "testValue";
        mockCacheService.GetStringAsync(key).Returns(value);

        // Act
        var result = await controller.Edit(key);

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        var model = Assert.IsType<CacheViewModel>(viewResult.Model);
        Assert.Equal(key, model.Key);
        Assert.Equal(value, model.Value);
    }

    [Fact]
    public async Task Edit_InvalidModel_ReturnsBadRequest()
    {
        // Arrange
        string key = "testKey";
        var model = new CacheViewModel { Key = key, Value = "invalid" };
        controller.ModelState.AddModelError("Value", "Invalid value");

        // Act
        var result = await controller.Edit(key, model);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.Equal(400, badRequestResult.StatusCode);
    }

    [Fact]
    public async Task Edit_ValidModel_UpdatesCacheAndReturnsCacheView()
    {
        // Arrange
        string key = "testKey";
        var model = new CacheViewModel { Key = key, Value = "value with &#39; apostrophe" };
        var cacheData = new Dictionary<string, List<string>>();
        mockCacheService.GetAll().Returns(cacheData);

        // Act
        var result = await controller.Edit(key, model);

        // Assert
        await mockCacheService.Received(1).SetStringAsync(key, "value with ' apostrophe");
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Equal("Cache", viewResult.ViewName);
    }

    [Fact]
    public async Task Delete_RemovesKeyFromCacheAndReturnsCacheView()
    {
        // Arrange
        string key = "testKey";
        var cacheData = new Dictionary<string, List<string>>();
        mockCacheService.GetAll().Returns(cacheData);

        // Act
        var result = await controller.Delete(key);

        // Assert
        await mockCacheService.Received(1).RemoveAsync(key);
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Equal("Cache", viewResult.ViewName);
    }

    [Fact]
    public async Task Flush_RemovesAllCacheAndRedirectsToCache()
    {
        // Act
        var result = await controller.Flush();

        // Assert
        await mockCacheService.Received(1).RemoveAllAsync();
        var redirectResult = Assert.IsType<RedirectResult>(result);
        Assert.Equal("/cache", redirectResult.Url);
    }
}
