﻿using PharmaLex.SmartTRACE.Web.Models;
namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class IModelTests
    {
        public class TestModel : IModel
        {
            public int Id { get; set; }
            public string? Name { get; set; }
        }
        [Fact]
        public void ToJson_SingleInstance_ReturnsValidJson()
        {
            // Arrange
            var model = new TestModel { Id = 1, Name = "Test" };

            // Act
            var json = model.ToJson();

            // Assert
            Assert.Equal("{\"Id\":1,\"Name\":\"Test\"}", json);
        }
        [Fact]
        public void ToJson_EnumerableInstance_ReturnsValidJson()
        {
            // Arrange
            var models = new List<TestModel>
            {
                new TestModel { Id = 1, Name = "Test1" },
                new TestModel { Id = 2, Name = "Test2" }
            };
            // Act
            var json = models.ToJson();

            // Assert
            Assert.Equal("[{\"Id\":1,\"Name\":\"Test1\"},{\"Id\":2,\"Name\":\"Test2\"}]", json);
        }
    }
}
