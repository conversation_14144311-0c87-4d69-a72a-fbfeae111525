﻿using PharmaLex.DataAccess;

namespace PharmaLex.SmartTRACE.Entities.Audit
{
    public partial class CountryAudit: AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }

        public int? Id { get; set; }
        public string Name { get; set; }
        public string TwoLetterCode { get; set; }
        public int? RegionId { get; set; }
    }
}
