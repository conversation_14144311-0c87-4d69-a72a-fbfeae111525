﻿using System;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.Ich
{
    public static class EnumDescriptionExtensions
    {
        public static string GetEnumDescription(this Enum enumType)
        {
            return enumType.GetType().GetMember(enumType.ToString())
                           .First()
                           .GetCustomAttribute<DescriptionAttribute>()
                           .Description;
        }

        public static string GetEnumDescription<T>(this string value) where T: struct, IConvertible
        {
            T item = Enum.Parse<T>(value, true);
            return (item as Enum).GetEnumDescription();
        }

        public static string GetXmlEnumAttributeValueFromEnum<TEnum>(this TEnum value) where TEnum : struct, IConvertible
        {
            var enumType = typeof(TEnum);
            if (!enumType.IsEnum) return string.Empty;

            var member = enumType.GetMember(value.ToString()).FirstOrDefault();
            if (member == null) return string.Empty;

            var attribute = member.GetCustomAttributes(false).OfType<XmlEnumAttribute>().FirstOrDefault();
            if (attribute == null) return string.Empty;
            return attribute.Name;
        }
    }
}
