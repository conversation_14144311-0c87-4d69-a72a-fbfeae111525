﻿using Microsoft.EntityFrameworkCore.Query;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using System.Linq;
using System;

namespace PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces
{
    public interface IAppSubmissionsRepository: IRepository<Submission>
    {
        IQueryable<Submission> GetQueryableItems(Func<IQueryable<Submission>, IIncludableQueryable<Submission, object>> include = null);
    }
}
