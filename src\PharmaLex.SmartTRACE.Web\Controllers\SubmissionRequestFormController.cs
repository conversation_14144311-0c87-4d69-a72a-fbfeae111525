﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    public class SubmissionRequestFormController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper _mapper;
        private readonly INotificationService notificationService;
        private readonly IConfiguration configuration;

        public SubmissionRequestFormController(IDistributedCacheServiceFactory cache, IMapper mapper, INotificationService notificationService, IConfiguration configuration)
        {
            this.cache = cache;
            this._mapper = mapper;
            this.notificationService = notificationService;
            this.configuration = configuration;
        }

        [HttpGet, Route("/submission-request/new/{appId}"), Authorize(Policy = "Editor")]
        public async Task<IActionResult> New(int appId) 
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var appCache = cache.CreateMappedEntity<Application, ApplicationModel>();
            var appProductCache = cache.CreateEntity<ApplicationProduct>();
            var productCache = cache.CreateMappedEntity<Product, ProductModel>();
            var app = await appCache.FirstOrDefaultAsync(x => x.Id == appId);
            var appProduct = await appProductCache.FirstOrDefaultAsync(x => x.ApplicationId == appId);
            var product = await productCache.FirstOrDefaultAsync(x => x.Id == appProduct.ProductId);
            var currAppCountries = await GetCountriesPerApplication(appId);

            var model = new EditSubmissionRequestFormViewModel()
            {
                SubmissionRequestForm = new SubmissionRequestFormModel()
                {
                    ApplicationNumber = app.ApplicationNumber,
                    ApplicationId = appId,
                    ProcedureType = (await PicklistHelper.ExtractPicklist(cache, app.ProcedureTypeId)).Name,
                    InitialSentToEmail = this.configuration.GetValue<string>("AppSettings:SmartTraceConfigurableEmail"),
                    CountryIds = currAppCountries.Select(x => x.Id).ToList()
                },
                Picklists = await PicklistHelper.GetPicklistDataBasedOnCountry(cache, currAppCountries),
                Countries = currAppCountries,
                Projects = await this.cache.CreateMappedEntity<Project, ProjectModel>().WhereAsync(x => x.ClientId == product.ClientId)
            };
            return View("EditSubmissionRequestForm", model);
        }

        [HttpPost, Route("/submission-request/new/{appId}"), ValidateAntiForgeryToken, Authorize(Policy = "Editor")]
        public async Task<IActionResult> SaveNew(int appId, EditSubmissionRequestFormViewModel model)
        {
            var appProductCache = cache.CreateEntity<ApplicationProduct>();
            var productCache = cache.CreateMappedEntity<Product, ProductModel>();
            var appProduct = await appProductCache.FirstOrDefaultAsync(x => x.ApplicationId == appId);
            var product = await productCache.FirstOrDefaultAsync(x => x.Id == appProduct.ProductId);
            var client = await cache.CreateEntity<Client>().FirstOrDefaultAsync(x => x.Id == product.ClientId);

            if (!ModelState.IsValid)
            {
                model.Picklists = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>().AllAsync();
                model.Countries = await GetCountriesPerApplication(appId);
                
                model.Projects = await this.cache.CreateMappedEntity<Project, ProjectModel>().WhereAsync(x => x.ClientId == product.ClientId);
                return View("EditSubmissionRequestForm", model);
            }

            var appCache = cache.CreateMappedEntity<Application, ApplicationModel>();
            var app = await appCache.FirstOrDefaultAsync(x => x.Id == appId);

            var submissionCache = cache.CreateTrackedEntity<Submission>().Configure(o => o.Include(x => x.Application));
            var submission = _mapper.Map<Submission>(model.SubmissionRequestForm);
            submission.LifecycleStateId = (int)SubmissionLifeCycleState.Draft;
            submission.PreviousLifecycleStateId = submission.LifecycleStateId;
            submissionCache.Add(submission);
            await submissionCache.SaveChangesAsync();

            submission.UniqueId = $"SUB{submission.Id:D3}";
            await submissionCache.SaveChangesAsync();

            var submissionResourceCache = cache.CreateTrackedEntity<SubmissionResource>();
            var submissionResource = _mapper.Map<SubmissionResource>(model.SubmissionRequestForm);
            submissionResource.SubmissionId = submission.Id;
            if (model.SubmissionRequestForm.SendNotification && string.IsNullOrEmpty(model.SubmissionRequestForm.InitialSentToEmail))
            {
                submissionResource.InitialSentToEmail = this.configuration.GetValue<string>("AppSettings:SmartTraceConfigurableEmail");
            }
            submissionResourceCache.Add(submissionResource);
            await submissionResourceCache.SaveChangesAsync();

            var subCountryCache = cache.CreateTrackedEntity<SubmissionCountry>();
            foreach (var id in model.SubmissionRequestForm.CountryIds)
            {
                subCountryCache.Add(new SubmissionCountry()
                {
                    SubmissionId = submission.Id,
                    CountryId = id
                });
            }

            await subCountryCache.SaveChangesAsync();

            var createdByUser = await cache.CreateMappedEntity<User, UserModel>().FirstOrDefaultAsync(x => x.Email.ToLower() == submission.CreatedBy.ToLower());
            var submissionModel = await cache.CreateMappedEntity<Submission, SubmissionModel>().Configure(o => o.Include(x => x.SubmissionResource)).FirstOrDefaultAsync(x => x.Id == submission.Id);
            submissionModel.SubmissionType = (await PicklistHelper.ExtractPicklist(cache, submissionModel.SubmissionTypeId))?.Name;

            if (model.SubmissionRequestForm.SendNotification || !string.IsNullOrEmpty(submissionResource.InitialSentToEmail))
            {
                await this.notificationService.SendNotification(submissionResource.InitialSentToEmail, submissionModel, app.ApplicationNumber, createdByUser.DisplayFullName, this.Request, product.Name, client.Name);
                if (!submissionResource.InitialSentToEmail.Contains(createdByUser.Email))
                {
                    await this.notificationService.SendNotification(createdByUser.Email, submissionModel, app.ApplicationNumber, createdByUser.DisplayFullName, this.Request, product.Name, client.Name);
                }

                return Redirect($"/applications/view/{appId}");
            }

            this.AddConfirmationNotification($"<em>{submission.UniqueId}</em> created");
            return Redirect($"/submissions/edit/{submission.Id}");
        }

        private async Task<IList<CountryModel>> GetCountriesPerApplication(int appId)
        {
            var appCountryCache = cache.CreateEntity<ApplicationCountry>();
            var appCountry = await appCountryCache.WhereAsync(x => x.ApplicationId == appId);
            var countryIds = appCountry.Select(a => a.CountryId);
            var countries = (await cache.CreateMappedEntity<Country, CountryModel>().WhereAsync(x => countryIds.Contains(x.Id)))
                            .OrderBy(x => x.Name)
                            .ToList();
            return countries;
        }
    }
}
