﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.SmartTRACE.Entities;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class SubmissionResourceModel : IModel
    {
        public SubmissionResourceModel()
        {
            this.Publishers = new List<UserFindResultModel>();
        }
        public int Id { get; set; }

        public int? PriorityId { get; set; }
        public string Priority { get; set; }

        public int? EstimatedSizeId { get; set; }
        public string EstimatedSize { get; set; }

        [ModelBinder(BinderType = typeof(DecimalBinder))]
        public decimal? EstimatedHours { get; set; }
        public string RegulatoryLead { get; set; }
        public string PublishingLead { get; set; }
        public int? PublishingTeamId { get; set; }
        public string PublishingTeam { get; set; }
        public string InitialSentToEmail { get; set; }
        public string DisplayPublishers { get; set; }

        public string Comments { get; set; }
        public int SubmissionId { get; set; }
        public IList<UserFindResultModel> Publishers { get; set; }
    }

    public class SubmissionResourceMappingProfile : Profile
    {
        public SubmissionResourceMappingProfile()
        {
            this.CreateMap<SubmissionResource, SubmissionResourceModel>();
            this.CreateMap<SubmissionResourceModel, SubmissionResource>()
                .ForMember(d => d.InitialSentToEmail, s => s.Ignore());
            this.CreateMap<SubmissionResource, EditSubmissionResourceViewModel>()
                .ForMember(d => d.SubmissionResource, s => s.MapFrom(x => x));
            this.CreateMap<EditSubmissionResourceViewModel, SubmissionResource>();
            this.CreateMap<SubmissionRequestFormModel, SubmissionResource>()
                 .ForMember(d => d.Comments, s => s.Ignore());
        }
    }
}
