﻿using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Models.ViewModels;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public interface ISubmissionService
    {
        Task<IList<SubmissionViewModel>> GetAllSubmissionData(ClaimsPrincipal user, SubmissionFilterModel model = null);
        Task<Submission> GetSubmission(int submissionId);
        Task<ApiPagedListResult<SubmissionTableViewModel>> GetPagedSubmissionsAsync(<PERSON>laimsPrincipal user, int skip, int take, SubmissionFilterModel model, string sort);
    }
}
