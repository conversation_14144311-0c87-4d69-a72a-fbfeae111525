﻿using Microsoft.EntityFrameworkCore.Design;
using PharmaLex.DataAccess.Design;
using System.IO;

namespace PharmaLex.SmartTRACE.Entities
{
    public class DesignTimeContextFactory : IDesignTimeDbContextFactory<SmartTRACEContext>
    {
        public SmartTRACEContext CreateDbContext(string[] args)
        {
            return new DesignPlxDbContextResolver<SmartTRACEContext>
                (Path.Combine(Directory.GetCurrentDirectory().Replace(".Entities", ".Web"), "appSettings.json"))
                .ServiceProvider.GetService(typeof(SmartTRACEContext)) as SmartTRACEContext;
        }
    }
}
