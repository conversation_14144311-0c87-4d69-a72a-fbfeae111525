﻿using PharmaLex.SmartTRACE.Web.Helpers.Extensions;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Extensions
{
    public class ExpressionExtensionsTests
    {
        [Fact]
        public void AndAlso_CombinesExpressionsCorrectly()
        {
            // Arrange
            Expression<Func<int, bool>> expr1 = x => x > 5;
            Expression<Func<int, bool>> expr2 = x => x < 10;

            // Act
            var combined = expr1.AndAlso(expr2);
            var compiled = combined.Compile();

            // Assert
            Assert.True(compiled(7));
            Assert.False(compiled(4));
            Assert.False(compiled(12));
        }
    }
}
