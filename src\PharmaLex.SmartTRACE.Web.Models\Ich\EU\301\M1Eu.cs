﻿using System;
using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.Eu301
{

    [Serializable]
    [XmlRoot("eu-envelope", Namespace = "", IsNullable = false)]
    public partial class euenvelope
    {
        [XmlElement("envelope")]
        public envelope[] envelope { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class envelope
    {
        public string identifier { get; set; }
        public submission submission { get; set; }
        [XmlElement("submission-unit")]
        public submissionunit submissionunit { get; set; }
        public string applicant { get; set; }
        public agency agency { get; set; }
        public procedure procedure { get; set; }
        [XmlElement("invented-name")]
        public string[] inventedname { get; set; }
        [XmlElement("inn")]
        public string[] inn { get; set; }
        public string sequence { get; set; }
        [XmlElement("related-sequence")]
        public string[] relatedsequence { get; set; }
        [XmlElement("submission-description")]
        public string submissiondescription { get; set; }
        [XmlAttribute]
        public envelopeCountry country { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class submission
    {
        public string number { get; set; }
        [XmlArray("procedure-tracking")]
        [XmlArrayItem("number", IsNullable = false)]
        public string[] proceduretracking { get; set; }
        [XmlArray("tracking")]
        [XmlArrayItem("number", IsNullable = false)]
        public string[] tracking { get; set; }
        [XmlAttribute]
        public submissionType type { get; set; }
        [XmlAttribute]
        public submissionMode mode { get; set; }
        [XmlIgnore()]
        public bool modeSpecified { get; set; }
    }

    [Serializable]
    [XmlRoot("submission-unit", Namespace = "", IsNullable = false)]
    public partial class submissionunit
    {
        [XmlAttribute]
        public submissionunitType type { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class agency
    {
        [XmlAttribute]
        public agencyCode code { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class procedure
    {
        [XmlAttribute]
        public procedureType type { get; set; }
    }

    [Serializable]
    [XmlRoot("procedure-tracking", Namespace = "", IsNullable = false)]
    public partial class proceduretracking
    {
        [XmlElement("number")]
        public string[] number { get; set; }
    }

    [Serializable]
    [XmlRoot("node-extension", Namespace = "", IsNullable = false)]
    public partial class nodeextension
    {

        public string title { get; set; }
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class leaf
    {
        public leaf()
        {
            this.type = "simple";
        }
        public string title { get; set; }
        [XmlElement("link-text")]
        public linktext linktext { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        [XmlAttribute("application-version")]
        public string applicationversion { get; set; }
        [XmlAttribute]
        public string version { get; set; }
        [XmlAttribute("font-library")]
        public string fontlibrary { get; set; }
        [XmlAttribute]
        public leafOperation operation { get; set; }
        [XmlAttribute("modified-file")]
        public string modifiedfile { get; set; }
        [XmlAttribute]
        public string checksum { get; set; }
        [XmlAttribute("checksum-type")]
        public string checksumtype { get; set; }
        [XmlAttribute]
        public string keywords { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string type { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string role { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string href { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefShow show { get; set; }
        [XmlIgnore()]
        public bool showSpecified { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefActuate actuate { get; set; }
        [XmlIgnore()]
        public bool actuateSpecified { get; set; }
        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("link-text", Namespace = "", IsNullable = false)]
    public partial class linktext
    {
        [XmlElement("xref")]
        public xref[] Items { get; set; }
        [XmlText]
        public string[] Text { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class xref
    {
        public xref()
        {
            this.type = "simple";
        }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string type { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string role { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string title { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string href { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefShow show { get; set; }
        [XmlIgnore()]
        public bool showSpecified { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefActuate actuate { get; set; }
        [XmlIgnore()]
        public bool actuateSpecified { get; set; }
    }

    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3c.org/1999/xlink")]
    public enum xrefShow
    {
        @new,
        replace,
        embed,
        other,
        none,
    }

    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3c.org/1999/xlink")]

    public enum xrefActuate
    {
        onLoad,
        onRequest,
        other,
        none,
    }

    [Serializable]
    public enum leafOperation
    {
        @new,
        append,
        replace,
        delete,
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class specific
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute]
        public specificCountry country { get; set; }
    }

    [Serializable]
    [XmlRoot("pi-doc", Namespace = "", IsNullable = false)]
    public partial class pidoc
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
        [XmlAttribute]
        public pidocType type { get; set; }
        [XmlAttribute]
        public pidocCountry country { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-eu", Namespace = "", IsNullable = false)]
    public partial class m1eu
    {
        [XmlArray("m1-0-cover")]
        [XmlArrayItem("specific", IsNullable = false)]
        public specific[] m10cover { get; set; }
        [XmlArray("m1-2-form")]
        [XmlArrayItem("specific", IsNullable = false)]
        public specific[] m12form { get; set; }
        [XmlElement("m1-3-pi")]
        public m13pi m13pi { get; set; }
        [XmlElement("m1-4-expert")]
        public m14expert m14expert { get; set; }
        [XmlElement("m1-5-specific")]
        public m15specific m15specific { get; set; }
        [XmlElement("m1-6-environrisk")]
        public m16environrisk m16environrisk { get; set; }
        [XmlElement("m1-7-orphan")]
        public m17orphan m17orphan { get; set; }
        [XmlElement("m1-8-pharmacovigilance")]
        public m18pharmacovigilance m18pharmacovigilance { get; set; }
        [XmlElement("m1-9-clinical-trials")]
        public m19clinicaltrials m19clinicaltrials { get; set; }
        [XmlElement("m1-10-paediatrics")]
        public m110paediatrics m110paediatrics { get; set; }
        [XmlArray("m1-responses")]
        [XmlArrayItem("specific", IsNullable = false)]
        public specific[] m1responses { get; set; }
        [XmlArray("m1-additional-data")]
        [XmlArrayItem("specific", IsNullable = false)]
        public specific[] m1additionaldata { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-pi", Namespace = "", IsNullable = false)]
    public partial class m13pi
    {

        [XmlArray("m1-3-1-spc-label-pl")]
        [XmlArrayItem("pi-doc", IsNullable = false)]
        public pidoc[] m131spclabelpl { get; set; }
        [XmlArray("m1-3-2-mockup")]
        [XmlArrayItem("specific", IsNullable = false)]
        public specific[] m132mockup { get; set; }
        [XmlArray("m1-3-3-specimen")]
        [XmlArrayItem("specific", IsNullable = false)]
        public specific[] m133specimen { get; set; }
        [XmlArray("m1-3-4-consultation")]
        [XmlArrayItem("specific", IsNullable = false)]
        public specific[] m134consultation { get; set; }
        [XmlArray("m1-3-5-approved")]
        [XmlArrayItem("specific", IsNullable = false)]
        public specific[] m135approved { get; set; }
        [XmlElement("m1-3-6-braille")]
        public m136braille m136braille { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-6-braille", Namespace = "", IsNullable = false)]
    public partial class m136braille
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-expert", Namespace = "", IsNullable = false)]
    public partial class m14expert
    {

        [XmlElement("m1-4-1-quality")]
        public m141quality m141quality { get; set; }
        [XmlElement("m1-4-2-non-clinical")]
        public m142nonclinical m142nonclinical { get; set; }
        [XmlElement("m1-4-3-clinical")]
        public m143clinical m143clinical { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-1-quality", Namespace = "", IsNullable = false)]
    public partial class m141quality
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-2-non-clinical", Namespace = "", IsNullable = false)]
    public partial class m142nonclinical
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-3-clinical", Namespace = "", IsNullable = false)]
    public partial class m143clinical
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-specific", Namespace = "", IsNullable = false)]
    public partial class m15specific
    {

        [XmlElement("m1-5-1-bibliographic")]
        public m151bibliographic m151bibliographic { get; set; }
        [XmlElement("m1-5-2-generic-hybrid-bio-similar")]
        public m152generichybridbiosimilar m152generichybridbiosimilar { get; set; }
        [XmlElement("m1-5-3-data-market-exclusivity")]
        public m153datamarketexclusivity m153datamarketexclusivity { get; set; }
        [XmlElement("m1-5-4-exceptional-circumstances")]
        public m154exceptionalcircumstances m154exceptionalcircumstances { get; set; }
        [XmlElement("m1-5-5-conditional-ma")]
        public m155conditionalma m155conditionalma { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-1-bibliographic", Namespace = "", IsNullable = false)]
    public partial class m151bibliographic
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-2-generic-hybrid-bio-similar", Namespace = "", IsNullable = false)]
    public partial class m152generichybridbiosimilar
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-3-data-market-exclusivity", Namespace = "", IsNullable = false)]
    public partial class m153datamarketexclusivity
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-4-exceptional-circumstances", Namespace = "", IsNullable = false)]
    public partial class m154exceptionalcircumstances
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-5-conditional-ma", Namespace = "", IsNullable = false)]
    public partial class m155conditionalma
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-environrisk", Namespace = "", IsNullable = false)]
    public partial class m16environrisk
    {
        [XmlElement("m1-6-1-non-gmo", typeof(m161nongmo))]
        [XmlElement("m1-6-2-gmo", typeof(m162gmo))]
        public object Item { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-1-non-gmo", Namespace = "", IsNullable = false)]
    public partial class m161nongmo
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-2-gmo", Namespace = "", IsNullable = false)]
    public partial class m162gmo
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-orphan", Namespace = "", IsNullable = false)]
    public partial class m17orphan
    {
        [XmlElement("m1-7-1-similarity")]
        public m171similarity m171similarity { get; set; }
        [XmlElement("m1-7-2-market-exclusivity")]
        public m172marketexclusivity m172marketexclusivity { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-1-similarity", Namespace = "", IsNullable = false)]
    public partial class m171similarity
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-2-market-exclusivity", Namespace = "", IsNullable = false)]
    public partial class m172marketexclusivity
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-pharmacovigilance", Namespace = "", IsNullable = false)]
    public partial class m18pharmacovigilance
    {
        [XmlElement("m1-8-1-pharmacovigilance-system")]
        public m181pharmacovigilancesystem m181pharmacovigilancesystem { get; set; }
        [XmlElement("m1-8-2-risk-management-system")]
        public m182riskmanagementsystem m182riskmanagementsystem { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-1-pharmacovigilance-system", Namespace = "", IsNullable = false)]
    public partial class m181pharmacovigilancesystem
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-2-risk-management-system", Namespace = "", IsNullable = false)]
    public partial class m182riskmanagementsystem
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-clinical-trials", Namespace = "", IsNullable = false)]
    public partial class m19clinicaltrials
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-10-paediatrics", Namespace = "", IsNullable = false)]
    public partial class m110paediatrics
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-0-cover", Namespace = "", IsNullable = false)]
    public partial class m10cover
    {
        [XmlElement("specific")]
        public specific[] specific { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-form", Namespace = "", IsNullable = false)]
    public partial class m12form
    {
        [XmlElement("specific")]
        public specific[] specific { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-spc-label-pl", Namespace = "", IsNullable = false)]
    public partial class m131spclabelpl
    {
        [XmlElement("pi-doc")]
        public pidoc[] pidoc { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-2-mockup", Namespace = "", IsNullable = false)]
    public partial class m132mockup
    {
        [XmlElement("specific")]
        public specific[] specific { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-3-specimen", Namespace = "", IsNullable = false)]
    public partial class m133specimen
    {
        [XmlElement("specific")]
        public specific[] specific { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-4-consultation", Namespace = "", IsNullable = false)]
    public partial class m134consultation
    {
        [XmlElement("specific")]
        public specific[] specific { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-5-approved", Namespace = "", IsNullable = false)]
    public partial class m135approved
    {
        [XmlElement("specific")]
        public specific[] specific { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-responses", Namespace = "", IsNullable = false)]
    public partial class m1responses
    {
        [XmlElement("specific")]
        public specific[] specific { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-additional-data", Namespace = "", IsNullable = false)]
    public partial class m1additionaldata
    {
        [XmlElement("specific")]
        public specific[] specific { get; set; }
    }

    [Serializable]
    [XmlRoot("eu-backbone", Namespace = "http://europa.eu.int", IsNullable = false)]
    public partial class eubackbone
    {
        public eubackbone()
        {
            this.dtdversion = "3.0.1";
        }
        [XmlArray("eu-envelope", Namespace = "")]
        [XmlArrayItem("envelope", IsNullable = false)]
        public envelope[] euenvelope { get; set; }
        [XmlElement("m1-eu", Namespace = "")]
        public m1eu m1eu { get; set; }
        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
        public string dtdversion { get; set; }
    }
}