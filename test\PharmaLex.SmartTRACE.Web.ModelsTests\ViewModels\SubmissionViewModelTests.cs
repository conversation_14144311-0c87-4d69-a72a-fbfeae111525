﻿using PharmaLex.SmartTRACE.Web.Models.eCTD32.US33;
using PharmaLex.SmartTRACE.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class SubmissionViewModelTests
    {
        [Fact]
        public void SubmissionViewModel_Get_SetValue()
        {
            //Arrange
            var model=new SubmissionViewModel();
            model.DisplayPublishers = "test";
            model.ApplicationNumber = "001";
            model.ClientName = "client";
            model.ProjectName = "p1";
            model.ProjectCode = "pc";
            model.ProjectOpportunityNumber = "1";
            model.ContractOwner = "nitish";
            model.RegulatoryLead = "nitish";
            //Assert
            Assert.NotNull(model);
        }
    }
}
