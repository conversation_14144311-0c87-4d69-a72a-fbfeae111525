using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class RegionsControllerTests
    {
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IMapper _mapper;
        public List<Region> Regions =
                [
                    new Region { Id = 1, Name = "region1" },
                    new Region { Id = 2, Name = "region2" },
                ];
        #region Constructor 
        public RegionsControllerTests()
        {
            _cache = Substitute.For<IDistributedCacheServiceFactory>();
            _mapper = Substitute.For<IMapper>();

        }
        #endregion

        #region Action Method Index
        [Fact]
        public async Task Index_Returns_RegionModelList()
        {
            // Arrange
            var mappedCache = Substitute.For<IMappedEntityCacheServiceProxy<Region, RegionModel>>();
            var controller = new RegionsController(_cache, _mapper);
            var regions = GetRegionModel();
            _cache.CreateMappedEntity<Region, RegionModel>().Returns(mappedCache);
            mappedCache.AllAsync().Returns(Task.FromResult(regions));

            // Act
            var result = await controller.Index();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsAssignableFrom<IEnumerable<RegionModel>>(viewResult.ViewData.Model);
            Assert.NotNull(viewResult);
            Assert.Equal(regions, model);

        }
        #endregion
        #region Method New Return Region Model
        [Fact]
        public void New_Returns_ViewResult_With_RegionsModel()
        {
            // Arrange
            var viewName = "EditRegion";
            var controller = new RegionsController(_cache, _mapper);

            // Act
            var result = controller.New();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.IsAssignableFrom<RegionModel>(viewResult.ViewData.Model);
            Assert.Equal(viewName, viewResult.ViewName);
        }
        #endregion

        #region  New(save) with invalid (id,  model)
        [Fact]
        public async Task Save_New_with_InValidData()
        {
            //Arrange
            RegionModel regionModel = GetRegionModel()[0];
            string ViewName = "EditRegion";
            var controller = new RegionsController(_cache, _mapper);
            controller.ModelState.AddModelError("SessionName", "Required");
            //Act
            var result = await controller.New(regionModel) as ViewResult;
            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.ViewName);
            Assert.Equal(result.ViewName, ViewName);
        }
        [Fact]
        public async Task Edit_HtmlTagsInName_ShouldReturnBadRequest()
        {
            //Arrange
            RegionModel regionModel = GetRegionModel()[0];
            string ViewName = "EditRegion";
            regionModel.Name = "<div>Test</div>";
            var controller = new RegionsController(_cache, _mapper);
            //Act
            var result = await controller.New(regionModel) as ViewResult;
            // Assert
            Assert.NotNull(result?.ViewName);
            Assert.Equal(result.ViewName, ViewName);
        }
        #endregion
        #region  SaveNew with valid (id,  model)
        [Fact]
        public async Task Save_New_with_ValidData()
        {
            // Arrange
            var controller = new RegionsController(_cache, _mapper);
            var projCache = Substitute.For<ITrackedEntityCacheServiceProxy<Region>>();
            _cache.CreateTrackedEntity<Region>().Returns(projCache);
            _mapper
               .Map<RegionModel, Region>(Arg.Any<RegionModel>())
               .Returns(callInfo =>
               {
                   var projModel = callInfo.Arg<RegionModel>();
                   return Regions.FirstOrDefault();
               });

            var model = GetRegionModel()[0];
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            controller.TempData = tempData;

            // Act
            var result = await controller.New(model);

            // Assert
            var viewResult = Assert.IsType<RedirectResult>(result);
            var redirectResult = viewResult as RedirectResult;
            var ResposeUrl = "/regions";
            Assert.NotNull(redirectResult);
            Assert.Equal(redirectResult.Url, ResposeUrl);

        }
        #endregion
        #region Edit Returns EditProject Model
        [Fact]

        public async Task Edit_Returns_EditProjectModel()
        {
            // Arrange
            var controller = new RegionsController(_cache, _mapper);
            var regCache = Substitute.For<IMappedEntityCacheServiceProxy<Region, RegionModel>>();
            var countryCache = Substitute.For<IMappedEntityCacheServiceProxy<Country, CountryModel>>();
            RegionModel model = GetRegionModel()[0];
            List<CountryModel> countryList = new List<CountryModel>() { new CountryModel() { Id = 11, Name = "country1", RegionId = 1 } };
            _cache.CreateMappedEntity<Region, RegionModel>().Returns(regCache);
            _cache.CreateMappedEntity<Country, CountryModel>().Returns(countryCache);
            regCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Region, bool>>>()).Returns(model);
            countryCache.WhereAsync(Arg.Any<Expression<Func<Country, bool>>>()).Returns(countryList);

            // Act
            var result = await controller.Edit(model.Id);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal("EditRegion", viewResult.ViewName);
            var output = Assert.IsAssignableFrom<RegionModel>(viewResult.ViewData.Model);
            Assert.Equal(model, output);

        }
        #endregion
        #region Action Method Edit(int clientId) for invalid
        [Fact]
        public async Task Edit_Invalid()
        {
            // Arrange
            int id = 10;
            var controller = new RegionsController(_cache, _mapper);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.Edit(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region Edit save with invalid model
        [Fact]
        public async Task Edit_Save_With_InValidData()
        {
            //Arrange
            RegionModel regionModel = GetRegionModel()[0];
            var controller = new RegionsController(_cache, _mapper);
            controller.ModelState.AddModelError("SessionName", "Required");
            var countryCache = Substitute.For<IMappedEntityCacheServiceProxy<Country, CountryModel>>();
            List<CountryModel> countryList = new List<CountryModel>() { new CountryModel() { Id = 11, Name = "country1", RegionId = 1 } };
            _cache.CreateMappedEntity<Country, CountryModel>().Returns(countryCache);

            //Act
            var result = await controller.Edit(regionModel.Id, regionModel) as BadRequestObjectResult;
            // Assert
            Assert.NotNull(result);
            Assert.Equal(400,result.StatusCode);
        }
        #endregion
        #region  Edit save with valid (id,  model)
        [Fact]
        public async Task Edit_Save_With_ValidData()
        {
            // Arrange
            var controller = new RegionsController(_cache, _mapper);
            var projCache = Substitute.For<ITrackedEntityCacheServiceProxy<Region>>();
            _cache.CreateTrackedEntity<Region>().Returns(projCache);
            projCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Region, bool>>>()).Returns(Regions[0]);
            _mapper
               .Map<RegionModel, Region>(Arg.Any<RegionModel>())
               .Returns(callInfo =>
               {
                   var projModel = callInfo.Arg<RegionModel>();
                   return Regions.FirstOrDefault();
               });

            var model = GetRegionModel()[0];
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            controller.TempData = tempData;

            // Act
            var result = await controller.Edit(model.Id, model);

            // Assert
            var viewResult = Assert.IsType<RedirectResult>(result);
            var redirectResult = viewResult as RedirectResult;
            var ResposeUrl = "/regions";
            Assert.NotNull(redirectResult);
            Assert.Equal(redirectResult.Url, ResposeUrl);

        }
        #endregion
        #region static methods
        private static List<RegionModel> GetRegionModel()
        {
            List<CountryModel> countries = new List<CountryModel>();
            return new List<RegionModel>
            {
                new RegionModel { Id=1, Abbreviation="rgn1",  Countries=countries, Name="R1"},
                new RegionModel { Id=2, Abbreviation="rgn2",  Countries=countries, Name="R2"},
                new RegionModel { Id=3, Abbreviation="rgn3",  Countries=countries, Name="R3"},
            };
        }
        #endregion
    }
}