﻿using PharmaLex.SmartTRACE.Web.Helpers.Extensions;
using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Extensions
{
    public class SubmissionFilterModelExtensionsTests
    {
        [Fact]
        public void FromFilters_ValidFilters_UpdatesModelCorrectly()
        {
            // Arrange
            var model = new SubmissionFilterModel();
            string[] filters = new string[]
            {
            "ClientName=>Astra",
            "ApplicationNumber=>PE345",
            "SequenceNumber=>678",
            "SubmissionUnit=>johnson",
            "SubmissionMode=>lateral",
            "SubmissionType=>low",
            "Description=>Test Description",
            "DossierFormat=>Centralised",
            "RegulatoryLead=>Pavan",
            "LifecycleState=>Planned"
            };

            // Act
            model = model.FromFilters(filters);

            // Assert
            Assert.Equal("Astra", model.ClientName);
            Assert.Equal("PE345", model.ApplicationNumber);
            Assert.Equal("678", model.SequenceNumber);
            Assert.Equal("johnson", model.SubmissionUnit);
            Assert.Equal("lateral", model.SubmissionMode);
            Assert.Equal("low", model.SubmissionType);
            Assert.Equal("Test Description", model.Description);
            Assert.Equal("Centralised", model.DossierFormat);
            Assert.Equal("Pavan", model.RegulatoryLead);
            Assert.Equal("Planned", model.LifecycleState);
        }
        [Fact]
        public void FromFilters_EmptyFilters_ShouldReturnEmptyModel()
        {
            // Arrange
            var model = new SubmissionFilterModel();
            string[] filters = Array.Empty<string>();

            // Act
            var result = model.FromFilters(filters);

            // Assert
            Assert.Null(result.ClientName);
            Assert.Null(result.ApplicationNumber);
            Assert.Null(result.SequenceNumber);
            Assert.Null(result.SubmissionUnit);
            Assert.Null(result.SubmissionMode);
            Assert.Null(result.SubmissionType);
            Assert.Null(result.Description);
            Assert.Null(result.LifecycleState);
            Assert.Null(result.DossierFormat);
            Assert.Null(result.RegulatoryLead);
            Assert.Null(result.LifecycleState);
        }
        [Fact]
        public void FromFilters_NullFilters_ShouldReturnEmptyModel()
        {
            // Arrange
            var model = new SubmissionFilterModel();
            string[] filters = {
            "invalidfilter",
            "clientname=>InvalidClient"
        };

            // Act
            var result = model.FromFilters(filters);

            // Assert
            Assert.Equal("InvalidClient", result.ClientName);
        }
    }
}

