﻿using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public class ExpressionParameterReplacer : ExpressionVisitor
    {
        private IDictionary<ParameterExpression, ParameterExpression> ParameterReplacements { get; set; }

        public ExpressionParameterReplacer (IList<ParameterExpression> fromParameters, IList<ParameterExpression> toParameters)
        {
            ParameterReplacements = new Dictionary<ParameterExpression, ParameterExpression>();

            for (int i = 0; i != fromParameters.Count && i != toParameters.Count; i++)
            { 
                ParameterReplacements.Add(fromParameters[i], toParameters[i]); 
            }
        }

        protected override Expression VisitParameter(ParameterExpression node)
        {
            ParameterExpression replacement;

            if (ParameterReplacements.TryGetValue(node, out replacement))
            { 
                node = replacement; 
            }

            return base.VisitParameter(node);
        }
    }
}