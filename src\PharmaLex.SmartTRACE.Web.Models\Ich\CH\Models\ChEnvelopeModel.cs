﻿using AutoMapper;
using PharmaLex.SmartTRACE.Web.Models.Ich.CH32;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.CH.Models
{
    public class ChEnvelopeModel : IModule1DataModel
    {
        public string[] ApplicationNumber { get; set; }
        public string SubmissionDescription { get; set; }
        public string[] InventedName { get; set; }
        public GalenicFormModel[] GalenicForm { get; set; }
        public string DmfNumber { get; set; }
        public string PmfNumber { get; set; }
        public string[] Inn { get; set; }
        public string Applicant { get; set; }
        public string DmfHolder { get; set; }
        public string PmfHolder { get; set; }
        public string Agency { get; set; }
        public string[] ApplicationTypes { get; set; }
        public string Paragraph13tpa { get; set; }
        public string Ectdsequence { get; set; }
        public string[] RelatedectDSequence { get; set; }
        public string Country { get; set; }
    }

    public class GalenicFormModel
    {
        public string SwissMedicNumber { get; set; }
        public string Name { get; set; }
        public string GalenicNameLanguage { get; set; }
        public string GalenicNameValue { get; set; }
    }

    public class ChEnvelopModelProfile : Profile
    {
        public ChEnvelopModelProfile()
        {
            this.CreateMap<envelope, ChEnvelopeModel>();
            this.CreateMap<galenicform, GalenicFormModel>();
        }
    }
}
