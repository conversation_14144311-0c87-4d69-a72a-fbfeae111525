﻿namespace PharmaLex.SmartTRACE.Web.DataFactory
{
    public class DataFactoryPipelineSettings
    {
        public string Instance { get; set; }
        public string Subscription { get; set; }
        public string ResourceGroupName { get; set; }
        public string FactoryName { get; set; }
        public string UnpackPipelineName { get; set; }
        public string UploadPath { get; set; }
        public string UnpackPath { get; set; }
        public string AllSequencesPath { get; set; }
        public string DocumentsPath { get; set; }
        public string CopyPipelineName { get; set; }
    }
}
