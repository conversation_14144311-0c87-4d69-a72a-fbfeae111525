﻿using AutoMapper;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.Authentication.B2C;
using System.Collections.Generic;
using System.Threading.Tasks;
using SendGrid;
using Microsoft.AspNetCore.Http;
using SendGrid.Helpers.Mail;
using Microsoft.Extensions.Configuration;
using PharmaLex.DataAccess;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public class UserService : IUserService
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper mapper;
        private readonly IAzureAdB2CGraphService graphService;
        private readonly IOidcService oidcService;
        private readonly HttpContext context;
        private readonly IConfiguration configuration;

        public UserService(IDistributedCacheServiceFactory cache,
            IMapper mapper,
            IAzureAdB2CGraphService graphService,
            IOidcService oidcService,
            IHttpContextAccessor context,
            IConfiguration configuration)
        {
            this.cache = cache;
            this.mapper = mapper;
            this.graphService = graphService;
            this.oidcService = oidcService;
            this.context = context.HttpContext;
            this.configuration = configuration;
        }
        private HttpRequest Request => this.context.Request;

        public async Task<bool> ProcessExternalUser(UserClientsModel userModel)
        {
            var users = cache.CreateTrackedEntity<User>().Configure(o => o
                            .Include(x => x.UserClaim)
                                .ThenInclude(x => x.Claim));
            User user = await users.FirstOrDefaultAsync(x => x.Email.ToLower() == userModel.User.Email.ToLower());

            if (user != null)
                return false;

            bool isNewDbUser = user is null;
            if (isNewDbUser)
            {
                user = new User();
                users.Add(user);
            }

            mapper.Map(userModel.User, user);

            if (!await this.graphService.UserExists(userModel.User.Email) && isNewDbUser)
            {
                await SendSignupEmail(userModel.User);
            }
            else if (isNewDbUser)
            {
                await this.NotifyUser(userModel.User);
            }

            await users.SaveChangesAsync();

            return true;
        }

        public async Task<string> ProcessSignupInvitationResend(UserModel userModel)
        {
            var users = cache.CreateTrackedEntity<User>();
            User u = await users.FirstOrDefaultAsync(x => x.Id == userModel.Id);

            u.InvitationEmailLink = await SendSignupEmail(userModel);
            await users.SaveChangesAsync();

            return u.InvitationEmailLink;
        }

        private async Task<string> SendSignupEmail(UserModel userModel)
        {
            string signupInvitation = oidcService.GetSignupInvitationLink("signup-invitation",
                new Dictionary<string, string>
                {
                        { "displayName", $"{userModel.GivenName} {userModel.FamilyName}" },
                        { "givenName", userModel.GivenName },
                        { "surname", userModel.FamilyName },
                        { "email", userModel.Email }
                });

            await this.NotifyUser(userModel, signupInvitation);

            return signupInvitation;
        }

        private async Task NotifyUser(UserModel user, string signupInvitation = null)
        {
            bool IsemailsSendingEnabled = this.configuration.GetValue<bool>("emailsSendingEnabled");
            if (IsemailsSendingEnabled)
            {
                string apiKey = this.configuration.GetValue<string>("SendGrid");
                string senderEmail = this.configuration.GetValue<string>("AppSettings:SmartPHLEXAdminEmail");

                SendGridClient client = new SendGridClient(apiKey);
                var from = new EmailAddress(senderEmail, "SmartPHLEX admin");
                var to = new EmailAddress(user.Email);

                var model = new ExternalUserCreationModel
                {
                    FullName = $"{user.GivenName} {user.FamilyName}",
                    Url = signupInvitation,
                    OriginalUrl = $"{this.Request.Scheme}://{this.Request.Host}",
                    ContactMail = senderEmail,
                    ExpirationDays = this.configuration.GetValue<string>("AzureAdB2CPolicy:LinkExpiresAfterDays")
                };

                var templateId = this.configuration.GetValue<string>($"AppSettings:{(string.IsNullOrEmpty(signupInvitation) ? "ExternalUserLoginEmailTemplateId" : "ExternalUserSignUpEmailTemplateId")}");

                var msg = new SendGridMessage();
                msg.SetFrom(from);
                msg.AddTo(to);
                msg.SetTemplateId(templateId);
                msg.SetTemplateData(model);

                await client.SendEmailAsync(msg);
            }
        }
    }
}
