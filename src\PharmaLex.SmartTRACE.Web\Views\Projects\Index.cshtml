﻿@model IEnumerable<ProjectModel>
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization

<div id="projects" class="manage-container">
    <header class="manage-header">
        <h2>Manage Projects</h2>
        <a id="exportButton" class="button icon-button-download">Export</a>
    </header>
    <filtered-table :items="projects" :columns="columns" :filters="filters" :link="link"></filtered-table>
    <form id="exportForm" style="display: none;" method="post" action="/projects/export">
        @Html.AntiForgeryToken()
    </form>
</div>

@section Scripts {
    <script type="text/javascript">

    var pageConfig = {
        appElement: '#projects',
        data() {
            return {
                link: '/projects/edit/',
                projects: @Html.Raw(JsonConvert.SerializeObject(Model, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })),
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            type: 'text'
                        },
                        {
                            dataKey: 'code',
                            sortKey: 'code',
                            header: 'Code',
                            type: 'text'
                        },
                        {
                            dataKey: 'opportunityNumber',
                            sortKey: 'opportunityNumber',
                            header: 'Opportunity Number',
                            type: 'text'
                        },
                        {
                            dataKey: 'clientName',
                            sortKey: 'clientName',
                            header: 'Client Name',
                            type: 'text'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'name',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'code',
                        options: [],
                        type: 'search',
                        header: 'Search Code',
                        fn: v => p => p.code.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'opportunityNumber',
                        options: [],
                        type: 'search',
                        header: 'Search Opportunity Number',
                        fn: v => p => p.opportunityNumber.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'clientName',
                        options: [],
                        type: 'search',
                        header: 'Search Client Name',
                        fn: v => p => p.clientName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                ]
            };
        },
        mounted() {
            document.getElementById('exportButton').addEventListener('click', (e) => {
                e.preventDefault();
                document.getElementById('exportForm').submit();
            });
        }
    };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}