﻿using PharmaLex.SmartTRACE.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.WebTests.Models.ViewModels
{
    public class ErrorViewModelTests
    {
        [Fact]
        public void GetErrorViewModel()
        {
            //Arrange
            ErrorViewModel errorViewModel = new ErrorViewModel()
            {
                AppName = "Test",
                NotifyEmail = "Test",
                RequestId = "1"
            };
            //Assert
            Assert.NotNull(errorViewModel);
        }
        [Fact]
        public void GetErrorViewModelForRequestId()
        {
            //Arrange
            ErrorViewModel errorViewModel = new ErrorViewModel()
            {
                AppName = "Test",
                NotifyEmail = "Test", 
            };
            //Assert
            Assert.NotNull(errorViewModel);
            Assert.False(errorViewModel.ShowRequestId);
        }
    }
}
