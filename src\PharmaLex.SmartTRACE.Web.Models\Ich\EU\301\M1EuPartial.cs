﻿using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32;
using PharmaLex.SmartTRACE.Web.Models.Ich.Eu;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.Eu301
{
    public partial class eubackbone : CtdSectionBase, IEctdModule1
    {
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m1eu
                };

                return nodes.Where(x => x != null).ToList();
            }
        }

        public List<CtdSectionBase> Content
        {
            get
            {
                return this.ChildNodes.FirstOrDefault()?.ChildNodes ?? new List<CtdSectionBase>();
            }
        }

        public List<EctdModule1Data> Data => this.euenvelope?.Select(x => x as EctdModule1Data).ToList() ?? new List<EctdModule1Data>();

        public string DtdVersion => this.dtdversion;
        public string Region => "EU";
    }
    public partial class envelope : EuEnvelope
    {
        public override string Country => this.country.GetEnumDescription();

        public override string Identifier => this.identifier;

        public override CtdSubmission Submission =>
                new CtdSubmission
                {
                    Type = this.submission.type.GetEnumDescription(),
                    Mode = this.submission.mode.GetEnumDescription()
                };

        public override string SubmissionUnitType => this.submissionunit?.type.GetEnumDescription();

        public override string Applicant => this.applicant;

        public override string InventedName => string.Join(", ", this.inventedname);

        public override string ProcedureTrackingNumber => string.Join(", ", this.submission.proceduretracking ?? new string[0]);

        public override string TrackingNumber => string.Join(", ", this.submission.tracking ?? new string[0]);

        public override string AgencyCode => this.agency.code.GetEnumDescription();

        public override string Inn => string.Join(", ", this.inn ?? new string[0]);

        public override string Sequence => this.sequence;

        public override string RelatedSequence => string.Join(", ", this.relatedsequence ?? new string[0]);

        public override string SubmissionDescription => this.submissiondescription;
        public override string Procedure => this.procedure.type.GetEnumDescription();
    }
    public partial class submission : CtdSectionBase
    {
    }
    public partial class submissionunit : CtdSectionBase
    {
    }
    public partial class agency : CtdSectionBase
    {
    }
    public partial class procedure : CtdSectionBase
    {
    }

    public partial class m1eu : CtdSectionBase
    {
        public CtdSectionBase CoverLetter
        {
            get
            {
                return this.m10cover != null ? new CustomCtdSection("Cover Letter", "1.0", this.m10cover) : null;
            }
        }

        public CtdSectionBase ApplicationForm
        {
            get
            {
                return this.m12form != null ? new CustomCtdSection("Application Form", "1.2", this.m12form) : null;
            }
        }

        public CtdSectionBase Responses
        {
            get
            {
                return this.m1responses != null ? new CustomCtdSection("Responses to Questions", "1.11", this.m1responses) : null;
            }
        }

        public CtdSectionBase AdditionalData
        {
            get
            {
                return this.m1additionaldata != null ? new CustomCtdSection("Additional Data", "1.12", this.m1additionaldata) : null;
            }
        }
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.CoverLetter,
                    this.ApplicationForm,
                    this.m13pi,
                    this.m14expert,
                    this.m15specific,
                    this.m16environrisk,
                    this.m17orphan,
                    this.m18pharmacovigilance,
                    this.m19clinicaltrials,
                    this.m110paediatrics,
                    this.Responses,
                    this.AdditionalData
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class specific : CtdSectionBase
    {
        public override string CtdName
        {
            get
            {
                return this.country.GetEnumDescription();
            }
        }
    }
    public partial class leaf : CtdSectionBase, Ileaf
    {
        public string op
        {
            get
            {
                string opr = this.operation.ToString();
                return $"{ char.ToUpper(opr[0]) }{ opr.Substring(1) }";
            }
        }

        public string text
        {
            get
            {
                return this.title;
            }
        }

        public string Submission { get; set; }
        public string Path { get; set; }
    }
    public partial class linktext : CtdSectionBase
    {
    }
    public partial class xref : CtdSectionBase
    {
    }
    public partial class nodeextension : CtdSectionBase, Inodeextension
    {
        public override string CtdName
        {
            get
            {
                return $"{this.title} {this.ID}";
            }
        }
        public override string CtdModule => string.Empty;
        public override string CtdSection => string.Empty;
    }
    public partial class m13pi : CtdSectionBase
    {
        public override string CtdName => "Product Information";
        public m131spclabelpl M131Labelling
        {
            get
            {
                return new m131spclabelpl() { pidoc = this.m131spclabelpl ?? new pidoc[] { } };
            }
        }

        public CtdSectionBase M132Mockup
        {
            get
            {
                return this.m132mockup != null ? new CustomCtdSection("Mock-up", "1.3.2", this.m132mockup) : null;
            }
        }

        public CtdSectionBase M133Specimen
        {
            get
            {
                return this.m133specimen != null ? new CustomCtdSection("Specimen", "1.3.3", this.m133specimen) : null;
            }
        }

        public CtdSectionBase M134Consultation
        {
            get
            {
                return this.m134consultation != null ? new CustomCtdSection("Consultation", "1.3.4", this.m134consultation) : null;
            }
        }

        public CtdSectionBase M135Approved
        {
            get
            {
                return this.m135approved != null ? new CustomCtdSection("Product Information already approved in the Member States", "1.3.5", this.m135approved) : null;
            }
        }
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.M131Labelling,
                    this.M132Mockup,
                    this.M133Specimen,
                    this.M134Consultation,
                    this.M135Approved,
                    this.m136braille
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class pidoc : CtdSectionBase
    {
    }
    public partial class m136braille : CtdSectionBase
    {
    }
    public partial class m14expert : CtdSectionBase
    {
        public override string CtdName => "Information about the Experts";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m141quality,
                    this.m142nonclinical,
                    this.m143clinical
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m141quality : CtdSectionBase
    {
    }
    public partial class m142nonclinical : CtdSectionBase
    {
        public override string CtdName => "Non-Clinical";
    }
    public partial class m143clinical : CtdSectionBase
    {
    }
    public partial class m15specific : CtdSectionBase
    {
        public override string CtdName => "Specific Requirements for Different Types of Applications";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m151bibliographic,
                    this.m152generichybridbiosimilar,
                    this.m153datamarketexclusivity,
                    this.m154exceptionalcircumstances,
                    this.m155conditionalma

                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m151bibliographic : CtdSectionBase
    {
        public override string CtdName => "Information for Bibliographical Applications";
    }
    public partial class m152generichybridbiosimilar : CtdSectionBase
    {
        public override string CtdName => "Information for Generic, 'Hybrid' or Bio-similar Applications";
    }
    public partial class m153datamarketexclusivity : CtdSectionBase
    {
        public override string CtdName => "(Extended) Data/Market Exclusivity";
    }
    public partial class m154exceptionalcircumstances : CtdSectionBase
    {
        public override string CtdName => "Exceptional Circumstances";
    }
    public partial class m155conditionalma : CtdSectionBase
    {
        public override string CtdName => "Conditional Marketing Authorisation";
    }
    public partial class m16environrisk : CtdSectionBase
    {
        public override string CtdName => "Environmental Risk Assessment";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();
                if (this.Item == null) return nodes;

                m161nongmo nongmo = this.Item as m161nongmo;
                if (nongmo != null) return new List<CtdSectionBase> { nongmo };

                m162gmo gmo = this.Item as m162gmo;
                if (gmo != null) return new List<CtdSectionBase> { gmo };

                return nodes;
            }
        }
    }
    public partial class m161nongmo : CtdSectionBase
    {
        public override string CtdName => "Non-GMO";
    }
    public partial class m162gmo : CtdSectionBase
    {
        public override string CtdName => "GMO";
    }
    public partial class m17orphan : CtdSectionBase
    {
        public override string CtdName => "Information relating to Orphan Market Exclusivity";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m171similarity,
                    this.m172marketexclusivity

                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m171similarity : CtdSectionBase
    {
    }
    public partial class m172marketexclusivity : CtdSectionBase
    {
        public override string CtdName => "Market Exclusivity";
    }
    public partial class m18pharmacovigilance : CtdSectionBase
    {
        public override string CtdName => "Information relating to Pharmacovigilance";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m181pharmacovigilancesystem,
                    this.m182riskmanagementsystem
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }
    public partial class m181pharmacovigilancesystem : CtdSectionBase
    {
        public override string CtdName => "Pharmacovigilance System";
    }
    public partial class m182riskmanagementsystem : CtdSectionBase
    {
        public override string CtdName => "Risk-management System";
    }
    public partial class m19clinicaltrials : CtdSectionBase
    {
        public override string CtdName => "Information relating to Clinical Trials";
    }
    public partial class m110paediatrics : CtdSectionBase
    {
        public override string CtdName => "Information relating to Paediatrics";
    }
    public partial class euenvelope : CtdSectionBase
    {
    }
    public partial class proceduretracking : CtdSectionBase
    {
    }
    public partial class m10cover : CtdSectionBase
    {
        public override string CtdName => "Cover Letter";
    }
    public partial class m12form : CtdSectionBase
    {
        public override string CtdName => "Application Form";
    }
    public partial class m131spclabelpl : CtdSectionBase
    {
        public override string CtdName => "SmPC, Labelling and Package Leaflet";
        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                if (this.pidoc == null) return null;

                List<CtdSectionBase> nodes = new List<CtdSectionBase>();
                foreach (var country in this.pidoc.GroupBy(x => x.country))
                {
                    List<CtdSectionBase> langNodes = new List<CtdSectionBase>();
                    foreach (var lang in country.GroupBy(x => x.lang))
                    {
                        List<CtdSectionBase> typeNodes = new List<CtdSectionBase>();
                        foreach (var type in lang.GroupBy(x => x.type))
                        {
                            typeNodes.Add(new CustomCtdSection(type.Key.GetEnumDescription(), string.Empty, new CtdSectionBase[] { }, type.SelectMany(x => x.Documents).ToArray()));
                        }

                        langNodes.Add(new CustomCtdSection(lang.Key.GetEnumDescription<Language>(), string.Empty, typeNodes.ToArray()));
                    }
                    nodes.Add(new CustomCtdSection(country.Key.GetEnumDescription(), string.Empty, langNodes.ToArray()));
                }

                return nodes;
            }
        }
    }
    public partial class m132mockup : CtdSectionBase
    {
        public override string CtdName => "Mock-up";
    }
    public partial class m133specimen : CtdSectionBase
    {
    }
    public partial class m134consultation : CtdSectionBase
    {
        public override string CtdName => "Consultation with Target Patient Groups";
    }
    public partial class m135approved : CtdSectionBase
    {
    }
    public partial class m1responses : CtdSectionBase
    {
    }
    public partial class m1additionaldata : CtdSectionBase
    {
    }
}
