﻿using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.US
{
    public abstract class UsAdmin : EctdModule1Data
    {
        public abstract UsEnvelopeSummary Summary { get; }
        public abstract List<UsEnvelopeContact> Contacts { get; }
        public abstract UsApplicationInformation Application { get; }
    }

    public class UsEnvelopeSummary
    {
        public string ApplicantName { get; set; }
        public string ApplicantId { get; set; }
        public string Description { get; set; }
    }

    public class UsEnvelopeContact
    {
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
    }

    public class UsApplicationInformation
    {
        public string ApplicationContainingFiles { get; set; }
        public string ApplicationType { get; set; }
        public string ApplicationNumber { get; set; }
        public string SubmissionType { get; set; }
        public string SubmissionId { get; set; }
        public string SubmissionSubType { get; set; }
        public string SequenceNumber { get; set; }
        public string RelatedSequenceNumber { get; set; }
    }
}
