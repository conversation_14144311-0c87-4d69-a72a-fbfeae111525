﻿using AutoMapper;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.SmartTRACE.Web.Models;
using NSubstitute;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using System.Linq.Expressions;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.DataAccess;
using Microsoft.EntityFrameworkCore;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class ProductsControllerTests
    {
        private IDistributedCacheServiceFactory _cache;
        private IMapper mapperMock;
        private IProductExport ProductExportMock;
        ProductsController productController;

        public ProductsControllerTests()
        {
            _cache = Substitute.For<IDistributedCacheServiceFactory>();
            mapperMock = Substitute.For<IMapper>();
            ProductExportMock = Substitute.For<IProductExport>();
            productController = new ProductsController(_cache, mapperMock, ProductExportMock);

        }
        [Fact]
        public async Task Index_Returns_ViewResult_With_Products()
        {
            var _productCache = Substitute.For<IMappedEntityCacheServiceProxy<Product, ProductModel>>();
            //Arrange          
            var productModels = new List<ProductModel>
            {
                new ProductModel { DosageForm = "Dosage Form1", DosageFormId = 1 },
                new ProductModel { DosageForm = "Dosage Form2", DosageFormId = 2}
            };
            var picklistdatamodel = new List<PicklistDataModel>
            {
                new PicklistDataModel {Id = 1,Name = "Dosage Form1" },
                new PicklistDataModel {Id = 2,Name = "Dosage Form2" },
            };
            _cache.CreateMappedEntity<Product, ProductModel>()
                .Configure(Arg.Any<Func<IIncludable<Product>, IIncludable<Product>>>()).Returns(_productCache);
            _productCache.AllAsync().Returns(Task.FromResult(productModels));

            var _picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);

            for (int i = 0; i < picklistdatamodel.Count; i++)
            {
                _picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(picklistdatamodel[i]);
            }
            // Act
            var result = await productController.Index();

            // Assert
            Assert.NotNull(result);
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult.Model);
        }
        [Fact]
        public async Task New_ReturnsViewWithGoToClient()
        {
            string expectedViewName = "GoToClient";
            // Act
            var result = await productController.New() as ViewResult;
            //// Assert
            Assert.NotNull(result);
            Assert.Equal(expectedViewName, result?.ViewName);
            var model = result?.Model as EditProductViewModel;
            Assert.NotNull(model);
        }
        [Fact]
        public async Task New_ReturnsViewWithEditProduct()
        {
            var _activeSubstanceCache = Substitute.For<IMappedEntityCacheServiceProxy<ActiveSubstance, ActiveSubstanceModel>>();
            var _clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            var _picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            var listactivesubstancemodel = new List<ActiveSubstanceModel>()
            {
                new ActiveSubstanceModel{Name = "xyz",ClientName = "client1"},
                new ActiveSubstanceModel{Name = "abc",ClientName = "client2"},
            };
            var listclientmodel = new List<ClientModel>()
            {
                new ClientModel{Name = "xyz",HasError = true},
                new ClientModel{Name = "abc",HasError = false},
            };
            var picklistdatamodel = new List<PicklistDataModel>()
            {
                new PicklistDataModel{Name = "xyz",Country = "Belgium" },
                new PicklistDataModel{Name = "abc",Country = "germany"},
            };
            _cache.CreateMappedEntity<ActiveSubstance, ActiveSubstanceModel>().Returns(_activeSubstanceCache);
            _activeSubstanceCache.WhereAsync(Arg.Any<Expression<Func<ActiveSubstance, bool>>>())
                .Returns(listactivesubstancemodel);

            _cache.CreateMappedEntity<Client, ClientModel>().Returns(_clientCache);
            _clientCache.AllAsync().Returns(listclientmodel);

            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);
            _picklistCache.AllAsync().Returns(picklistdatamodel);

            string expectedViewName = "EditProduct";
            // Act
            var result = await productController.New() as ViewResult;
            //// Assert
            Assert.NotNull(result);
            Assert.Equal(expectedViewName, result?.ViewName);
            var model = result?.Model as EditProductViewModel;
            Assert.NotNull(model);
        }
        [Fact]
        public async Task SaveNew_ReturnsViewWithEditProduct_invalidmodel()
        {
            EditProductViewModel editProductViewModel = new EditProductViewModel();
            productController.ModelState.AddModelError("SessionName", "Required");
            //Act
            var result = await productController.SaveNew(editProductViewModel) as BadRequestObjectResult;
            //Assert
            Assert.NotNull(result);
            Assert.Equal(400, result?.StatusCode);
        }
        [Fact]
        public async Task SaveNew_ReturnsViewWithProduct_validmodel()
        {
            var editproductviewmodel = new EditProductViewModel()
            {
                IsApplicationAssigned = true,
                Product = new ProductModel()
                {
                    Name = "Test",
                    ActiveSubstances = new List<ActiveSubstanceModel>()
                    {
                       new ActiveSubstanceModel{HasDuplicate=false},
                       new ActiveSubstanceModel{HasDuplicate=true},
                    }
                }
            };
            var _productCache = Substitute.For<ITrackedEntityCacheServiceProxy<Product>>();
            var _activeSubstanceCache = Substitute.For<IMappedEntityCacheServiceProxy<ActiveSubstance, ActiveSubstanceModel>>();
            var _clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            var _picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            var listactivesubstancemodel = new List<ActiveSubstanceModel>()
            {
                new ActiveSubstanceModel{Name = "xyz",ClientName = "client1"},
                new ActiveSubstanceModel{Name = "abc",ClientName = "client2"},
            };
            var listclientmodel = new List<ClientModel>()
            {
                new ClientModel{Name = "xyz",HasError = true},
                new ClientModel{Name = "abc",HasError = false},
            };
            var picklistdatamodel = new List<PicklistDataModel>()
            {
                new PicklistDataModel{Name = "xyz",Country = "Belgium" },
                new PicklistDataModel{Name = "abc",Country = "germany"},
            };
            _cache.CreateMappedEntity<ActiveSubstance, ActiveSubstanceModel>().Returns(_activeSubstanceCache);
            _activeSubstanceCache.WhereAsync(Arg.Any<Expression<Func<ActiveSubstance, bool>>>())
                .Returns(listactivesubstancemodel);

            _cache.CreateMappedEntity<Client, ClientModel>().Returns(_clientCache);
            _clientCache.AllAsync().Returns(listclientmodel);

            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);
            _picklistCache.AllAsync().Returns(picklistdatamodel);

            var product = new Product()
            {
                Name = "ProductName"
            };

            _cache.CreateTrackedEntity<Product>().Returns(_productCache);
            mapperMock.Map<Product>(editproductviewmodel.Product).Returns(product);
            _productCache.Add(product);
            _productCache.SaveChangesAsync().Returns(Task.FromResult);

            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            productController.TempData = tempData;
            // Act
            var result = await productController.SaveNew(editproductviewmodel) as RedirectResult;
            //// Assert
            Assert.NotNull(result?.Url);
            var expectedResult = "/products";
            Assert.Equal(result.Url, expectedResult);
        }
        [Fact]
        public async Task SaveNew_ReturnsViewWithEditProduct_DbUpdateException()
        {
            var editproductviewmodel = new EditProductViewModel()
            {
                Product = new ProductModel()
                {
                    Name = "Test"
                }
            };
            string expectedViewName = "EditProduct";
            var product = new Product()
            {
                Name = "ProductName"
            };
            var objClientCache = Substitute.For<ITrackedEntityCacheServiceProxy<Product>>();
            _cache.CreateTrackedEntity<Product>().Returns(objClientCache);
            mapperMock.Map<Product>(editproductviewmodel.Product).Returns(product);
            objClientCache.SaveChangesAsync().Returns(Task.FromException<int>(new DbUpdateException()));
            var result = await productController.SaveNew(editproductviewmodel) as ViewResult;
            Assert.Equal(expectedViewName, result?.ViewName);
            Assert.NotNull(result?.Model);
        }
        [Fact]
        public async Task Edit_Product_ReturnsView_EditProduct()
        {
            // Arrange
            int productId = 1;
            var editproductviewmodel = new EditProductViewModel()
            {
                Product = new ProductModel()
                {
                    Name = "Test"
                }
            };
            var product = new Product()
            {
                Name = "ProductName"
            };
            var objClientCache = Substitute.For<ITrackedEntityCacheServiceProxy<Product>>();


            _cache.CreateTrackedEntity<Product>()
                                       .Configure(Arg.Any<Func<IIncludable<Product>, IIncludable<Product>>>()).Returns(objClientCache);
            mapperMock.Map<EditProductViewModel>(product).Returns(editproductviewmodel);
            objClientCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(product);

            var _activeSubstanceCache = Substitute.For<IMappedEntityCacheServiceProxy<ActiveSubstance, ActiveSubstanceModel>>();
            var _clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            var _picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            var listactivesubstancemodel = new List<ActiveSubstanceModel>()
            {
                new ActiveSubstanceModel{Name = "xyz",ClientName = "client1"},
                new ActiveSubstanceModel{Name = "abc",ClientName = "client2"},
            };
            var listclientmodel = new List<ClientModel>()
            {
                new ClientModel{Name = "xyz",HasError = true},
                new ClientModel{Name = "abc",HasError = false},
            };
            var picklistdatamodel = new List<PicklistDataModel>()
            {
                new PicklistDataModel{Name = "xyz",Country = "Belgium" },
                new PicklistDataModel{Name = "abc",Country = "germany"},
            };
            _cache.CreateMappedEntity<ActiveSubstance, ActiveSubstanceModel>().Returns(_activeSubstanceCache);
            _activeSubstanceCache.WhereAsync(Arg.Any<Expression<Func<ActiveSubstance, bool>>>())
                .Returns(listactivesubstancemodel);

            _cache.CreateMappedEntity<Client, ClientModel>().Returns(_clientCache);
            _clientCache.AllAsync().Returns(listclientmodel);

            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);
            _picklistCache.AllAsync().Returns(picklistdatamodel);

            String ExpectedViewName = "EditProduct";

            // Act
            var result = await productController.Edit(productId) as ViewResult;
            // Assert
            Assert.NotNull(result);
            Assert.Equal(ExpectedViewName, result.ViewName);
            Assert.NotNull(result?.Model);
        }
        [Fact]
        public async Task Edit_InvalidModel()
        {
            // Arrange
            int id = 10;
            productController.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await productController.Edit(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        [Fact]
        public async Task SaveEdit_Product_ReturnsView_EditProduct()
        {
            string expectedViewName = "EditProduct";
            EditProductViewModel editProductViewModel = new EditProductViewModel();
            productController.ModelState.AddModelError("SessionName", "Required");
            var result = await productController.SaveEdit(1, editProductViewModel) as ViewResult;
            Assert.Equal(expectedViewName, result?.ViewName);
            Assert.NotNull(result?.Model);
        }
        [Fact]
        public async Task SaveEdit_Product_ReturnsRedirectResult_Products()
        {
            var editproductviewmodel = new EditProductViewModel()
            {
                Product = new ProductModel()
                {
                    Name = "Test",
                    ActiveSubstances= new List<ActiveSubstanceModel>()
                    {
                        new ActiveSubstanceModel{ClientName="abc",Name="xyz"},
                        new ActiveSubstanceModel{ClientName="abc1",Name="xyz1"},
                    }
                },
                
            };
            var product = new Product()
            {
                Name = "ProductName"
            };

            var _missingActiveSubstances = Substitute.For<ITrackedEntityCacheServiceProxy<ActiveSubstanceProduct>>();
            var _activeSubsCache = Substitute.For<ITrackedEntityCacheServiceProxy<ActiveSubstance>>();
            var _activeSubstanceProduct = Substitute.For<ITrackedEntityCacheServiceProxy<ActiveSubstanceProduct>>();

            var _productCache = Substitute.For<ITrackedEntityCacheServiceProxy<Product>>();
            var _activeSubstanceCache = Substitute.For<IMappedEntityCacheServiceProxy<ActiveSubstance, ActiveSubstanceModel>>();
            var _clientCache = Substitute.For<IMappedEntityCacheServiceProxy<Client, ClientModel>>();
            var _picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            var listactivesubstancemodel = new List<ActiveSubstanceModel>()
            {
                new ActiveSubstanceModel{Name = "xyz",ClientName = "client1"},
                new ActiveSubstanceModel{Name = "abc",ClientName = "client2"},
            };
            var listclientmodel = new List<ClientModel>()
            {
                new ClientModel{Name = "xyz",HasError = true},
                new ClientModel{Name = "abc",HasError = false},
            };
            var picklistdatamodel = new List<PicklistDataModel>()
            {
                new PicklistDataModel{Name = "xyz",Country = "Belgium" },
                new PicklistDataModel{Name = "abc",Country = "germany"},
            };
            _cache.CreateMappedEntity<ActiveSubstance, ActiveSubstanceModel>().Returns(_activeSubstanceCache);
            _activeSubstanceCache.WhereAsync(Arg.Any<Expression<Func<ActiveSubstance, bool>>>())
                .Returns(listactivesubstancemodel);

            _cache.CreateMappedEntity<Client, ClientModel>().Returns(_clientCache);
            _clientCache.AllAsync().Returns(listclientmodel);

            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);
            _picklistCache.AllAsync().Returns(picklistdatamodel);

            _cache.CreateTrackedEntity<Product>().Returns(_productCache);
            _productCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(product);
            mapperMock.Map(editproductviewmodel, product);

            var activesubstance=new ActiveSubstance()
            {
                Id=1
            };
            var activesubstanceproductlist = new List<ActiveSubstanceProduct>()
            {
                new ActiveSubstanceProduct{ ProductId=1,Id=1,ActiveSubstanceId=1 }
            };

            _cache.CreateTrackedEntity<ActiveSubstance>().Returns(_activeSubsCache);
            _activeSubsCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<ActiveSubstance, bool>>>()).Returns(activesubstance);
            _cache.CreateTrackedEntity<ActiveSubstanceProduct>().Returns(_activeSubstanceProduct);
            _activeSubstanceProduct.Where(Arg.Any<Expression<Func<ActiveSubstanceProduct, bool>>>()).Returns(activesubstanceproductlist);
            _activeSubstanceProduct.WhereAsync(Arg.Any<Expression<Func<ActiveSubstanceProduct, bool>>>()).Returns(Task.FromResult(activesubstanceproductlist));

            _cache.CreateTrackedEntity<ActiveSubstanceProduct>().Returns(_missingActiveSubstances);
            _missingActiveSubstances.Where(Arg.Any<Expression<Func<ActiveSubstanceProduct, bool>>>()).Returns(activesubstanceproductlist);
            _missingActiveSubstances.WhereAsync(Arg.Any<Expression<Func<ActiveSubstanceProduct, bool>>>()).Returns(Task.FromResult(activesubstanceproductlist));

            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            productController.TempData = tempData;

            // Act
            var result = await productController.SaveEdit(1, editproductviewmodel) as RedirectResult;
            Assert.NotNull(result?.Url);
            var expectedResult = "/products";
            Assert.Equal(result.Url, expectedResult);
        }
        [Fact]
        public async Task SaveEdit_ReturnsViewWithEditProduct_DbUpdateException()
        {
            var editproductviewmodel = new EditProductViewModel()
            {
                Product = new ProductModel()
                {
                    Name = "Test"
                }
            };
            string expectedViewName = "EditProduct";
            var product = new Product()
            {
                Name = "ProductName"
            };
            var objClientCache = Substitute.For<ITrackedEntityCacheServiceProxy<Product>>();
            _cache.CreateTrackedEntity<Product>().Returns(objClientCache);
            objClientCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(product);
            mapperMock.Map(editproductviewmodel, product);
            objClientCache.SaveChangesAsync().Returns(Task.FromException<int>(new DbUpdateException()));
            var result = await productController.SaveEdit(1,editproductviewmodel) as ViewResult;
            Assert.Equal(expectedViewName, result?.ViewName);
            Assert.NotNull(result?.Model);
        }
        [Fact]
        public async Task ChangeState_ReturnsOkResult()
        {
            var _productCache = Substitute.For<ITrackedEntityCacheServiceProxy<Product>>();
            var product = new Product()
            {
                Name = "ProductName"
            };

            _cache.CreateTrackedEntity<Product>().Returns(_productCache);
            _productCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Product, bool>>>()).Returns(product);
            int id = 1;
            var result = await productController.ChangeState(1, id.ToString()) as OkObjectResult;
            Assert.Equal(200, result?.StatusCode);

        }
        [Fact]
        public async Task ChangeState_InvalidModel()
        {
            // Arrange
            int id = 10;
            productController.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await productController.ChangeState(1,id.ToString()) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        [Fact]
        public async Task Export_Returns_FileResult()
        {
            // Arrange
            var mockFileContent = new byte[] { 1, 2, 3 };
            var exportResultStream = new MemoryStream(mockFileContent);
            ProductExportMock.Export().Returns(Task.FromResult<byte[]>(mockFileContent));
            // Act
            var result = await productController.Export() as FileContentResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", result.ContentType);
            Assert.NotNull(result.FileContents);
        }
    }
}
