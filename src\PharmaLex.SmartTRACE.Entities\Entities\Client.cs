﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class Client : EntityBase
    {
        public Client()
        {
            Product = new HashSet<Product>();
            Project = new HashSet<Project>();
            ActiveSubstance = new HashSet<ActiveSubstance>();
            UserClient = new HashSet<UserClient>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public int ContractOwnerId { get; set; }

        public virtual ICollection<Product> Product { get; set; }
        public virtual ICollection<Project> Project { get; set; }
        public virtual ICollection<ActiveSubstance> ActiveSubstance { get; set; }
        public virtual ICollection<UserClient> UserClient { get; set; }
    }
}
