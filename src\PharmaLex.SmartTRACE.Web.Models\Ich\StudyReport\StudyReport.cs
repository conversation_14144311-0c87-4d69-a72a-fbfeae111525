﻿using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.StudyReport
{
    [XmlRoot("study-identifier", Namespace = "", IsNullable = false)]
    public partial class studyidentifier
    {
        public string title { get; set; }

        [XmlElement("study-id")]
        public string studyid { get; set; }

        [XmlElement("category")]
        public category[] category { get; set; }
    }

    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class category
    {
        [XmlAttribute()]
        public string name { get; set; }

        [XmlAttribute("info-type")]
        public string infotype { get; set; }

        [XmlText()]
        public string Value { get; set; }
    }

    [XmlRoot("study-document", Namespace = "", IsNullable = false)]
    public partial class studydocument
    {
        [XmlElement("content-block", typeof(contentblock))]
        [XmlElement("doc-content", typeof(doccontent))]
        public object[] Items { get; set; }
    }

    [XmlRoot("content-block", Namespace = "", IsNullable = false)]
    public partial class contentblock
    {
        [XmlElement("block-title")]
        public string blocktitle { get; set; }

        [XmlElement("property")]
        public property[] property { get; set; }

        [XmlElement("file-tag")]
        public filetag[] filetag { get; set; }

        [XmlElement("content-block", typeof(contentblock))]
        [XmlElement("doc-content", typeof(doccontent))]
        public object[] Items { get; set; }
    }

    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class property
    {
        [XmlAttribute()]
        public string name { get; set; }

        [XmlAttribute("info-type")]
        public string infotype { get; set; }

        [XmlText()]
        public string Value { get; set; }
    }

    [XmlRoot("file-tag", Namespace = "", IsNullable = false)]
    public partial class filetag
    {
        [XmlElement("property")]
        public property[] property { get; set; }

        [XmlAttribute()]
        public string name { get; set; }

        [XmlAttribute("info-type")]
        public string infotype { get; set; }
    }

    [XmlRoot("doc-content", Namespace = "", IsNullable = false)]
    public partial class doccontent
    {
        public doccontent()
        {
            this.type = "simple";
        }
        public string title { get; set; }

        [XmlElement("property")]
        public property[] property { get; set; }

        [XmlElement("file-tag")]
        public filetag[] filetag { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/1999/xlink")]
        public string href { get; set; }

        public string type { get; set; }

        [XmlAttribute()]
        public string test { get; set; }
    }

    [XmlRoot(Namespace = "http://www.ich.org/ectd", IsNullable = false)]
    public partial class study
    {
        public study()
        {
            this.dtdversion = "2.2";
        }

        [XmlElement("study-identifier", Namespace = "")]
        public studyidentifier studyidentifier { get; set; }

        [XmlElement("study-document", Namespace = "")]
        public studydocument studydocument { get; set; }

        public string lang { get; set; }

        [XmlAttribute("dtd-version", Form = System.Xml.Schema.XmlSchemaForm.Qualified, Namespace = "")]
        public string dtdversion { get; set; }
    }
}