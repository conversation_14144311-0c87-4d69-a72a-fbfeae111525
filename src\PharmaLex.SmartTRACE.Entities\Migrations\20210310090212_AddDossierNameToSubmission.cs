﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class AddDossierNameToSubmission : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Dossie<PERSON><PERSON><PERSON>",
                schema: "Audit",
                table: "Submission_Audit",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Dossier<PERSON><PERSON>",
                table: "Submission",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.SqlFileExec("0006-AddDossierNameToSubmission-UpdateSubmissionTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>ssier<PERSON><PERSON>",
                schema: "Audit",
                table: "Submission_Audit");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
                table: "Submission");
        }
    }
}
