using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;
namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class ProjectsControllerTests
    {
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IMapper _mapper;
        private readonly IProjectExport _projectExport;
        public List<Project> existingProj = new List<Project>
                {
                    new() { Id = 1, Name = "proj1",ClientId=1, Code="11" },
                    new() { Id = 2, Name = "Existingproj",ClientId=2 ,Code="22" },
                };
        #region Constructor 
        public ProjectsControllerTests()
        {
            _cache = Substitute.For<IDistributedCacheServiceFactory>();
            _mapper = Substitute.For<IMapper>();
            _projectExport = Substitute.For<IProjectExport>();

        }
        #endregion
        #region Action Method Index
        [Fact]

        public async Task Index_Returns_ProjectModelList()
        {
            // Arrange
            var mappedCache = Substitute.For<IMappedEntityCacheServiceProxy<Project, ProjectModel>>();
            var controller = new ProjectsController(_cache, _mapper, _projectExport);
            var projects = GetProjectModel();
            _cache.CreateMappedEntity<Project, ProjectModel>()
              .Configure(Arg.Any<Func<IIncludable<Project>, IIncludable<Project>>>()).Returns(mappedCache);
            mappedCache.AllAsync().Returns(Task.FromResult(projects));

            // Act
            var result = await controller.Index();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsAssignableFrom<IEnumerable<ProjectModel>>(viewResult.ViewData.Model);
            Assert.Equal(projects, model);

        }
        #endregion
        #region Action Method New(int clientId) for invalid
        [Fact]
        public async Task New_Invalid()
        {
            // Arrange
            int id = 10;
            var controller = new ProjectsController(_cache, _mapper, _projectExport);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result =  controller.New(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region Action Method New(int clientId)
        [Fact]
        public void New_Returns_ViewResult_With_Edit_ProjectModel()
        {
            // Arrange
            int clientid = 111;
            var controller = new ProjectsController(_cache, _mapper, _projectExport);

            // Act
            var result = controller.New(clientid) as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("EditProject", result.ViewName);
            Assert.IsType<ProjectModel>(result.Model);
            var resultprojModel = result.Model as ProjectModel;
            Assert.Equal(resultprojModel?.ClientId, clientid);
        }
        #endregion
        #region  SaveNew with invalid model(Id,  model)
        [Fact]
        public async Task SaveNew_with_InValidData()
        {
            int clientId = 1;
            string ViewName = "EditProject";
            var controller = new ProjectsController(_cache, _mapper, _projectExport);
            controller.ModelState.AddModelError("SessionName", "Required");
            var result = await controller.SaveNew(clientId, null) as ViewResult;
            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.ViewName);
            Assert.Equal(result.ViewName, ViewName);
        }
        #endregion
        #region  SaveNew with valid (id,  model)
        [Fact]
        public async Task SaveNew_with_ValidData()
        {
            // Arrange
            var id = 11;
            var controller = new ProjectsController(_cache, _mapper, _projectExport);
            var projCache = Substitute.For<ITrackedEntityCacheServiceProxy<Project>>();
            _cache.CreateTrackedEntity<Project>().Returns(projCache);
            _mapper
               .Map<Project>(Arg.Any<ProjectModel>())
               .Returns(callInfo =>
               {
                   var projModel = callInfo.Arg<ProjectModel>();
                   return existingProj.FirstOrDefault();
               });
            var model = new ProjectModel { Name = "proj1" };
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            controller.TempData = tempData;
            // Act
            var result = await controller.SaveNew(id, model);
            // Assert
            var viewResult = Assert.IsType<RedirectResult>(result);
            var redirectResult = viewResult as RedirectResult;
            var ResposeUrl = "/clients/edit/";
            Assert.NotNull(redirectResult);
            Assert.Equal(redirectResult.Url, ResposeUrl+id);

        }
        #endregion
        #region Edit Returns EditProject Model
        [Fact]

        public async Task Edit_Returns_EditProjectModel()
        {
            // Arrange
            var controller = new ProjectsController(_cache, _mapper, _projectExport);
            var projCache = Substitute.For<IMappedEntityCacheServiceProxy<Project, ProjectModel>>();
            ProjectModel projects = GetProjectModel()[0];
            _cache.CreateMappedEntity<Project, ProjectModel>().Returns(projCache);
            projCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Project, bool>>>()).Returns(projects);

            // Act
            var result = await controller.Edit(projects.Id);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal("EditProject", viewResult.ViewName);
            var model = Assert.IsAssignableFrom<ProjectModel>(viewResult.ViewData.Model);
            Assert.Equal(projects, model);

        }
        #endregion
        #region Action Method New(int clientId) for invalid
        [Fact]
        public async Task Edit_Invalid()
        {
            // Arrange
            int id = 10;
            var controller = new ProjectsController(_cache, _mapper, _projectExport);
            controller.ModelState.AddModelError("id", "ErrorMessage");

            // Act
            var result = await controller.Edit(id) as BadRequestObjectResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        #endregion
        #region  SaveEdit with valid data -SaveEdit(int id, ProjectModel model)
        [Fact]
        public async Task SaveEdit_with_ValidData()
        {
            // Arrange
            var id = 1;
            var ResposeUrl = "/clients/edit/";
            var controller = new ProjectsController(_cache, _mapper, _projectExport);
            var projCache = Substitute.For<ITrackedEntityCacheServiceProxy<Project>>();
            _cache.CreateTrackedEntity<Project>().Returns(projCache);
            projCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Project, bool>>>()).Returns(existingProj[0]);
            _mapper
               .Map<Project>(Arg.Any<ProjectModel>())
               .Returns(callInfo =>
               {
                   var projModel = callInfo.Arg<ProjectModel>();
                   return existingProj.FirstOrDefault();
               });
            var model = new ProjectModel { Name = "Existingproj1" };
            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            controller.TempData = tempData;

            // Act
            var result = await controller.SaveEdit(id, model);

            // Assert
            var viewResult = Assert.IsType<RedirectResult>(result);
            Assert.NotNull(viewResult);
            var resp = result as RedirectResult;
            Assert.Equal(resp?.Url, ResposeUrl + id);
        }
        #endregion
        #region  SaveEdit with invalid model( Id,  model)
        [Fact]
        public async Task SaveEdit_With_InValidData()
        {
            int clientId = 1;
            string ViewName = "EditProject";
            var controller = new ProjectsController(_cache, _mapper, _projectExport);
            controller.ModelState.AddModelError("SessionName", "Required");
            var result = await controller.SaveEdit(clientId, null) as ViewResult;
            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.ViewName);
            Assert.Equal(result.ViewName, ViewName);
        }
        #endregion
        #region Action Method Export Post

        [Fact]
        public async Task Export_Returns_FileResult()
        {
            // Arrange
            var mockFileContent = new byte[] { 1, 2, 3 }; // Mock file content
            var exportResultStream = new MemoryStream(mockFileContent);
            _projectExport.Export().Returns(Task.FromResult<byte[]>(mockFileContent));
            var controller = new ProjectsController(_cache, _mapper, _projectExport);
            // Act
            var result = await controller.Export();

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileResult.ContentType);
            Assert.NotNull(fileResult.FileContents);
        }

        #endregion
        #region static methods
        private static List<ProjectModel> GetProjectModel()
        {
            return new List<ProjectModel>
            {
                new ProjectModel { Id=1,ClientId=1, ClientName="client1", Code="11" , Name="Project1", OpportunityNumber="111"},
                new ProjectModel { Id=2,ClientId=2, ClientName="client2", Code="22" , Name="Project2", OpportunityNumber="222"},
                new ProjectModel { Id=3,ClientId=3, ClientName="client3", Code="33" , Name="Project3", OpportunityNumber="333"}
            };
        }
        #endregion
    }
}