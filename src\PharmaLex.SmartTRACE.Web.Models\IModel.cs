﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models;

public interface IModel { }

public static class IModelExtensions
{
    public static string ToJson(this IModel model)
    {
        var settings = new JsonSerializerSettings
        {
            StringEscapeHandling = StringEscapeHandling.EscapeHtml
        };
        return JsonConvert.SerializeObject(model, settings);
    }

    public static string ToJson(this IEnumerable<IModel> model)
    {
        var settings = new JsonSerializerSettings
        {
            StringEscapeHandling = StringEscapeHandling.EscapeHtml
        };
        return JsonConvert.SerializeObject(model, settings);
    }
}
