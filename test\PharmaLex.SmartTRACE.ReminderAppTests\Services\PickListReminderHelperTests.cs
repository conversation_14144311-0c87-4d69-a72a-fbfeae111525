﻿using PharmaLex.DataAccess;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.RemindersApp.Models;
using PharmaLex.SmartTRACE.RemindersApp.Services;
using PharmaLex.SmartTRACE.WebTests;

namespace PharmaLex.SmartTRACE.ReminderAppTests.Services
{
    public class PickListReminderHelperTests
    {
        private readonly IRepositoryFactory _repoFactory;
        public PickListReminderHelperTests()
        {
            var dbctxReslover = TestHelpers.GetPlxDbContextResolver();
            var dbCtx = ((SmartTRACEContext)dbctxReslover.Context);
            _repoFactory = new RepositoryFactory(dbctxReslover, Substitute.For<IUserContext>());
            
            #region Fake Data
            if (!dbCtx.PicklistData.Any(x => x.Name == "test1" || x.Name == "test2"))
            {
                dbCtx.PicklistData.Add(new PicklistData { Id = 7,PicklistTypeId=1, Name = "test1", CreatedBy = "test", LastUpdatedBy = "test" });
                dbCtx.PicklistData.Add(new PicklistData { Id = 8,PicklistTypeId=1, Name = "test2", CreatedBy = "test", LastUpdatedBy = "test" });
            }
            dbCtx.SaveChanges();
            #endregion
        }
        [Fact]
        public void GetPickListName_Returns_Name()
        {
            //Arrange
            var piklistService = new PickListReminderHelper();

            //Act
             string result= piklistService.GetPickListName(_repoFactory,7);

            //Assert
            Assert.Equal("test1", result);
        }
        [Fact]
        public void GetPickListName_Returns_EmptyString()
        {
            //Arrange
            var piklistService = new PickListReminderHelper();

            //Act
            string result = piklistService.GetPickListName(_repoFactory, 1);

            //Assert
            Assert.Equal("", result);
        }
    }
}
