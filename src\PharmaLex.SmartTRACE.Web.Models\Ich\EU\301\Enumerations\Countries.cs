﻿using System;
using System.ComponentModel;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.Eu301
{
    [Serializable]
    public enum pidocCountry
    {
        [Description("Austria (at)")]
        at,
        [Description("Belgium (be)")]
        be,
        [Description("Bulgaria (bg)")]
        bg,
        [Description("common")]
        common,
        [Description("Cyprus (cy)")]
        cy,
        [Description("Czech Republic (cz)")]
        cz,
        [Description("Germany (de)")]
        de,
        [Description("Denmark (dk)")]
        dk,
        [Description("EU (edqm)")]
        edqm,
        [Description("Estonia (ee)")]
        ee,
        [Description("Greece (el)")]
        el,
        [Description("Spain (es)")]
        es,
        [Description("EU (ema)")]
        ema,
        [Description("Finland (fi)")]
        fi,
        [Description("France (fr)")]
        fr,
        [Description("Croatia (hr)")]
        hr,
        [Description("Hungary (hu)")]
        hu,
        [Description("Ireland (ie)")]
        ie,
        [Description("Iceland (is)")]
        @is,
        [Description("Italy (it)")]
        it,
        [Description("Liechtenstein (li)")]
        li,
        [Description("Lithuania (lt)")]
        lt,
        [Description("Luxembourg (lu)")]
        lu,
        [Description("Latvia (lv)")]
        lv,
        [Description("Malta (mt)")]
        mt,
        [Description("Netherlands (nl)")]
        nl,
        [Description("Norway (no)")]
        no,
        [Description("Poland (pl)")]
        pl,
        [Description("Portugal (pt)")]
        pt,
        [Description("Romania (ro)")]
        ro,
        [Description("Sweden (se)")]
        se,
        [Description("Slovenia (si)")]
        si,
        [Description("Slovakia (sk)")]
        sk,
        [Description("United Kingdom (uk)")]
        uk,
    }

    [Serializable]
    public enum specificCountry
    {
        [Description("Austria (at)")]
        at,
        [Description("Belgium (be)")]
        be,
        [Description("Bulgaria (bg)")]
        bg,
        [Description("common")]
        common,
        [Description("Cyprus (cy)")]
        cy,
        [Description("Czech Republic (cz)")]
        cz,
        [Description("Germany (de)")]
        de,
        [Description("Denmark (dk)")]
        dk,
        [Description("EU (edqm)")]
        edqm,
        [Description("Estonia (ee)")]
        ee,
        [Description("Greece (el)")]
        el,
        [Description("Spain (es)")]
        es,
        [Description("EU (ema)")]
        ema,
        [Description("Finland (fi)")]
        fi,
        [Description("France (fr)")]
        fr,
        [Description("Croatia (hr)")]
        hr,
        [Description("Hungary (hu)")]
        hu,
        [Description("Ireland (ie)")]
        ie,
        [Description("Iceland (is)")]
        @is,
        [Description("Italy (it)")]
        it,
        [Description("Liechtenstein (li)")]
        li,
        [Description("Lithuania (lt)")]
        lt,
        [Description("Luxembourg (lu)")]
        lu,
        [Description("Latvia (lv)")]
        lv,
        [Description("Malta (mt)")]
        mt,
        [Description("Netherlands (nl)")]
        nl,
        [Description("Norway (no)")]
        no,
        [Description("Poland (pl)")]
        pl,
        [Description("Portugal (pt)")]
        pt,
        [Description("Romania (ro)")]
        ro,
        [Description("Sweden (se)")]
        se,
        [Description("Slovenia (si)")]
        si,
        [Description("Slovakia (sk)")]
        sk,
        [Description("United Kingdom (uk)")]
        uk,
    }

    [Serializable]
    public enum envelopeCountry
    {
        [Description("Austria (at)")]
        at,
        [Description("Belgium (be)")]
        be,
        [Description("Bulgaria (bg)")]
        bg,
        [Description("common")]
        common,
        [Description("Cyprus (cy)")]
        cy,
        [Description("Czech Republic (cz)")]
        cz,
        [Description("Germany (de)")]
        de,
        [Description("Denmark (dk)")]
        dk,
        [Description("EU (edqm)")]
        edqm,
        [Description("Estonia (ee)")]
        ee,
        [Description("Greece (el)")]
        el,
        [Description("Spain (es)")]
        es,
        [Description("EU (ema)")]
        ema,
        [Description("Finland (fi)")]
        fi,
        [Description("France (fr)")]
        fr,
        [Description("Croatia (hr)")]
        hr,
        [Description("Hungary (hu)")]
        hu,
        [Description("Ireland (ie)")]
        ie,
        [Description("Iceland (is)")]
        @is,
        [Description("Italy (it)")]
        it,
        [Description("Liechtenstein (li)")]
        li,
        [Description("Lithuania (lt)")]
        lt,
        [Description("Luxembourg (lu)")]
        lu,
        [Description("Latvia (lv)")]
        lv,
        [Description("Malta (mt)")]
        mt,
        [Description("Netherlands (nl)")]
        nl,
        [Description("Norway (no)")]
        no,
        [Description("Poland (pl)")]
        pl,
        [Description("Portugal (pt)")]
        pt,
        [Description("Romania (ro)")]
        ro,
        [Description("Sweden (se)")]
        se,
        [Description("Slovenia (si)")]
        si,
        [Description("Slovakia (sk)")]
        sk,
        [Description("United Kingdom (uk)")]
        uk,
    }
}
