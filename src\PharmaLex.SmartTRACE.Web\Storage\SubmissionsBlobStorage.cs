﻿using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Options;
using Pharmalex.AzureCloudStorage;
using Pharmalex.AzureCloudStorage.Interfaces;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.CommonFnctions;
using PharmaLex.SmartTRACE.Web.DataFactory;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Storage
{
    public interface ISubmissionBlobStorage : IAzureBlobClient { }
    public class SubmissionBlobStorage : AzureBlobClient, ISubmissionBlobStorage
    {
        public SubmissionBlobStorage(IOptions<StorageSettings> settings) : base(new AzureBlobClientConfiguration { StorageAccountName = settings.Value.Account, ContainerName = settings.Value.Container, VisualStudioTenantId = settings.Value.TenantId }) { }
    }

    public interface ISubmissionBlobContainer : IAzureBlobContainerClient
    {
        string GetFullFilePath(string zipFileName, string clientId = null, string applicationId = null, string filePath = null);
        Task<List<string>> ListSequencesAsync();
        Task<bool> CopySequenceFromPublicToPrivate(string zipFileName, string clientId = null, string applicationId = null, string submissionUniqueId = null, string documentTypeId = null, string version = null);
        Task<bool> UnpackSequenceAsync(string zipFileName, string clientId = null, string applicationId = null, 
                                       string submissionUniqueId = null, string documentTypeId = null, string version = null);
        Task<string> GetSequenceUploadLinkAsync(string fileName, string clientId = null, string applicationId = null, 
                                                string submissionUniqueId = null, string documentTypeId = null, string version = null);
        Task<BlobDownloadResult> GetFileDownloadAsync(string zipFileName, string fileName, string clientId = null,
                                             string applicationId = null, string submissionUniqueId = null, string documentTypeId = null, string version = null);
        Task<BlobDownloadResult> GetFileDownloadAsync(string fileName, string clientId, string applicationId);
        Task<string> GetFileDownloadLinkAsync(string zipFileName, string fileName, string clientId = null,
                                              string applicationId = null, string submissionUniqueId = null, string documentTypeId = null, string version = null);
        Task<string> GetFileDownloadLinkAsync(string fileName, string client, string applicationId);
        Task ClearSequenceAsync(string zipFileName, string clientId = null, string applicationId = null,
                                string submissionUniqueId = null, string documentTypeId = null, string version = null);
       Task UpdateSequenceMetadata(string zipFileName, string clientId = null, string applicationId = null, 
                                    string submissionUniqueId = null, string documentTypeId = null, string version = null);
        Task<List<string>> GetSequences(string sequenceLocation, bool recursive = false);
        string GetUnpackPath(string zipFileName, string fileName, string clientId = null,
                                              string applicationId = null, string submissionUniqueId = null, string documentTypeId = null, string version = null);
        string GetUploadPath(string zipFileName, string fileName, string clientId = null,
                                              string applicationId = null, string submissionUniqueId = null, string documentTypeId = null, string version = null);
    }

    public static class Constants
    {
        public static readonly string _CONTAINER = "container";
        public static readonly string _PREFIX = "prefix";
        public static readonly string _FILENAME = "filename";
    }
    public class SubmissionBlobContainer : AzureBlobContainerClient, ISubmissionBlobContainer
    {
        private readonly IAzureDataFactoryManagementClient adfClient;
        private readonly DataFactoryPipelineSettings settings;
        

        public SubmissionBlobContainer(
            ISubmissionBlobStorage storage,
            IAzureDataFactoryManagementClient adfClient,
            IOptions<DataFactoryPipelineSettings> pipelineSettings)
            : base(storage)
        {
            this.adfClient = adfClient;
            this.settings = pipelineSettings.Value;
        }

        public async Task ClearSequenceAsync(
            string zipFileName, 
            string clientId = null, 
            string applicationId = null, 
            string submissionUniqueId = null,
            string documentTypeId = null,
            string version = null)
        {
            string submissionUnpackedDirectoryName = this.adfClient.GetUnpackPath(zipFileName);
            string uploadedFileName = this.adfClient.GetUploadPath(zipFileName);

            if (!string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(applicationId) && 
                !string.IsNullOrEmpty(submissionUniqueId) && !string.IsNullOrEmpty(documentTypeId) && !string.IsNullOrEmpty(version))
            {
                submissionUnpackedDirectoryName = this.adfClient.GetUnpackPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);
                uploadedFileName = this.adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);
            }
            else if (string.IsNullOrEmpty(documentTypeId) && string.IsNullOrEmpty(version))
            {
                submissionUnpackedDirectoryName = this.adfClient.GetUnpackPath(zipFileName, clientId, applicationId);
                uploadedFileName = this.adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId);
            }
            await this.SetAccessTierAsync(uploadedFileName, "Cool");
            await this.DeleteDirectoryAsync(submissionUnpackedDirectoryName);
            await this.DeleteBlobAsync(uploadedFileName);
        }

        public async Task<BlobDownloadResult> GetFileDownloadAsync(
            string zipFileName,
            string fileName,
            string clientId = null,
            string applicationId = null,
            string submissionUniqueId = null,
            string documentTypeId = null,
            string version = null)
        {
            string unpackedDirectoryName = this.adfClient.GetUnpackPath(zipFileName);
            var cb = await this.GetBlobClientAsync($"{unpackedDirectoryName}/{fileName}");
            var uploadedFilePath = string.Empty;

            if (!string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(applicationId) && !string.IsNullOrEmpty(submissionUniqueId) && !string.IsNullOrEmpty(documentTypeId) && !string.IsNullOrEmpty(version))
            {
                uploadedFilePath = this.adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);
                cb = await this.GetBlobClientAsync($"{uploadedFilePath}");
            }
            else if (string.IsNullOrEmpty(fileName))
            {
                uploadedFilePath = this.adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId);
                cb = await this.GetBlobClientAsync($"{uploadedFilePath}");
            }

            return await cb.DownloadContentAsync();
        }
        public async Task<BlobDownloadResult> GetFileDownloadAsync(string fileName, string clientId, string applicationId)
        {
            string unpackedDirectoryName = this.adfClient.GetUnpackPath(fileName, clientId, applicationId);
            var cb = await this.GetBlobClientAsync(unpackedDirectoryName);
            return await cb.DownloadContentAsync();
        }

        public async Task<string> GetFileDownloadLinkAsync(
            string zipFileName,
            string fileName,
            string clientId = null,
            string applicationId = null,
            string submissionUniqueId = null,
            string documentTypeId = null,
            string version = null)
        {
            string unpackedDirectoryName = this.adfClient.GetUnpackPath(zipFileName);
            var cb = await this.GetBlobClientAsync($"{unpackedDirectoryName}/{fileName}");
            var uploadedFilePath = string.Empty;

            if (!string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(applicationId) && !string.IsNullOrEmpty(submissionUniqueId) && !string.IsNullOrEmpty(documentTypeId) && !string.IsNullOrEmpty(version))
            {
                uploadedFilePath = this.adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);
                cb = await this.GetBlobClientAsync($"{uploadedFilePath}");
            }
            else if (string.IsNullOrEmpty(fileName))
            {
                uploadedFilePath = this.adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId);
                cb = await this.GetBlobClientAsync($"{uploadedFilePath}");
            }

            return cb.GetDownloadLink();
        }
        public async Task<string> GetFileDownloadLinkAsync(string fileName, string client, string applicationId)
        {
            string unpackedDirectoryName = this.adfClient.GetUnpackPath(fileName, client, applicationId);
            var cb = await this.GetBlobClientAsync(unpackedDirectoryName);
            return cb.GetDownloadLink();
        }

        public async Task<string> GetSequenceUploadLinkAsync(string fileName, 
            string clientId = null, 
            string applicationId = null, 
            string submissionUniqueId = null, 
            string documentTypeId = null,
            string version = null)
        {
                string uploadFilePath = BlobCommonFunctions.GetUploadLink(adfClient, fileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);
                return (await this.GetBlobClientAsync(uploadFilePath)).GetUploadLink();
        }
        

        public async Task<bool>CopySequenceFromPublicToPrivate(string zipFileName, string clientId = null,
            string applicationId = null,
            string submissionUniqueId = null,
            string documentTypeId = null,
            string version = null)
        {
             
            if (!string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(applicationId) && !string.IsNullOrEmpty(submissionUniqueId) &&
               !string.IsNullOrEmpty(documentTypeId) && !string.IsNullOrEmpty(version))
            {
                return await this.adfClient.RunPipelineAsync(new Dictionary<string, object>
                    {
                        { Constants._CONTAINER,  (await _azureBlobClient.GetContainerAsync()).Name },
                        { Constants._PREFIX, this.adfClient.GetUploadPath(null, clientId, applicationId, submissionUniqueId, documentTypeId, version) },
                        { Constants._FILENAME, zipFileName }
                    }, this.settings.CopyPipelineName);
            }
            else if (string.IsNullOrEmpty(documentTypeId) && string.IsNullOrEmpty(version))
            {
                return await this.adfClient.RunPipelineAsync(new Dictionary<string, object>
                    {
                        { Constants._CONTAINER,  (await _azureBlobClient.GetContainerAsync()).Name },
                        { Constants._PREFIX, this.adfClient.GetUploadPath(null, clientId, applicationId, submissionUniqueId) },
                        { Constants._FILENAME, zipFileName }
                    }, this.settings.CopyPipelineName);
            }
            return await this.adfClient.RunPipelineAsync(new Dictionary<string, object>
            {
                { Constants._CONTAINER,  (await _azureBlobClient.GetContainerAsync()).Name },
                { Constants._PREFIX, this.adfClient.GetUploadPath() },
                { Constants._FILENAME, zipFileName }
            }, this.settings.CopyPipelineName);
        }

        public async Task<bool> UnpackSequenceAsync(string zipFileName, 
            string clientId = null, 
            string applicationId = null,                                                    
            string submissionUniqueId = null, 
            string documentTypeId = null, 
            string version = null)
        {
            if (!string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(applicationId) && !string.IsNullOrEmpty(submissionUniqueId) &&
                !string.IsNullOrEmpty(documentTypeId) && !string.IsNullOrEmpty(version))
            {
                    return await this.adfClient.RunPipelineAsync(new Dictionary<string, object>
                    {
                        { "container",  (await _azureBlobClient.GetContainerAsync()).Name },
                        { "uploadprefix", this.adfClient.GetUploadPath(null, clientId, applicationId, submissionUniqueId, documentTypeId, version) },
                        { "unpackprefix", this.adfClient.GetUnpackPath(null, clientId, applicationId, submissionUniqueId, documentTypeId, version) },
                        { "filename", zipFileName }
                    }, this.settings.UnpackPipelineName);
            }
            else if(string.IsNullOrEmpty(documentTypeId) && string.IsNullOrEmpty(version))
            {
                return await this.adfClient.RunPipelineAsync(new Dictionary<string, object>
                    {
                        { "container",  (await _azureBlobClient.GetContainerAsync()).Name },
                        { "uploadprefix", this.adfClient.GetUploadPath(null, clientId, applicationId, submissionUniqueId) },
                        { "unpackprefix", this.adfClient.GetUnpackPath(null, clientId, applicationId) },
                        { "filename", zipFileName }
                    }, this.settings.UnpackPipelineName);
            }

            return await this.adfClient.RunPipelineAsync(new Dictionary<string, object>
            {
                { "container",  (await _azureBlobClient.GetContainerAsync()).Name },
                { "uploadprefix", this.adfClient.GetUploadPath() },
                { "unpackprefix", this.adfClient.GetUnpackPath() },
                { "filename", zipFileName }
            }, this.settings.UnpackPipelineName);
        }

        public async Task<List<string>> ListSequencesAsync()
        {
            var path = this.adfClient.GetUnpackPath();
            var blobs = await this.ListDirectoriesAsync(path);
            return blobs.Select(x => x.prefix.Split('/')[1]).ToList();
        }

        public string GetFullFilePath(string zipFileName, string clientId = null, string applicationId = null, string filePath = null)
        {
            if (!string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(applicationId))
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    return this.adfClient.GetUnpackPath(zipFileName, clientId, applicationId);
                }
                return $"{this.adfClient.GetUnpackPath(zipFileName, clientId, applicationId)}/{filePath}";
            }

            if (string.IsNullOrEmpty(filePath))
            {
                return this.adfClient.GetUnpackPath(zipFileName);
            }

            return $"{this.adfClient.GetUnpackPath(zipFileName)}/{filePath}";
        }

        public async Task<List<string>> GetSequences(string sequenceLocation, bool recursive = false)
        {
            var baseDirectories = (await this.ListDirectoriesAsync(sequenceLocation))
                .Select(x => (Prefix: x.prefix, Uri: x.uri.ToString().TrimEnd('/'))).ToList();

            await this.ListBlobsAsync(sequenceLocation);

            if (recursive)
            {
                var directories = new List<string>();
                
                foreach (var directory in baseDirectories)
                {
                    directories.AddRange((await this.ListDirectoriesAsync(directory.Uri))
                        .Select(x => x.uri.ToString().TrimEnd('/')));
                }

                var suffixes = directories.Select(x => x.Split($"{sequenceLocation}/").Last()).OrderBy(x => x).ToList();
                return suffixes;
            }
            
            return baseDirectories.Select(x => x.Uri.Substring(x.Uri.LastIndexOf('/') + 1)).OrderBy(x => x).ToList();
        }

        public async Task UpdateSequenceMetadata(string zipFileName, 
            string clientId = null, string 
            applicationId = null, 
            string submissionUniqueId = null,
            string documentTypeId = null, 
            string version = null)
        {
            var uploadedFilePath = this.adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);
            if (documentTypeId == ((int)DocumentType.Dossier).ToString())
            {
                uploadedFilePath = this.adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId);
            }
            var cb = await this.GetBlobClientAsync($"{uploadedFilePath}");
            var metadata = new Dictionary<string, string>();
            metadata.Add("unpacked", "true");
            await cb.SetMetadataAsync(metadata);
        }

        public string GetUnpackPath(string zipFileName, string fileName, string clientId = null, string applicationId = null, string submissionUniqueId = null, string documentTypeId = null, string version = null)
        {
            string unpackedDirectoryName = this.adfClient.GetUnpackPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);

            return unpackedDirectoryName;
        }

        public string GetUploadPath(string zipFileName, string fileName, string clientId = null, string applicationId = null, string submissionUniqueId = null, string documentTypeId = null, string version = null)
        {
            string uploadPath = this.adfClient.GetUploadPath(zipFileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);

            return uploadPath;
        }
    }
}
