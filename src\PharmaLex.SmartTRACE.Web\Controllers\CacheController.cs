﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching;
using PharmaLex.SmartTRACE.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    [Route("/")]
    public class CacheController : BaseController
    {
        private readonly IDistributedCacheService cache;
        public CacheController(IDistributedCacheService cache) => this.cache = cache;

        [HttpGet("cache"), Authorize(Policy = "SuperAdmin")]
        public IActionResult Cache()
        {
            return View(this.GetCacheModel());
        }

        [HttpGet("cache/edit/{key}"), Authorize(Policy = "SuperAdmin")]
        public async Task<IActionResult> Edit(string key)
        {
            string val = await cache.GetStringAsync(key);
            return View(new CacheViewModel { Key = key, Value = val });
        }

        [HttpPost, Route("cache/edit/{key}"), Authorize(Policy = "SuperAdmin"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(string key, CacheViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            Regex regex = new Regex("\r\n[ ]*", RegexOptions.None, TimeSpan.FromMilliseconds(100));
            string value = regex.Replace(model.Value, string.Empty);
            await this.cache.SetStringAsync(key, value.Replace("&#39;", "'"));
            return View("Cache", this.GetCacheModel());
        }

        [HttpPost, Route("cache/delete"), Authorize(Policy = "SuperAdmin")]
        public async Task<IActionResult> Delete(string key)
        {
            await cache.RemoveAsync(key);

            return View("Cache", this.GetCacheModel());
        }

        [HttpPost, Route("cache/flush"), Authorize(Policy = "SuperAdmin")]
        public async Task<IActionResult> Flush()
        {
            await cache.RemoveAllAsync();

            return this.Redirect("/cache");
        }

        private List<CacheViewModel> GetCacheModel()
        {
            List<CacheViewModel> models = [];

            var allCache = cache.GetAll();
            foreach (string dep in allCache.Keys.OrderBy(x => x))
            {
                foreach (string key in allCache[dep])
                {
                    models.Add(new CacheViewModel
                    {
                        Key = dep,
                        Value = key
                    });
                }
            }

            return models;
        }
    }
}
