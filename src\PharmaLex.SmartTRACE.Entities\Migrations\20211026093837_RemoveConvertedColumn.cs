﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class RemoveConvertedColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Converted",
                schema: "Audit",
                table: "DocshifterDocumentFile_Audit");

            migrationBuilder.DropColumn(
                name: "Converted",
                table: "DocshifterDocumentFile");

            migrationBuilder.SqlFileExec("0023-RemoveConvertedColumn-UpdateDocshifterDocumentFileTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "Converted",
                schema: "Audit",
                table: "DocshifterDocumentFile_Audit",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "Converted",
                table: "DocshifterDocumentFile",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
