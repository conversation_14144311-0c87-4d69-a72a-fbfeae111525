﻿using PharmaLex.SmartTRACE.Web.Models.Ich.CH.Enumerations;
using System;
using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.CH32
{
    [Serializable]
    [XmlRoot("ch-envelope", Namespace = "", IsNullable = false)]
    public partial class chenvelope
    {
        public envelope envelope { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class envelope
    {
        [XmlElement("application-number")]
        public string[] applicationnumber { get; set; }

        [XmlElement("submission-description")]
        public string submissiondescription { get; set; }

        [XmlElement("invented-name")]
        public string[] inventedname { get; set; }

        [XmlElement("galenic-form")]
        public galenicform[] galenicform { get; set; }

        [XmlElement("dmf-number")]
        public string dmfnumber { get; set; }

        [XmlElement("pmf-number")]
        public string pmfnumber { get; set; }

        [XmlElement("inn")]
        public string[] inn { get; set; }
        public string applicant { get; set; }

        [XmlElement("dmf-holder")]
        public string dmfholder { get; set; }

        [XmlElement("pmf-holder")]
        public string pmfholder { get; set; }
        public string agency { get; set; }

        [XmlElement("application")]
        public application[] application { get; set; }

        [XmlElement("paragraph-13-tpa")]
        public string paragraph13tpa { get; set; }

        [XmlElement("ectd-sequence")]
        public string ectdsequence { get; set; }

        [XmlElement("related-ectd-sequence")]
        public string[] relatedectdsequence { get; set; }

        [XmlAttribute()]
        public envelopeCountry country { get; set; }
    }

    [Serializable]
    [XmlRoot("galenic-form", Namespace = "", IsNullable = false)]
    public partial class galenicform
    {
        [XmlElement("swissmedic-number")]
        public string swissmedicnumber { get; set; }

        [XmlElement("galenic-name")]
        public galenicname galenicname { get; set; }

        [XmlAttribute()]
        public string name { get; set; }
    }

    [Serializable]
    [XmlRoot("galenic-name", Namespace = "", IsNullable = false)]
    public partial class galenicname
    {
        [XmlAttribute()]
        public galenicnameLanguage language { get; set; }

        [XmlText()]
        public string Value { get; set; }
    }  

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class application
    {
        [XmlAttribute()]
        public applicationType type { get; set; }
    }

    [Serializable]
    [XmlRoot("node-extension", Namespace = "", IsNullable = false)]
    public partial class nodeextension
    {
        public string title { get; set; }

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class leaf
    {
        public leaf()
        {
            this.type = "simple";
        }
        public string title { get; set; }

        [XmlElement("link-text")]
        public linktext linktext { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute("application-version")]
        public string applicationversion { get; set; }

        [XmlAttribute()]
        public string version { get; set; }

        [XmlAttribute("font-library")]
        public string fontlibrary { get; set; }

        [XmlAttribute()]
        public leafOperation operation { get; set; }

        [XmlAttribute("modified-file")]
        public string modifiedfile { get; set; }

        [XmlAttribute()]
        public string checksum { get; set; }

        [XmlAttribute("checksum-type")]
        public string checksumtype { get; set; }

        [XmlAttribute()]
        public string keywords { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string type { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string role { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string href { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefShow show { get; set; }
        [XmlIgnore()]
        public bool showSpecified { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefActuate actuate { get; set; }
        [XmlIgnore()]
        public bool actuateSpecified { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("link-text", Namespace = "", IsNullable = false)]
    public partial class linktext
    {
        [XmlElement("xref")]
        public xref[] Items { get; set; }

        [XmlText()]
        public string[] Text { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class xref
    {
        public xref()
        {
            this.type = "simple";
        }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string type { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string role { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string title { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string href { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefShow show { get; set; }

        [XmlIgnore()]
        public bool showSpecified { get; set; }
        public xrefActuate actuate { get; set; }
        [XmlIgnore()]
        public bool actuateSpecified { get; set; }
    }

    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3c.org/1999/xlink")]
    public enum xrefShow
    {
        @new,
        replace,
        embed,
        other,
        none,
    }

    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3c.org/1999/xlink")]
    public enum xrefActuate
    {
        onLoad,
        onRequest,
        other,
        none,
    }

    [Serializable]
    public enum leafOperation
    {
        @new,
        append,
        replace,
        delete,
    }

    [Serializable]
    [XmlRoot("m1-ch", Namespace = "", IsNullable = false)]
    public partial class m1ch
    {
        [XmlElement("m1-galenic-form")]
        public m1galenicform[] m1galenicform { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-galenic-form", Namespace = "", IsNullable = false)]
    public partial class m1galenicform
    {
        [XmlElement("m1-0-cover")]
        public m10cover m10cover { get; set; }

        [XmlElement("m1-2-applvar")]
        public m12applvar m12applvar { get; set; }

        [XmlElement("m1-3-pi")]
        public m13pi m13pi { get; set; }

        [XmlElement("m1-4-expert")]
        public m14expert m14expert { get; set; }

        [XmlElement("m1-5-bioavailability")]
        public m15bioavailability m15bioavailability { get; set; }

        [XmlElement("m1-6-environrisk")]
        public m16environrisk m16environrisk { get; set; }

        [XmlElement("m1-7-decisions-authorities")]
        public m17decisionsauthorities m17decisionsauthorities { get; set; }

        [XmlElement("m1-8-pharmacovigilance")]
        public m18pharmacovigilance m18pharmacovigilance { get; set; }

        [XmlElement("m1-9-fast-track-decision")]
        public m19fasttrackdecision m19fasttrackdecision { get; set; }

        [XmlElement("m1-10-paediatrics")]
        public m110paediatrics m110paediatrics { get; set; }

        [XmlElement("m1-11-orphandrug")]
        public m111orphandrug m111orphandrug { get; set; }

        [XmlElement("m1-12-art14sec1letabisquater")]
        public m112art14sec1letabisquater m112art14sec1letabisquater { get; set; }

        [XmlElement("m1-swiss-responses")]
        public m1swissresponses m1swissresponses { get; set; }

        [XmlElement("m1-additional-info")]
        public m1additionalinfo m1additionalinfo { get; set; }

        [XmlAttribute()]
        public string name { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-0-cover", Namespace = "", IsNullable = false)]
    public partial class m10cover
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

    }

    [Serializable]
    [XmlRoot("m1-2-applvar", Namespace = "", IsNullable = false)]
    public partial class m12applvar
    {
        [XmlElement("m1-2-1-foapplvar")]
        public m121foapplvar m121foapplvar { get; set; }

        [XmlElement("m1-2-2-form-add")]
        public m122formadd m122formadd { get; set; }

        [XmlElement("m1-2-3-quality")]
        public m123quality m123quality { get; set; }

        [XmlElement("m1-2-4-manufacturing")]
        public m124manufacturing m124manufacturing { get; set; }

        [XmlElement("m1-2-5-others")]
        public m125others m125others { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-1-foapplvar", Namespace = "", IsNullable = false)]
    public partial class m121foapplvar
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-form-add", Namespace = "", IsNullable = false)]
    public partial class m122formadd
    {
        [XmlElement("m1-2-2-1-form-full-declaration")]
        public m1221formfulldeclaration m1221formfulldeclaration { get; set; }

        [XmlElement("m1-2-2-2-form-manufacturer-information")]
        public m1222formmanufacturerinformation m1222formmanufacturerinformation { get; set; }

        [XmlElement("m1-2-2-3-form-status-marketing-authorisations-abroad")]
        public m1223formstatusmarketingauthorisationsabroad m1223formstatusmarketingauthorisationsabroad { get; set; }

        [XmlElement("m1-2-2-4-form-variation-requiring-notification")]
        public m1224formvariationrequiringnotification m1224formvariationrequiringnotification { get; set; }

        [XmlElement("m1-2-2-5-form-quality-variation-requiring-approval")]
        public m1225formqualityvariationrequiringapproval m1225formqualityvariationrequiringapproval { get; set; }

        [XmlElement("m1-2-2-6-form-application-for-extension-of-authorisation")]
        public m1226formapplicationforextensionofauthorisation m1226formapplicationforextensionofauthorisation { get; set; }

        [XmlElement("m1-2-2-7-form-human-blood-components")]
        public m1227formhumanbloodcomponents m1227formhumanbloodcomponents { get; set; }

        [XmlElement("m1-2-2-8-form-substances-of-animal-or-human-origin")]
        public m1228formsubstancesofanimalorhumanorigin m1228formsubstancesofanimalorhumanorigin { get; set; }

        [XmlElement("m1-2-2-9-form-pharmaceutical-information-for-parenteral-preparations")]
        public m1229formpharmaceuticalinformationforparenteralpreparations m1229formpharmaceuticalinformationforparenteralpreparations { get; set; }

        [XmlElement("m1-2-2-10-form-co-marketing-confirmation")]
        public m12210formcomarketingconfirmation m12210formcomarketingconfirmation { get; set; }

        [XmlElement("m1-2-2-11-form-import-according-to-paragraph-14-section-2-tpa")]
        public m12211formimportaccordingtoparagraph14section2tpa m12211formimportaccordingtoparagraph14section2tpa { get; set; }

        [XmlElement("m1-2-2-12-form-safety-changes-to-product-information")]
        public m12212formsafetychangestoproductinformation m12212formsafetychangestoproductinformation { get; set; }

        [XmlElement("m1-2-2-13-form-change-of-marketing-authorisation-holder")]
        public m12213formchangeofmarketingauthorisationholder m12213formchangeofmarketingauthorisationholder { get; set; }

        [XmlElement("m1-2-2-14-cl-formal-control")]
        public m12214clformalcontrol m12214clformalcontrol { get; set; }

        [XmlElement("m1-2-2-15-cl-formal-control-13")]
        public m12215clformalcontrol13 m12215clformalcontrol13 { get; set; }

        [XmlElement("m1-2-2-16-form-psur-for-human-medicines")]
        public m12216formpsurforhumanmedicines m12216formpsurforhumanmedicines { get; set; }

        [XmlElement("m1-2-2-17-form-declaration-radiopharmaceuticals")]
        public m12217formdeclarationradiopharmaceuticals m12217formdeclarationradiopharmaceuticals { get; set; }

        [XmlElement("m1-2-2-18-form-confirmation-substances-from-gmo")]
        public m12218formconfirmationsubstancesfromgmo m12218formconfirmationsubstancesfromgmo { get; set; }

        [XmlElement("m1-2-2-19-form-dmf")]
        public m12219formdmf m12219formdmf { get; set; }

        [XmlElement("m1-2-2-20-form-information-applications-art-13-tpa")]
        public m12220forminformationapplicationsart13tpa m12220forminformationapplicationsart13tpa { get; set; }

        [XmlElement("m1-2-2-21-form-notification-sample-packages")]
        public m12221formnotificationsamplepackages m12221formnotificationsamplepackages { get; set; }

        [XmlElement("m1-2-2-22-form-notification-of-no-marketing-or-interruption-to-distribution")]
        public m12222formnotificationofnomarketingorinterruptiontodistribution m12222formnotificationofnomarketingorinterruptiontodistribution { get; set; }

        [XmlElement("m1-2-2-23-form-application-for-recognition-of-orphan-drug-status")]
        public m12223formapplicationforrecognitionoforphandrugstatus m12223formapplicationforrecognitionoforphandrugstatus { get; set; }

        [XmlElement("m1-2-2-24-application-for-recognition-of-fast-track-status")]
        public m12224applicationforrecognitionoffasttrackstatus m12224applicationforrecognitionoffasttrackstatus { get; set; }

        [XmlElement("m1-2-2-25-form-pip")]
        public m12225formpip m12225formpip { get; set; }

        [XmlElement("m1-2-2-26-gcpinspections")]
        public m12226gcpinspections m12226gcpinspections { get; set; }

        [XmlElement("m1-2-2-99-other-forms")]
        public m12299otherforms m12299otherforms { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-1-form-full-declaration", Namespace = "", IsNullable = false)]
    public partial class m1221formfulldeclaration
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-2-form-manufacturer-information", Namespace = "", IsNullable = false)]
    public partial class m1222formmanufacturerinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-3-form-status-marketing-authorisations-abroad", Namespace = "", IsNullable = false)]
    public partial class m1223formstatusmarketingauthorisationsabroad
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-4-form-variation-requiring-notification", Namespace = "", IsNullable = false)]
    public partial class m1224formvariationrequiringnotification
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-5-form-quality-variation-requiring-approval", Namespace = "", IsNullable = false)]
    public partial class m1225formqualityvariationrequiringapproval
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-6-form-application-for-extension-of-authorisation", Namespace = "", IsNullable = false)]
    public partial class m1226formapplicationforextensionofauthorisation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-7-form-human-blood-components", Namespace = "", IsNullable = false)]
    public partial class m1227formhumanbloodcomponents
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-8-form-substances-of-animal-or-human-origin", Namespace = "", IsNullable = false)]
    public partial class m1228formsubstancesofanimalorhumanorigin
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-9-form-pharmaceutical-information-for-parenteral-preparations", Namespace = "", IsNullable = false)]
    public partial class m1229formpharmaceuticalinformationforparenteralpreparations
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-10-form-co-marketing-confirmation", Namespace = "", IsNullable = false)]
    public partial class m12210formcomarketingconfirmation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-11-form-import-according-to-paragraph-14-section-2-tpa", Namespace = "", IsNullable = false)]
    public partial class m12211formimportaccordingtoparagraph14section2tpa
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-12-form-safety-changes-to-product-information", Namespace = "", IsNullable = false)]
    public partial class m12212formsafetychangestoproductinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-13-form-change-of-marketing-authorisation-holder", Namespace = "", IsNullable = false)]
    public partial class m12213formchangeofmarketingauthorisationholder
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-14-cl-formal-control", Namespace = "", IsNullable = false)]
    public partial class m12214clformalcontrol
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-15-cl-formal-control-13", Namespace = "", IsNullable = false)]
    public partial class m12215clformalcontrol13
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-16-form-psur-for-human-medicines", Namespace = "", IsNullable = false)]
    public partial class m12216formpsurforhumanmedicines
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-17-form-declaration-radiopharmaceuticals", Namespace = "", IsNullable = false)]
    public partial class m12217formdeclarationradiopharmaceuticals
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-18-form-confirmation-substances-from-gmo", Namespace = "", IsNullable = false)]
    public partial class m12218formconfirmationsubstancesfromgmo
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-19-form-dmf", Namespace = "", IsNullable = false)]
    public partial class m12219formdmf
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-20-form-information-applications-art-13-tpa", Namespace = "", IsNullable = false)]
    public partial class m12220forminformationapplicationsart13tpa
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-21-form-notification-sample-packages", Namespace = "", IsNullable = false)]
    public partial class m12221formnotificationsamplepackages
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-22-form-notification-of-no-marketing-or-interruption-to-distribution", Namespace = "", IsNullable = false)]
    public partial class m12222formnotificationofnomarketingorinterruptiontodistribution
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-23-form-application-for-recognition-of-orphan-drug-status", Namespace = "", IsNullable = false)]
    public partial class m12223formapplicationforrecognitionoforphandrugstatus
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-24-application-for-recognition-of-fast-track-status", Namespace = "", IsNullable = false)]
    public partial class m12224applicationforrecognitionoffasttrackstatus
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-25-form-pip", Namespace = "", IsNullable = false)]
    public partial class m12225formpip
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-26-gcpinspections", Namespace = "", IsNullable = false)]
    public partial class m12226gcpinspections
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-2-99-other-forms", Namespace = "", IsNullable = false)]
    public partial class m12299otherforms
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-3-quality", Namespace = "", IsNullable = false)]
    public partial class m123quality
    {
        [XmlElement("m1-2-3-1-dmf-letter-of-access")]
        public m1231dmfletterofaccess m1231dmfletterofaccess { get; set; }

        [XmlElement("m1-2-3-2-certificate-of-suitability-for-active-substance")]
        public m1232certificateofsuitabilityforactivesubstance m1232certificateofsuitabilityforactivesubstance { get; set; }

        [XmlElement("m1-2-3-3-certificate-of-suitability-for-tse")]
        public m1233certificateofsuitabilityfortse m1233certificateofsuitabilityfortse { get; set; }

        [XmlElement("m1-2-3-4-ema-certificate-for-plasma-master-file-pmf")]
        public m1234emacertificateforplasmamasterfilepmf m1234emacertificateforplasmamasterfilepmf { get; set; }

        [XmlElement("m1-2-3-5-ema-certificate-for-vaccine-antigen-master-file-vamf")]
        public m1235emacertificateforvaccineantigenmasterfilevamf m1235emacertificateforvaccineantigenmasterfilevamf { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-3-1-dmf-letter-of-access", Namespace = "", IsNullable = false)]
    public partial class m1231dmfletterofaccess
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-3-2-certificate-of-suitability-for-active-substance", Namespace = "", IsNullable = false)]
    public partial class m1232certificateofsuitabilityforactivesubstance
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-3-3-certificate-of-suitability-for-tse", Namespace = "", IsNullable = false)]
    public partial class m1233certificateofsuitabilityfortse
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-3-4-ema-certificate-for-plasma-master-file-pmf", Namespace = "", IsNullable = false)]
    public partial class m1234emacertificateforplasmamasterfilepmf
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-3-5-ema-certificate-for-vaccine-antigen-master-file-vamf", Namespace = "", IsNullable = false)]
    public partial class m1235emacertificateforvaccineantigenmasterfilevamf
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-4-manufacturing", Namespace = "", IsNullable = false)]
    public partial class m124manufacturing
    {
        [XmlElement("m1-2-4-1-gmp-certificate-or-other-gmp-documents")]
        public m1241gmpcertificateorothergmpdocuments m1241gmpcertificateorothergmpdocuments { get; set; }

        [XmlElement("m1-2-4-2-manufacturing-authorisation")]
        public m1242manufacturingauthorisation m1242manufacturingauthorisation { get; set; }

        [XmlElement("m1-2-4-3-complete-manufacturing-information-with-flow-chart")]
        public m1243completemanufacturinginformationwithflowchart m1243completemanufacturinginformationwithflowchart { get; set; }

        [XmlElement("m1-2-4-4-confirmation-on-gmp-conformity")]
        public m1244confirmationongmpconformity m1244confirmationongmpconformity { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-4-1-gmp-certificate-or-other-gmp-documents", Namespace = "", IsNullable = false)]
    public partial class m1241gmpcertificateorothergmpdocuments
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-4-2-manufacturing-authorisation", Namespace = "", IsNullable = false)]
    public partial class m1242manufacturingauthorisation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-4-3-complete-manufacturing-information-with-flow-chart", Namespace = "", IsNullable = false)]
    public partial class m1243completemanufacturinginformationwithflowchart
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-4-4-confirmation-on-gmp-conformity", Namespace = "", IsNullable = false)]
    public partial class m1244confirmationongmpconformity
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-5-others", Namespace = "", IsNullable = false)]
    public partial class m125others
    {
        [XmlElement("m1-2-5-1-comparison-of-approved-product-information")]
        public m1251comparisonofapprovedproductinformation m1251comparisonofapprovedproductinformation { get; set; }

        [XmlElement("m1-2-5-2-company-core-data-sheet")]
        public m1252companycoredatasheet m1252companycoredatasheet { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-5-1-comparison-of-approved-product-information", Namespace = "", IsNullable = false)]
    public partial class m1251comparisonofapprovedproductinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-5-2-company-core-data-sheet", Namespace = "", IsNullable = false)]
    public partial class m1252companycoredatasheet
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-pi", Namespace = "", IsNullable = false)]
    public partial class m13pi
    {
        [XmlElement("m1-3-1-professionals")]
        public m131professionals m131professionals { get; set; }

        [XmlElement("m1-3-2-patient")]
        public m132patient m132patient { get; set; }

        [XmlElement("m1-3-3-packaging")]
        public m133packaging m133packaging { get; set; }

        [XmlElement("m1-3-4-professionals-other-countries")]
        public m134professionalsothercountries m134professionalsothercountries { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-professionals", Namespace = "", IsNullable = false)]
    public partial class m131professionals
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-2-patient", Namespace = "", IsNullable = false)]
    public partial class m132patient
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-3-packaging", Namespace = "", IsNullable = false)]
    public partial class m133packaging
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-4-professionals-other-countries", Namespace = "", IsNullable = false)]
    public partial class m134professionalsothercountries
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-expert", Namespace = "", IsNullable = false)]
    public partial class m14expert
    {
        [XmlElement("m1-4-1-quality")]
        public m141quality m141quality { get; set; }

        [XmlElement("m1-4-2-non-clinical")]
        public m142nonclinical m142nonclinical { get; set; }

        [XmlElement("m1-4-3-clinical")]
        public m143clinical m143clinical { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-1-quality", Namespace = "", IsNullable = false)]
    public partial class m141quality
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-2-non-clinical", Namespace = "", IsNullable = false)]
    public partial class m142nonclinical
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-3-clinical", Namespace = "", IsNullable = false)]
    public partial class m143clinical
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-bioavailability", Namespace = "", IsNullable = false)]
    public partial class m15bioavailability
    {
        [XmlElement("m1-5-1-info-accord-app-iv-guideline-bioequivalence")]
        public m151infoaccordappivguidelinebioequivalence m151infoaccordappivguidelinebioequivalence { get; set; }

        [XmlElement("m1-5-2-reference-product")]
        public m152referenceproduct m152referenceproduct { get; set; }

        [XmlElement("m1-5-3-confirmation-identity-bioequivalence")]
        public m153confirmationidentitybioequivalence m153confirmationidentitybioequivalence { get; set; }

        [XmlElement("m1-5-4-art14-tab-compare")]
        public m154art14tabcompare m154art14tabcompare { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-1-info-accord-app-iv-guideline-bioequivalence", Namespace = "", IsNullable = false)]
    public partial class m151infoaccordappivguidelinebioequivalence
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-2-reference-product", Namespace = "", IsNullable = false)]
    public partial class m152referenceproduct
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-3-confirmation-identity-bioequivalence", Namespace = "", IsNullable = false)]
    public partial class m153confirmationidentitybioequivalence
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-4-art14-tab-compare", Namespace = "", IsNullable = false)]
    public partial class m154art14tabcompare
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }    

    [Serializable]
    [XmlRoot("m1-6-environrisk", Namespace = "", IsNullable = false)]
    public partial class m16environrisk
    {
        [XmlElement("m1-6-1-nongmo", typeof(m161nongmo))]
        [XmlElement("m1-6-2-gmo", typeof(m162gmo))]
        public object Item { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-1-nongmo", Namespace = "", IsNullable = false)]
    public partial class m161nongmo
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-2-gmo", Namespace = "", IsNullable = false)]
    public partial class m162gmo
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-decisions-authorities", Namespace = "", IsNullable = false)]
    public partial class m17decisionsauthorities
    {
        [XmlElement("m1-7-1-responses")]
        public m171responses m171responses { get; set; }

        [XmlElement("m1-7-2-assessment")]
        public m172assessment m172assessment { get; set; }

        [XmlElement("m1-7-3-eu-decisions")]
        public m173eudecisions m173eudecisions { get; set; }

        [XmlElement("m1-7-4-fda-decision")]
        public m174fdadecision m174fdadecision { get; set; }

        [XmlElement("m1-7-5-foreign-decisions")]
        public m175foreigndecisions m175foreigndecisions { get; set; }

        [XmlElement("m1-7-6-article13adddoc")]
        public m176article13adddoc m176article13adddoc { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-1-responses", Namespace = "", IsNullable = false)]
    public partial class m171responses
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-2-assessment", Namespace = "", IsNullable = false)]
    public partial class m172assessment
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-3-eu-decisions", Namespace = "", IsNullable = false)]
    public partial class m173eudecisions
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-4-fda-decision", Namespace = "", IsNullable = false)]
    public partial class m174fdadecision
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-5-foreign-decisions", Namespace = "", IsNullable = false)]
    public partial class m175foreigndecisions
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-6-article13adddoc", Namespace = "", IsNullable = false)]
    public partial class m176article13adddoc
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-pharmacovigilance", Namespace = "", IsNullable = false)]
    public partial class m18pharmacovigilance
    {
        [XmlElement("m1-8-1-pharmacovigilance-system")]
        public m181pharmacovigilancesystem m181pharmacovigilancesystem { get; set; }

        [XmlElement("m1-8-2-risk-management-system")]
        public m182riskmanagementsystem m182riskmanagementsystem { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-1-pharmacovigilance-system", Namespace = "", IsNullable = false)]
    public partial class m181pharmacovigilancesystem
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-2-risk-management-system", Namespace = "", IsNullable = false)]
    public partial class m182riskmanagementsystem
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-fast-track-decision", Namespace = "", IsNullable = false)]
    public partial class m19fasttrackdecision
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-10-paediatrics", Namespace = "", IsNullable = false)]
    public partial class m110paediatrics
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-orphandrug", Namespace = "", IsNullable = false)]
    public partial class m111orphandrug
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-art14sec1letabisquater", Namespace = "", IsNullable = false)]
    public partial class m112art14sec1letabisquater
    {
        [XmlElement("m1-12-1-eueftaauthorisation")]
        public m1121eueftaauthorisation m1121eueftaauthorisation { get; set; }

        [XmlElement("m1-12-2-eueftadocreference")]
        public m1122eueftadocreference m1122eueftadocreference { get; set; }

        [XmlElement("m1-12-3-overallmedicaluse")]
        public m1123overallmedicaluse m1123overallmedicaluse { get; set; }

        [XmlElement("m1-12-4-cantonalauthorisation")]
        public m1124cantonalauthorisation m1124cantonalauthorisation { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-1-eueftaauthorisation", Namespace = "", IsNullable = false)]
    public partial class m1121eueftaauthorisation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-2-eueftadocreference", Namespace = "", IsNullable = false)]
    public partial class m1122eueftadocreference
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-3-overallmedicaluse", Namespace = "", IsNullable = false)]
    public partial class m1123overallmedicaluse
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-4-cantonalauthorisation", Namespace = "", IsNullable = false)]
    public partial class m1124cantonalauthorisation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-swiss-responses", Namespace = "", IsNullable = false)]
    public partial class m1swissresponses
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-additional-info", Namespace = "", IsNullable = false)]
    public partial class m1additionalinfo
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }
    }

    [Serializable]
    [XmlRoot("ch-backbone", Namespace = "http://www.swissmedic.ch", IsNullable = false)]
    public partial class chbackbone
    {
        public chbackbone()
        {
            this.dtdversion = "1.4";
        }
        [XmlElement("ch-envelope", Namespace = "")]
        public chenvelope chenvelope { get; set; }

        [XmlArray("m1-ch", Namespace = "")]
        [XmlArrayItem("m1-galenic-form", IsNullable = false)]
        public m1galenicform[] m1ch { get; set; }
        public string lang { get; set; }

        [XmlAttribute("dtd-version", Namespace = "")]
        public string dtdversion { get; set; }
    }
}
