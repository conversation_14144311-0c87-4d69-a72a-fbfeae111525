﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using NSubstitute;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.WebTests;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Authorization
{
    public class AuthorizationHelperTests
    {
        readonly IServiceCollection services;

        public AuthorizationHelperTests()
        {
            var configuration = TestHelpers.GetConfiguration();
            var dbctxReslover = TestHelpers.GetPlxDbContextResolver();

            var repoFactory = new RepositoryFactory(dbctxReslover, Substitute.For<IUserContext>());


            #region Fake Data
            var dbCtx = ((SmartTRACEContext)dbctxReslover.Context);

            if (!dbCtx.Claim.Any(x => x.Id > 0))
            {
                dbCtx.Claim.Add(new Entities.Claim { Name = "SuperAdmin", ClaimType = "admin", CreatedBy = "support", LastUpdatedBy = "support" });
                dbCtx.Claim.Add(new Entities.Claim { Name = "BusinessAdmin", ClaimType = "admin", CreatedBy = "support", LastUpdatedBy = "support" });
                dbCtx.Claim.Add(new Entities.Claim { Name = "editor", ClaimType = "application", CreatedBy = "support", LastUpdatedBy = "support" });
                dbCtx.Claim.Add(new Entities.Claim { Name = "reader", ClaimType = "application", CreatedBy = "support", LastUpdatedBy = "support" });
                dbCtx.Claim.Add(new Entities.Claim { Name = "UserAdmin", ClaimType = "admin", CreatedBy = "support", LastUpdatedBy = "support" });
                dbCtx.Claim.Add(new Entities.Claim { Name = "ExternalEditor", ClaimType = "application", CreatedBy = "support", LastUpdatedBy = "support" });
                dbCtx.Claim.Add(new Entities.Claim { Name = "PrivilegedExternalEditor", ClaimType = "application", CreatedBy = "support", LastUpdatedBy = "support" });
                dbCtx.SaveChanges();
            }

            #endregion
            services = new ServiceCollection();
            services.AddSingleton(configuration);
            services.AddSingleton<IRepositoryFactory>(repoFactory);
            services.AddOptions();
            services.AddLogging();
        }
        [Fact]
        public void AddPolicies_SuccessTest()
        {
            services.AddPolicies();
            var serviceProvider = services.BuildServiceProvider();
            var authorizationOptions = serviceProvider.GetRequiredService<IOptions<AuthorizationOptions>>().Value;

            // Assert
            Assert.NotNull(authorizationOptions);
            Assert.NotNull(authorizationOptions.GetPolicy("admin"));
        }
    }
}
