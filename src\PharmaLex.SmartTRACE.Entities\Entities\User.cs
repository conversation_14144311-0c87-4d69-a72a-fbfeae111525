﻿using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class User : EntityBase
    {
        public User()
        {
            SubmissionPublisher = new HashSet<SubmissionPublisher>();
            UserClaim = new HashSet<UserClaim>();
            UserClient = new HashSet<UserClient>();
        }

        public int Id { get; set; }
        public string Email { get; set; }
        public string GivenName { get; set; }
        public string FamilyName { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public int UserTypeId { get; set; }
        public bool AutoAccessClients { get; set; }
        public string InvitationEmailLink { get; set; }

        public virtual ICollection<UserClaim> UserClaim { get; set; }
        public virtual ICollection<SubmissionPublisher> SubmissionPublisher { get; set; }
        public virtual ICollection<UserClient> UserClient { get; set; }
    }
}
