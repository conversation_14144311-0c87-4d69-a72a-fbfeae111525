﻿using System.ComponentModel;

namespace PharmaLex.SmartTRACE.Entities.Enums
{
    public enum PicklistType
    {
        [Description("Medicinal Product Domain")]
        MedicinalProductDomain = 1,

        [Description("Medicinal Product Type")]
        MedicinalProductType = 2,

        [Description("Dosage Form")]
        DosageForm = 3,

        [Description("Application Type")]
        ApplicationType = 4,

        [Description("Procedure Type")]
        ProcedureType = 5,

        [Description("Delivery Details")]
        SubmissionDeliveryDetails = 6,

        [Description("Dossier Format")]
        DossierFormat = 7,

        [Description("Submission Type")]
        SubmissionType = 8,

        [Description("Submission Unit")]
        SubmissionUnit = 9,

        [Description("Submission Mode")]
        SubmissionMode = 10,

        Priority = 11,

        [Description("Estimated Size")]
        EstimatedSize = 12,

        [Description("Publishing Team")]
        PublishingTeam = 13,

        [Description("Contract Owner")]
        ContractOwner = 14
    }
}
