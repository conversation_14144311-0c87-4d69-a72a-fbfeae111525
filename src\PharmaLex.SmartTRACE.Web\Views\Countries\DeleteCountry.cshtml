﻿@model CountryModel
@{
    ViewData["Title"] = "Delete country";
}

<div class="manage-container">
    <header class="manage-header">
        <h2>Delete <em>@Model.Name</em></h2>
        <a class="button secondary icon-button-back" href="/countries/@Model.Id">Back</a>
    </header>
    <form method="post">
        <div class="form-col">
            <p>Warning: Deleting is permanent and once deleted the country cannot be restored.</p>
            <p>&nbsp;</p>
            <p>Are you sure you want to delete the country '<strong>@Model.Name</strong>'?</p>
        </div>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/countries/edit/@Model.Id">Cancel</a>
            <button class="delete-button icon-button-delete">Delete</button>
        </div>
    </form>
</div>