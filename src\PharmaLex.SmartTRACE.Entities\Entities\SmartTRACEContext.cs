﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class SmartTRACEContext : PlxDbContext
    {
        public virtual DbSet<Claim> Claim { get; set; }
        public virtual DbSet<User> User { get; set; }
        public virtual DbSet<UserClaim> UserClaim { get; set; }
        public virtual DbSet<PicklistData> PicklistData { get; set; }
        public virtual DbSet<Application> Application { get; set; }
        public virtual DbSet<RegulatoryAuthority> RegulatoryAuthority { get; set; }
        public virtual DbSet<Country> Country { get; set; }
        public virtual DbSet<Region> Region { get; set; }
        public virtual DbSet<Client> Client { get; set; }
        public virtual DbSet<Product> Product { get; set; }
        public virtual DbSet<ActiveSubstance> ActiveSubstance { get; set; }
        public virtual DbSet<ActiveSubstanceProduct> ActiveSubstanceProduct { get; set; }
        public virtual DbSet<RegulatoryAuthorityCountry> RegulatoryAuthorityCountry { get; set; }
        public virtual DbSet<Project> Project { get; set; }
        public virtual DbSet<Submission> Submission { get; set; }
        public virtual DbSet<SubmissionResource> SubmissionResource { get; set; }
        public virtual DbSet<SubmissionPublisher> SubmissionPublisher { get; set; }
        public virtual DbSet<ApplicationCountry> ApplicationCountry { get; set; }
        public virtual DbSet<SubmissionCountry> SubmissionCountry { get; set; }
        public virtual DbSet<ApplicationProduct> ApplicationProduct { get; set; }
        public virtual DbSet<PicklistDataCountry> PicklistDataCountry { get; set; }
        public virtual DbSet<UserClient> UserClient { get; set; }
        public virtual DbSet<Document> Document { get; set; }
        public virtual DbSet<DocshifterDocumentFile> DocshifterDocumentFile { get; set; }

        public SmartTRACEContext(DbContextOptions options, IUserContext userContext, IDbConnectionService sqlService)
            : base(options, userContext, sqlService) { }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.EntityBase<Claim>(entity =>
            {
                entity.Property(e => e.ClaimType).HasMaxLength(32);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(1024);
            });

            modelBuilder.EntityBase<User>(entity =>
            {
                entity.Property(e => e.Email)
                    .IsRequired()
                    .HasMaxLength(256);

                entity.Property(e => e.FamilyName).HasMaxLength(512);

                entity.Property(e => e.GivenName).HasMaxLength(512);

                entity.Property(e => e.LastLoginDate).HasColumnType("datetime");
            });

            modelBuilder.EntityBase<UserClaim>(entity =>
            {
                entity.HasKey(x => new { x.UserId, x.ClaimId });

                entity.HasOne(d => d.Claim)
                    .WithMany(p => p.UserClaim)
                    .HasForeignKey(x => x.ClaimId)
                    .HasConstraintName("FK_UserClaim_Claim");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserClaim)
                    .HasForeignKey(x => x.UserId)
                    .HasConstraintName("FK_UserClaim_User");
            });

            modelBuilder.EntityBase<PicklistData>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);
            });

            modelBuilder.EntityBase<Application>(entity =>
            {
                entity.Property(e => e.ApplicationNumber).HasMaxLength(32);

                entity.Property(e => e.Comments).HasMaxLength(250);
            });

            modelBuilder.EntityBase<RegulatoryAuthority>();

            modelBuilder.EntityBase<Country>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(128);

                entity.Property(e => e.TwoLetterCode).HasMaxLength(128);

                entity.HasOne(d => d.Region)
                    .WithMany(p => p.Country)
                    .HasForeignKey(x => x.RegionId)
                    .HasConstraintName("FK_Country_Region");

                entity.HasIndex(x => x.Name)
                     .HasDatabaseName("UC_Country_Name")
                     .IsUnique();
            });

            modelBuilder.EntityBase<Region>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(128);

                entity.Property(e => e.Abbreviation).HasMaxLength(8);
            });

            modelBuilder.EntityBase<Client>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(128);

                entity.HasIndex(x => x.Name)
                      .HasDatabaseName("UC_Client_Name")
                      .IsUnique();
            });

            modelBuilder.EntityBase<Product>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);

                entity.Property(e => e.Strength).HasMaxLength(64);

                entity.HasIndex(x => new { x.Name, x.ClientId, x.Strength, x.DosageFormId })
                       .HasDatabaseName("UC_Product_Name_ClientId_Strength_DosageFormId")
                       .IsUnique();

                entity.HasOne(d => d.Client)
                    .WithMany(p => p.Product)
                    .HasForeignKey(x => x.ClientId)
                    .HasConstraintName("FK_Product_Client");
            });

            modelBuilder.EntityBase<ActiveSubstance>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);

                entity.HasIndex(x => new { x.Name, x.ClientId })
                .HasDatabaseName("UC_ActiveSubstance_Name_ClientId")
                .IsUnique();

                entity.HasOne(d => d.Client)
                  .WithMany(p => p.ActiveSubstance)
                  .HasForeignKey(x => x.ClientId)
                  .HasConstraintName("FK_ActiveSubstance_Client")
                  .OnDelete(DeleteBehavior.NoAction);
            });

            modelBuilder.EntityBase<Project>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(256);

                entity.Property(e => e.Code).HasMaxLength(32);

                entity.Property(e => e.OpportunityNumber).HasMaxLength(32);

                entity.HasOne(d => d.Client)
                    .WithMany(p => p.Project)
                    .HasForeignKey(d => d.ClientId)
                    .HasConstraintName("FK_Project_Client");
            });

            modelBuilder.EntityBase<Submission>(entity =>
            {
                entity.Property(e => e.UniqueId).HasMaxLength(32);

                entity.Property(e => e.SequenceNumber).HasMaxLength(10);

                entity.Property(e => e.RelatedSequenceNumber).HasMaxLength(10);

                entity.Property(e => e.SerialNumber).HasMaxLength(10);

                entity.Property(e => e.ReferenceNumber).HasMaxLength(50);

                entity.Property(e => e.CespNumber).HasMaxLength(128);

                entity.Property(e => e.DocubridgeVersionId).HasMaxLength(35);

                entity.Property(e => e.HealthAuthorityDueDate).HasColumnType("datetime");

                entity.Property(e => e.AuthoringDeadline).HasColumnType("datetime");

                entity.Property(e => e.PlannedDispatchDate).HasColumnType("datetime");

                entity.Property(e => e.ActualDispatchDate).HasColumnType("datetime");

                entity.Property(e => e.PlannedSubmissionDate).HasColumnType("datetime");

                entity.Property(e => e.ActualSubmissionDate).HasColumnType("datetime");

                entity.Property(e => e.WithdrawalDate).HasColumnType("datetime");

                entity.HasOne(d => d.SubmissionResource)
                    .WithOne(p => p.Submission)
                    .HasForeignKey<SubmissionResource>(d => d.SubmissionId);

                entity.HasOne(d => d.Application)
                    .WithMany(p => p.Submission)
                    .HasForeignKey(d => d.ApplicationId)
                    .HasConstraintName("FK_Submission_Application");
            });

            modelBuilder.EntityBase<SubmissionResource>(entity =>
            {
                entity.Property(e => e.RegulatoryLead).HasMaxLength(256);

                entity.Property(e => e.PublishingLead).HasMaxLength(256);

                entity.Property(e => e.InitialSentToEmail).HasMaxLength(256);

                entity.Property(e => e.EstimatedHours).HasColumnType("decimal(18, 2)");
            });

            modelBuilder.EntityBase<ActiveSubstanceProduct>(entity => 
            {
                entity.HasIndex(x => new { x.ProductId, x.ActiveSubstanceId })
                      .HasDatabaseName("UC_ActiveSubstanceProduct_ProductId_ActiveSubstanceId")
                      .IsUnique();

                entity.HasOne(d => d.ActiveSubstance)
                      .WithMany(p => p.ActiveSubstanceProduct)
                      .HasForeignKey(x => x.ActiveSubstanceId)
                      .HasConstraintName("FK_ActiveSubstanceProduct_ActiveSubstance");

                entity.HasOne(d => d.Product)
                      .WithMany(p => p.ActiveSubstanceProduct)
                      .HasForeignKey(x => x.ProductId)
                      .HasConstraintName("FK_ActiveSubstanceProduct_Product");
            });

            modelBuilder.EntityBase<RegulatoryAuthorityCountry>(entity =>
            {
                entity.HasIndex(x => new { x.CountryId, x.RegulatoryAuthorityId })
                      .HasDatabaseName("UC_RegulatoryAuthorityCountry_CountryId_RegulatoryAuthorityId")
                      .IsUnique();

                entity.HasOne(d => d.RegulatoryAuthority)
                      .WithMany(p => p.RegulatoryAuthorityCountry)
                      .HasForeignKey(x => x.RegulatoryAuthorityId)
                      .HasConstraintName("FK_RegulatoryAuthorityCountry_RegulatoryAuthority");

                entity.HasOne(d => d.Country)
                      .WithMany(p => p.RegulatoryAuthorityCountry)
                      .HasForeignKey(x => x.CountryId)
                      .HasConstraintName("FK_RegulatoryAuthorityCountry_Country");
            });

            modelBuilder.EntityBase<ApplicationCountry>(entity =>
            {
                entity.HasIndex(x => new { x.ApplicationId, x.CountryId })
                        .HasDatabaseName("UC_ApplicationCountry_ApplicationId_CountryId")
                        .IsUnique();

                entity.HasOne(d => d.Application)
                      .WithMany(p => p.ApplicationCountry)
                      .HasForeignKey(x => x.ApplicationId)
                      .HasConstraintName("FK_ApplicationCountry_Application");

                entity.HasOne(d => d.Country)
                      .WithMany(p => p.ApplicationCountry)
                      .HasForeignKey(x => x.CountryId)
                      .HasConstraintName("FK_ApplicationCountry_Country");
            });

            modelBuilder.EntityBase<SubmissionCountry>(entity =>
            {
                entity.HasIndex(x => new { x.SubmissionId, x.CountryId })
                      .HasDatabaseName("UC_SubmissionCountry_SubmissionId_CountryId")
                      .IsUnique();

                entity.HasOne(d => d.Submission)
                      .WithMany(p => p.SubmissionCountry)
                      .HasForeignKey(x => x.SubmissionId)
                      .HasConstraintName("FK_SubmissionCountry_Submission");

                entity.HasOne(d => d.Country)
                      .WithMany(p => p.SubmissionCountry)
                      .HasForeignKey(x => x.CountryId)
                      .HasConstraintName("FK_SubmissionCountry_Country");
            });

            modelBuilder.EntityBase<ApplicationProduct>(entity =>
            {
                entity.HasIndex(x => new { x.ApplicationId, x.ProductId })
                      .HasDatabaseName("UC_ApplicationProduct_ApplicationId_ProductId")
                      .IsUnique();

                entity.HasOne(d => d.Application)
                      .WithMany(p => p.ApplicationProduct)
                      .HasForeignKey(x => x.ApplicationId)
                      .HasConstraintName("FK_ApplicationProduct_Application");

                entity.HasOne(d => d.Product)
                      .WithMany(p => p.ApplicationProduct)
                      .HasForeignKey(x => x.ProductId)
                      .HasConstraintName("FK_ApplicationProduct_Product");
            });

            modelBuilder.EntityBase<PicklistDataCountry>(entity =>
            {
                entity.HasIndex(x => new { x.PicklistDataId, x.CountryId })
                      .HasDatabaseName("UC_PicklistDataCountry_PicklistDataId_CountryId")
                      .IsUnique();

                entity.HasOne(d => d.PicklistData)
                      .WithMany(p => p.PicklistDataCountry)
                      .HasForeignKey(x => x.PicklistDataId)
                      .HasConstraintName("FK_PicklistDataCountry_PicklistData");

                entity.HasOne(d => d.Country)
                      .WithMany(p => p.PicklistDataCountry)
                      .HasForeignKey(x => x.CountryId)
                      .HasConstraintName("FK_PicklistDataCountry_Product");
            });

            modelBuilder.EntityBase<UserClient>(entity =>
            {
                entity.HasKey(x => new { x.ClientId, x.UserId });

                entity.HasOne(d => d.Client)
                        .WithMany(p => p.UserClient)
                        .HasForeignKey(x => x.ClientId)
                        .HasConstraintName("FK_UserClient_Client");

                entity.HasOne(d => d.User)
                      .WithMany(p => p.UserClient)
                      .HasForeignKey(x => x.UserId)
                      .HasConstraintName("FK_UserClient_User");
            });

            modelBuilder.EntityBase<SubmissionPublisher>(entity =>
            {
                entity.HasIndex(x => new { x.SubmissionResourceId, x.PublisherId })
                      .HasDatabaseName("UC_SubmissionPublisher_SubmissionResourceId_PublisherId")
                      .IsUnique();

                entity.HasOne(d => d.SubmissionResource)
                      .WithMany(p => p.SubmissionPublisher)
                      .HasForeignKey(x => x.SubmissionResourceId)
                      .HasConstraintName("FK_SubmissionResource_SubmissionResource");

                entity.HasOne(d => d.Publisher)
                      .WithMany(p => p.SubmissionPublisher)
                      .HasForeignKey(x => x.PublisherId)
                      .HasConstraintName("FK_SubmissionPublisher_Publisher");
            });

            modelBuilder.EntityBase<Document>();

            modelBuilder.EntityBase<DocshifterDocumentFile>(entity =>
            {
                entity.HasOne(d => d.Document)
                      .WithMany(p => p.DocshifterDocumentFile)
                      .HasForeignKey(x => x.DocumentId)
                      .HasConstraintName("FK_Document_DocshifterDocumentFile");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);

        protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
        {
            configurationBuilder.Conventions.Add(_ => new BlankTriggerAddingConvention());
        }
    }
}
