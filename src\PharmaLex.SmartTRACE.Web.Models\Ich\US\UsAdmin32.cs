﻿using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.US
{
    public abstract class UsAdmin32 : EctdModule1Data
    {
        public abstract UsApplicantInformation Applicant { get; }
        public abstract UsApplicationInformation Application { get; }
    }

    public class UsApplicantInformation
    {
        public string CompanyName { get; set; }
        public string Date { get; set; }

        public string ProductName { get; set; }
    }
}
