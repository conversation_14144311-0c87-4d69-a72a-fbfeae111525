﻿<script type="text/x-template" id="workflow-progress-template">
    <ul id="workflow-progress" class="workflow-progress">
        <li :class="stepClass(1)" v-on:click="workflowStepClicked(1, 'EditSubmission')">Draft</li>
        <li class="workflow-arrow"></li>
        <li :class="stepClass(2)">Planned</li>
        <li class="workflow-arrow"></li>
        <li :class="stepClass(3)">In Progress</li>
        <li class="workflow-arrow"></li>
        <li :class="stepClass(4)">Ready for Publishing</li>
        <li class="workflow-arrow"></li>
        <li :class="stepClass(5)">QC Review</li>
        <li class="workflow-arrow"></li>
        <li :class="stepClass(6)">Approved for Submission</li>
        <li class="workflow-arrow"></li>
        <li :class="stepClass(7)">Submitted</li>
        <li class="workflow-arrow"></li>
        <li :class="stepClass(8)">Archived</li>
        <li class="workflow-arrow"></li>
        <li :class="stepClass(9)">HA Withdrawn</li>
        <li class="workflow-arrow" v-if="showStep"></li>
        <li :class="stepClass(10)" v-if="showStep">Obsolete</li>
    </ul>
</script>
<script type="text/javascript">
    vueApp.component('workflow-progress', {
        template: '#workflow-progress-template',
        props: {
            id: Number,
            currentStep: Number,
            finalSubmissionState: Number,
            lastActiveStep: Number,
            showStep: Boolean
        },
        methods: {
            stepClass(step) {
                if (step === this.currentStep) {
                    return 'workflow-step workflow-step-active';
                }

                if (step < this.currentStep && step <= this.lastActiveStep) {
                    return 'workflow-step workflow-step-done';
                }

                if (step < this.currentStep && step !== this.lastActiveStep && this.currentStep === 10) {
                    return 'workflow-step';
                }

                if (step === this.finalSubmissionState || step === this.currentStep) {
                    return 'workflow-step workflow-step-ready';            
                }

                return 'workflow-step';
            },
            workflowStepClicked(step, view) {
                if (step == this.currentStep) {
                    return;
                }
                if (step === 10) {
                    return;
                }
                if (step > this.currentStep) {
                    return;
                }
            }
        }
    });
</script>