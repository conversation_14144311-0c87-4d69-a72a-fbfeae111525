﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.SmartTRACE.Web.HealthCheck;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NSubstitute;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using System.Net;
using Microsoft.AspNetCore.TestHost;

namespace PharmaLex.SmartTRACE.WebTests.HealthCheck
{
    public class HealthCheckExtensionsTests
    {
        readonly IHost host;
        public HealthCheckExtensionsTests()
        {
            var configuration = TestHelpers.GetConfiguration();

            host = new HostBuilder().ConfigureWebHostDefaults(
                builder => builder
                .UseTestServer()
                .Configure(app => {
                    app.UseRouting();
                    app.UseEndpoints(endpoints =>
                    {
                        endpoints.MapCustomHealthChecks();
                    });
                })
                .ConfigureServices(
                    services => {
                        services.AddSingleton(configuration);
                        services.AddOptions();
                        services.AddLogging();
                        services.ConfigureHealthChecks(configuration);

                    })
            ).Build();

        }

        [Fact]
        public async Task Check_ConfigureServices()
        {
            var expectedJson = "{\"Status\":\"Healthy\",\"App\":\"testhost\",\"Version\":\"********\"}";
           
            using (host)
            {
                host.Start();
                var server = host.GetTestServer();
                var client = server.CreateClient();
                var response = await client.GetAsync("/api/health");
                var actualStatus = response.StatusCode;
                var actualContent = await response.Content.ReadAsStringAsync();
                Assert.Equal(HttpStatusCode.OK, actualStatus);
                Assert.Equal(expectedJson, actualContent);
            }
        }
    }
}
