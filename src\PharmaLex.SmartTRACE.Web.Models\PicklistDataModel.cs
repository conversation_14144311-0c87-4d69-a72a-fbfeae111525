﻿using AutoMapper;
using PharmaLex.SmartTRACE.Entities;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class PicklistDataModel: IModel
    {
        public PicklistDataModel()
        {
            this.CountriesIds = new List<int>();
        }
        public int Id { get; set; }

        [Required]
        [StringLength(256, ErrorMessage = "The Name field cannot exceed 256 characters.")]
        [RegularExpression(@"^[a-zA-Z0-9-_\s]*$", ErrorMessage = "Invalid characters in the Name field.")]
        public string Name { get; set; }

        public int PicklistTypeId { get; set; }

        public IList<int> CountriesIds { get; set; }

        public string Country { get; set; }
        public bool Selected { get; set; }
    }

    public class PicklistDataMappingProfile : Profile
    {
        public PicklistDataMappingProfile()
        {
            this.CreateMap<PicklistData, PicklistDataModel>()
             .ForMember(d => d.Country, s => s.Ignore());
            this.CreateMap<PicklistDataModel, PicklistData>();
            this.CreateMap<PicklistData, EditPicklistDataViewModel>()
              .ForMember(d => d.Picklist, s => s.MapFrom(x => x));
        }
    }
}
