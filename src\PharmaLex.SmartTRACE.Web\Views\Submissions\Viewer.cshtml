﻿@model SubmissionsModel
@using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@using PharmaLex.Authentication.B2C;
@{
    ViewData["Title"] = Model.Sequence;
    string storageKey = $"{this.User.GetEmail()}/{Model.Sequence}";
}
<div id="submissions" class="manage-container viewer" v-cloak>
    @Html.AntiForgeryToken()
    <header class="custom-header">
        <div>
            <h2>{{getSequenceTitle()}}</h2>
            <h2 v-if="details">{{getApplicationDetails()}}</h2>
        </div>
        <a class="button secondary icon-button-back" :href="getBackLink()">{{getBackButtonLabel()}}</a>
        <a class="button icon-button-delete" v-if="origin == 'eCTD'" v-on:click.stop="clear">Delete</a>
    </header>
    <div class="pane-container">
        <div class="loader" v-if="clearing">
            <img src="@Cdn.GetUrl("/images/loaders/spinner-75.svg")" alt="a double ring" />
        </div>
        <collapsible :title="'Sequence list'">
            <div class="submission-item" v-for="(s, i) in submissions">
                <input :id="s.value" type="checkbox" v-model="s.selected" v-on:change="onChange(s)" />
                <label :for="s.value">{{s.name.split('/')[s.name.split('/').length - 1]}}</label>
                <context-menu v-if="s.envelope && s.envelope.length">
                    <div v-on:click="onShowEnvelope(s)">Envelope</div>
                </context-menu>
            </div>
        </collapsible>
        <collapsible :title="'Tree view'">
            <treeview :items="tree" v-if="tree && tree.length"></treeview>
        </collapsible>
        <collapsible :title="'Selected content'">
            <template v-if="tree && tree.length && selected">
                <div v-if="!isRootModuleSelected" class="node">
                    <div style="cursor: pointer;" class="node-row" v-on:click.stop="onLevelUp">
                        <i class="icon-level-up"></i>
                        <div>[ ... ]</div>
                    </div>
                </div>
                <div class="node" v-for="s in selected.childNodes">
                    <div style="cursor: pointer;" :class="['node-row', {'changed': s.nodes.find(x => x.op !== 'New')}]" v-on:click.stop="onSelected(s)">
                        <i class="icon-folder"></i>
                        <div><span>{{s.name}}</span></div>
                        <context-menu v-if="getDocumentMenuItems(s)">
                            <div v-for="item in getDocumentMenuItems(s)" v-on:click="onFileMenuItemClicked(item, s)">{{item}}</div>
                        </context-menu>
                    </div>
                </div>
                <div class="node" v-for="s in selected.nodes">
                    <div :class="['node-row', `${s.op.toLowerCase()}`]">
                        <i class="icon-file"></i>
                        <div class="document" :title="s.href" v-on:click.stop="onOpenDocument(s)">
                            <span>{{s.text}} ({{s.submission.split('/')[s.submission.split('/').length - 1]}} {{s.op}})</span>
                        </div>
                        <context-menu v-if="getDocumentMenuItems(s)">
                            <div v-for="item in getDocumentMenuItems(s)" v-on:click="onFileMenuItemClicked(item, s)">{{item}}</div>
                        </context-menu>
                    </div>
                </div>
            </template>
        </collapsible>
    </div>
    <modal-dialog v-if="document"
                  v-on:close="onCloseDocument(true)"
                  :title="document.text"
                  :width="'80%'" :height="'80%'">
        <iframe class="pdf-viewer" :src="`/pdfjs/web/viewer.html?file=${document.path}`" v-on:load="documentModalLoaded($event)" title="document path"></iframe>
    </modal-dialog>
    <modal-dialog v-if="history && !document"
                  v-on:close="onCloseHistory"
                  :title="history.length === 1 ? history[0][0].text : 'Cumulative View'"
                  :width="'40%'" :height="'60%'">
        <div v-for="h in history" class="file-history">
            <div v-if="history.length > 1" class="title"><h6>{{h[0].text}}</h6></div>
            <div v-for="(hh, j) in h" :class="['file-history-item', {'selected': hh.selected}]">
                <i v-if="j" class="icon-down-large" style="padding-bottom: 5px;"></i>
                <div>
                    <div style="margin-bottom:10px;"><span>{{hh.text}}</span></div>
                    <span>Submission: {{hh.submission.split('/')[hh.submission.split('/').length - 1]}}</span>
                    <span>Operation: {{hh.op}}</span>
                    <div class="icons" v-if="hh.op !== 'Delete'">
                        <i class="icon-download" title="Download" v-on:click.stop="onFileMenuItemClicked('Download', hh)"></i>
                        <i class="icon-eye" title="View" v-on:click.stop="onOpenDocument(hh)"></i>
                    </div>
                </div>
            </div>
        </div>
    </modal-dialog>
    <modal-dialog v-if="envelope && envelope.region === 'EU'"
                  v-on:close="onCloseEnvelope"
                  :title="envelope.name"
                  :width="'50%'" :height="'80%'">
        <div class="envelope-header">
            <h3>{{envelope.region}} Module 1</h3>
            <div>DTD Version {{envelope.dtd}}</div>
        </div>
        <div v-for="env in envelope.envelope" class="envelope-item">
            <h4>Envelope for {{env.country}}</h4>
            <span>Identifier:</span>
            <span>{{env.identifier}}</span>
            <span>Submission:</span>
            <div class="envelope-detail">
                <span>Type:</span>
                <span>{{env.submission.type}}</span>
                <span>Mode:</span>
                <span>{{env.submission.mode}}</span>
            </div>
            <span v-if="env.procedureTrackingNumber || env.trackingNumber">Procedure Tracking Number(s):</span>
            <span v-if="env.procedureTrackingNumber">{{env.procedureTrackingNumber}}</span>
            <span v-if="env.trackingNumber">{{env.trackingNumber}}</span>
            <span>Submission Unit:</span>
            <div class="envelope-detail">
                <span>Type:</span>
                <span>{{env.submissionUnitType}}</span>
            </div>
            <span>Applicant:</span>
            <span>{{env.applicant}}</span>
            <span>Agency:</span>
            <span>{{env.agencyCode}}</span>
            <span>Procedure:</span>
            <span>{{env.procedure}}</span>
            <span>Invented Name:</span>
            <span>{{env.inventedName}}</span>
            <span>INN:</span>
            <span>{{env.inn}}</span>
            <span>Sequence:</span>
            <span>{{env.sequence}}</span>
            <span>Related Sequence:</span>
            <span>{{env.relatedSequence}}</span>
            <span>Submission Description:</span>
            <span>{{env.submissionDescription}}</span>
        </div>
    </modal-dialog>
    <modal-dialog v-if="envelope && (envelope.region === 'Europe (Non-EU)')"
                  v-on:close="onCloseEnvelope"
                  :title="envelope.name"
                  :width="'50%'" :height="'80%'">
        <div class="envelope-header">
            <h3>CH Module 1</h3>
            <div>DTD Version {{envelope.dtd}}</div>
        </div>
        <div v-for="env in envelope.envelope" class="envelope-item">
            <span>Application Number:</span>
            <span>{{env.applicationNumber.join(',')}}</span>
            <span>Submission Description:</span>
            <span>{{env.submissionDescription}}</span>
            <span>Invented Name:</span>
            <span>{{env.inventedName.join(',')}}</span>
            <span>Galenic Form Name:</span>
            <div>
                <div v-for="galenicForm in env.galenicForm">
                    <span>{{env.galenicForm[0].name}}</span><br />
                    <span>Swissmedic Number:</span><span>&emsp;{{env.galenicForm[0].swissMedicNumber}}</span><br />
                    <span>Galenic Name:</span><span>&emsp;{{ env.galenicForm[0].galenicNameLanguage | country }}: {{env.galenicForm[0].galenicNameValue}}</span>
                </div>
            </div>
            <span>DMF Number:</span>
            <span>{{env.dmfNumber}}</span>
            <span>PMF Number:</span>
            <span>{{env.pmfNumber}}</span>
            <span>INN:</span>
            <span>{{env.inn.join(',')}}</span>
            <span>Applicant:</span>
            <span>{{env.applicant}}</span>
            <span>DMF Holder:</span>
            <span>{{env.dmfHolder}}</span>
            <span>PMF Holder:</span>
            <span>{{env.pmfHolder}}</span>
            <span>Agency:</span>
            <span>{{env.agency}}</span>
            <span>Application:</span>
            <span>{{env.applicant}}</span>
            <span>Article 13 TPA:</span>
            <span>{{env.paragraph13tpa}}</span>
            <span>Sequence:</span>
            <span>{{env.ectdsequence}}</span>
            <span>Related Sequence:</span>
            <span>{{env.relatedectDSequence.join(',')}}</span>
        </div>
    </modal-dialog>
    <modal-dialog v-if="envelope && envelope.region === 'US'"
                  v-on:close="onCloseEnvelope"
                  :title="envelope.name"
                  :width="'50%'" :height="'80%'">
        <div class="envelope-header">
            <h3>{{envelope.region}} Module 1</h3>
            <div>DTD Version {{envelope.dtd}}</div>
        </div>
        <div v-for="env in envelope.envelope" class="envelope-item us">
            <div v-if="env.summary">
                <span>Summary</span>
            </div>
            <div class="envelope-detail us" v-if="env.summary">
                <span>Applicant Name:</span>
                <span>{{env.summary.applicantName}}</span>
                <span>Applicant Id:</span>
                <span>{{env.summary.applicantId}}</span>
                <span>Description:</span>
                <span>{{env.summary.description}}</span>
            </div>
            <div v-if=env.contacts>
                <span>Contact Information</span>
            </div>
            <div v-if=env.contacts>
                <div v-for="c in env.contacts" class="envelope-detail us">
                    <span>Applicant Contact:</span>
                    <span>{{c.name}}</span>
                    <span>Contact Info:</span>
                    <span>{{c.phone}}</span>
                    <span>E-mail:</span>
                    <span>{{c.email}}</span>
                </div>
            </div>
            <div>
                <span>Application Information</span>
            </div>
            <div class="envelope-detail us">
                <span>Application Containing Files:</span>
                <span>{{env.application.applicationContainingFiles}}</span>
                <span>Application Type:</span>
                <span>{{env.application.applicationType | applicationType}}</span>
                <span>Application Number:</span>
                <span>{{env.application.applicationNumber}}</span>
                <span>Submission Type:</span>
                <span>{{env.application.submissionType | submissionType}}</span>
                <span>Submission Id:</span>
                <span>{{env.application.submissionId}}</span>
                <span>Submission Sub-Type:</span>
                <span>{{env.application.submissionSubType | submissionSubType}}</span>
                <span>Sequence number:</span>
                <span>{{env.application.sequenceNumber}}</span>
            </div>
            <div v-if="env.applicant">
                <span>Applicant</span>
            </div>
            <div class="envelope-detail us" v-if="env.applicant">
                <span>Company Name:</span>
                <span>{{env.applicant.companyName}}</span>
                <span>Date:</span>
                <span>{{env.applicant.date}}</span>
                <span>Product Name:</span>
                <span>{{env.applicant.productName}}</span>
            </div>
        </div>
    </modal-dialog>
    <modal-dialog v-if="envelope && (envelope.region === 'AU')"
                  v-on:close="onCloseEnvelope"
                  :title="envelope.name"
                  :width="'50%'" :height="'80%'">
        <div class="envelope-header">
            <h3>AU Module 1</h3>
            <div>DTD Version {{envelope.dtd}}</div>
        </div>
        <div v-for="env in envelope.envelope" class="envelope-item">
            <span>eSubmission Identifier:</span>
            <span>{{env.esubId}}</span>
            <span>Client Id:</span>
            <span>{{env.clientId}}</span>
            <span>Australian Approved Name(s):</span>
            <span>{{env.aan.join(',')}}</span>
            <span>Product Name:</span>
            <span>{{env.productName.join(',')}}</span>
            <span>ARTG Number:</span>
            <span>{{env.artgNumber.join(',')}}</span>
            <span>Submission Number:</span>
            <span>{{env.submissionNumber.join(',')}}</span>
            <span>Sequence Number:</span>
            <span>{{env.sequenceNumber}}</span>
            <span>Related Sequence Number:</span>
            <span>{{env.relatedSequenceNumber}}</span>
            <span>Regulatory Activity Lead:</span>
            <div>
                <span>Code:</span>
                <span>{{env.regActivityLead.code}}</span><br />
                <span>Code Version:</span>
                <span>{{env.regActivityLead.codeVersion}}</span>
            </div>
            <span>Sequence Type:</span>
            <div>
                <div v-for="sequence in env.sequenceType">
                    <span>Code:</span>
                    <span>&emsp;{{sequence.code}}</span><br />
                    <span>Code Version:</span>
                    <span>&emsp;{{ sequence.codeVersion}}</span><br />
                    <span>Sequence Description Code:</span>
                    <span>&emsp;{{sequence.sequenceDescriptionCode}}</span><br />
                    <span>Sequence Description Code Version:</span>
                    <span>&emsp;{{sequence.sequenceDescriptionCodeVersion}}</span>
                </div>
            </div>
            <template v-if="env.workSharing">
                <div v-for="sharing in env.workSharing">
                    <span>Esub Id:</span>
                    <span>&emsp;{{sharing.esubId}}</span><br />
                    <span>Submission Number:</span>
                    <span>&emsp;{{ sharing.submissionNumber.join(',')}}</span><br />
                    <span>Sequence Number:</span>
                    <span>&emsp;{{sharing.sequenceNumber}}</span>
                </div>
            </template>
            <span>Submission Mode:</span>
            <span>{{env.submissionMode}}</span>
            <span>Email:</span>
            <span>{{env.email.join(',')}}</span>
        </div>
    </modal-dialog>
    <form id="downloadForm" style="display: none;" method="get" action=""></form>
</div>

@section VueComponentScripts {
    <script>
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        vueApp.component('collapsible', {
                template: '#collapsible-template',
                props: {
                    title: {
                        type: String,
                    },
                    state: {
                        type: String,
                        default: 'open'
                    },
                    orientation: {
                        type: String,
                        default: 'vertical'
                    }
                },
                computed: {
                    style() {
                        return `flex-direction: ${this.orientation === 'vertical' ? 'column' : 'row'};`;
                    }
                }
            });
            vueApp.component('modal-dialog', {
                template: '#modal-dialog-template',
                props: {
                    title: {
                        type: String,
                    },
                    width: {
                        type: String,
                        default: '40%'
                    },
                    height: {
                        type: String,
                        default: '60%'
                    }
                }
            });
            vueApp.component('context-menu', {
                template: '#context-menu-template',
                data() {
                    return {
                        open: false,
                        timeout: null
                    };
                },
                methods: {
                    toggleState(isOpen) {
                        if (this.timeout && this.open === isOpen) return clearTimeout(this.timeout);
                        this.timeout = setTimeout(() => {
                            this.open = isOpen
                            this.timeout = null;
                        }, 300);
                    }
                },
                props: {
                    icon: {
                        default: 'icon-cog'
                    }
                }
            });
            vueApp.component('treeview-node', {
                template: '#treeview-node-template',
                data() {
                    return {
                        node: this.item,
                        timeout: null
                    };
                },
                props: {
                    item: {
                        type: Object,
                        required: true
                    }
                },
                computed: {
                    isOpen() { return this.$store.getters.isOpen(this.item) },
                    isSelected() { return this.$store.getters.isSelected(this.item) },
                    menuItems() {
                        const items = this.item.childNodes.length ? ['Expand All', 'Collapse All'] : [];

                        if (this.item.nodes.length)
                            items.push('Cumulative View');

                        return items;
                    },
                    hasChanges() {
                        return this.item.nodes.find(x => x.op !== 'New');
                    }
                },
                methods: {
                    onSelected() {
                        this.$store.commit('selected', this.item);
                    },
                    onMenuItemClicked(menuItem) {
                        const findAllExpandableChildren = (nodes, expandables) => {
                            return nodes.reduce((acc, node) => {
                                if (node.childNodes.length) {
                                    acc.push(node);

                                    return findAllExpandableChildren(node.childNodes, acc);
                                }

                                return acc;
                            }, expandables || []);
                        };
                        const findAllChildDocuments = (nodes, documents) => {
                            return nodes.reduce((acc, node) => {
                                if (node.nodes.length) {
                                    node.nodes.forEach(x => acc.push(x));
                                }
                                if (node.childNodes.length) {
                                    return findAllChildDocuments(node.childNodes, acc);
                                }

                                return acc;
                            }, documents || []);
                        };
                        switch (menuItem) {
                            case 'Expand All':
                                this.$store.commit('addOpen', findAllExpandableChildren([this.item]));
                                break;
                            case 'Collapse All':
                                this.$store.commit('removeOpen', findAllExpandableChildren([this.item]));
                                break;
                            case 'Cumulative View':
                                this.$store.dispatch('showHistory', findAllChildDocuments([this.item]));
                                break;
                        }
                    },
                    onOpen() {
                        if (!this.$store.getters.isOpen(this.item)) {
                            let findInTree = (item, nodes) => {
                                let found = false;
                                return nodes.reduce((acc, n) => {
                                    if (found) return acc;

                                    if (n === item) {
                                        acc.push(item);
                                        found = true;
                                        return acc;
                                    }

                                    if (acc.length) {
                                        found = true;
                                        acc.push(n);
                                    } else {
                                        acc = findInTree(item, n.childNodes);
                                    }

                                    return acc;
                                }, []);
                            };

                            this.$store.commit('addOpen', findInTree(this.item, this.$store.state.tree));
                        } else {
                            this.$store.commit('removeOpen', [this.item]);
                        }
                    }
                }
            });
            vueApp.component('treeview', {
                template: '#treeview-template',
                data() {
                    return {
                        nodes: this.items
                    };
                },
                props: {
                    items: {
                        type: Array,
                        required: true
                    }
                }
            });
    </script>
}

@section Scripts {
    <script src="@Cdn.GetUrl("js/plx.js")"></script>
    <script src="@Cdn.GetUrl("js/toast.js")"></script>  
    <script type="text/x-template" id="modal-dialog-template">
        <div class="dialog-surface" v-on:click="$emit('close')">
            <div class="dialog-container modal-dialog-container" v-on:click.stop :style="'width: '+width+'; height: '+ height+';'">
                <div class="dialog-content modal-dialog-content">
                    <h5>{{title}}</h5>
                    <i class="icon-cancel-circled dialog-closer" v-on:click="$emit('close')"></i>
                    <div>
                        <slot></slot>
                    </div>
                </div>
            </div>
        </div>
    </script>
    <script type="text/x-template" id="treeview-node-template">
        <div class="node">
            <div :class="['node-row', {'selected': isSelected, 'changed': hasChanges}]" v-on:click.stop="onSelected">
                <div>
                    <i v-if="node.childNodes.length" :class="[{ 'icon-right-dir': !isOpen, 'icon-down-dir': isOpen }]" v-on:click.stop="onOpen"></i>
                </div>
                <i :class="[{ 'icon-folder': !isOpen, 'icon-folder-open': isOpen }]"></i>
                <div>
                    <span>{{node.name}}</span>
                    <context-menu v-if="menuItems.length">
                        <div v-for="item in menuItems" v-on:click="onMenuItemClicked(item)">{{item}}</div>
                    </context-menu>
                </div>
            </div>
            <div class="node-children" v-if="isOpen">
                <treeview-node v-for="(child, i) in node.childNodes" :item="child" :key="node.id + '.' + child.id + '.' + i"></treeview-node>
            </div>
        </div>
    </script>
    <script type="text/x-template" id="treeview-template">
        <div class="treeview">
            <treeview-node v-for="(child, i) in nodes" v-model:item="child" :key="'M' + i"></treeview-node>
        </div>
    </script>
    <script type="text/x-template" id="context-menu-template">
        <div class="menu-container" v-on:click.stop="open && toggleState(false)" v-on:mouseleave="toggleState(false)">
            <i :class="['menu-icon', icon]" v-on:mouseenter="toggleState(true)"></i>
            <div v-if="open" class="menu">
                <div class="context">
                    <slot></slot>
                </div>
            </div>
        </div>
    </script>
    <script type="text/x-template" id="collapsible-template">
        <section class="collapsible">
            <header>{{title}}</header>
            <div class="collapsible-container" :style="style">
                <slot></slot>
            </div>
        </section>
    </script>

    <script type="text/javascript">
        var store = Vuex.createStore({
            state: {
                    envelope: null,
                    showingAll: true,
                    storageKey: '@storageKey',
                    submissions: [],
                    sequence: null,
                    timeout: null,
                    tree: [],
                    open: [],
                    selected: null,
                    clearing: false,
                    document: null,
                    history: null,
                    recursive: @Html.Raw(JsonConvert.SerializeObject(Model.Recursive)),
                    origin: @Html.Raw(JsonConvert.SerializeObject(Model.Origin)),
                    clientId: @Html.Raw(JsonConvert.SerializeObject(Model.ClientId, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })),
                    applicationId: @Html.Raw(JsonConvert.SerializeObject(Model.ApplicationId)),
                    submissionId: @Html.Raw(JsonConvert.SerializeObject(Model.SubmissionId)),
                    details: @Html.Raw(JsonConvert.SerializeObject(Model.SubmissionDetails)),
                    documentModalHistory: [],
                    iframeInternalLinkHistory: {}
                },
                mutations: {
                    envelope(state, envelope) {
                        state.envelope = envelope;
                    },
                    showingAll(state, all) {
                        state.showingAll = all;
                    },
                    document(state, document) {
                        state.document = document;
                    },
                    documentText(state, documentText) {
                        state.document = {
                            ...state.document,
                            text: documentText
                        };
                    },
                    history(state, history) {
                        state.history = history
                    },
                    clearing(state, clearing) {
                        state.clearing = clearing;
                    },
                    timeout(state, timeout) {
                        state.timeout = timeout;
                    },
                    clearTimeout(state) {
                        clearTimeout(state.timeout);
                        state.timeout = null;
                    },
                    submissions(state, submissions) {
                        state.submissions = [...submissions];
                    },
                    sequence(state, sequence) {
                        state.sequence = sequence;
                    },
                    tree(state, modules) {
                        var comparer = (a, b) => {
                            if (a.name.startsWith('M') && b.name.startsWith('M'))
                                return a.name > b.name ? 1 : -1;

                            if (!a.name.startsWith('M') && !b.name.startsWith('M'))
                                return a.name > b.name ? 1 : -1;

                            return a.name.startsWith('M') ? -1 : 1;
                        };

                        state.tree = [...modules].sort(comparer);
                    },
                    addOpen(state, open) {
                        state.open = [...new Set([...state.open, ...open])]
                    },
                    removeOpen(state, collapsed) {
                        let tmp = [...state.open];
                        collapsed.forEach(c => tmp.splice(tmp.indexOf(c), 1));
                        state.open = tmp;
                    },
                    clearOpen(state) {
                        state.open = Vue.ref([]);
                    },
                    selected(state, selected) {
                        state.selected = selected;
                    },
                    selectSubmission(state, submission) {
                        let tmp = [...state.submissions];

                        if (submission.value === 'All') {
                            tmp.forEach((s, i) => {
                                if (i > 0) s.selected = false;
                            });
                        } else {
                            tmp[0].selected = false;
                        }

                        return state.submissions = tmp;
                    },
                    addToDocumentModalHistory(state, document) {
                        if (!state.documentModalHistory.map(x => x.id).includes(document.id)) {
                            state.documentModalHistory.push(document);
                        }
                    },
                    removeLastDocumentFromModalHistory(state) {
                        state.documentModalHistory.pop();
                        if (state.documentModalHistory.length != 0) {
                            state.document = state.documentModalHistory[state.documentModalHistory.length - 1];
                        }
                    },
                    clearDocumentModalHistory(state, document) {
                        state.documentModalHistory = Vue.ref([]);
                    },
                    addToIframeInternalLinkHistory(state, history) {
                        state.iframeInternalLinkHistory[history.file] = history.pageNumber;
                    },
                    clearIframeInternalLinkHistory(state) {
                        state.iframeInternalLinkHistory = {};
                    }

                }, 
                getters: {
                    selectedSubmissions: state => state.submissions.filter(x => x.selected).map(x => x.value),
                    isOpen: state => item => state.open.includes(item),
                    isSelected: state => item => state.selected ? state.selected === item : false,
                    framesHistoryLength: (state) => {
                        var historyLength = state.documentModalHistory.length;

                        var internalLinksClicked = 0;
                        for (var key in state.iframeInternalLinkHistory) {
                            if (state.iframeInternalLinkHistory[key])
                                internalLinksClicked++;
                        }

                        return historyLength + internalLinksClicked;
                    }
                },
                actions: {
                    initialize({ commit, dispatch }) {
                        let all = { name: 'Current', selected: true, value: 'All' };
                        let model = @Html.Raw(JsonConvert.SerializeObject(Model));

                        if (model.error) {
                            plx.toast.show(model.error, 2, 'failed', null, 20000);
                        } else {
                            let sequences = model.tree[0].envelope.map(s => {
                                return {
                                    value: s.submission,
                                    name: s.display,
                                    selected: false,
                                    dtd: s.dtdVersion,
                                    region: s.region,
                                    envelope: s.list
                                };
                            });

                            if (model.origin == 'Submission') {
                                commit('submissions', sequences);
                            }
                            else {
                                commit('submissions', [all, ...sequences]);
                            }
                        }

                        commit('sequence', model.sequence);

                        if (model.tree && model.tree.length) {
                            commit('tree', model.tree);
                        }

                        dispatch('storeDocuments', model.tree);
                    },
                    load({ commit, getters }) {
                        let url = `/submissions/tree/${store.state.sequence}?${getters.selectedSubmissions.reduce((acc, v) => { acc += `submissions=${v}&`; return acc }, '')}&recursive=${store.state.recursive}`;
                        if (store.state.applicationId && store.state.clientId) {
                            url = url.concat(`&clientId=${store.state.clientId}&applicationId=${store.state.applicationId}`);
                        }
                        fetch(url, {
                            method: 'GET',
                            credentials: 'same-origin',
                            headers: {
                                "Content-Type": "application/json"
                            }
                        }).then(r => r.json())
                            .then(tree => {
                                commit('tree', tree);
                                commit('showingAll', !getters.selectedSubmissions.length || getters.selectedSubmissions.length == store.state.submissions.length)
                                commit('clearing', false);
                            })
                            .catch(error => {
                                console.log(error);
                            });
                    },
                    submissionChange({ commit, dispatch }, submission) {
                        commit('clearing', true);
                        commit('selectSubmission', submission);
                        commit('clearTimeout');
                        commit('clearOpen');
                        commit('selected', null);
                        commit('tree', []);
                        commit('timeout', setTimeout(() => {
                            dispatch('load');
                        }, 1000));
                    },
                    clear() {
                        store.commit('clearing', true);
                        fetch(`/submissions/clear/${store.state.sequence}`, {
                            method: 'DELETE',
                            credentials: 'same-origin',
                            headers: {
                                "Content-Type": "application/json",
                                'RequestVerificationToken': token
                            }
                        }).then(_ => {
                            plx.toast.show(`Sequence cleared`, 2, 'confirm', 3000);
                            setTimeout(() => {
                                store.commit('clearing', false);
                                document.location.href = `/submissions`;
                            }, 2000);
                        })
                            .catch(error => {
                                console.log(error);
                            });
                    },
                    storeDocuments(_, tree) {
                        const walkTree = (nodes, documents) => {
                            return nodes.reduce((acc, node) => {
                                if (node.nodes.length) {
                                    node.nodes.forEach(x => acc.push(x));
                                }

                                return walkTree(node.childNodes, acc);
                            }, documents || []);
                        };

                        const allDocuments = walkTree(tree);

                        localStorage.setItem(store.state.storageKey, JSON.stringify(allDocuments));
                    },
                    showHistory({ commit }, history) {
                        history = Array.isArray(history) ? history : [history];

                        if (store.state.showingAll) {
                            history.forEach(x => x.selected = true);
                        } else {
                            const fullHistoryFiles = JSON.parse(localStorage.getItem(store.state.storageKey));

                            history = history.reduce((acc, h) => {
                                let found = fullHistoryFiles.find(x => x.href === h.href);
                                if (found) {
                                    found.selected = true;
                                } else {
                                    found = fullHistoryFiles.find(x => {
                                        let historyFound = x.historical.find(y => y.href === h.href);
                                        if (historyFound) {
                                            historyFound.selected = true;
                                        }

                                        return historyFound;
                                    });
                                }

                                acc.push(found);
                                return acc;
                            }, []);
                        }

                        history = history.map(x => {
                            return [x, x.historical].flat();
                        });

                        commit('history', history);
                    }
                }});


        var pageConfig = {
            appElement: '#submissions',
            data() {
                return {
                    timeout: null
                };
            },
            computed: {
                isRootModuleSelected() {
                    return store.state.tree.includes(store.state.selected);
                },
                ...Vuex.mapState([
                    'tree',
                    'selected',
                    'submissions',
                    'sequence',
                    'clearing',
                    'document',
                    'history',
                    'envelope',
                    'details',
                    'origin'
                ])
            },
            methods: {
                onChange(submission) {
                    store.dispatch('submissionChange', submission);
                },
                onSelected(item) {
                    store.commit('addOpen', [store.state.selected]);
                    store.commit('selected', item);
                },
                onSequenceSelected(sequence) {
                    window.location.href = 'submissions/' + sequence;
                },
                onOpenDocument(document, pageNumber) {
                    if (document.op === 'Delete') return;

                    if (document.submission.toLowerCase().endsWith('-workingdocuments')) {

                        if (document.href.toLowerCase().endsWith('.docx') || document.href.toLowerCase().endsWith('.doc')) {

                            let route = `/documents/download/${document.href}`;
                            this.downloadDocument(route);
                            return;
                        }

                        if (document.href.includes(store.state.sequence))
                            document.href = document.href.substring(document.href.indexOf(store.state.sequence) + store.state.sequence.length + 1);
                    }

                    if (document.href.substring(document.href.lastIndexOf('.')).toLowerCase() !== '.pdf') return;

                    var pageSection = '';
                    if (pageNumber) {
                        pageSection = `#page=${++pageNumber}&v=${new Date().getTime()}`;
                    }
                    else {
                        `#v=${new Date().getTime()}`;
                    }

                    document.path = `/submissions/${store.state.sequence}/download/${document.href}${pageSection}`;

                    if (store.state.clientId && store.state.applicationId) {
                        if (store.state.sequence.toLowerCase() !== "current" && !document.href.includes(store.state.sequence)) {
                            document.href = `${store.state.sequence}/${document.href}`;
                        }
                        document.path = `/submissions/download/${store.state.clientId}/${store.state.applicationId}/${document.href}${pageSection}`;
                    }

                    window.location.hash = 'modalState=open';
                    store.commit('document', document);
                },
                onCloseDocument(closeAllDocuments) {
                    if (window.location.hash) {
                        if (closeAllDocuments) {
                            store.commit('clearIframeInternalLinkHistory');

                            let totalHistoryLengths = store.getters.framesHistoryLength;
                            window.frames[0].history.go(-totalHistoryLengths);

                            store.commit('clearDocumentModalHistory');
                        } else {
                            // close curent document and load previous one
                            this.$store.commit('removeLastDocumentFromModalHistory');
                            window.history.back();
                        }
                    }
                    else {
                        // close the document and clear the history
                        store.commit('clearDocumentModalHistory');
                        store.commit('document', null);
                        store.commit('clearIframeInternalLinkHistory');
                    }
                },
                onCloseHistory() {
                    store.commit('history', null);
                },
                onCloseEnvelope() {
                    store.commit('envelope', null);
                },
                onFileMenuItemClicked(menuItem, file) {
                    switch (menuItem) {
                        case 'Download':
                            let route = `/submissions/${store.state.sequence}/download/${file.href}`;

                            if (file.submission.toLowerCase().endsWith('-workingdocuments')) {
                                if (file.href.endsWith('.docx') || file.href.endsWith('.doc')) {
                                    route = `/documents/download/${file.href}`;
                                }

                                if (file.href.includes(store.state.sequence)) {
                                    file.href = file.href.substring(file.href.indexOf(store.state.sequence) + store.state.sequence.length + 1);
                                    route = `/submissions/${store.state.sequence}/download/${file.href}`;
                                }

                            } else if (store.state.clientId && store.state.applicationId) {
                                let fileName = file.href;

                                if (store.state.sequence != "Current" && !fileName.startsWith(store.state.sequence)) {
                                    fileName = `${store.state.sequence}/${fileName}`;
                                }

                                route = `/submissions/download/${store.state.clientId}/${store.state.applicationId}/${fileName}`;
                            }

                            this.downloadDocument(route);
                            break;
                        case 'History':
                            store.dispatch('showHistory', file);
                            break;
                        case 'Cumulative View':
                            {
                                const findAllChildDocuments = (nodes, documents) => {
                                    return nodes.reduce((acc, node) => {
                                        if (node.nodes.length) {
                                            node.nodes.forEach(x => acc.push(x));
                                        }
                                        if (node.childNodes.length) {
                                            return findAllChildDocuments(node.childNodes, acc);
                                        }

                                        return acc;
                                    }, documents || []);
                                };

                                this.$store.dispatch('showHistory', findAllChildDocuments([file]));
                                break;
                            }
                    }
                },
                getDocumentMenuItems(document) {
                    if (document.nodes && document.nodes.length)
                        return ['Cumulative View'];

                    if (document.op) {
                        if (document.op === 'Delete')
                            return ['History'];

                        if (document.submission.endsWith('-workingdocuments')) {
                            if (document.href.endsWith('pdf'))
                                return ['Download'];
                            else if (document.href.endsWith('docx') || document.href.endsWith('doc'))
                                return null;
                        }

                        return ['Download', 'History'];
                    }
                },
                onLevelUp() {
                    const findParent = (items) => {
                        return items.reduce((acc, item) => {
                            if (acc) return acc;

                            if (item.childNodes.find(x => x === store.state.selected)) {
                                acc = item;
                            } else {
                                return findParent(item.childNodes);
                            }

                            return acc;
                        }, null);
                    };

                    store.commit('selected', findParent(this.tree));
                },
                getBackLink() {
                    if (store.state.origin == 'eCTD') {
                        return '/submissions';
                    }

                    if (store.state.origin == 'Application') {
                        return `/applications/view/${store.state.applicationId}`;
                    }

                    if (store.state.origin == 'Submission') {
                        var uniqueId = +store.state.submissionId.replace("SUB", "");
                        console.log(uniqueId);
                        return `/submissions/view/${uniqueId}`;
                    }
                },
                getBackButtonLabel() {
                    if (store.state.origin == 'eCTD') {
                        return "Back";
                    }

                    return `Back to ${store.state.origin}`;
                },
                getSequenceTitle() {
                    if (store.state.origin == 'eCTD') {
                        return store.state.sequence;
                    }

                    if (store.state.origin == 'Submission') {
                        return `${store.state.details.product}, ${store.state.clientId}`;
                    }
                },
                getApplicationDetails() {
                    return `${store.state.details.applicationType}, ${store.state.details.applicationNumber}, ${store.state.details.procedureType}`
                },
                ...Vuex.mapActions(['clear']),
                ...Vuex.mapMutations({
                    onShowEnvelope: 'envelope'
                }),
                interLinkClicked(evt) {
                    if (evt.data.prevPage) {
                        store.commit('addToIframeInternalLinkHistory', { file: evt.data.file, pageNumber: evt.data.prevPage });
                        return;
                    }

                    var url = evt.data.unsafeurl;
                    
                    if(url){
                        var filePath = this.buildDocumentFilePath(url);
                        var pageNumber = url.replace(/(\.\.\/)*(\w[\w\W\/.-]+)#\[(\d*),[\w\W]+/g, '$3');
                        var selectedSubmissionsNames = this.$store.state.submissions.filter(s => s.selected).map(s => s.name);

                        var document = this.findDocument(filePath, selectedSubmissionsNames);
                        if (document) {
                            this.onOpenDocument(document, pageNumber);
                        }
                        else {
                            plx.toast.show('Document could not be found in the selected sequence', 2, 'failed', null, 20000);
                        }
                    }                    
                },
                findDocument(filePath, submissions) {

                    const fullHistoryFiles = JSON.parse(localStorage.getItem(store.state.storageKey));
                    let fileHistory = [filePath].reduce((acc, h) => {
                        let found = fullHistoryFiles.find(x => x.href.endsWith(h));
                        if (!found) {
                            let historyFound = fullHistoryFiles.find(x => x.historical.find(y => y.href.endsWith(h)));
                            if (historyFound) {
                                found = historyFound.historical.find(y => y.href.endsWith(h));
                            }
                        }

                        acc.push(found);
                        return acc;
                    }, []);

                    if (fileHistory.length == 0)
                        return;

                    // when 'current' sequence is selected, look for the last version of the file
                    if (submissions.length == 0 || (submissions.length == 1 && submissions[0].toLowerCase() == 'current')) {
                        return fileHistory[fileHistory.length - 1];
                    }

                    var isFilePathRelativeToModule = /^m\d\//.test(filePath);
                    // search for the file in the selected sequences
                    for (var i = submissions.length - 1; i >= 0; i--) {

                        let fullFilePath = filePath;

                        if (isFilePathRelativeToModule)
                            fullFilePath = `${submissions[i]}/${filePath}`;

                        if (!fullFilePath.startsWith(submissions[i]))
                            continue;

                        let file = fileHistory.find(f => f.href == filePath);
                        if (file)
                            return file;
                    }
                },
                buildDocumentFilePath(url) {
                    var filePath = url.replace(/(\.\.\/)*(\w[\w\W\/.-]+)#[\w\W]+/g, '$2');

                    var isFilePathRelativeToModule = /^m\d\//.test(filePath);
                    var isFilePathRelativeToSequence = /\d{4}\/m\d\//.test(filePath);

                    if (isFilePathRelativeToModule || isFilePathRelativeToSequence)
                        return filePath;

                    var currentDocument = this.$store.state.document.href;

                    //the filepath is relative to the current file directory
                    var directoryLevelUp = (url.match(/\.\.\//g) || []).length;
                    var directories = currentDocument.split('/');
                    var newDirectoryStructure = currentDocument.split('/', directories.length - (directoryLevelUp + 1));

                    if (store.state.clientId && store.state.applicationId) {
                        newDirectoryStructure.shift();
                    }

                    filePath = newDirectoryStructure.concat(filePath.split('/')).join('/');

                    return filePath;
                },
                downloadDocument(documentUrl) {
                    const form = window.document.getElementById('downloadForm');
                    form.action = documentUrl;
                    form.submit();
                },
                historyChanged(event) {

                    if (window.frames.length > 0) {
                        let pdfFrame = window.frames[0];

                        let file = pdfFrame.location.search;
                        let pdfFrameHash = new URLSearchParams(pdfFrame.location.hash.replace('#', ''));
                        if (pdfFrameHash.has('page')) {
                            let previousPageNumber = +(pdfFrameHash.get('page'));
                            if (previousPageNumber != window.frames[0].PDFViewerApplication.page && this.$store.state.iframeInternalLinkHistory[file]) {
                                //check if internal link has been clicked. In this case add additional item in the browser history.
                                let previousPageFromHistory = this.$store.state.iframeInternalLinkHistory[file];
                                pdfFrame.PDFViewerApplication.page = previousPageFromHistory;

                                this.$store.state.iframeInternalLinkHistory[file] = null;
                                window.history.pushState({}, null, window.location);
                                return;
                            }
                        }
                    }

                    var openDocument = this.$store.state.document;
                    var hash = window.location.hash

                    if (openDocument && !hash) {
                        this.onCloseDocument();
                    }
                },
                documentModalLoaded(event) {
                    var documentModalHistory = this.$store.state.documentModalHistory;

                    var queryStringParms = new URLSearchParams(window.frames[0].location.search);
                    var filePath = queryStringParms.get('file');

                    if (documentModalHistory.length > 1) {
                        var prevDocument = documentModalHistory[documentModalHistory.length - 2];
                        if (prevDocument.path == filePath) {
                            this.$store.commit('removeLastDocumentFromModalHistory');

                            // if the document is readded into state, it will trigger adding new item into the history
                            this.$store.commit('documentText', prevDocument.text);
                            return;
                        }
                    }

                    var currentDocument = this.$store.state.document;
                    this.$store.commit('addToDocumentModalHistory', currentDocument);
                }
            },
            filters: {
                country: function (value) {
                    switch (value) {
                        case 'de': return 'German';
                        case 'fr': return 'French';
                        case 'it': return 'Italian';
                        default: return value;
                    }
                },
                submissionType: function (value) {
                    switch (value) {
                        case 'fdast1': return 'Original Application';
                        case 'fdast2': return 'Efficacy Supplement';
                        case 'fdast3': return 'Chemistry Manufacturing Controls Supplement';
                        case 'fdast4': return 'Labeling Supplement';
                        case 'fdast5': return 'Annual Report';
                        case 'fdast6': return 'Product Correspondence';
                        case 'fdast7': return 'Postmarketing Requirements or Postmarketing Commitments';
                        case 'fdast8': return 'Promotional Labeling Advertising';
                        case 'fdast9': return 'IND Safety Reports';
                        case 'fdast10': return 'Periodic Safety Reports';
                        case 'fdast11': return 'REMS Supplement';
                        default: return value;
                    }
                },
                submissionSubType: function (value) {
                    switch (value) {
                        case 'fdasst1': return 'Original';
                        case 'fdasst2': return 'Presubmission';
                        case 'fdasst3': return 'Application';
                        case 'fdasst4': return 'Amendment';
                        case 'fdasst5': return 'Resubmission';
                        case 'fdasst6': return 'Report';
                        case 'fdasst7': return 'Correspondence';
                        default: return value;
                    }
                },
                applicationType: function (value) {
                    switch (value) {
                        case 'fdaat1': return 'New Drug Application (NDA)';
                        case 'fdaat2': return 'Abbreviated New Drug Application (ANDA)';
                        case 'fdaat3': return 'Biologic License Application (BLA)';
                        case 'fdaat4': return 'Investigational New Drug (IND)';
                        case 'fdaat5': return 'Drug Master File (DMF)';
                        case 'fdaat6': return 'Emergency Use Authorization (EUA)';
                        case 'fdaat7': return 'Investigational Device Exemption (IDE)';
                        case 'fdaat8': return 'Safety Issue (FDA Use Only)';
                        case 'fdaat9': return 'Premarket Approval Application (PMA)';
                        case 'fdaat10': return 'Premarket Notification 510k (510K)';
                        default: return value;
                    }
                }
            },
            mounted() {
                store.dispatch('initialize');
                window.addEventListener('message', this.interLinkClicked);
                window.addEventListener("popstate", this.historyChanged);
            },
            beforeDestroy() {
                window.removeEventListener('message', this.interLinkClicked);
                window.removeEventListener("popstate", this.historyChanged);
            }
        };
    </script>
}


