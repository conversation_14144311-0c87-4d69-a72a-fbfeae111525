﻿@inject PharmaLex.SmartTRACE.Web.Helpers.VersionCdnHelper Cdn

<script type="text/x-template" id="collection-cell-template">
    <td>
        <span>{{val[0]}}</span>
        <span class="tipified" :data-tippy-content="tipContent" v-on:click.stop
              v-if="val.length>1">+ {{val.length-1}}</span>
    </td>
</script>
<script type="text/x-template" id="data-table-pager-template">
    <div :class="{'pager-top': location==='top', 'pager-bottom': location==='bottom'}" style="display: flex; width: 100%; justify-content:space-between">
        <div class="dataTables_info" style="white-space: nowrap;">
            Showing {{itemCount===0 ? 0 : startIndex + 1}} to {{endIndex}} of {{itemCount}} entries
            <span v-if="totalItems!==itemCount">(filtered from {{totalItems}})</span>
        </div>
        <div class="dataTables_info" style="white-space: nowrap;">
            Page size
            <select v-on:change="pageSizeChange($event.target.value)" style="margin-top:-.3rem;min-width:25px;">
                <option v-for="s in [10, 25, 50, 100]" :selected="pageSize===s" :value="s">{{s}}</option>
            </select>
        </div>
        <div class="dataTables_paginate paging_full_numbers">
            <a v-on:click="$emit('page-index-change', 0)" :aria-disabled="pageCount===0||pageIndex===0" v-bind:class="[{'disabled': pageCount===0||pageIndex===0}, 'first']">First</a>
            <a v-on:click="$emit('page-index-change', Math.max(0, pageIndex - 1))" :aria-disabled="pageCount===0||pageIndex===0" v-bind:class="[{'disabled': pageCount===0||pageIndex===0}, 'previous']">Previous</a>
            <div style="display:flex; flex-wrap:wrap;">
                <a v-for="i in pageCount" v-on:click="$emit('page-index-change', i-1)" :aria-disabled="pageIndex===i-1" v-bind:class="[{'current': pageIndex===i-1}]">{{i}}</a>
            </div>
            <a v-on:click="$emit('page-index-change', Math.min(pageCount - 1, pageIndex + 1))" :aria-disabled="pageCount===0||pageIndex===pageCount-1" v-bind:class="[{'disabled': pageCount===0||pageIndex===pageCount-1}, 'next']">Next</a>
            <a v-on:click="$emit('page-index-change', pageCount - 1)" :aria-disabled="pageCount===0||pageIndex===pageCount-1" v-bind:class="[{'disabled': pageCount===0||pageIndex===pageCount-1}, 'last']">Last</a>
        </div>
    </div>
</script>
<script type="text/x-template" id="select-filter-template">
    <div :class="['table-filter', 'icon-filter', {'active': !!value, 'open': isOpen }]"
         v-on:click="open()">
        <ul v-show="isOpen" class="table-filter-items"
            @@keydown.enter.prevent="change"
            @@keydown.down="onArrowDown"
            @@keydown.up="onArrowUp"
            ref="select">
            <li v-if="config.header" @@click.stop="change()"
                :class="[{ 'is-active': selectedIndex ===-1 }, 'table-filter-item']">{{config.header}}</li>
            <li v-for="(fi, i) in availableOptions" :title="fi.value"
                :key="i"
                @@click.stop="change(fi.key)"
                :class="[{ 'is-active': selectedIndex === i }, 'table-filter-item']">
                {{ fi.value }}
            </li>
        </ul>
    </div>
</script>
<script type="text/x-template" id="select-multiple-filter-template">
    <div :class="['table-filter', 'icon-filter', {'active': !!value, 'open': isOpen }]"
         v-on:click="open()">
        <ul v-show="isOpen" class="table-filter-items"
            @@keydown.enter.prevent="change"
            @@keydown.down="onArrowDown"
            @@keydown.up="onArrowUp"
            ref="select">
            <li v-if="config.header" @@click.stop="change()"
                :class="[{ 'is-active': !availableOptions.filter(a => a.selected).length }, 'table-filter-item', 'icon-button-cancel']">{{config.header}}</li>
            <li v-for="(fi, i) in availableOptions" :title="fi.value"
                :key="i"
                @@click.stop="change(fi.value, i)"
                :class="[{ 'is-active': fi.selected }, 'table-filter-item']">
                {{ fi.value }}
            </li>
        </ul>
    </div>
</script>
<script type="text/x-template" id="search-filter-template">
    <div :class="['table-filter', 'search', 'icon-filter', {'active': !!value, 'open': isOpen }]"
         v-on:click="open()">
        <div v-show="isOpen" class="table-filter-items" v-on:click.stop @@keydown.enter.prevent="close">
            <input ref="search" type="search" :placeholder="config.header" v-model="modelValue" v-on:input="change($event.target.value)" />
        </div>
    </div>
</script>
<script type="text/x-template" id="text-cell-template">
    <td v-if="editMode === false">{{val}}</td>
    <td v-else><input type="text" :value="val" :name="`${typeName}[${rowIndex}].${fieldName}`" :required="required" /></td>
</script>
<script type="text/x-template" id="bool-cell-template">
    <td v-if="editMode === false">
        <div style="display:flex;align-items:center;justify-content:center;"><i :class="val === true ? 'tick' : 'cross'"></i></div>
    </td>
    <td v-else>
        <label class="switch-container" style="display:flex;align-items:center;justify-content:center;">
            No
            <input type="checkbox" :id="`${typeName}[${rowIndex}]_${fieldName}`" :name="`${typeName}[${rowIndex}].${fieldName}`" class="switch" value="true" :checked="val" />
            <label :for="`${typeName}[${rowIndex}]_${fieldName}`" class="switch"></label>
            Yes
        </label>
    </td>
</script>
<script type="text/x-template" id="filtered-table-template">
    <div class="dataTables_wrapper no-footer">
        <data-table-pager v-if="pagerLocation.includes('top') && !editMode"
                          :item-count="itemCount"
                          :page-index="pageIndex"
                          :page-size="size"
                          :location="'top'"
                          :total-items="items.length"
                          v-on:page-index-change="changePage"
                          v-on:page-size-change="changePageSize">
        </data-table-pager>
        <table class="dataTable filtered" v-bind:class="styling">
            <thead>
                <tr>
                    <th v-for="column in columns.config"
                        v-bind:title="column.header||column.dataKey"
                        v-bind:style="getColumnStyle(column)">
                        <div :class="['table-header', {'bool':column.type==='bool'}]">
                            <div>
                                {{column.header||column.dataKey}}
                            </div>
                            <component v-on:click="sort(column)"
                                 v-if="column.sortKey"
                                 :title="'Sort by ' + (column.header||column.dataKey)"
                                 :class="['sorter', {'active': sortModel[column.sortKey], 'sorting': column.sortKey&&!sortModel[column.sortKey], 'sorting_asc': sortModel[column.sortKey] > 0},{'sorting_desc': sortModel[column.sortKey] < 0}]"></component>
                            <component v-if="!editMode && getFilterByKey(column.dataKey)"
                                 v-bind:is="getFilterByKey(column.dataKey).type + '-filter'"
                                 v-model="filterModel[column.dataKey]"
                                 @@filter="filter"
                                 :filtered="filterItems"
                                 :config="getFilterByKey(column.dataKey)"
                                 :title="'Filter by ' + (column.header||column.dataKey)"></component>
                        </div>
                    </th>
                    <th class="data-table-actions">
                        <div style="display:flex;">
                            <div v-if="!editMode && internalFilters.length" v-on:click="clear" :class="['table-filter icon-filter clear', {'active': isFiltered}]" title="Clear filters"></div>
                            <i v-if="(addurl || editMode) && !editing" v-on:click="add" class="icon-button-add data-table-add-button" title="Add item"></i>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(row, index) in pageItems" v-on:click="edit(row, columns)" :key="row[columns.idKey]" :class="setRowClass(row)">
                    <component v-for="column in columns.config" v-bind:is="column.type + '-cell'" :href="getHref(row, columns)" :val="row[column.dataKey]" :edit-mode="editMode" :title="column.type === 'html' ? false : row[column.dataKey]" :key="column.dataKey" :type-name="typeName" :field-name="column.dataKey" :row-index="index" :required="column.required"></component>
                    <td>
                        <template v-if="editMode">
                            <i class="icon-trash icon-only-button" v-if="row['canDelete'] === true" title="Delete" v-on:click="deleteItemClicked(row[columns.idKey])"></i>
                            <i class="icon-info-circled" v-else :title="noDeleteMessage"></i>
                            <input type="hidden" :name="`${typeName}[${index}].${columns.idKey}`" :value="row[columns.idKey]" />
                        </template>
                    </td>
                </tr>
            </tbody>
        </table>
        <div v-if="!filterItems.length" class="no-records-container">
            {{noRecordsMessage}}
        </div>
        <data-table-pager v-if="pagerLocation.includes('bottom') && !editMode"
                          :item-count="itemCount"
                          :page-index="pageIndex"
                          :page-size="size"
                          :location="'bottom'"
                          :total-items="items.length"
                          v-on:page-index-change="changePage"
                          v-on:page-size-change="changePageSize">
        </data-table-pager>
    </div>
</script>
<script src="~/js/vue/filtered-table.js" asp-append-version="true"></script>
