﻿CREATE TRIGGER [dbo].[PicklistData_Insert] ON [dbo].[PicklistData]
FOR INSERT AS
INSERT INTO [Audit].[PicklistData_Audit]
SELECT 'I', [Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[PicklistData_Update] ON [dbo].[PicklistData]
FOR UPDATE AS
INSERT INTO [Audit].[PicklistData_Audit]
SELECT 'U', [Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[PicklistData_Delete] ON [dbo].[PicklistData]
FOR DELETE AS
INSERT INTO [Audit].[PicklistData_Audit]
SELECT 'D', [Id], [Name], [PicklistTypeId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO