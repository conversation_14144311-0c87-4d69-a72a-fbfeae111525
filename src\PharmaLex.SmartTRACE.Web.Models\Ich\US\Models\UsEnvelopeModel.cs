﻿using AutoMapper;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.US.Models
{
    public class UsEnvelopeModel : IModule1DataModel
    {
        public UsEnvelopeSummary Summary { get; set; }
        public List<UsEnvelopeContact> Contacts { get; set; }
        public UsApplicationInformation Application { get; set; }
        public UsApplicantInformation Applicant { get; set; }
    }

    public class CtdEnvelopeMappingProfile : Profile
    {
        public CtdEnvelopeMappingProfile()
        {
            this.CreateMap<UsAdmin, UsEnvelopeModel>();
            this.CreateMap<UsAdmin32, UsEnvelopeModel>();
        }
    }
}
