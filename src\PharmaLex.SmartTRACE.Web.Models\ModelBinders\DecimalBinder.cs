﻿using Microsoft.AspNetCore.Mvc.ModelBinding;
using System.Globalization;
using System.Threading.Tasks;

using IModelBinder = Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder;

namespace PharmaLex.SmartTRACE.Entities
{
    public class DecimalBinder : IModelBinder
    {
        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            try
            {
                var valueProviderResult = bindingContext.ValueProvider.GetValue(bindingContext.ModelName);

                var value = valueProviderResult.FirstValue;

                if (string.IsNullOrEmpty(value))
                {
                    return Task.CompletedTask;
                }

                // Remove unnecessary commas and spaces
                value = value.Replace(",", string.Empty).Trim();

                decimal myValue = decimal.Parse(value, CultureInfo.InvariantCulture);

                bindingContext.Result = ModelBindingResult.Success(myValue);
            }
            catch (System.Exception)
            {
            }
            return Task.CompletedTask;
        }
    }
}
