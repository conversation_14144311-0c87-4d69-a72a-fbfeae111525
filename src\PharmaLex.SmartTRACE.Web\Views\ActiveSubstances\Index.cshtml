﻿@model IEnumerable<ActiveSubstanceModel>
@using PharmaLex.SmartTRACE.Entities.Enums
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@inject AutoMapper.IMapper mapper

@{
    var lifecycleStates = mapper.Map<IEnumerable<ApplicationLifecycleStateList>>(Enum.GetValues(typeof(CommonLifecycleState)));
}

<div id="active-substances" class="manage-container">
    <header class="manage-header">
        <h2>Manage Active Substances</h2>
        <a class="button icon-button-add" href="/active-substances/new">Add active substance</a>
    </header>
    <filtered-table :items="activeSubstances" :columns="columns" :filters="filters" :link="link"></filtered-table>
</div>

@section Scripts {
    <script type="text/javascript">

        var pageConfig = {
            appElement: '#active-substances',
            data() {
                return {
                    link: '/active-substances/edit/',
                    activeSubstances: @Html.Raw(JsonConvert.SerializeObject(Model, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver(), StringEscapeHandling = StringEscapeHandling.EscapeHtml })),
                    columns: {
                        idKey: 'id',
                        styleKey: 'lifecycleState',
                        config: [
                            {
                                dataKey: 'name',
                                sortKey: 'name',
                                header: 'Name',
                                type: 'text'
                            },
                            {
                                dataKey: 'clientName',
                                sortKey: 'clientName',
                                header: 'Client',
                                type: 'text'
                            },
                            {
                                dataKey: 'lifecycleState',
                                sortKey: 'lifecycleState',
                                header: 'Lifecycle State',
                                type: 'text'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'name',
                            options: [],
                            type: 'search',
                            header: 'Search Name',
                            fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'clientName',
                            options: [],
                            type: 'search',
                            header: 'Search Client',
                            fn: v => p => p.clientName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'lifecycleState',
                            options: @Html.Raw(lifecycleStates.ToJson()),
                            filterCollection: 'lifecycleState',
                            display: 'id',
                            type: 'select',
                            header: 'Filter By State',
                            fn: v => p => p.lifecycleState === v,
                            convert: v => v
                        },
                    ]
                };
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}