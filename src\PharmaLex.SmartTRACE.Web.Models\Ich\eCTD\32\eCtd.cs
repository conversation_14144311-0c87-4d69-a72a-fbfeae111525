﻿using System;
using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32
{
    [Serializable]
    public partial class leaf
    {
        public leaf()
        {
            this.type = "simple";
        }
        public title title { get; set; }

        [XmlElement("link-text")]
        public linktext linktext { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute("application-version")]
        public string applicationversion { get; set; }

        [XmlAttribute()]
        public string version { get; set; }

        [XmlAttribute("font-library")]
        public string fontlibrary { get; set; }

        [XmlAttribute()]
        public leafOperation operation { get; set; }

        [XmlAttribute("modified-file")]
        public string modifiedfile { get; set; }

        [XmlAttribute()]
        public string checksum { get; set; }

        [XmlAttribute("checksum-type")]
        public string checksumtype { get; set; }

        [XmlAttribute()]
        public string keywords { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string type { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string role { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string href { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefShow show { get; set; }

        [XmlIgnore]
        public bool showSpecified { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefActuate actuate { get; set; }

        [XmlIgnore]
        public bool actuateSpecified { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    public partial class title
    {

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlText]
        public string Value { get; set; }
    }

    [Serializable]
    [XmlRoot("link-text", Namespace = "", IsNullable = false)]
    public partial class linktext
    {

        [XmlElement("xref")]
        public xref[] Items { get; set; }

        [XmlText]
        public string[] Text { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
    }

    [Serializable]
    public partial class xref
    {
        public xref()
        {
            this.type = "simple";
        }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string type { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string role { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string title { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string href { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefShow show { get; set; }

        [XmlIgnore]
        public bool showSpecified { get; set; }

        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefActuate actuate { get; set; }

        [XmlIgnore]
        public bool actuateSpecified { get; set; }
    }

    [Serializable]
    [XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.w3c.org/1999/xlink")]
    public enum xrefShow
    {

        @new,

        replace,

        embed,

        other,

        none,
    }

    [Serializable]
    [XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.w3c.org/1999/xlink")]

    public enum xrefActuate
    {

        onLoad,

        onRequest,

        other,

        none,
    }

    [Serializable]
    public enum leafOperation
    {

        @new,

        append,

        replace,

        delete,
    }

    [Serializable]
    [XmlRoot("node-extension", Namespace = "", IsNullable = false)]
    public partial class nodeextension
    {

        public title title { get; set; }

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-administrative-information-and-prescribing-information", Namespace = "", IsNullable = false)]
    public partial class m1administrativeinformationandprescribinginformation
    {

        [XmlElement("leaf", typeof(leaf))]
        public leaf[] leaf { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-common-technical-document-summaries", Namespace = "", IsNullable = false)]
    public partial class m2commontechnicaldocumentsummaries
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m2-2-introduction")]
        public m22introduction m22introduction { get; set; }

        [XmlElement("m2-3-quality-overall-summary")]
        public m23qualityoverallsummary m23qualityoverallsummary { get; set; }

        [XmlElement("m2-4-nonclinical-overview")]
        public m24nonclinicaloverview m24nonclinicaloverview { get; set; }

        [XmlElement("m2-5-clinical-overview")]
        public m25clinicaloverview m25clinicaloverview { get; set; }

        [XmlElement("m2-6-nonclinical-written-and-tabulated-summaries")]
        public m26nonclinicalwrittenandtabulatedsummaries m26nonclinicalwrittenandtabulatedsummaries { get; set; }

        [XmlElement("m2-7-clinical-summary")]
        public m27clinicalsummary m27clinicalsummary { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-2-introduction", Namespace = "", IsNullable = false)]
    public partial class m22introduction
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-3-quality-overall-summary", Namespace = "", IsNullable = false)]
    public partial class m23qualityoverallsummary
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlElement("m2-3-introduction")]
        public m23introduction m23introduction { get; set; }

        [XmlElement("m2-3-s-drug-substance")]
        public m23sdrugsubstance[] m23sdrugsubstance { get; set; }

        [XmlElement("m2-3-p-drug-product")]
        public m23pdrugproduct[] m23pdrugproduct { get; set; }

        [XmlElement("m2-3-a-appendices")]
        public m23aappendices m23aappendices { get; set; }

        [XmlElement("m2-3-r-regional-information")]
        public m23rregionalinformation m23rregionalinformation { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-3-introduction", Namespace = "", IsNullable = false)]
    public partial class m23introduction
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-3-s-drug-substance", Namespace = "", IsNullable = false)]
    public partial class m23sdrugsubstance
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

        [XmlAttribute()]
        public string substance { get; set; }

        [XmlAttribute()]
        public string manufacturer { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-3-p-drug-product", Namespace = "", IsNullable = false)]
    public partial class m23pdrugproduct
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

        [XmlAttribute("product-name")]
        public string productname { get; set; }

        [XmlAttribute()]
        public string dosageform { get; set; }

        [XmlAttribute()]
        public string manufacturer { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-3-a-appendices", Namespace = "", IsNullable = false)]
    public partial class m23aappendices
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-3-r-regional-information", Namespace = "", IsNullable = false)]
    public partial class m23rregionalinformation
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-4-nonclinical-overview", Namespace = "", IsNullable = false)]
    public partial class m24nonclinicaloverview
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-5-clinical-overview", Namespace = "", IsNullable = false)]
    public partial class m25clinicaloverview
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-6-nonclinical-written-and-tabulated-summaries", Namespace = "", IsNullable = false)]
    public partial class m26nonclinicalwrittenandtabulatedsummaries
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m2-6-1-introduction")]
        public m261introduction m261introduction { get; set; }

        [XmlElement("m2-6-2-pharmacology-written-summary")]
        public m262pharmacologywrittensummary m262pharmacologywrittensummary { get; set; }

        [XmlElement("m2-6-3-pharmacology-tabulated-summary")]
        public m263pharmacologytabulatedsummary m263pharmacologytabulatedsummary { get; set; }

        [XmlElement("m2-6-4-pharmacokinetics-written-summary")]
        public m264pharmacokineticswrittensummary m264pharmacokineticswrittensummary { get; set; }

        [XmlElement("m2-6-5-pharmacokinetics-tabulated-summary")]
        public m265pharmacokineticstabulatedsummary m265pharmacokineticstabulatedsummary { get; set; }

        [XmlElement("m2-6-6-toxicology-written-summary")]
        public m266toxicologywrittensummary m266toxicologywrittensummary { get; set; }

        [XmlElement("m2-6-7-toxicology-tabulated-summary")]
        public m267toxicologytabulatedsummary m267toxicologytabulatedsummary { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-6-1-introduction", Namespace = "", IsNullable = false)]
    public partial class m261introduction
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-6-2-pharmacology-written-summary", Namespace = "", IsNullable = false)]
    public partial class m262pharmacologywrittensummary
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-6-3-pharmacology-tabulated-summary", Namespace = "", IsNullable = false)]
    public partial class m263pharmacologytabulatedsummary
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-6-4-pharmacokinetics-written-summary", Namespace = "", IsNullable = false)]
    public partial class m264pharmacokineticswrittensummary
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-6-5-pharmacokinetics-tabulated-summary", Namespace = "", IsNullable = false)]
    public partial class m265pharmacokineticstabulatedsummary
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-6-6-toxicology-written-summary", Namespace = "", IsNullable = false)]
    public partial class m266toxicologywrittensummary
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-6-7-toxicology-tabulated-summary", Namespace = "", IsNullable = false)]
    public partial class m267toxicologytabulatedsummary
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-7-clinical-summary", Namespace = "", IsNullable = false)]
    public partial class m27clinicalsummary
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m2-7-1-summary-of-biopharmaceutic-studies-and-associated-analytical-methods")]
        public m271summaryofbiopharmaceuticstudiesandassociatedanalyticalmethods m271summaryofbiopharmaceuticstudiesandassociatedanalyticalmethods { get; set; }

        [XmlElement("m2-7-2-summary-of-clinical-pharmacology-studies")]
        public m272summaryofclinicalpharmacologystudies m272summaryofclinicalpharmacologystudies { get; set; }

        [XmlElement("m2-7-3-summary-of-clinical-efficacy")]
        public m273summaryofclinicalefficacy[] m273summaryofclinicalefficacy { get; set; }

        [XmlElement("m2-7-4-summary-of-clinical-safety")]
        public m274summaryofclinicalsafety m274summaryofclinicalsafety { get; set; }

        [XmlElement("m2-7-5-literature-references")]
        public m275literaturereferences m275literaturereferences { get; set; }

        [XmlElement("m2-7-6-synopses-of-individual-studies")]
        public m276synopsesofindividualstudies m276synopsesofindividualstudies { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-7-1-summary-of-biopharmaceutic-studies-and-associated-analytical-methods", Namespace = "", IsNullable = false)]
    public partial class m271summaryofbiopharmaceuticstudiesandassociatedanalyticalmethods
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-7-2-summary-of-clinical-pharmacology-studies", Namespace = "", IsNullable = false)]
    public partial class m272summaryofclinicalpharmacologystudies
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-7-3-summary-of-clinical-efficacy", Namespace = "", IsNullable = false)]
    public partial class m273summaryofclinicalefficacy
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

        [XmlAttribute()]
        public string indication { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-7-4-summary-of-clinical-safety", Namespace = "", IsNullable = false)]
    public partial class m274summaryofclinicalsafety
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-7-5-literature-references", Namespace = "", IsNullable = false)]
    public partial class m275literaturereferences
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m2-7-6-synopses-of-individual-studies", Namespace = "", IsNullable = false)]
    public partial class m276synopsesofindividualstudies
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-quality", Namespace = "", IsNullable = false)]
    public partial class m3quality
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-body-of-data")]
        public m32bodyofdata m32bodyofdata { get; set; }

        [XmlElement("m3-3-literature-references")]
        public m33literaturereferences m33literaturereferences { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-body-of-data", Namespace = "", IsNullable = false)]
    public partial class m32bodyofdata
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-s-drug-substance")]
        public m32sdrugsubstance[] m32sdrugsubstance { get; set; }

        [XmlElement("m3-2-p-drug-product")]
        public m32pdrugproduct[] m32pdrugproduct { get; set; }

        [XmlElement("m3-2-a-appendices")]
        public m32aappendices m32aappendices { get; set; }

        [XmlElement("m3-2-r-regional-information")]
        public m32rregionalinformation m32rregionalinformation { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-drug-substance", Namespace = "", IsNullable = false)]
    public partial class m32sdrugsubstance
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-s-1-general-information")]
        public m32s1generalinformation m32s1generalinformation { get; set; }

        [XmlElement("m3-2-s-2-manufacture")]
        public m32s2manufacture m32s2manufacture { get; set; }

        [XmlElement("m3-2-s-3-characterisation")]
        public m32s3characterisation m32s3characterisation { get; set; }

        [XmlElement("m3-2-s-4-control-of-drug-substance")]
        public m32s4controlofdrugsubstance m32s4controlofdrugsubstance { get; set; }

        [XmlElement("m3-2-s-5-reference-standards-or-materials")]
        public m32s5referencestandardsormaterials m32s5referencestandardsormaterials { get; set; }

        [XmlElement("m3-2-s-6-container-closure-system")]
        public m32s6containerclosuresystem m32s6containerclosuresystem { get; set; }

        [XmlElement("m3-2-s-7-stability")]
        public m32s7stability m32s7stability { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

        [XmlAttribute()]
        public string substance { get; set; }

        [XmlAttribute()]
        public string manufacturer { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-1-general-information", Namespace = "", IsNullable = false)]
    public partial class m32s1generalinformation
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-s-1-1-nomenclature")]
        public m32s11nomenclature m32s11nomenclature { get; set; }

        [XmlElement("m3-2-s-1-2-structure")]
        public m32s12structure m32s12structure { get; set; }

        [XmlElement("m3-2-s-1-3-general-properties")]
        public m32s13generalproperties m32s13generalproperties { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-1-1-nomenclature", Namespace = "", IsNullable = false)]
    public partial class m32s11nomenclature
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-1-2-structure", Namespace = "", IsNullable = false)]
    public partial class m32s12structure
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-1-3-general-properties", Namespace = "", IsNullable = false)]
    public partial class m32s13generalproperties
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-2-manufacture", Namespace = "", IsNullable = false)]
    public partial class m32s2manufacture
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-s-2-1-manufacturer")]
        public m32s21manufacturer m32s21manufacturer { get; set; }

        [XmlElement("m3-2-s-2-2-description-of-manufacturing-process-and-process-controls")]
        public m32s22descriptionofmanufacturingprocessandprocesscontrols m32s22descriptionofmanufacturingprocessandprocesscontrols { get; set; }

        [XmlElement("m3-2-s-2-3-control-of-materials")]
        public m32s23controlofmaterials m32s23controlofmaterials { get; set; }

        [XmlElement("m3-2-s-2-4-controls-of-critical-steps-and-intermediates")]
        public m32s24controlsofcriticalstepsandintermediates m32s24controlsofcriticalstepsandintermediates { get; set; }

        [XmlElement("m3-2-s-2-5-process-validation-and-or-evaluation")]
        public m32s25processvalidationandorevaluation m32s25processvalidationandorevaluation { get; set; }

        [XmlElement("m3-2-s-2-6-manufacturing-process-development")]
        public m32s26manufacturingprocessdevelopment m32s26manufacturingprocessdevelopment { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-2-1-manufacturer", Namespace = "", IsNullable = false)]
    public partial class m32s21manufacturer
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-2-2-description-of-manufacturing-process-and-process-controls", Namespace = "", IsNullable = false)]
    public partial class m32s22descriptionofmanufacturingprocessandprocesscontrols
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-2-3-control-of-materials", Namespace = "", IsNullable = false)]
    public partial class m32s23controlofmaterials
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-2-4-controls-of-critical-steps-and-intermediates", Namespace = "", IsNullable = false)]
    public partial class m32s24controlsofcriticalstepsandintermediates
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-2-5-process-validation-and-or-evaluation", Namespace = "", IsNullable = false)]
    public partial class m32s25processvalidationandorevaluation
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-2-6-manufacturing-process-development", Namespace = "", IsNullable = false)]
    public partial class m32s26manufacturingprocessdevelopment
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-3-characterisation", Namespace = "", IsNullable = false)]
    public partial class m32s3characterisation
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-s-3-1-elucidation-of-structure-and-other-characteristics")]
        public m32s31elucidationofstructureandothercharacteristics m32s31elucidationofstructureandothercharacteristics { get; set; }

        [XmlElement("m3-2-s-3-2-impurities")]
        public m32s32impurities m32s32impurities { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-3-1-elucidation-of-structure-and-other-characteristics", Namespace = "", IsNullable = false)]
    public partial class m32s31elucidationofstructureandothercharacteristics
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-3-2-impurities", Namespace = "", IsNullable = false)]
    public partial class m32s32impurities
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-4-control-of-drug-substance", Namespace = "", IsNullable = false)]
    public partial class m32s4controlofdrugsubstance
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-s-4-1-specification")]
        public m32s41specification m32s41specification { get; set; }

        [XmlElement("m3-2-s-4-2-analytical-procedures")]
        public m32s42analyticalprocedures m32s42analyticalprocedures { get; set; }

        [XmlElement("m3-2-s-4-3-validation-of-analytical-procedures")]
        public m32s43validationofanalyticalprocedures m32s43validationofanalyticalprocedures { get; set; }

        [XmlElement("m3-2-s-4-4-batch-analyses")]
        public m32s44batchanalyses m32s44batchanalyses { get; set; }

        [XmlElement("m3-2-s-4-5-justification-of-specification")]
        public m32s45justificationofspecification m32s45justificationofspecification { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-4-1-specification", Namespace = "", IsNullable = false)]
    public partial class m32s41specification
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-4-2-analytical-procedures", Namespace = "", IsNullable = false)]
    public partial class m32s42analyticalprocedures
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-4-3-validation-of-analytical-procedures", Namespace = "", IsNullable = false)]
    public partial class m32s43validationofanalyticalprocedures
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-4-4-batch-analyses", Namespace = "", IsNullable = false)]
    public partial class m32s44batchanalyses
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-4-5-justification-of-specification", Namespace = "", IsNullable = false)]
    public partial class m32s45justificationofspecification
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-5-reference-standards-or-materials", Namespace = "", IsNullable = false)]
    public partial class m32s5referencestandardsormaterials
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-6-container-closure-system", Namespace = "", IsNullable = false)]
    public partial class m32s6containerclosuresystem
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-7-stability", Namespace = "", IsNullable = false)]
    public partial class m32s7stability
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-s-7-1-stability-summary-and-conclusions")]
        public m32s71stabilitysummaryandconclusions m32s71stabilitysummaryandconclusions { get; set; }

        [XmlElement("m3-2-s-7-2-post-approval-stability-protocol-and-stability-commitment")]
        public m32s72postapprovalstabilityprotocolandstabilitycommitment m32s72postapprovalstabilityprotocolandstabilitycommitment { get; set; }

        [XmlElement("m3-2-s-7-3-stability-data")]
        public m32s73stabilitydata m32s73stabilitydata { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-7-1-stability-summary-and-conclusions", Namespace = "", IsNullable = false)]
    public partial class m32s71stabilitysummaryandconclusions
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-7-2-post-approval-stability-protocol-and-stability-commitment", Namespace = "", IsNullable = false)]
    public partial class m32s72postapprovalstabilityprotocolandstabilitycommitment
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-s-7-3-stability-data", Namespace = "", IsNullable = false)]
    public partial class m32s73stabilitydata
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-drug-product", Namespace = "", IsNullable = false)]
    public partial class m32pdrugproduct
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-p-1-description-and-composition-of-the-drug-product")]
        public m32p1descriptionandcompositionofthedrugproduct m32p1descriptionandcompositionofthedrugproduct { get; set; }

        [XmlElement("m3-2-p-2-pharmaceutical-development")]
        public m32p2pharmaceuticaldevelopment m32p2pharmaceuticaldevelopment { get; set; }

        [XmlElement("m3-2-p-3-manufacture")]
        public m32p3manufacture m32p3manufacture { get; set; }

        [XmlElement("m3-2-p-4-control-of-excipients")]
        public m32p4controlofexcipients[] m32p4controlofexcipients { get; set; }

        [XmlElement("m3-2-p-5-control-of-drug-product")]
        public m32p5controlofdrugproduct m32p5controlofdrugproduct { get; set; }

        [XmlElement("m3-2-p-6-reference-standards-or-materials")]
        public m32p6referencestandardsormaterials m32p6referencestandardsormaterials { get; set; }

        [XmlElement("m3-2-p-7-container-closure-system")]
        public m32p7containerclosuresystem m32p7containerclosuresystem { get; set; }

        [XmlElement("m3-2-p-8-stability")]
        public m32p8stability m32p8stability { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

        [XmlAttribute("product-name")]
        public string productname { get; set; }

        [XmlAttribute()]
        public string dosageform { get; set; }

        [XmlAttribute()]
        public string manufacturer { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-1-description-and-composition-of-the-drug-product", Namespace = "", IsNullable = false)]
    public partial class m32p1descriptionandcompositionofthedrugproduct
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-2-pharmaceutical-development", Namespace = "", IsNullable = false)]
    public partial class m32p2pharmaceuticaldevelopment
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-3-manufacture", Namespace = "", IsNullable = false)]
    public partial class m32p3manufacture
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-p-3-1-manufacturers")]
        public m32p31manufacturers m32p31manufacturers { get; set; }

        [XmlElement("m3-2-p-3-2-batch-formula")]
        public m32p32batchformula m32p32batchformula { get; set; }

        [XmlElement("m3-2-p-3-3-description-of-manufacturing-process-and-process-controls")]
        public m32p33descriptionofmanufacturingprocessandprocesscontrols m32p33descriptionofmanufacturingprocessandprocesscontrols { get; set; }

        [XmlElement("m3-2-p-3-4-controls-of-critical-steps-and-intermediates")]
        public m32p34controlsofcriticalstepsandintermediates m32p34controlsofcriticalstepsandintermediates { get; set; }

        [XmlElement("m3-2-p-3-5-process-validation-and-or-evaluation")]
        public m32p35processvalidationandorevaluation m32p35processvalidationandorevaluation { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-3-1-manufacturers", Namespace = "", IsNullable = false)]
    public partial class m32p31manufacturers
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-3-2-batch-formula", Namespace = "", IsNullable = false)]
    public partial class m32p32batchformula
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-3-3-description-of-manufacturing-process-and-process-controls", Namespace = "", IsNullable = false)]
    public partial class m32p33descriptionofmanufacturingprocessandprocesscontrols
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-3-4-controls-of-critical-steps-and-intermediates", Namespace = "", IsNullable = false)]
    public partial class m32p34controlsofcriticalstepsandintermediates
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-3-5-process-validation-and-or-evaluation", Namespace = "", IsNullable = false)]
    public partial class m32p35processvalidationandorevaluation
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-4-control-of-excipients", Namespace = "", IsNullable = false)]
    public partial class m32p4controlofexcipients
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-p-4-1-specifications")]
        public m32p41specifications m32p41specifications { get; set; }

        [XmlElement("m3-2-p-4-2-analytical-procedures")]
        public m32p42analyticalprocedures m32p42analyticalprocedures { get; set; }

        [XmlElement("m3-2-p-4-3-validation-of-analytical-procedures")]
        public m32p43validationofanalyticalprocedures m32p43validationofanalyticalprocedures { get; set; }

        [XmlElement("m3-2-p-4-4-justification-of-specifications")]
        public m32p44justificationofspecifications m32p44justificationofspecifications { get; set; }

        [XmlElement("m3-2-p-4-5-excipients-of-human-or-animal-origin")]
        public m32p45excipientsofhumanoranimalorigin m32p45excipientsofhumanoranimalorigin { get; set; }

        [XmlElement("m3-2-p-4-6-novel-excipients")]
        public m32p46novelexcipients m32p46novelexcipients { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

        [XmlAttribute()]
        public string excipient { get; set; }

        public string productname { get; set; }

        public string dosageform { get; set; }

        public string manufacturer { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-4-1-specifications", Namespace = "", IsNullable = false)]
    public partial class m32p41specifications
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-4-2-analytical-procedures", Namespace = "", IsNullable = false)]
    public partial class m32p42analyticalprocedures
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-4-3-validation-of-analytical-procedures", Namespace = "", IsNullable = false)]
    public partial class m32p43validationofanalyticalprocedures
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-4-4-justification-of-specifications", Namespace = "", IsNullable = false)]
    public partial class m32p44justificationofspecifications
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-4-5-excipients-of-human-or-animal-origin", Namespace = "", IsNullable = false)]
    public partial class m32p45excipientsofhumanoranimalorigin
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-4-6-novel-excipients", Namespace = "", IsNullable = false)]
    public partial class m32p46novelexcipients
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-5-control-of-drug-product", Namespace = "", IsNullable = false)]
    public partial class m32p5controlofdrugproduct
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-p-5-1-specifications")]
        public m32p51specifications m32p51specifications { get; set; }

        [XmlElement("m3-2-p-5-2-analytical-procedures")]
        public m32p52analyticalprocedures m32p52analyticalprocedures { get; set; }

        [XmlElement("m3-2-p-5-3-validation-of-analytical-procedures")]
        public m32p53validationofanalyticalprocedures m32p53validationofanalyticalprocedures { get; set; }

        [XmlElement("m3-2-p-5-4-batch-analyses")]
        public m32p54batchanalyses m32p54batchanalyses { get; set; }

        [XmlElement("m3-2-p-5-5-characterisation-of-impurities")]
        public m32p55characterisationofimpurities m32p55characterisationofimpurities { get; set; }

        [XmlElement("m3-2-p-5-6-justification-of-specifications")]
        public m32p56justificationofspecifications m32p56justificationofspecifications { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-5-1-specifications", Namespace = "", IsNullable = false)]
    public partial class m32p51specifications
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-5-2-analytical-procedures", Namespace = "", IsNullable = false)]
    public partial class m32p52analyticalprocedures
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-5-3-validation-of-analytical-procedures", Namespace = "", IsNullable = false)]
    public partial class m32p53validationofanalyticalprocedures
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-5-4-batch-analyses", Namespace = "", IsNullable = false)]
    public partial class m32p54batchanalyses
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-5-5-characterisation-of-impurities", Namespace = "", IsNullable = false)]
    public partial class m32p55characterisationofimpurities
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-5-6-justification-of-specifications", Namespace = "", IsNullable = false)]
    public partial class m32p56justificationofspecifications
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-6-reference-standards-or-materials", Namespace = "", IsNullable = false)]
    public partial class m32p6referencestandardsormaterials
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-7-container-closure-system", Namespace = "", IsNullable = false)]
    public partial class m32p7containerclosuresystem
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-8-stability", Namespace = "", IsNullable = false)]
    public partial class m32p8stability
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-p-8-1-stability-summary-and-conclusion")]
        public m32p81stabilitysummaryandconclusion m32p81stabilitysummaryandconclusion { get; set; }

        [XmlElement("m3-2-p-8-2-post-approval-stability-protocol-and-stability-commitment")]
        public m32p82postapprovalstabilityprotocolandstabilitycommitment m32p82postapprovalstabilityprotocolandstabilitycommitment { get; set; }

        [XmlElement("m3-2-p-8-3-stability-data")]
        public m32p83stabilitydata m32p83stabilitydata { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-8-1-stability-summary-and-conclusion", Namespace = "", IsNullable = false)]
    public partial class m32p81stabilitysummaryandconclusion
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-8-2-post-approval-stability-protocol-and-stability-commitment", Namespace = "", IsNullable = false)]
    public partial class m32p82postapprovalstabilityprotocolandstabilitycommitment
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-p-8-3-stability-data", Namespace = "", IsNullable = false)]
    public partial class m32p83stabilitydata
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-a-appendices", Namespace = "", IsNullable = false)]
    public partial class m32aappendices
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m3-2-a-1-facilities-and-equipment")]
        public m32a1facilitiesandequipment[] m32a1facilitiesandequipment { get; set; }

        [XmlElement("m3-2-a-2-adventitious-agents-safety-evaluation")]
        public m32a2adventitiousagentssafetyevaluation[] m32a2adventitiousagentssafetyevaluation { get; set; }

        [XmlElement("m3-2-a-3-excipients")]
        public m32a3excipients m32a3excipients { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-a-1-facilities-and-equipment", Namespace = "", IsNullable = false)]
    public partial class m32a1facilitiesandequipment
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

        [XmlAttribute()]
        public string manufacturer { get; set; }

        [XmlAttribute()]
        public string substance { get; set; }

        [XmlAttribute()]
        public string dosageform { get; set; }

        [XmlAttribute("product-name")]
        public string productname { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-a-2-adventitious-agents-safety-evaluation", Namespace = "", IsNullable = false)]
    public partial class m32a2adventitiousagentssafetyevaluation
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

        [XmlAttribute()]
        public string manufacturer { get; set; }

        [XmlAttribute()]
        public string substance { get; set; }

        [XmlAttribute()]
        public string dosageform { get; set; }

        [XmlAttribute("product-name")]
        public string productname { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-a-3-excipients", Namespace = "", IsNullable = false)]
    public partial class m32a3excipients
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-2-r-regional-information", Namespace = "", IsNullable = false)]
    public partial class m32rregionalinformation
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m3-3-literature-references", Namespace = "", IsNullable = false)]
    public partial class m33literaturereferences
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-nonclinical-study-reports", Namespace = "", IsNullable = false)]
    public partial class m4nonclinicalstudyreports
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m4-2-study-reports")]
        public m42studyreports m42studyreports { get; set; }

        [XmlElement("m4-3-literature-references")]
        public m43literaturereferences m43literaturereferences { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-study-reports", Namespace = "", IsNullable = false)]
    public partial class m42studyreports
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m4-2-1-pharmacology")]
        public m421pharmacology m421pharmacology { get; set; }

        [XmlElement("m4-2-2-pharmacokinetics")]
        public m422pharmacokinetics m422pharmacokinetics { get; set; }

        [XmlElement("m4-2-3-toxicology")]
        public m423toxicology m423toxicology { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-1-pharmacology", Namespace = "", IsNullable = false)]
    public partial class m421pharmacology
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m4-2-1-1-primary-pharmacodynamics")]
        public m4211primarypharmacodynamics m4211primarypharmacodynamics { get; set; }

        [XmlElement("m4-2-1-2-secondary-pharmacodynamics")]
        public m4212secondarypharmacodynamics m4212secondarypharmacodynamics { get; set; }

        [XmlElement("m4-2-1-3-safety-pharmacology")]
        public m4213safetypharmacology m4213safetypharmacology { get; set; }

        [XmlElement("m4-2-1-4-pharmacodynamic-drug-interactions")]
        public m4214pharmacodynamicdruginteractions m4214pharmacodynamicdruginteractions { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-1-1-primary-pharmacodynamics", Namespace = "", IsNullable = false)]
    public partial class m4211primarypharmacodynamics
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-1-2-secondary-pharmacodynamics", Namespace = "", IsNullable = false)]
    public partial class m4212secondarypharmacodynamics
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-1-3-safety-pharmacology", Namespace = "", IsNullable = false)]
    public partial class m4213safetypharmacology
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-1-4-pharmacodynamic-drug-interactions", Namespace = "", IsNullable = false)]
    public partial class m4214pharmacodynamicdruginteractions
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-2-pharmacokinetics", Namespace = "", IsNullable = false)]
    public partial class m422pharmacokinetics
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m4-2-2-1-analytical-methods-and-validation-reports")]
        public m4221analyticalmethodsandvalidationreports m4221analyticalmethodsandvalidationreports { get; set; }

        [XmlElement("m4-2-2-2-absorption")]
        public m4222absorption m4222absorption { get; set; }

        [XmlElement("m4-2-2-3-distribution")]
        public m4223distribution m4223distribution { get; set; }

        [XmlElement("m4-2-2-4-metabolism")]
        public m4224metabolism m4224metabolism { get; set; }

        [XmlElement("m4-2-2-5-excretion")]
        public m4225excretion m4225excretion { get; set; }

        [XmlElement("m4-2-2-6-pharmacokinetic-drug-interactions")]
        public m4226pharmacokineticdruginteractions m4226pharmacokineticdruginteractions { get; set; }

        [XmlElement("m4-2-2-7-other-pharmacokinetic-studies")]
        public m4227otherpharmacokineticstudies m4227otherpharmacokineticstudies { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-2-1-analytical-methods-and-validation-reports", Namespace = "", IsNullable = false)]
    public partial class m4221analyticalmethodsandvalidationreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-2-2-absorption", Namespace = "", IsNullable = false)]
    public partial class m4222absorption
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-2-3-distribution", Namespace = "", IsNullable = false)]
    public partial class m4223distribution
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-2-4-metabolism", Namespace = "", IsNullable = false)]
    public partial class m4224metabolism
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-2-5-excretion", Namespace = "", IsNullable = false)]
    public partial class m4225excretion
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-2-6-pharmacokinetic-drug-interactions", Namespace = "", IsNullable = false)]
    public partial class m4226pharmacokineticdruginteractions
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-2-7-other-pharmacokinetic-studies", Namespace = "", IsNullable = false)]
    public partial class m4227otherpharmacokineticstudies
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-toxicology", Namespace = "", IsNullable = false)]
    public partial class m423toxicology
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m4-2-3-1-single-dose-toxicity")]
        public m4231singledosetoxicity m4231singledosetoxicity { get; set; }

        [XmlElement("m4-2-3-2-repeat-dose-toxicity")]
        public m4232repeatdosetoxicity m4232repeatdosetoxicity { get; set; }

        [XmlElement("m4-2-3-3-genotoxicity")]
        public m4233genotoxicity m4233genotoxicity { get; set; }

        [XmlElement("m4-2-3-4-carcinogenicity")]
        public m4234carcinogenicity m4234carcinogenicity { get; set; }

        [XmlElement("m4-2-3-5-reproductive-and-developmental-toxicity")]
        public m4235reproductiveanddevelopmentaltoxicity m4235reproductiveanddevelopmentaltoxicity { get; set; }

        [XmlElement("m4-2-3-6-local-tolerance")]
        public m4236localtolerance m4236localtolerance { get; set; }

        [XmlElement("m4-2-3-7-other-toxicity-studies")]
        public m4237othertoxicitystudies m4237othertoxicitystudies { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-1-single-dose-toxicity", Namespace = "", IsNullable = false)]
    public partial class m4231singledosetoxicity
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-2-repeat-dose-toxicity", Namespace = "", IsNullable = false)]
    public partial class m4232repeatdosetoxicity
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-3-genotoxicity", Namespace = "", IsNullable = false)]
    public partial class m4233genotoxicity
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m4-2-3-3-1-in-vitro")]
        public m42331invitro m42331invitro { get; set; }

        [XmlElement("m4-2-3-3-2-in-vivo")]
        public m42332invivo m42332invivo { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-3-1-in-vitro", Namespace = "", IsNullable = false)]
    public partial class m42331invitro
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-3-2-in-vivo", Namespace = "", IsNullable = false)]
    public partial class m42332invivo
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-4-carcinogenicity", Namespace = "", IsNullable = false)]
    public partial class m4234carcinogenicity
    {
        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m4-2-3-4-1-long-term-studies")]
        public m42341longtermstudies m42341longtermstudies { get; set; }

        [XmlElement("m4-2-3-4-2-short-or-medium-term-studies")]
        public m42342shortormediumtermstudies m42342shortormediumtermstudies { get; set; }

        [XmlElement("m4-2-3-4-3-other-studies")]
        public m42343otherstudies m42343otherstudies { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-4-1-long-term-studies", Namespace = "", IsNullable = false)]
    public partial class m42341longtermstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-4-2-short-or-medium-term-studies", Namespace = "", IsNullable = false)]
    public partial class m42342shortormediumtermstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-4-3-other-studies", Namespace = "", IsNullable = false)]
    public partial class m42343otherstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-5-reproductive-and-developmental-toxicity", Namespace = "", IsNullable = false)]
    public partial class m4235reproductiveanddevelopmentaltoxicity
    {
        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m4-2-3-5-1-fertility-and-early-embryonic-development")]
        public m42351fertilityandearlyembryonicdevelopment m42351fertilityandearlyembryonicdevelopment { get; set; }

        [XmlElement("m4-2-3-5-2-embryo-fetal-development")]
        public m42352embryofetaldevelopment m42352embryofetaldevelopment { get; set; }

        [XmlElement("m4-2-3-5-3-prenatal-and-postnatal-development-including-maternal-function")]
        public m42353prenatalandpostnataldevelopmentincludingmaternalfunction m42353prenatalandpostnataldevelopmentincludingmaternalfunction { get; set; }

        [XmlElement("m4-2-3-5-4-studies-in-which-the-offspring-juvenile-animals-are-dosed-and-or-furth" +
            "er-evaluated")]
        public m42354studiesinwhichtheoffspringjuvenileanimalsaredosedandorfurtherevaluated m42354studiesinwhichtheoffspringjuvenileanimalsaredosedandorfurtherevaluated { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-5-1-fertility-and-early-embryonic-development", Namespace = "", IsNullable = false)]
    public partial class m42351fertilityandearlyembryonicdevelopment
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-5-2-embryo-fetal-development", Namespace = "", IsNullable = false)]
    public partial class m42352embryofetaldevelopment
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-5-3-prenatal-and-postnatal-development-including-maternal-function", Namespace = "", IsNullable = false)]
    public partial class m42353prenatalandpostnataldevelopmentincludingmaternalfunction
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-5-4-studies-in-which-the-offspring-juvenile-animals-are-dosed-and-or-furth" +
        "er-evaluated", Namespace = "", IsNullable = false)]
    public partial class m42354studiesinwhichtheoffspringjuvenileanimalsaredosedandorfurtherevaluated
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-6-local-tolerance", Namespace = "", IsNullable = false)]
    public partial class m4236localtolerance
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-7-other-toxicity-studies", Namespace = "", IsNullable = false)]
    public partial class m4237othertoxicitystudies
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m4-2-3-7-1-antigenicity")]
        public m42371antigenicity m42371antigenicity { get; set; }

        [XmlElement("m4-2-3-7-2-immunotoxicity")]
        public m42372immunotoxicity m42372immunotoxicity { get; set; }

        [XmlElement("m4-2-3-7-3-mechanistic-studies")]
        public m42373mechanisticstudies m42373mechanisticstudies { get; set; }

        [XmlElement("m4-2-3-7-4-dependence")]
        public m42374dependence m42374dependence { get; set; }

        [XmlElement("m4-2-3-7-5-metabolites")]
        public m42375metabolites m42375metabolites { get; set; }

        [XmlElement("m4-2-3-7-6-impurities")]
        public m42376impurities m42376impurities { get; set; }

        [XmlElement("m4-2-3-7-7-other")]
        public m42377other m42377other { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-7-1-antigenicity", Namespace = "", IsNullable = false)]
    public partial class m42371antigenicity
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-7-2-immunotoxicity", Namespace = "", IsNullable = false)]
    public partial class m42372immunotoxicity
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-7-3-mechanistic-studies", Namespace = "", IsNullable = false)]
    public partial class m42373mechanisticstudies
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-7-4-dependence", Namespace = "", IsNullable = false)]
    public partial class m42374dependence
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-7-5-metabolites", Namespace = "", IsNullable = false)]
    public partial class m42375metabolites
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-7-6-impurities", Namespace = "", IsNullable = false)]
    public partial class m42376impurities
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-2-3-7-7-other", Namespace = "", IsNullable = false)]
    public partial class m42377other
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m4-3-literature-references", Namespace = "", IsNullable = false)]
    public partial class m43literaturereferences
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-clinical-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5clinicalstudyreports
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m5-2-tabular-listing-of-all-clinical-studies")]
        public m52tabularlistingofallclinicalstudies m52tabularlistingofallclinicalstudies { get; set; }

        [XmlElement("m5-3-clinical-study-reports")]
        public m53clinicalstudyreports m53clinicalstudyreports { get; set; }

        [XmlElement("m5-4-literature-references")]
        public m54literaturereferences m54literaturereferences { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-2-tabular-listing-of-all-clinical-studies", Namespace = "", IsNullable = false)]
    public partial class m52tabularlistingofallclinicalstudies
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-clinical-study-reports", Namespace = "", IsNullable = false)]
    public partial class m53clinicalstudyreports
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m5-3-1-reports-of-biopharmaceutic-studies")]
        public m531reportsofbiopharmaceuticstudies m531reportsofbiopharmaceuticstudies { get; set; }

        [XmlElement("m5-3-2-reports-of-studies-pertinent-to-pharmacokinetics-using-human-biomaterials")]
        public m532reportsofstudiespertinenttopharmacokineticsusinghumanbiomaterials m532reportsofstudiespertinenttopharmacokineticsusinghumanbiomaterials { get; set; }

        [XmlElement("m5-3-3-reports-of-human-pharmacokinetics-pk-studies")]
        public m533reportsofhumanpharmacokineticspkstudies m533reportsofhumanpharmacokineticspkstudies { get; set; }

        [XmlElement("m5-3-4-reports-of-human-pharmacodynamics-pd-studies")]
        public m534reportsofhumanpharmacodynamicspdstudies m534reportsofhumanpharmacodynamicspdstudies { get; set; }

        [XmlElement("m5-3-5-reports-of-efficacy-and-safety-studies")]
        public m535reportsofefficacyandsafetystudies[] m535reportsofefficacyandsafetystudies { get; set; }

        [XmlElement("m5-3-6-reports-of-postmarketing-experience")]
        public m536reportsofpostmarketingexperience m536reportsofpostmarketingexperience { get; set; }

        [XmlElement("m5-3-7-case-report-forms-and-individual-patient-listings")]
        public m537casereportformsandindividualpatientlistings m537casereportformsandindividualpatientlistings { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-1-reports-of-biopharmaceutic-studies", Namespace = "", IsNullable = false)]
    public partial class m531reportsofbiopharmaceuticstudies
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m5-3-1-1-bioavailability-study-reports")]
        public m5311bioavailabilitystudyreports m5311bioavailabilitystudyreports { get; set; }

        [XmlElement("m5-3-1-2-comparative-ba-and-bioequivalence-study-reports")]
        public m5312comparativebaandbioequivalencestudyreports m5312comparativebaandbioequivalencestudyreports { get; set; }

        [XmlElement("m5-3-1-3-in-vitro-in-vivo-correlation-study-reports")]
        public m5313invitroinvivocorrelationstudyreports m5313invitroinvivocorrelationstudyreports { get; set; }

        [XmlElement("m5-3-1-4-reports-of-bioanalytical-and-analytical-methods-for-human-studies")]
        public m5314reportsofbioanalyticalandanalyticalmethodsforhumanstudies m5314reportsofbioanalyticalandanalyticalmethodsforhumanstudies { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-1-1-bioavailability-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5311bioavailabilitystudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-1-2-comparative-ba-and-bioequivalence-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5312comparativebaandbioequivalencestudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-1-3-in-vitro-in-vivo-correlation-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5313invitroinvivocorrelationstudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-1-4-reports-of-bioanalytical-and-analytical-methods-for-human-studies", Namespace = "", IsNullable = false)]
    public partial class m5314reportsofbioanalyticalandanalyticalmethodsforhumanstudies
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-2-reports-of-studies-pertinent-to-pharmacokinetics-using-human-biomaterials", Namespace = "", IsNullable = false)]
    public partial class m532reportsofstudiespertinenttopharmacokineticsusinghumanbiomaterials
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m5-3-2-1-plasma-protein-binding-study-reports")]
        public m5321plasmaproteinbindingstudyreports m5321plasmaproteinbindingstudyreports { get; set; }

        [XmlElement("m5-3-2-2-reports-of-hepatic-metabolism-and-drug-interaction-studies")]
        public m5322reportsofhepaticmetabolismanddruginteractionstudies m5322reportsofhepaticmetabolismanddruginteractionstudies { get; set; }

        [XmlElement("m5-3-2-3-reports-of-studies-using-other-human-biomaterials")]
        public m5323reportsofstudiesusingotherhumanbiomaterials m5323reportsofstudiesusingotherhumanbiomaterials { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-2-1-plasma-protein-binding-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5321plasmaproteinbindingstudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-2-2-reports-of-hepatic-metabolism-and-drug-interaction-studies", Namespace = "", IsNullable = false)]
    public partial class m5322reportsofhepaticmetabolismanddruginteractionstudies
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-2-3-reports-of-studies-using-other-human-biomaterials", Namespace = "", IsNullable = false)]
    public partial class m5323reportsofstudiesusingotherhumanbiomaterials
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-3-reports-of-human-pharmacokinetics-pk-studies", Namespace = "", IsNullable = false)]
    public partial class m533reportsofhumanpharmacokineticspkstudies
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m5-3-3-1-healthy-subject-pk-and-initial-tolerability-study-reports")]
        public m5331healthysubjectpkandinitialtolerabilitystudyreports m5331healthysubjectpkandinitialtolerabilitystudyreports { get; set; }

        [XmlElement("m5-3-3-2-patient-pk-and-initial-tolerability-study-reports")]
        public m5332patientpkandinitialtolerabilitystudyreports m5332patientpkandinitialtolerabilitystudyreports { get; set; }

        [XmlElement("m5-3-3-3-intrinsic-factor-pk-study-reports")]
        public m5333intrinsicfactorpkstudyreports m5333intrinsicfactorpkstudyreports { get; set; }

        [XmlElement("m5-3-3-4-extrinsic-factor-pk-study-reports")]
        public m5334extrinsicfactorpkstudyreports m5334extrinsicfactorpkstudyreports { get; set; }

        [XmlElement("m5-3-3-5-population-pk-study-reports")]
        public m5335populationpkstudyreports m5335populationpkstudyreports { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-3-1-healthy-subject-pk-and-initial-tolerability-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5331healthysubjectpkandinitialtolerabilitystudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-3-2-patient-pk-and-initial-tolerability-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5332patientpkandinitialtolerabilitystudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-3-3-intrinsic-factor-pk-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5333intrinsicfactorpkstudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-3-4-extrinsic-factor-pk-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5334extrinsicfactorpkstudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-3-5-population-pk-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5335populationpkstudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-4-reports-of-human-pharmacodynamics-pd-studies", Namespace = "", IsNullable = false)]
    public partial class m534reportsofhumanpharmacodynamicspdstudies
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m5-3-4-1-healthy-subject-pd-and-pk-pd-study-reports")]
        public m5341healthysubjectpdandpkpdstudyreports m5341healthysubjectpdandpkpdstudyreports { get; set; }

        [XmlElement("m5-3-4-2-patient-pd-and-pk-pd-study-reports")]
        public m5342patientpdandpkpdstudyreports m5342patientpdandpkpdstudyreports { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-4-1-healthy-subject-pd-and-pk-pd-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5341healthysubjectpdandpkpdstudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-4-2-patient-pd-and-pk-pd-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5342patientpdandpkpdstudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-5-reports-of-efficacy-and-safety-studies", Namespace = "", IsNullable = false)]
    public partial class m535reportsofefficacyandsafetystudies
    {

        [XmlElement("leaf")]
        public leaf[] leaf { get; set; }

        [XmlElement("m5-3-5-1-study-reports-of-controlled-clinical-studies-pertinent-to-the-claimed-in" +
            "dication")]
        public m5351studyreportsofcontrolledclinicalstudiespertinenttotheclaimedindication m5351studyreportsofcontrolledclinicalstudiespertinenttotheclaimedindication { get; set; }

        [XmlElement("m5-3-5-2-study-reports-of-uncontrolled-clinical-studies")]
        public m5352studyreportsofuncontrolledclinicalstudies m5352studyreportsofuncontrolledclinicalstudies { get; set; }

        [XmlElement("m5-3-5-3-reports-of-analyses-of-data-from-more-than-one-study")]
        public m5353reportsofanalysesofdatafrommorethanonestudy m5353reportsofanalysesofdatafrommorethanonestudy { get; set; }

        [XmlElement("m5-3-5-4-other-study-reports")]
        public m5354otherstudyreports m5354otherstudyreports { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

        [XmlAttribute()]
        public string indication { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-5-1-study-reports-of-controlled-clinical-studies-pertinent-to-the-claimed-in" +
        "dication", Namespace = "", IsNullable = false)]
    public partial class m5351studyreportsofcontrolledclinicalstudiespertinenttotheclaimedindication
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-5-2-study-reports-of-uncontrolled-clinical-studies", Namespace = "", IsNullable = false)]
    public partial class m5352studyreportsofuncontrolledclinicalstudies
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-5-3-reports-of-analyses-of-data-from-more-than-one-study", Namespace = "", IsNullable = false)]
    public partial class m5353reportsofanalysesofdatafrommorethanonestudy
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-5-4-other-study-reports", Namespace = "", IsNullable = false)]
    public partial class m5354otherstudyreports
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-6-reports-of-postmarketing-experience", Namespace = "", IsNullable = false)]
    public partial class m536reportsofpostmarketingexperience
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-3-7-case-report-forms-and-individual-patient-listings", Namespace = "", IsNullable = false)]
    public partial class m537casereportformsandindividualpatientlistings
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m5-4-literature-references", Namespace = "", IsNullable = false)]
    public partial class m54literaturereferences
    {

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "http://www.ich.org/ectd", IsNullable = false)]
    public partial class ectd
    {
        public ectd()
        {
            this.dtdversion = "3.2";
        }

        [XmlElement("m1-administrative-information-and-prescribing-information", Namespace = "")]
        public m1administrativeinformationandprescribinginformation m1administrativeinformationandprescribinginformation { get; set; }

        [XmlElement("m2-common-technical-document-summaries", Namespace = "")]
        public m2commontechnicaldocumentsummaries m2commontechnicaldocumentsummaries { get; set; }

        [XmlElement("m3-quality", Namespace = "")]
        public m3quality m3quality { get; set; }

        [XmlElement("m4-nonclinical-study-reports", Namespace = "")]
        public m4nonclinicalstudyreports m4nonclinicalstudyreports { get; set; }

        [XmlElement("m5-clinical-study-reports", Namespace = "")]
        public m5clinicalstudyreports m5clinicalstudyreports { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

        [XmlAttribute("dtd-version", Form = System.Xml.Schema.XmlSchemaForm.Qualified, Namespace = "")]
        public string dtdversion { get; set; }
    }
}