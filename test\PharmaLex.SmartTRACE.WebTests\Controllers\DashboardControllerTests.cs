﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using NSubstitute;
using NuGet.Protocol;
using PharmaLex.Caching;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.Helpers;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Audit;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.WebTests;
using System.Collections.Concurrent;
using System.Linq.Expressions;
using System.Security.Claims;
using System.Text;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class DashboardControllerTests
    {
        private readonly IAuthorizationService _authorizationService;
        private readonly DashboardController sut;
        public DashboardControllerTests()
        {
            #region TestData and config
            var config = TestHelpers.GetConfiguration();
            var dbCtxReslover = TestHelpers.GetPlxDbContextResolver();
            var dbCtx = (SmartTRACEContext)dbCtxReslover.Context;
            if (!dbCtx.Client.Any(x => x.Id == 100003))
                dbCtx.Client.Add(new Client { Id = 100003, Name = "Test Client", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            if (!dbCtx.User.Any(x => x.Id == 100003))
                dbCtx.User.Add(new User { Id = 100003, Email = "<EMAIL>", GivenName = "Test", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            if (!dbCtx.UserClient.Any(x => x.ClientId == 100003 && x.UserId == 100003))
                dbCtx.UserClient.Add(new UserClient { ClientId = 100003, UserId = 100003, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            if (!dbCtx.Project.Any(x => x.Id == 100003))
                dbCtx.Project.Add(new Project { Id = 100003, ClientId = 100003, Name = "Test Project", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            if (!dbCtx.Submission.Any(x => x.Id == 100003 && x.ProjectId == 100003))
                dbCtx.Submission.Add(new Submission { Id = 100003, ProjectId = 100003, PlannedDispatchDate = DateTime.Now, ActualDispatchDate = DateTime.Now, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            dbCtx.SaveChanges();

            #endregion

            #region Fake Httpcontext with identity
            IHttpContextAccessor httpContextAccessor = new HttpContextAccessor();
            var context = new DefaultHttpContext();
            context.User = new ClaimsPrincipal(new ClaimsIdentity(new List<System.Security.Claims.Claim> { new System.Security.Claims.Claim("emails", "<EMAIL>"),
            new System.Security.Claims.Claim("name:", "test"), new System.Security.Claims.Claim("plx:userid", "100003") }));
            httpContextAccessor.HttpContext = context;
            var userCtx = new PlxUserContext(httpContextAccessor);
            #endregion

            #region Fake Cache
            var cacheKey = config.GetSection("Static:App").Value + "|" + config.GetSection("Static:Env").Value + "|CacheDependencyMap";
            var ds = new ConcurrentDictionary<string, List<string>>(
               new List<KeyValuePair<string, List<string>>> { new KeyValuePair<string, List<string>>("", new List<string> { "" }) }).ToJson();
            var fakeCache = Substitute.For<IDistributedCache>();
            fakeCache.Get(cacheKey).Returns(Encoding.UTF8.GetBytes(ds));
            #endregion

            #region Fake repofactory and Distributed cache serviice
            var repoFactory = new RepositoryFactory(dbCtxReslover, userCtx);

            var cacheService = new DistributedCacheService(fakeCache, config);
            var mapper = new MapperConfiguration(cfg => { cfg.AddProfile<SubmissionMappingProfile>(); }).CreateMapper();
            var distributedCacheServiceFactory = new DistributedCacheServiceFactory(repoFactory, cacheService, mapper, config);
            #endregion

            #region Fake AuthorizationService and Sut
            _authorizationService = Substitute.For<IAuthorizationService>();
            sut = new DashboardController(distributedCacheServiceFactory, _authorizationService);
            sut.ControllerContext.HttpContext = context;
            #endregion

        }

        [Fact]
        public async Task Index_ReturnIndexView()
        {
            //Arrange
            _authorizationService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "Reader").Returns(Task.FromResult(AuthorizationResult.Failed()));
            //Act
            var result = await sut.Index();
            //Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var resultModel = Assert.IsType<DashboardViewModel>(viewResult.Model);
            Assert.Equal("test", resultModel?.CurrentUserName);
        }

        [Fact]
        public async Task Index_ReturnSubmissionDashboard_ForAdminUser()
        {
            //Arrange
            _authorizationService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "Reader").Returns(Task.FromResult(AuthorizationResult.Success()));
            _authorizationService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "Admin").Returns(Task.FromResult(AuthorizationResult.Success()));
            //Act
            var result = Assert.IsType<ViewResult>(await sut.Index());
            //Assert
            Assert.NotNull(result);
            var resultModel = Assert.IsType<DashboardSubmissionViewModel>(result.Model);
            Assert.Equal(1, resultModel?.AllSubmissionsCount);
        }

        [Fact]
        public async Task Index_ReturnSubmissionDashboard_ForNonAdminUser()
        {
            //Arrange
            _authorizationService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "Reader").Returns(Task.FromResult(AuthorizationResult.Success()));
            _authorizationService.AuthorizeAsync(Arg.Is<ClaimsPrincipal>(cp => cp.Identity != null), Arg.Any<object>(), "Admin").Returns(Task.FromResult(AuthorizationResult.Failed()));
            //Act
            var result = await sut.Index() as ViewResult;
            //Assert
            Assert.NotNull(result);
            var resultModel = result.Model as DashboardSubmissionViewModel;
            Assert.Equal(1, resultModel?.AllSubmissionsCount);
        }
    }
}
