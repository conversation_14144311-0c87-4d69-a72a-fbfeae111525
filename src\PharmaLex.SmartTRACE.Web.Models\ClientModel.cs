﻿using AutoMapper;
using PharmaLex.SmartTRACE.Entities;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class ClientModel : IModel
    {
        public int Id { get; set; }

        [Required, StringLength(128)]
        public string Name { get; set; }

        [Required]
        public int ContractOwnerId { get; set; }

        public string ContractOwner { get; set; }

        public bool HasError { get; set; }

        public string ErrorMessage { get; set; }
    }

    public class ClientMappingProfile : Profile
    {
        public ClientMappingProfile()
        {
            this.CreateMap<Client, ClientModel>().ReverseMap();
        }
    }
}
