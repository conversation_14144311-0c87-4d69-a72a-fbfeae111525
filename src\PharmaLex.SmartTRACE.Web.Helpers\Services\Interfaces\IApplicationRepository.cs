﻿using Microsoft.EntityFrameworkCore.Query;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using System.Linq;
using System;

namespace PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces
{
    public interface IApplicationRepository : IRepository<Application>
    {
        IQueryable<Application> GetQueryableItems(Func<IQueryable<Application>, IIncludableQueryable<Application, object>> include = null);
    }
}