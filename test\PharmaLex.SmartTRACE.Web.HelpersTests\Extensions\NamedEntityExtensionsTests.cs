﻿using PharmaLex.SmartTRACE.Web.Helpers.Extensions;
using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Extensions
{
    public class NamedEntityExtensionsTests
    {
        [Fact]
        public void ToSelectList_ConvertsNamedEntityModelsToSelectListItems()
        {
            // Arrange
            var models = new List<NamedEntityModel>
            {
                new NamedEntityModel { Id = 1, Name = "First" },
                new NamedEntityModel { Id = 2, Name = "Second" },
                new NamedEntityModel { Id = 3, Name = "Third" }
            };

            // Act
            var selectListItems = models.ToSelectList().ToList();

            // Assert
            Assert.Equal(3, selectListItems.Count);
            Assert.Equal("First", selectListItems[0].Text);
            Assert.Equal("1", selectListItems[0].Value);
            Assert.Equal("Second", selectListItems[1].Text);
            Assert.Equal("2", selectListItems[1].Value);
            Assert.Equal("Third", selectListItems[2].Text);
            Assert.Equal("3", selectListItems[2].Value);
        }
    }
}
