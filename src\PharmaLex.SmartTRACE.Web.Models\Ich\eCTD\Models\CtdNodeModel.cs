﻿using AutoMapper;
using PharmaLex.SmartTRACE.Entities;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models
{
    public class CtdNodeModel : IModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Title { get; set; }
        public int SortOrder { get; set; }
        public int CtdModuleId { get; set; }
        public string Number { get; set; }
        public List<M1DataModel> Envelope { get; set; } = new List<M1DataModel>();
        public List<LeafModel> Nodes { get; set; } = new List<LeafModel>();
        public List<CtdNodeModel> ChildNodes { get; set; } = new List<CtdNodeModel>();

        public CtdNodeModel()
        {

        }

        public CtdNodeModel(string submission) : this(submission, submission)
        {

        }

        public CtdNodeModel(string submission, string directoryName)
        {
            Title = directoryName;
            Name = directoryName;
            Envelope = new List<M1DataModel>() { new M1DataModel() { Submission = submission, Display = submission } };
        }
    }
}
