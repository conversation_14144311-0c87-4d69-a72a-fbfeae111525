﻿<script type="text/x-template" id="yes-no-dialog-template">
    <div class="dialog-surface" v-if="!closed" v-on:click="close">
        <div class="dialog-container" v-on:click.stop style="display: block; width: auto; height: auto; text-align: center">
            <i class="icon-cancel-circled dialog-closer" v-on:click="close"></i>
            <div style="margin-top:20px;">
            <span :class="this.config.fontSize">{{this.config.message}}</span>
                    <div style="margin-top:40px;">
                        <button v-on:click="close" class="button secondary icon-button-cancel">{{this.config.noButton}}</button>
                        <button v-on:click="submit" :class="this.config.buttonClass">{{this.config.yesButton}}</button>
                    </div>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
    vueApp.component('yes-no-dialog', {
        template: '#yes-no-dialog-template',
        data() {
            return {
                closed: true,
                pressedYesButton: false
            };
        },
        props: {
            config: {
                type: Object,
                default: {}
            }
        },
        methods: {
            submit() {
                this.$emit('button-pressed', !this.pressedYesButton);
                this.closed = true;
            },
            close() {
                this.$emit('button-pressed', this.pressedYesButton);
                this.closed = true;
            },
            open() {
                this.closed = false;
            },
            reAssign() {
                let links = document.getElementsByName(this.config.elementName);

                if (links) {
                    links.forEach(link => {
                        link.removeEventListener('click', this.open);
                        link.addEventListener('click', this.open);
                    });
                }
            }
        },
        mounted() {
            let links = document.getElementsByName(this.config.elementName);

            if (links) {
                links.forEach(link => link.addEventListener('click', this.open));
            }
        },
        unmounted() {
            let links = document.getElementsByName(this.config.elementName);

            if (links) {
                links.forEach(link => link.removeEventListener('click', this.open));
            }
        }
    });
</script>
