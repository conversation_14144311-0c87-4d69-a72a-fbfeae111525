﻿using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using System.Linq;

namespace PharmaLex.SmartTRACE.RemindersApp.Services
{
    public class PickListReminderHelper : IPickListReminderHelper
    {
        public string GetPickListName(IRepositoryFactory repoFactory, int? typeId)
        {
            string name = "";
            var picklistData = repoFactory.Create<PicklistData>().Configure(s => s).ToList();
            var picklistItem = picklistData.FirstOrDefault(x => x.Id == typeId);
            if (picklistItem != null)
            {
                name = picklistItem.Name;
            }
            return name;
        }
    }
}
