﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class AddEstimatedHours : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "EstimatedHours",
                schema: "Audit",
                table: "SubmissionResource_Audit",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "EstimatedHours",
                table: "SubmissionResource",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.SqlFileExec("0008-AddEstimatedHours-UpdateSubmissionResourceTriggers.sql");
            migrationBuilder.SqlFileExec("0008-AddEstimatedHours-TransferAllocatedHoursData.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EstimatedHours",
                schema: "Audit",
                table: "SubmissionResource_Audit");

            migrationBuilder.DropColumn(
                name: "EstimatedHours",
                table: "SubmissionResource");
        }
    }
}
