﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class RemoveUserAdminPermission : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ProcedureNumber",
                schema: "Audit",
                table: "Application_Audit");

            migrationBuilder.DropColumn(
                name: "ProcedureNumber",
                table: "Application");

            migrationBuilder.SqlFileExec("0002-RemoveUsedAdminPermission-RemoveUserAdmin.sql");

            migrationBuilder.SqlFileExec("0002-RemoveUsedAdminPermission-UpdateApplicationTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ProcedureNumber",
                schema: "Audit",
                table: "Application_Audit",
                type: "nvarchar(32)",
                maxLength: 32,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProcedureNumber",
                table: "Application",
                type: "nvarchar(32)",
                maxLength: 32,
                nullable: true);
        }
    }
}
