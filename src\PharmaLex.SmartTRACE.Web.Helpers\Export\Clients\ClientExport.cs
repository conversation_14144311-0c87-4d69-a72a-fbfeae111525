﻿using NPOI.SS.UserModel;
using NPOI.SS.Util;
using PharmaLex.Caching.Data;
using PharmaLex.Office;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public class ClientExport : ExcelWriter, IClientExport
    {
        private readonly IDistributedCacheServiceFactory cache;

        public ClientExport(IDistributedCacheServiceFactory cache)
        {
            this.cache = cache;
        }

        public async Task<byte[]> Export()
        {
            var wb = this.CreateWorkbook();
            ISheet exSheet = wb.Workbook.CreateSheet("Clients");
            int rowIndex = 0;

            var clientCache = this.cache.CreateMappedEntity<Client, ClientModel>();

            var allClients = await clientCache.AllAsync();

            List<string> columnNames = new List<string>
            {
                "Name", "Contract Owner"
            };
            exSheet.CreateRow(rowIndex++, wb.Styles["header"], columnNames.ToArray());

            foreach (var client in allClients.OrderBy(x => x.Name))
            {
                List<string> clientProps = new List<string>()
                {
                    client.Name,
                    (await PicklistHelper.ExtractPicklist(cache, client.ContractOwnerId))?.Name,
                };
                var row = exSheet.CreateRow(rowIndex++);
                for (int i = 0; i < clientProps.Count; i++)
                {
                    row.CreateCell(i, clientProps[i], wb.Styles["wrapped"]);
                }
            }

            exSheet.AutoSizeColumns(0, columnNames.Count);
            exSheet.SetAutoFilter(new CellRangeAddress(0, 0, 0, columnNames.Count - 1));

            return wb.Workbook.ToByteArray();
        }
    }
}
