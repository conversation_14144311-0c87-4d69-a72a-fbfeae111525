﻿using PharmaLex.SmartTRACE.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class SubmissionDetailsModelTests
    {
        [Fact]
        public void SubmissionDetailsModel_Get_SetValue()
        {
            //Arrange
            var model = new SubmissionDetailsModel();
            model.Product = "Test";
            model.ApplicationType = "Test";
            model.ApplicationNumber = "Test123";
            model.ProcedureType = "Test";
            //Assert
            Assert.NotNull(model);
        }
    }
}
