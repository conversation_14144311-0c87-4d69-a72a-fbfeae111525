﻿using Microsoft.AspNetCore.Http;
using Azure.ResourceManager.DataFactory;
using Microsoft.Extensions.Options;
using PharmaLex.Authentication.B2C;
using System.Collections.Generic;
using System.Threading.Tasks;
using Azure.ResourceManager;
using Azure.ResourceManager.DataFactory.Models;
using System;
using Azure.Core;
using Azure.ResourceManager.Resources;
using Microsoft.Graph.Models;

namespace PharmaLex.SmartTRACE.Web.DataFactory
{
    public interface IAzureDataFactoryManagementClient
    {
        string GetUploadPath(string zipFileName = null, string clientId = null, string applicationId = null, string submissionUniqueId = null, string documentTypeId = null, string version = null);
        string GetUnpackPath(string zipFileName = null, string clientId = null, string applicationId = null, string submissionUniqueId = null, string documentTypeId = null, string version = null);
        Task<bool> RunPipelineAsync(IDictionary<string, object> parameters, string pipelineName, ArmClient client = null);
    }
    public class AzureDataFactoryManagementClient : IAzureDataFactoryManagementClient
    {
        private readonly string userEmail;
        private readonly DataFactoryPipelineSettings settings;
        private readonly IDataFactoryAuthenticationPovider authenticationProvider;
        public AzureDataFactoryManagementClient(
            IDataFactoryAuthenticationPovider authenticationProvider,
            IOptions<DataFactoryPipelineSettings> pipelineSettings,
            IHttpContextAccessor httpContextAccessor)
        {
            this.userEmail = httpContextAccessor.HttpContext.User.Identity.GetEmail();
            settings = pipelineSettings.Value;
            this.authenticationProvider = authenticationProvider;
        }

        public async Task<bool> RunPipelineAsync(IDictionary<string, object> parameters, string pipelineName, ArmClient client = null)
        {
            var dataFactoryResource = GetDataFactoryResource(client);
            var pipelineResource = (await dataFactoryResource.GetDataFactoryPipelineAsync(pipelineName)).Value;
            IDictionary<string, BinaryData> keyValuePairs = new Dictionary<string, BinaryData>();
            foreach (var keyValuePair in parameters)
            {
                keyValuePairs.Add(keyValuePair.Key,new BinaryData(keyValuePair.Value));
            }
            var runResponse = (await pipelineResource.CreateRunAsync(parameterValueSpecification: keyValuePairs)).Value;

            DataFactoryPipelineRunInfo pipelineRun;
            while (true)
            {
                pipelineRun =await dataFactoryResource.GetPipelineRunAsync(runResponse.RunId.ToString());
                if (pipelineRun.Status == "InProgress" || pipelineRun.Status == "Queued")
                    System.Threading.Thread.Sleep(2000);
                else
                    return pipelineRun.Status == "Succeeded";
            }
        }

        public string GetUploadPath(string zipFileName = null, string clientId = null, string applicationId = null, 
                                    string submissionUniqueId = null, string documentTypeId = null, string version = null)
        {
            string uploadPath = $"{this.userEmail}";
                
            if (!string.IsNullOrEmpty(clientId) && 
                !string.IsNullOrEmpty(applicationId) && 
                !string.IsNullOrEmpty(submissionUniqueId) && 
                !string.IsNullOrEmpty(documentTypeId) &&
                !string.IsNullOrEmpty(version))
            {
                uploadPath = $"{clientId}/{applicationId}/{submissionUniqueId}/{documentTypeId}/{version}";
            }
            else if (!string.IsNullOrEmpty(clientId) && 
                     !string.IsNullOrEmpty(applicationId) && 
                     !string.IsNullOrEmpty(submissionUniqueId) && 
                     string.IsNullOrEmpty(documentTypeId) && 
                     string.IsNullOrEmpty(version))
            {
                uploadPath = $"{clientId}/{applicationId}/{submissionUniqueId}";
            }

            if (!string.IsNullOrEmpty(zipFileName))
            {
                uploadPath = $"{uploadPath}/{zipFileName}";
            }

            return uploadPath;
        }

        public string GetUnpackPath(string zipFileName = null, 
            string clientId = null, 
            string applicationId = null, 
            string submissionUniqueId = null,
            string documentTypeId = null, 
            string version = null)
        {
            string unpackPath = $"{this.userEmail}";

            if (!string.IsNullOrEmpty(clientId) && 
                !string.IsNullOrEmpty(applicationId) && 
                !string.IsNullOrEmpty(submissionUniqueId) &&
                !string.IsNullOrEmpty(documentTypeId) && 
                !string.IsNullOrEmpty(version))
            {
                unpackPath = $"{clientId}/{applicationId}/{this.settings.DocumentsPath}/{submissionUniqueId}/{documentTypeId}/{version}";
            }
            else if (!string.IsNullOrEmpty(clientId) &&
                    !string.IsNullOrEmpty(applicationId) &&
                    string.IsNullOrEmpty(documentTypeId) && 
                    string.IsNullOrEmpty(version))
            {
                unpackPath = $"{clientId}/{applicationId}/{this.settings.AllSequencesPath}";
            }

            if (!string.IsNullOrEmpty(zipFileName))
            {
                unpackPath = $"{unpackPath}/{zipFileName}";
            }

            return unpackPath;
        }

        private DataFactoryResource GetDataFactoryResource(ArmClient client)
        {
            client = client ?? new ArmClient(authenticationProvider.CreateClientCrdential(), settings.Subscription);
            ResourceIdentifier resourceIdentifier = DataFactoryResource.CreateResourceIdentifier(settings.Subscription, settings.ResourceGroupName, settings.FactoryName);
            var dataFactoryResource = client.GetDataFactoryResource(resourceIdentifier);
            return dataFactoryResource;
        }
    }
}
