﻿using PharmaLex.DataAccess;
using System;

namespace PharmaLex.SmartTRACE.Entities.Audit
{
    public partial class RegulatoryAuthorityAudit: AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }

        public int? Id { get; set; }
        public string Name { get; set; }
        public int? Market { get; set; }
        public string Url { get; set; }
        public bool? NationalAuthority { get; set; }
        public string Acronym { get; set; }
    }
}
