﻿@model CountryModel

<div id="country" class="manage-container">
    <header class="manage-header">
        <h3>@Model.Name Country</h3>
        @if (Model.Id != 0)
        {
            <a class="button icon-button-delete" href="/countries/delete/@Model.Id">Delete</a>
        }
        <a href="/regions/edit/@Model.RegionId" class="button secondary icon-button-back">Back to Country list</a>
    </header>
    <form id="country-form" method="post">
        <div class="form-col">
            <label for="Name">Name*</label>
            <input asp-for="Name" type="text" v-bind:class="{'validation-error' : hasError}" required />
            <div id="nameValidationMessage" style="color:red;padding:10px"></div>
            @if (Model.HasError)
            {
                <div id="duplicateNameMessage" class="field-duplication-error">@Model.ErrorMessage</div>
            }
            <label for="TwoLetterCode">Two Letter Code</label>
            <input asp-for="TwoLetterCode" type="text" />
            <div id="twoLetterCodeValidationMessage" style="color:red;padding:10px"></div>
        </div>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/regions/edit/@Model.RegionId">Cancel</a>
            <button class="icon-button-save">Save</button>
        </div>
        <input type="hidden" asp-for="Id" />
        <input type="hidden" asp-for="RegionId" />
    </form>
</div>

@section Scripts {
    <script src="@Cdn.GetUrl("lib/jquery/dist/jquery.min.js")"></script>
    <script type="text/javascript">
        var pageConfig = {
            appElement: '#country',
            data() {
                return {
                    hasError: @Model.HasError.ToString().ToLower()
                };
            },
            mounted() {
                // Add a submit event listener to the form
                document.getElementById('country-form').addEventListener('submit', this.handleSubmit);
                document.getElementById('Name').addEventListener('change', this.handleChange('Name', 'nameValidationMessage'));
                document.getElementById('TwoLetterCode').addEventListener('change', this.handleChange('TwoLetterCode', 'twoLetterCodeValidationMessage'));
            },
            methods: {
                handleSubmit(event) {
                    var validName = this.validateInput(event, 'Name', 'nameValidationMessage');
                    var validTwoLetterCode = this.validateInput(event, 'TwoLetterCode', 'twoLetterCodeValidationMessage');
                    return validName && validTwoLetterCode;
                },
                validateInput(event, elementName, validationMessage) {
                    var inputValue = $(`#${elementName}`).val();
                    var unmatchedChars = findUnmatchedCharacters(inputValue);
                    if (unmatchedChars._value.length > 0) {
                        event.preventDefault();
                        $(`#${validationMessage}`).text(`${elementName} contains invalid characters: ` + unmatchedChars._value.join(' '));
                        $(`#${validationMessage}`).css('color', 'red');
                        $("#duplicateNameMessage").text('');
                        return false;
                    }
                    $(`#${validationMessage}`).text('');
                    return true;
                },
                handleChange(elementName, validationMessage) {
                    $(`#${elementName}`).change(function () {
                        $(`#${validationMessage}`).text('');
                    })
                }
            }
        };
    </script>
}