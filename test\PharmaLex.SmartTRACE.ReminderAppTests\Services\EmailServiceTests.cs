﻿using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.RemindersApp.Models;
using PharmaLex.SmartTRACE.RemindersApp.Services;
using Moq;
using SendGrid;
using SendGrid.Helpers.Mail;
using PharmaLex.SmartTRACE.WebTests;
using System.Net;
using SendGrid.Permissions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Models;
namespace PharmaLex.SmartTRACE.ReminderAppTests.Services
{
    public class EmailServiceTests
    {
        private readonly IConfiguration _configuration;
        public EmailServiceTests()
        {
            _configuration = Substitute.For<IConfiguration>();
        }
        [Fact]
        public async Task SendEmail_Task_Returns_UnauthorisedException()
        {

            //Arrange
            NotificationModel notificationModel = new NotificationModel()
            {
                To = "<EMAIL>",
                Subject = "sub",
                Submission = "submission app",
                Days = 30,
                Url = "url",
                Application = "app"
            };

            var configuration = new ConfigurationBuilder()
               .AddInMemoryCollection(
               [
                   new KeyValuePair<string, string?>("SendGrid", "test-Key"),
                   new KeyValuePair<string, string?>("emailsSendingEnabled", "true"),
                   new KeyValuePair<string, string?>("SystemAdminEmail","<EMAIL>")
               ])
               .Build();
            var emailService = new EmailService(configuration);
            string res = "{\"ErrorHttpStatusCode\":401,\"ErrorReasonPhrase\":\"Unauthorized\",\"SendGridErrorMessage\":\"The provided authorization grant is invalid, expired, or revoked\",\"FieldWithError\":null,\"HelpLink\":null}";

            //Act       
            var ex = await Assert.ThrowsAsync<SendGrid.Helpers.Errors.Model.UnauthorizedException>(() => emailService.SendAsync(notificationModel, "001"));

            // Assert
            Assert.Equal(res, ex.Message);
        }

        [Fact]
        public async Task SendEmail_Task_Returns_OkResponse()
        {

            //Arrange
            NotificationModel notificationModel = new NotificationModel()
            {
                To = "<EMAIL>",
                Subject = "sub",
                Submission = "submission app",
                Days = 30,
                Url = "url",
                Application = "app"
            };

            var configuration = new ConfigurationBuilder()
               .AddInMemoryCollection(
               [
                   new KeyValuePair<string, string?>("SendGrid", "test-Key"),
                   new KeyValuePair<string, string?>("emailsSendingEnabled", "false"),
                   new KeyValuePair<string, string?>("SystemAdminEmail","<EMAIL>")
               ])
               .Build();
            var emailService = new EmailService(configuration);
            string res = "{\"ErrorHttpStatusCode\":401,\"ErrorReasonPhrase\":\"Unauthorized\",\"SendGridErrorMessage\":\"The provided authorization grant is invalid, expired, or revoked\",\"FieldWithError\":null,\"HelpLink\":null}";

            //Act       
            var ex = await emailService.SendAsync(notificationModel, "001");

            // Assert
            Assert.True(ex.Item1);
            Assert.Equal(ex.Item2.ToString(), HttpStatusCode.OK.ToString());
        }

    }
}
