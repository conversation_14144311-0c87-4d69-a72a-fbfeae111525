﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Helpers.Constants;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using PharmaLex.SmartTRACE.Web.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers;

[Authorize("BusinessAdmin")]
[Route("/")]
public class PicklistDataController(IDistributedCacheServiceFactory cache, IMapper mapper, IPicklistDataService picklistDataService) : BaseController
{
    [HttpGet("data/{picklistTypeId}")]
    public async Task<IActionResult> PicklistData(int picklistTypeId)
    {
        if (!this.ModelState.IsValid)
        {
            return this.BadRequest(this.ModelState);
        }
        var pickListDataCountries = (await cache.CreateEntity<PicklistDataCountry>()
                                        .Configure(c => c
                                            .Include(i => i.PicklistData)
                                            .Include(i => i.Country))
                                    .WhereAsync(x => x.PicklistData.PicklistTypeId == picklistTypeId)).ToList();

        var picklistData = pickListDataCountries
        .Select(x => new
        {
            Id = x.PicklistData.Id,
            Name = x.PicklistData.Name,
            PicklistTypeId = x.PicklistData.PicklistTypeId,
            Country = x.Country.Name,
        })
        .GroupBy(x => new { x.Id, x.Name, x.PicklistTypeId })
        .Select(x => new PicklistDataModel
        {
            Id = x.Key.Id,
            Name = x.Key.Name,
            PicklistTypeId = x.Key.PicklistTypeId,
            Country = string.Join(", ", x.Select(c => c.Country).OrderBy(x => x)),
        })
        .ToList();

        var picklistItems = await cache.CreateMappedEntity<PicklistData, PicklistDataModel>().WhereAsync(x => x.PicklistTypeId == picklistTypeId);
        var picklistsWithoutCountries = picklistItems.Where(x => !picklistData.Select(c => c.Id).Contains(x.Id));
        picklistData.AddRange(picklistsWithoutCountries);

        return View(new ListPicklistDataViewModel()
        {
            PicklistData = picklistData,
            PicklistTypeId = picklistTypeId
        });
    }

    [HttpGet, Route("data/new/{picklistTypeId}")]
    public async Task<IActionResult> New(int picklistTypeId)
    {
        if (!this.ModelState.IsValid)
        {
            return this.BadRequest(this.ModelState);
        }
        var picklistItem = new EditPicklistDataViewModel()
        {
            Picklist = new PicklistDataModel()
            {
                PicklistTypeId = picklistTypeId
            },
            AllCountries = await cache.CreateMappedEntity<Country, CountryModel>().AllAsync()
        };

        return View("EditPicklistData", picklistItem);
    }

    [HttpPost, Route("data/new/{picklistTypeId}"), ValidateAntiForgeryToken]
    public async Task<IActionResult> New(int picklistTypeId, EditPicklistDataViewModel model)
    {
        if (!this.ModelState.IsValid)
        {
            model.AllCountries = await cache.CreateMappedEntity<Country, CountryModel>().AllAsync();
            return View("EditPicklistData", model);
        }

        if (await picklistDataService.IsPicklistNameDuplicate(model.Picklist.Name))
        {
            await SetErrorMessage(model);
            return View("EditPicklistData", model);
        }

        var picklistDataCache = cache.CreateTrackedEntity<PicklistData>();
        var picklistItem = mapper.Map<PicklistDataModel, PicklistData>(model.Picklist);
        picklistItem.PicklistTypeId = picklistTypeId;
        picklistDataCache.Add(picklistItem);
        await picklistDataCache.SaveChangesAsync();


        var picklistCountryCache = cache.CreateTrackedEntity<PicklistDataCountry>();
        foreach (var id in model.Picklist.CountriesIds)
        {
            picklistCountryCache.Add(new PicklistDataCountry()
            {
                PicklistDataId = picklistItem.Id,
                CountryId = id
            });
        }

        await picklistCountryCache.SaveChangesAsync();
        this.AddConfirmationNotification($"<em>{picklistItem.Name}</em> created");

        return Redirect($"/data/{picklistTypeId}");
    }

    [HttpGet, Route("data/edit/{id}")]
    public async Task<IActionResult> Edit(int id)
    {
        if (!this.ModelState.IsValid)
        {
            return this.BadRequest(this.ModelState);
        }
        var picklistDataCache = cache.CreateMappedEntity<PicklistData, EditPicklistDataViewModel>();
        var picklistCountryCache = cache.CreateEntity<PicklistDataCountry>();
        var picklistItem = await picklistDataCache.FirstOrDefaultAsync(x => x.Id == id);
        var picklistCountries = await picklistCountryCache.WhereAsync(x => x.PicklistDataId == id);

        foreach (var picklistCountry in picklistCountries)
        {
            picklistItem.Picklist.CountriesIds.Add(picklistCountry.CountryId);
        }

        picklistItem.AllCountries = await cache.CreateMappedEntity<Country, CountryModel>().AllAsync();
        return View("EditPicklistData", picklistItem);
    }

    [HttpPost, Route("data/edit/{id}"), ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, EditPicklistDataViewModel model)
    {
        if (!this.ModelState.IsValid)
        {
            model.AllCountries = await cache.CreateMappedEntity<Country, CountryModel>().AllAsync();
            return View("EditPicklistData", model);
        }

        if (await picklistDataService.IsPicklistNameDuplicate(model.Picklist.Name))
        {
            await SetErrorMessage(model);
            return View("EditPicklistData", model);
        }

        var picklistDataCache = cache.CreateTrackedEntity<PicklistData>();
        var picklistItem = await picklistDataCache.FirstOrDefaultAsync(x => x.Id == id);
        mapper.Map(model.Picklist, picklistItem);
        await picklistDataCache.SaveChangesAsync();

        var picklistCountryCache = cache.CreateTrackedEntity<PicklistDataCountry>();
        var picklistCountries = await picklistCountryCache.WhereAsync(x => x.PicklistDataId == id);
        var missingCountries = model.Picklist.CountriesIds.Where(x => !picklistCountries.Select(y => y.CountryId).Contains(x)).ToList();

        foreach (var picklistCountry in missingCountries)
        {
            picklistCountryCache.Add(new PicklistDataCountry
            {
                CountryId = picklistCountry,
                PicklistDataId = id
            });
        }

        var deletedPicklistCountries = picklistCountries.Where(x => !model.Picklist.CountriesIds.Select(y => y).Contains(x.CountryId)).ToList();

        foreach (var deletedPicklistCountry in deletedPicklistCountries)
        {
            picklistCountryCache.Remove(deletedPicklistCountry);
        }

        await picklistCountryCache.SaveChangesAsync();
        this.AddConfirmationNotification($"<em>{picklistItem.Name}</em> updated");

        return Redirect($"/data/{picklistItem.PicklistTypeId}");
    }

    [HttpGet("data/delete/{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        if (!this.ModelState.IsValid)
        {
            return this.BadRequest(this.ModelState);
        }
        var picklistDataCache = cache.CreateMappedEntity<PicklistData, PicklistDataModel>();
        var picklistItem = await picklistDataCache.FirstOrDefaultAsync(x => x.Id == id);
        return View("DeletePicklistData", picklistItem);
    }

    [HttpPost("data/delete/{id}"), ValidateAntiForgeryToken]
    public async Task<IActionResult> ConfirmedDelete(int id)
    {
        if (!this.ModelState.IsValid)
        {
            return this.BadRequest(this.ModelState);
        }
        var picklistDataCache = cache.CreateTrackedEntity<PicklistData>();
        var picklistItem = await picklistDataCache.FirstOrDefaultAsync(x => x.Id == id);
        picklistDataCache.Remove(picklistItem);
        await picklistDataCache.SaveChangesAsync();
        this.AddConfirmationNotification($"<em>{picklistItem.Name}</em> deleted");
        return Redirect($"/data/{picklistItem.PicklistTypeId}");
    }

    [HttpPost, Route("data/update-picklist"), Authorize(Policy = "Editor"), ValidateAntiForgeryToken]
    public async Task<ActionResult<IList<PicklistDataModel>>> UpdatePicklistData([FromBody] IList<CountryModel> countries)
    {
        if (!this.ModelState.IsValid)
        {
            return this.BadRequest(this.ModelState);
        }
        var picklists = await PicklistHelper.GetPicklistDataBasedOnCountry(cache, countries);
        return Ok(picklists);
    }

    [HttpGet, Route("data/countries/{procedureTypeId}"), Authorize(Policy = "Editor")]
    public async Task<IList<CountryModel>> GetCountriesPerProcedureType(int procedureTypeId)
    {
        if (!this.ModelState.IsValid)
        {
            return (IList<CountryModel>)this.BadRequest(this.ModelState);
        }
        var procedureTypeCache = cache.CreateEntity<PicklistData>()
                                       .Configure(o => o
                                          .Include(x => x.PicklistDataCountry)
                                            .ThenInclude(x => x.Country));
        var currentProcedureType = await procedureTypeCache.FirstOrDefaultAsync(x => x.Id == procedureTypeId);
        var countries = mapper.Map<IList<CountryModel>>(currentProcedureType.PicklistDataCountry.Select(x => x.Country));
        return countries.OrderBy(x => x.Name).ToList();
    }

    private async Task SetErrorMessage(EditPicklistDataViewModel model)
    {
        model.HasError = true;
        model.ErrorMessage = ErrorMessages.UniqueErrorMessage;
        model.AllCountries = await cache.CreateMappedEntity<Country, CountryModel>().AllAsync();
    }
}
