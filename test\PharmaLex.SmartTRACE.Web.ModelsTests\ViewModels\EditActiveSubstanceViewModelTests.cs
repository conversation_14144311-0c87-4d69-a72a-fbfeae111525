﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class EditActiveSubstanceViewModelTests
    {
        [Fact]
        public void DashboardViewModel_Get_SetValue()
        {
            //Arrange
            var model = new EditActiveSubstanceViewModel();
            model.IsProductAssigned = true;
            model.ActiveSubstance=new ActiveSubstanceModel();
            model.Clients = new List<ClientModel>();
            //Assert
            Assert.NotNull(model);
        }
    }
}
