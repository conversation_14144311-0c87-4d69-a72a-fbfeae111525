﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using NSubstitute;
using NuGet.Protocol;
using PharmaLex.Caching;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Data.Persistance.Repository;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.WebTests;
using SendGrid.Helpers.Mail;
using System.Collections.Concurrent;
using System.Security.Claims;
using System.Text;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Services
{
    public class SubmissionServiceTests
    {
        private readonly AppSubmissionsRepository _submissionsRepository;
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IAuthorizationService _authorizationService;
        public readonly SubmissionFilterModel submissionFilterModel = new SubmissionFilterModel()
        {
            ApplicationNumber = "121",
            ClientName = "client2",
            Description = "des",
            RegulatoryLead = "<EMAIL>",
            LifecycleState = "Planned",
            SequenceNumber = "001",
            SubmissionUnit = "1",
            DossierFormat = "format1,format2",
            SubmissionMode = "1",
            SubmissionType = "4"
        };
        private readonly SmartTRACEContext dbCtx;
       private readonly ClaimsPrincipal user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
      {
                     new System.Security.Claims.Claim(ClaimTypes.Name, "newuser"),
                     new System.Security.Claims.Claim(ClaimTypes.Role, "Reader"),
                     new System.Security.Claims.Claim("plx:userid","6")
      }));
        public SubmissionServiceTests()
        {
            _authorizationService = Substitute.For<IAuthorizationService>();
            #region Fake Distributed Cache Service and DbContextReslover
            var config = TestHelpers.GetConfiguration();
            var dbctxReslover = TestHelpers.GetPlxDbContextResolver();
            #region insert data
            dbCtx = ((SmartTRACEContext)dbctxReslover.Context);
            if (!dbCtx.UserClient.Any(x => x.ClientId == 10))
            {
                dbCtx.UserClient.Add(
                    new UserClient { ClientId = 10, UserId = 6, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            }
            if (!dbCtx.ApplicationProduct.Any(x => x.Id == 1111))
            {
                dbCtx.ApplicationProduct.Add(
                    new ApplicationProduct
                    {
                        Id = 1111,
                        ApplicationId = 99,
                        ProductId = 111,
                        CreatedBy = "test",
                        LastUpdatedBy = "test",
                        Product = new Product() { Id = 11123, Name = "prod1", ClientId = 10, CreatedBy = "test", LastUpdatedBy = "test" }
                    });

            }
            List<SubmissionPublisher> publisherList = new List<SubmissionPublisher>()
            {
                new SubmissionPublisher(){
                    Id=2, CreatedBy = "test", LastUpdatedBy = "test", Publisher=
                new User(){Id=34, FamilyName="sd",GivenName="test",Email="<EMAIL>" ,UserTypeId=3, CreatedBy = "test", LastUpdatedBy = "test"} } };
            if (!dbCtx.Submission.Any(x => x.Id == 33))
            {
                dbCtx.Submission.Add(
                    new Submission
                    {
                        DeliveryDetailsId = 2,
                        RelatedSequenceNumber = "342",
                        SourceDocumentsLocation = "loc",
                        ArchivedDocumentsLocation = "arcloc",
                        HealthAuthorityDueDate = DateTime.Now,
                        AuthoringDeadline = DateTime.Now,
                        PlannedDispatchDate = DateTime.Now,
                        PlannedSubmissionDate = DateTime.Now,
                        ActualDispatchDate = DateTime.Now,
                        ActualSubmissionDate = DateTime.Now,
                        DocubridgeVersionId = "2",
                        CespNumber = "23",
                        SerialNumber = "34",
                        ReferenceNumber = "45",
                        Comments = "comment",
                        Id = 33,
                        SequenceNumber = "001",
                        SubmissionUnitId = 15,
                        SubmissionModeId = 12,
                        LifecycleStateId = 2,
                        UniqueId = "1",
                        DossierFormatId = 11,
                        Description = "des",
                        SubmissionTypeId = 14,
                        ProjectId = 44,
                        CreatedBy = "test",
                        LastUpdatedBy = "test",
                        Project = new Project()
                        {
                            Id = 44,
                            Name = "project2",
                            Code = "23",
                            OpportunityNumber = "3456",
                            Client = new Client() { Id = 985, UserClient = new List<UserClient>() { new UserClient() { ClientId = 342, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>", UserId = 4262, Client = new Client() { Id = 876, Name = "baseclient", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" } } }, Name = "client242", CreatedBy = "test", LastUpdatedBy = "test", ContractOwnerId = 2 },
                            CreatedBy = "test",
                            LastUpdatedBy = "test"
                        },
                        Document = new List<Document>()
                        { new Document(){
                            Id=3, LastUpdatedBy= "test", DocumentTypeId=3,CreatedBy = "test" ,Name="docname"} },
                        Application = new Application()
                        {
                            Id = 99,
                            ApplicationNumber = "121",
                            CreatedBy = "test",
                            LastUpdatedBy = "test"
                        },
                        SubmissionResource = new SubmissionResource()
                        {
                            Id = 67,
                            PriorityId = 2,
                            EstimatedSizeId = 2,
                            PublishingTeamId = 2,
                            InitialSentToEmail = "test",
                            Comments = "comment",
                            RegulatoryLead = "<EMAIL>",
                            SubmissionPublisher = publisherList,
                            CreatedBy = "test",
                            LastUpdatedBy = "test"
                        }
                    });

            }
            if (!dbCtx.SubmissionPublisher.Any(x => x.Id == 55))
            {
                dbCtx.SubmissionPublisher.Add(new SubmissionPublisher
                {
                    Id = 55,
                    Publisher =
                    new User() { Id = 001, Email = "<EMAIL>", GivenName = "test", FamilyName = "fam name", CreatedBy = "test", LastUpdatedBy = "test" },
                    SubmissionResource = new SubmissionResource()
                    {
                        Id = 77,
                        SubmissionId = 33,
                        PriorityId = 2,
                        EstimatedSizeId = 2,
                        PublishingTeamId = 2,
                        InitialSentToEmail = "test",
                        Comments = "comment",
                        RegulatoryLead = "<EMAIL>",
                        CreatedBy = "test",
                        LastUpdatedBy = "test",
                        EstimatedHours = 2,
                        PublishingLead = "test"
                    },
                    CreatedBy = "test",
                    LastUpdatedBy = "test"
                });

            }
            if (!dbCtx.SubmissionCountry.Any(x => x.Id == 66))
            {
                dbCtx.SubmissionCountry.Add(new SubmissionCountry { Id = 66, SubmissionId = 33, CreatedBy = "test", LastUpdatedBy = "test", Country = new Country() { Id = 35, Name = "ind", CreatedBy = "test", LastUpdatedBy = "test" }, CountryId = 66 });

            }
            if (!dbCtx.PicklistData.Any(x => x.PicklistTypeId == 2 || x.PicklistTypeId == 7 || x.PicklistTypeId == 10 || x.PicklistTypeId == 9 || x.PicklistTypeId == 99))
            {
                dbCtx.PicklistData.AddRange(new List<PicklistData> {
                new PicklistData {Id=10, Name = "1", PicklistTypeId = 2, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData { Id=11,Name = "format1", PicklistTypeId = 7, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData {Id=12, Name = "1", PicklistTypeId = 10, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData { Id=13,Name = "1", PicklistTypeId = 2, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData {Id=14, Name = "4", PicklistTypeId = 99, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData {Id=15, Name = "1", PicklistTypeId = 9, CreatedBy = "test", LastUpdatedBy="test" },
                });
            }

            dbCtx.SaveChanges();
            #endregion
            _submissionsRepository = new AppSubmissionsRepository(dbCtx, Substitute.For<IUserContext>());
            IRepositoryFactory repoFactory = new RepositoryFactory(dbctxReslover, Substitute.For<IUserContext>());

            var configuration = new MapperConfiguration(cfg => cfg.AddProfile<PicklistDataMappingProfile>());
            var mapper = configuration.CreateMapper();
            var cacheKey = config.GetSection("Static:App").Value + "|" + config.GetSection("Static:Env").Value + "|CacheDependencyMap";
            var ds = new ConcurrentDictionary<string, List<string>>(
              new List<KeyValuePair<string, List<string>>> { new KeyValuePair<string, List<string>>("", new List<string> { "" }) }).ToJson();
            var cache = Substitute.For<IDistributedCache>();
            cache.Get(cacheKey).Returns(Encoding.UTF8.GetBytes(ds));
            var cacheService = new DistributedCacheService(cache, config);
            _cache = new DistributedCacheServiceFactory(repoFactory, cacheService, mapper, config);
            #endregion
            

        }

        [Fact]
        public async Task GetAllSubmissionData_ReturnsListModel_ForBusinessAdmin()
        {
            // Arrange
           
            _authorizationService.AuthorizeAsync(user, "BusinessAdmin").Returns(Task.FromResult(AuthorizationResult.Success()));

            SubmissionService submissionService = new SubmissionService(_cache, _authorizationService, _submissionsRepository);

            // Act
            var result = await submissionService.GetAllSubmissionData(user, submissionFilterModel);

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);
            Assert.Equal("client242", result[0].ClientName);
            Assert.Equal("format1", result[0].DossierFormat);
            Assert.Equal("Planned", result[0].LifecycleState);
            Assert.Equal("2", result[0].DocubridgeVersionId);
        }
        [Fact]
        public async Task GetAllSubmissionData_ReturnsListModel()
        {
            // Arrange
           
            _authorizationService.AuthorizeAsync(user, "BusinessAdmin").Returns(Task.FromResult(AuthorizationResult.Failed()));

            SubmissionService submissionService = new SubmissionService(_cache, _authorizationService, _submissionsRepository);

            // Act
            var result = await submissionService.GetAllSubmissionData(user, submissionFilterModel);

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);
            Assert.Equal("client242", result[0].ClientName);
            Assert.Equal("format1", result[0].DossierFormat);
            Assert.Equal("Planned", result[0].LifecycleState);
            Assert.Equal("2", result[0].DocubridgeVersionId);
        }
        [Fact]
        public async Task GetSubmission_ReturnsSubmissionModel()
        {
            // Arrange
           
            _authorizationService.AuthorizeAsync(user, "BusinessAdmin").Returns(Task.FromResult(AuthorizationResult.Success()));

            SubmissionService submissionService = new SubmissionService(_cache, _authorizationService, _submissionsRepository);

            // Act
            var result = await submissionService.GetSubmission(33);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(33, result.Id);
            Assert.Equal("1", result.UniqueId);
            Assert.Equal("comment", result.Comments);
            Assert.Equal("2", result.DocubridgeVersionId);
        }
        [Fact]
        public async Task GetPagedSubmissionsAsync_ReturnsModel()
        {
            // Arrange
           
            _authorizationService.AuthorizeAsync(user, "BusinessAdmin").Returns(Task.FromResult(AuthorizationResult.Success()));

            SubmissionService submissionService = new SubmissionService(_cache, _authorizationService, _submissionsRepository);

            // Act
            var result = await submissionService.GetPagedSubmissionsAsync(user, 0, 1, submissionFilterModel, null);

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result.Data);
            Assert.Equal("121", result.Data[0].ApplicationNumber);
            Assert.Equal("Planned", result.Data[0].LifecycleState);
            Assert.Equal("format1", result.Data[0].DossierFormat);
        }


    }
}
