﻿using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class EditClientViewModel: IModel
    {
        public EditClientViewModel()
        {
            this.Products = new List<ProductModel>();
            this.Projects = new List<ProjectModel>();
            this.AllContractOwners = new List<PicklistDataModel>();
        }

        public ClientModel Client { get; set; }

        public ProjectModel Project { get; set; }

        public IList<ProductModel> Products { get; set; }

        public IList<ProjectModel> Projects { get; set; }

        public IList<PicklistDataModel> AllContractOwners { get; set; }
    }
}
