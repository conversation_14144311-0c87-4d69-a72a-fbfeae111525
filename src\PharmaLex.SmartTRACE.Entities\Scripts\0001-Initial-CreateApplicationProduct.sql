﻿CREATE TRIGGER [dbo].[ApplicationProduct_Insert] ON [dbo].[ApplicationProduct]
FOR INSERT AS
INSERT INTO [Audit].[ApplicationProduct_Audit]
SELECT 'I', [ApplicationId], [ProductId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[ApplicationProduct_Update] ON [dbo].[ApplicationProduct]
FOR UPDATE AS
INSERT INTO [Audit].[ApplicationProduct_Audit]
SELECT 'U', [ApplicationId], [ProductId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[ApplicationProduct_Delete] ON [dbo].[ApplicationProduct]
FOR DELETE AS
INSERT INTO [Audit].[ApplicationProduct_Audit]
SELECT 'D', [ApplicationId], [ProductId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO