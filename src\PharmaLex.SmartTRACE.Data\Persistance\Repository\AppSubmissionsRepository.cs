﻿using Microsoft.EntityFrameworkCore.Query;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using System;
using System.Linq;

namespace PharmaLex.SmartTRACE.Data.Persistance.Repository
{
    public class AppSubmissionsRepository: TrackingGenericRepository<Submission>, IAppSubmissionsRepository
    {
        public AppSubmissionsRepository(SmartTRACEContext context, IUserContext userContext) : base(context, userContext.User)
        {
        }

        public IQueryable<Submission> GetQueryableItems(Func<IQueryable<Submission>, IIncludableQueryable<Submission, object>> include = null)
        {
            var query = context.Set<Submission>().AsQueryable();
            if (include != null)
            {
                query = include(query);
            }

            return query;
        }
    }
}
