﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using System.Text;

namespace PharmaLex.SmartTRACE.WebTests
{
    public static class TestHelpers
    {
        public static SmartTRACEContext GetTestDbContext()
        {
            var options = new DbContextOptionsBuilder<SmartTRACEContext>()
            .UseInMemoryDatabase(databaseName: "PlxDatabase")
            .UseInternalServiceProvider(new ServiceCollection()
            .AddEntityFrameworkInMemoryDatabase()
            .BuildServiceProvider())
            .Options;
            var userCtx = Substitute.For<IUserContext>();
            userCtx.User.Returns("<EMAIL>");
            var dbService = Substitute.For<IDbConnectionService>();
            var dbContext = new SmartTRACEContext(options, userCtx, dbService);
            dbContext.Database.EnsureDeleted();
            dbContext.Database.EnsureCreated();
            return dbContext;
        }

        public static IPlxDbContextResolver GetPlxDbContextResolver()
        {
            var dbctxReslover = Substitute.For<IPlxDbContextResolver>();
            var db = GetTestDbContext();
            dbctxReslover.Context.Returns(db);
            return dbctxReslover;
        }
        public static IConfiguration GetConfiguration() 
        {
            var configJson = "{\r\n  \"Logging\": {\r\n    \"LogLevel\": {\r\n      \"Default\": \"Warning\"\r\n    }\r\n  },\r\n  \"AzureAdB2C\": {\r\n    \"Instance\": \"https://testb2c.b2clogin.com\",\r\n    \"ClientId\": \"53f4fe97-71df-48ad-9bd9-3c6555487872\",\r\n    \"Domain\": \"smartphlexb2c.onmicrosoft.com\",\r\n    \"SignedOutCallbackPath\": \"/signout/B2C_1_signin\",\r\n    \"SignUpSignInPolicyId\": \"B2C_1_signup_signin_plx\",\r\n    \"ResetPasswordPolicyId\": \"b2c_1_password_reset\",\r\n    \"EditProfilePolicyId\": \"b2c_1_profile_edit\",\r\n    \"CallbackPath\": \"/signin-oidc\"\r\n  },\r\n  \"AzureAdB2CPolicy\": {\r\n    \"Tenant\": \"smartphlexb2c\",\r\n    \"TenantId\": \"f8b90da3-e024-492c-800e-1b793397f942\",\r\n    \"ClientId\": \"53f4fe97-71df-48ad-9bd9-3c6555487872\",\r\n    \"SigningCertThumbprint\": \"E1BA3541BA2553B9BC40927F1359F264E1724D61\",\r\n    \"LinkExpiresAfterDays\": 2,\r\n    \"Policies\": [\r\n      {\r\n        \"PolicyId\": \"B2C_1A_signup_invitation\",\r\n        \"CallbackPath\": \"/signin-oidc-invite\",\r\n        \"ApplicationCallbackPath\": \"/signup-invitation\"\r\n      },\r\n      {\r\n        \"PolicyId\": \"B2C_1_signin_local\",\r\n        \"CallbackPath\": \"/signin-local\"\r\n      },\r\n      {\r\n        \"PolicyId\": \"B2C_1_signin_federated\",\r\n        \"CallbackPath\": \"/signin-federated\"\r\n      }\r\n    ],\r\n    \"Domains\": [\r\n      [ \"pharmalex.com\", \"yespharmaservices.onmicrosoft.com\" ]\r\n    ]\r\n  },\r\n  \"AzureAdGraph\": {\r\n    \"ClientId\": \"e014e4a5-078f-4095-9d8f-6e07dd8ed25d\",\r\n    \"Domain\": \"yes-services.eu\",\r\n    \"ClientSecret\": \"\",\r\n    \"TenantId\": \"66b904a2-2bfc-4d24-a410-96b77b32bf77\"\r\n\r\n  },\r\n  \"AzureAdB2CGraph\": {\r\n    \"ClientId\": \"5c76d17d-764a-4e8e-bef7-c39afd6fb3bd\",\r\n    \"Domain\": \"smartphlexb2c.onmicrosoft.com\",\r\n    \"ClientSecret\": \"\"\r\n  },\r\n  \"DataFactoryPipeline\": {\r\n    \"Instance\": \"https://management.azure.com/\",\r\n    \"Subscription\": \"8f05cf92-4915-4f15-9950-74d44cde4bba\",\r\n    \"ResourceGroupName\": \"rg-str-dev-eun\",\r\n    \"FactoryName\": \"str-dev-adf-eun\",\r\n    \"UnpackPipelineName\": \"Unpack_Dev\",\r\n    \"UploadPath\": \"Uploaded\",\r\n    \"UnpackPath\": \"Unpacked\",\r\n    \"AllSequencesPath\": \"All\",\r\n    \"DocumentsPath\": \"Documents\",\r\n    \"TenantId\": \"66b904a2-2bfc-4d24-a410-96b77b32bf77\"\r\n  },\r\n  \"AppSettings\": {\r\n    \"BuildInfo\": \"Build:local\",\r\n    \"BuildNumber\": \"local\",\r\n    \"SystemAdminEmail\": \"<EMAIL>\",\r\n    \"SmartPHLEXAdminEmail\": \"<EMAIL>\",\r\n    \"SubmissionRequestStateChangedTemplateId\": \"d-50d955f84f744f0e9a9da4438f5c46b2\",\r\n    \"ExternalUserLoginEmailTemplateId\": \"d-a636d0c4e3974d17a7b162297ed41bd4\",\r\n    \"ExternalUserSignUpEmailTemplateId\": \"d-0424359a314842c69eb1fd328b2a65fe\",\r\n    \"SubmissionSourceDocumentsUploadedTemplateId\": \"d-c3fb71d0861f41b38496d793b368bc93\",\r\n    \"SmartTraceConfigurableEmail\": \"<EMAIL>\",\r\n    \"HomeScreen\": \"/dashboard\",\r\n    \"MaxTime\": \"100\"\r\n  },\r\n  \"DashboardLinks\": {\r\n    \"SystemAccessLink\": \"test/overview.aspx\",\r\n    \"TraceEmailLink\": \"mailto:<EMAIL>\",\r\n    \"LocalBusinessAdministratorLink\": \"https://test.com\"\r\n  },\r\n  \"Static\": {\r\n    \"App\": \"smarttrace\",\r\n    \"Env\": \"dev\",\r\n    \"Cdn\": \"https://test-endpoint.test.net\",\r\n    \"Version\": \"v1.35.0\",\r\n    \"Container\": \"content\"\r\n},\r\n  \"ConnectionStrings\": {\r\n    \"default\": \"Server=(TestDb)\\\\MSSQLLocalDB; Database=SmartTRACE; Trusted_connection=true; MultipleActiveResultSets=True;\"\r\n  },\r\n  \"SqlOptions\": {\r\n    \"EnableDetailedErrors\": true,\r\n    \"TenantId\": \"66b904a2-2bfc-4d24-a410-96b77b32bf77\"\r\n  },\r\n  \"AzureStorage\": {\r\n    \"Account\": \"strdevsharedeun\",\r\n    \"Container\": \"smarttrace\",\r\n    \"ConnectionString\": \"\",\r\n    \"TenantId\": \"66b904a2-2bfc-4d24-a410-96b77b32bf77\",\r\n    \"Url\": \"https://test.blob.core.windows.net/smarttrace\"\r\n  },\r\n  \"KeyVaultName\": \"str-dev-kv-eun\",\r\n  \"Migrations\": {\r\n    \"Assembly\": \"PharmaLex.SmartTRACE.Data\"\r\n  },\r\n  \"AllowedHosts\": \"*\"\r\n}\r\n";
            var configuration = new ConfigurationBuilder().AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(configJson))).Build();
            return configuration;
        }
    }
}
