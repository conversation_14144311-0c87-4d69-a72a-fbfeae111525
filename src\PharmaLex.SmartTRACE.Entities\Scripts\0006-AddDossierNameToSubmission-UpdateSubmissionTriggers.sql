﻿ALTER TRIGGER [dbo].[Submission_Insert] ON [dbo].[Submission]
FOR INSERT AS
INSERT INTO [Audit].[Submission_Audit]
SELECT 'I', [Id], [UniqueId], [DeliveryDetailsId], [SequenceNumber], [RelatedSequenceNumber], [DossierFormatId], [SerialNumber], [ReferenceNumber], [SubmissionTypeId], [SubmissionUnitId], [SubmissionModeId], [Description], [LifecycleStateId], [Comments], [HealthAuthorityDueDate], [AuthoringDeadline], [PlannedDispatchDate], [ActualDispatchDate], [PlannedSubmissionDate], [ActualSubmissionDate], [CespNumber], [WithdrawalDate], [SourceDocumentsLocation], [ArchivedDocumentsLocation], [ApplicationId], [ProjectId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [PreviousLifecycleStateId], [DossierName] FROM [Inserted]
GO

ALTER TRIGGER [dbo].[Submission_Update] ON [dbo].[Submission]
FOR UPDATE AS
INSERT INTO [Audit].[Submission_Audit]
SELECT 'U', [Id], [UniqueId], [DeliveryDetailsId], [SequenceNumber], [RelatedSequenceNumber], [DossierFormatId], [SerialNumber], [ReferenceNumber], [SubmissionTypeId], [SubmissionUnitId], [SubmissionModeId], [Description], [LifecycleStateId], [Comments], [HealthAuthorityDueDate], [AuthoringDeadline], [PlannedDispatchDate], [ActualDispatchDate], [PlannedSubmissionDate], [ActualSubmissionDate], [CespNumber], [WithdrawalDate], [SourceDocumentsLocation], [ArchivedDocumentsLocation], [ApplicationId], [ProjectId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [PreviousLifecycleStateId], [DossierName] FROM [Inserted]
GO

ALTER TRIGGER [dbo].[Submission_Delete] ON [dbo].[Submission]
FOR DELETE AS
INSERT INTO [Audit].[Submission_Audit]
SELECT 'D', [Id], [UniqueId], [DeliveryDetailsId], [SequenceNumber], [RelatedSequenceNumber], [DossierFormatId], [SerialNumber], [ReferenceNumber], [SubmissionTypeId], [SubmissionUnitId], [SubmissionModeId], [Description], [LifecycleStateId], [Comments], [HealthAuthorityDueDate], [AuthoringDeadline], [PlannedDispatchDate], [ActualDispatchDate], [PlannedSubmissionDate], [ActualSubmissionDate], [CespNumber], [WithdrawalDate], [SourceDocumentsLocation], [ArchivedDocumentsLocation], [ApplicationId], [ProjectId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()), [PreviousLifecycleStateId], [DossierName] FROM [Deleted]
GO