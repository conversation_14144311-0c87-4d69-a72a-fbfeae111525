﻿CREATE TRIGGER [dbo].[Claim_Insert] ON [dbo].[Claim]
FOR INSERT AS
INSERT INTO [Audit].[Claim_Audit]
SELECT 'I', [Id], [Name], [ClaimType], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Claim_Update] ON [dbo].[Claim]
FOR UPDATE AS
INSERT INTO [Audit].[Claim_Audit]
SELECT 'U', [Id], [Name], [ClaimType], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[Claim_Delete] ON [dbo].[Claim]
FOR DELETE AS
INSERT INTO [Audit].[Claim_Audit]
SELECT 'D', [Id], [Name], [ClaimType], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO

INSERT INTO [dbo].[Claim] SELECT 'SuperAdmin', 'admin', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[Claim] SELECT 'UserAdmin', 'admin', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[Claim] SELECT 'BusinessAdmin', 'admin', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[Claim] SELECT 'Editor', 'application', GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[Claim] SELECT 'Reader', 'application', GETDATE(), 'update script', GETDATE(), 'update script'
GO