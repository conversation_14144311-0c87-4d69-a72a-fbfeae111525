﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class EditProductViewModelTests
    {
        [Fact]
        public void EditProductViewModel_Get_SetValue()
        {
            //Arrange
            var model = new EditProductViewModel();
            model.IsApplicationAssigned = true;
            model.Product=new ProductModel();
            model.ActiveSubstances = new List<ActiveSubstanceModel>();
            model.AllClients = new List<ClientModel>();
            model.Picklists = new List<PicklistDataModel>();
            //Assert
            Assert.NotNull(model);

        }
    }
}
