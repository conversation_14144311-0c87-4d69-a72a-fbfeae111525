﻿using PharmaLex.SmartTRACE.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class UserClientsModelTests
    {
        [Fact]
        public void UserClientsModel_Get_SetValue()
        {
            //Arrange
            var model = new UserClientsModel();
            model.User = new UserModel();
            model.Clients =new List<ClientModel>();
            //Assert
            Assert.NotNull(model);
        }
    }
}
