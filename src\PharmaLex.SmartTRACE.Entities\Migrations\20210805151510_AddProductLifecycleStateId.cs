﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class AddProductLifecycleStateId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "LifecycleStateId",
                table: "Product",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.AddColumn<int>(
                name: "LifecycleStateId",
                schema: "Audit",
                table: "Product_Audit",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.SqlFileExec("0018-AddProductLifecycleStateId_UpdateProductTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LifecycleStateId",
                schema: "Audit",
                table: "Product_Audit");

            migrationBuilder.DropColumn(
                name: "LifecycleStateId",
                table: "Product");
        }
    }
}
