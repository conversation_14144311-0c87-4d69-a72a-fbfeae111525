﻿using System.ComponentModel;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.Eu301
{
    public enum Language
    {
        [Description("Bulgarian (bg)")]
        bg,
        [Description("Czech (cs)")]
        cs,
        [Description("Danish (da)")]
        da,
        [Description("German (de)")]
        de,
        [Description("Greek (el)")]
        el,
        [Description("English (en)")]
        en,
        [Description("Spanish (es)")]
        es,
        [Description("Estonian (et)")]
        et,
        [Description("Finnish (fi)")]
        fi,
        [Description("French (fr)")]
        fr,
        [Description("Croatian (hr)")]
        hr,
        [Description("Hungarian (hu)")]
        hu,
        [Description("Icelandic (is)")]
        @is,
        [Description("Italianitbg)")]
        it,
        [Description("Lithuanian (lt)")]
        lt,
        [Description("Latvian (lv)")]
        lv,
        [Description("Maltese (mt)")]
        mt,
        [Description("Dutch (nl)")]
        nl,
        [Description("Norwegian (no)")]
        no,
        [Description("Polish (pl)")]
        pl,
        [Description("Portuguese (pt)")]
        pt,
        [Description("Romanian (ro)")]
        ro,
        [Description("Slovak (sk)")]
        sk,
        [Description("Slovenian (sl)")]
        sl,
        [Description("Swedish (sv)")]
        sv
    }
}
