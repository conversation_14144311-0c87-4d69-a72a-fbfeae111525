﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>
  
  <ItemGroup>
    <Content Include="TextTemplates/**/*.pp">
      <Pack>true</Pack>
      <PackagePath>contentFiles\any\any\</PackagePath>
      <CopyToOutputDirectory>true</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  
  <ItemGroup>
    <None Remove="Scripts\0001-Initial-CreateActiveSubstance.sql" />
    <None Remove="Scripts\0001-Initial-CreateActiveSubstanceProduct.sql" />
    <None Remove="Scripts\0001-Initial-CreateApplication.sql" />
    <None Remove="Scripts\0001-Initial-CreateApplicationCountry.sql" />
    <None Remove="Scripts\0001-Initial-CreateApplicationProduct.sql" />
    <None Remove="Scripts\0001-Initial-CreateClaims.sql" />
    <None Remove="Scripts\0001-Initial-CreateClient.sql" />
    <None Remove="Scripts\0001-Initial-CreateCountry.sql" />
    <None Remove="Scripts\0001-Initial-CreatePicklistData.sql" />
    <None Remove="Scripts\0001-Initial-CreateProduct.sql" />
    <None Remove="Scripts\0001-Initial-CreateProject.sql" />
    <None Remove="Scripts\0001-Initial-CreateRegion.sql" />
    <None Remove="Scripts\0001-Initial-CreateRegulatoryAuthority.sql" />
    <None Remove="Scripts\0001-Initial-CreateRegulatoryAuthorityCountry.sql" />
    <None Remove="Scripts\0001-Initial-CreateSubmission.sql" />
    <None Remove="Scripts\0001-Initial-CreateSubmissionCountry.sql" />
    <None Remove="Scripts\0001-Initial-CreateSubmissionPublisher.sql" />
    <None Remove="Scripts\0001-Initial-CreateSubmissionResource.sql" />
    <None Remove="Scripts\0001-Initial-CreateUserClaims.sql" />
    <None Remove="Scripts\0001-Initial-CreateUsers.sql" />
    <None Remove="Scripts\0001-Initial-SetupCountryData.sql" />
    <None Remove="Scripts\0001-Initial-SetupPicklistCountryData.sql" />
    <None Remove="Scripts\0001-Initial-SetupPicklistData.sql" />
    <None Remove="Scripts\0001-Initial-SetupRegionData.sql" />
    <None Remove="Scripts\0002-RemoveUsedAdminPermission-RemoveUserAdmin.sql" />
    <None Remove="Scripts\0002-RemoveUsedAdminPermission-UpdateApplicationTriggers.sql" />
    <None Remove="Scripts\0003-AddInitialSentToEmail-UpdateSubmissionResourceTriggers.sql" />
    <None Remove="Scripts\0004-AddNewProcedureTypes-InsertProcedureTypes.sql" />
    <None Remove="Scripts\0005-AddPreviousSubmissionState-PopulatePreviousSubmissionState.sql" />
    <None Remove="Scripts\0005-AddPreviousSubmissionState-UpdateSubmissionTriggers.sql" />
    <None Remove="Scripts\0006-AddDossierNameToSubmission-UpdateSubmissionTriggers.sql" />
    <None Remove="Scripts\0007-AddUserClient-CreateUserClient.sql" />
    <None Remove="Scripts\0008-AddEstimatedHours-TransferAllocatedHoursData.sql" />
    <None Remove="Scripts\0008-AddEstimatedHours-UpdateSubmissionResourceTriggers.sql" />
    <None Remove="Scripts\0009-RemoveAllocatedHours-UpdateSubmissionResourceTriggers.sql" />
    <None Remove="Scripts\0010-AddUserAdminRole-AddUserAdmin.sql" />
    <None Remove="Scripts\0011-AddUserTypeAndAutoAccessClients-UpdateUserTriggers.sql" />
    <None Remove="Scripts\0012-AddCommentsToApplication-UpdateApplicationTriggers.sql" />
    <None Remove="Scripts\0013-AddDocumentTable-CreateDocument.sql" />
    <None Remove="Scripts\0013-AddDocumentTable-CreateDossierArchivesInDocumentTable.sql" />
    <None Remove="Scripts\0014-RemoveDossierName-UpdateSubmissionTriggers.sql" />
    <None Remove="Scripts\0015-AddUniqueUserEmailConstraint.sql" />
    <None Remove="Scripts\0016-AddExternalReaderRole-ChangeUserAcessToExternalReader.sql" />
    <None Remove="Scripts\0016-AddExternalReaderRole-InsertExternalReader.sql" />
    <None Remove="Scripts\0017-AddApplicationLifecycleStateId-UpdateApplicationTriggers.sql" />
    <None Remove="Scripts\0017-AddApplicationLifecycleStateId_InsertActiveState.sql" />
    <None Remove="Scripts\0018-AddProductLifecycleStateId_UpdateProductTriggers.sql" />
    <None Remove="Scripts\0019-AddActiveSubstanceLifecycleStateId_UpdateActiveSubstanceTriggers.sql" />
    <None Remove="Scripts\0020-AddDocshifterDocumentFileTable-CreateTriggers.sql" />
    <None Remove="Scripts\0021-AddDocubridgeVersionId-CreateTriggers.sql" />
    <None Remove="Scripts\0022-AddDocumentFileStateId-TransferDocumentFileStates.sql" />
    <None Remove="Scripts\0022-AddDocumentFileStateId-UpdateDocshifterDocumentFileTriggers.sql" />
    <None Remove="Scripts\0023-RemoveConvertedColumn-UpdateDocshifterDocumentFileTriggers.sql" />
    <None Remove="Scripts\0024-AddPriviligedExternalReaderRole-AddNewRole.sql" />
    <None Remove="Scripts\0025-RenameExternalReaderAndPrivilegedExternalReader-UpdateClaimValues.sql" />
    <None Remove="Scripts\0026-UpdatePicklistData-InsertNewPublishingTeam.sql" />
    <None Remove="Scripts\0027-AddUniqueNameConstraint-AddUniqueIndex.sql" />
    <None Remove="Scripts\0028-AddUserEmailLink-AlterUserTriggers.sql" />
  </ItemGroup>
  
  <ItemGroup>
    <EmbeddedResource Include="Scripts\0001-Initial-CreateActiveSubstance.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateActiveSubstanceProduct.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateApplication.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateApplicationCountry.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateApplicationProduct.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateClaims.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateClient.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateCountry.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreatePicklistData.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateProduct.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateProject.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateRegion.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateRegulatoryAuthority.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateRegulatoryAuthorityCountry.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateSubmission.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateSubmissionCountry.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateSubmissionPublisher.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateSubmissionResource.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateUserClaims.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-CreateUsers.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-SetupCountryData.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-SetupPicklistCountryData.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-SetupPicklistData.sql" />
    <EmbeddedResource Include="Scripts\0001-Initial-SetupRegionData.sql" />
    <EmbeddedResource Include="Scripts\0002-RemoveUsedAdminPermission-RemoveUserAdmin.sql" />
    <EmbeddedResource Include="Scripts\0002-RemoveUsedAdminPermission-UpdateApplicationTriggers.sql" />
    <EmbeddedResource Include="Scripts\0003-AddInitialSentToEmail-UpdateSubmissionResourceTriggers.sql" />
    <EmbeddedResource Include="Scripts\0004-AddNewProcedureTypes-InsertProcedureTypes.sql" />
    <EmbeddedResource Include="Scripts\0005-AddPreviousSubmissionState-PopulatePreviousSubmissionState.sql" />
    <EmbeddedResource Include="Scripts\0005-AddPreviousSubmissionState-UpdateSubmissionTriggers.sql" />
    <EmbeddedResource Include="Scripts\0006-AddDossierNameToSubmission-UpdateSubmissionTriggers.sql" />
    <EmbeddedResource Include="Scripts\0007-AddUserClient-CreateUserClient.sql" />
    <EmbeddedResource Include="Scripts\0008-AddEstimatedHours-TransferAllocatedHoursData.sql" />
    <EmbeddedResource Include="Scripts\0008-AddEstimatedHours-UpdateSubmissionResourceTriggers.sql" />
    <EmbeddedResource Include="Scripts\0009-RemoveAllocatedHours-UpdateSubmissionResourceTriggers.sql" />
    <EmbeddedResource Include="Scripts\0010-AddUserAdminRole-AddUserAdmin.sql" />
    <EmbeddedResource Include="Scripts\0011-AddUserTypeAndAutoAccessClients-UpdateUserTriggers.sql" />
    <EmbeddedResource Include="Scripts\0012-AddCommentsToApplication-UpdateApplicationTriggers.sql" />
    <EmbeddedResource Include="Scripts\0013-AddDocumentTable-CreateDocument.sql" />
    <EmbeddedResource Include="Scripts\0013-AddDocumentTable-CreateDossierArchivesInDocumentTable.sql" />
    <EmbeddedResource Include="Scripts\0014-RemoveDossierName-UpdateSubmissionTriggers.sql" />
    <EmbeddedResource Include="Scripts\0015-AddUniqueUserEmailConstraint.sql" />
    <EmbeddedResource Include="Scripts\0016-AddExternalReaderRole-ChangeUserAcessToExternalReader.sql" />
    <EmbeddedResource Include="Scripts\0016-AddExternalReaderRole-InsertExternalReader.sql" />
    <EmbeddedResource Include="Scripts\0017-AddApplicationLifecycleStateId-UpdateApplicationTriggers.sql" />
    <EmbeddedResource Include="Scripts\0017-AddApplicationLifecycleStateId_InsertActiveState.sql" />
    <EmbeddedResource Include="Scripts\0018-AddProductLifecycleStateId_UpdateProductTriggers.sql" />
    <EmbeddedResource Include="Scripts\0019-AddActiveSubstanceLifecycleStateId_UpdateActiveSubstanceTriggers.sql" />
    <EmbeddedResource Include="Scripts\0020-AddDocshifterDocumentFileTable-CreateTriggers.sql" />
    <EmbeddedResource Include="Scripts\0021-AddDocubridgeVersionId-CreateTriggers.sql" />
    <EmbeddedResource Include="Scripts\0022-AddDocumentFileStateId-TransferDocumentFileStates.sql" />
    <EmbeddedResource Include="Scripts\0022-AddDocumentFileStateId-UpdateDocshifterDocumentFileTriggers.sql" />
    <EmbeddedResource Include="Scripts\0023-RemoveConvertedColumn-UpdateDocshifterDocumentFileTriggers.sql" />
    <EmbeddedResource Include="Scripts\0024-AddPriviligedExternalReaderRole-AddNewRole.sql" />
    <EmbeddedResource Include="Scripts\0025-RenameExternalReaderAndPrivilegedExternalReader-UpdateClaimValues.sql" />
    <EmbeddedResource Include="Scripts\0026-UpdatePicklistData-InsertNewPublishingTeam.sql" />
	<EmbeddedResource Include="Scripts\0027-AddUniqueNameConstraint-AddUniqueIndex.sql" />
	<EmbeddedResource Include="Scripts\0028-AddUserEmailLink-AlterUserTriggers.sql" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.10" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
    <PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.258" />
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
  </ItemGroup>

</Project>
