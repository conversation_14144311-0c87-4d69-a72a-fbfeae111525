﻿using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class ProductModel : IModel
    {
        public ProductModel()
        {
            this.ActiveSubstances = new List<ActiveSubstanceModel>();
        }
        public int Id { get; set; }

        [Required, StringLength(256)]
        public string Name { get; set; }

        [Required]
        public int DosageFormId { get; set; }

        public string DosageForm { get; set; }
        [Required, StringLength(64)]
        public string Strength { get; set; }
        public int LifecycleStateId { get; set; }
        public string LifecycleState { get; set; }

        public int ClientId { get; set; }

        public string ClientName { get; set; }

        public IList<ActiveSubstanceModel> ActiveSubstances { get; set; }

        public bool HasError { get; set; }

        public string ErrorMessage { get; set; }
    }
    public class ProductLifecycleStateList : NamedEntityModel
    {
    }
    public class ProductMappingProfile : Profile
    {
        public ProductMappingProfile()
        {
            this.CreateMap<Product, ProductModel>()
                .ForMember(d => d.ActiveSubstances, s => s.MapFrom(x => x.ActiveSubstanceProduct.Select(a => a.ActiveSubstance)))
                .ForMember(d => d.ClientName, s => s.MapFrom(x => x.Client.Name));
            this.CreateMap<ProductModel, Product>()
                .ForMember(d => d.Id, s => s.Ignore());
            this.CreateMap<Product, EditProductViewModel>()
                .ForMember(d => d.Product, s => s.MapFrom(x => x));
            this.CreateMap<CommonLifecycleState, ProductLifecycleStateList>()
                .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
                .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));
        }
    }

}
