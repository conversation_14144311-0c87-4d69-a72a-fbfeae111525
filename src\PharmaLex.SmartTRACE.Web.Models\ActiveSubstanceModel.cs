﻿using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class ActiveSubstanceModel: IModel
    {
        public int Id { get; set; }

        [Required, StringLength(256)]
        public string Name { get; set; }
        public int ClientId { get; set; }
        public string ClientName { get; set; }
        public int LifecycleStateId { get; set; }
        public string LifecycleState { get; set; }
        public bool HasDuplicate { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class ActiveSubstanceLifecycleStateList : NamedEntityModel
    {
    }

    public class ActiveSubstanceMappingProfile : Profile
    {
        public ActiveSubstanceMappingProfile()
        {
            this.CreateMap<ActiveSubstance, ActiveSubstanceModel>()
                .ForMember(d => d.ClientName, s => s.MapFrom(x => x.Client.Name))
                .ForMember(d => d.LifecycleState, s => s.MapFrom(x => ((CommonLifecycleState)x.LifecycleStateId).ToString()));
            this.CreateMap<ActiveSubstanceModel, ActiveSubstance>()
                .ForMember(d => d.Client, s => s.Ignore());
            this.CreateMap<CommonLifecycleState, ActiveSubstanceLifecycleStateList>()
                .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
                .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));
        }
    }
}
