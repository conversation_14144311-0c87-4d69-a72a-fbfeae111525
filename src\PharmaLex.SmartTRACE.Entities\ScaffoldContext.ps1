﻿# PowerShell Command
Scaffold-DbContext "Server=(local);Database=SmartTRACE;Trusted_Connection=True;" Microsoft.EntityFrameworkCore.SqlServer -force -schemas "dbo" -outputDir Entities -noOnConfiguring -namespace PharmaLex.SmartTRACE.Entities
Scaffold-DbContext "Server=(local);Database=SmartTRACE;Trusted_Connection=True;" Microsoft.EntityFrameworkCore.SqlServer -force -schemas "Audit" -outputDir Audit -noOnConfiguring

#EF commands

# Update Db
dotnet ef database update --project src\SmartTRACE.Data --startup-project src\SmartTRACE.Web
dotnet ef migrations add {MigrationName} --project src\SmartTRACE.Data --startup-project src\SmartTRACE.Web
dotnet ef migrations remove {MigrationName} --project src\SmartTRACE.Data --startup-project src\SmartTRACE.Web
dotnet ef migrations script --project src\SmartTRACE.Data --startup-project src\SmartTRACE.Web