﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class DocumentModelTests
    {
        [Fact]
        public void DocumentModel_Get_SetValue()
        {
            //Arrange
            var model = new DocumentModel();
            model.Id = 1;
            model.Name = "test";
            model.DocumentTypeId = 1;
            model.DocumentType = "test";
            model.Version = 1;
            model.CreatedBy = "test";
            model.CreatedDate = DateTime.Now;   
            model.FilesCount=1;
            model.SubmissionId = 1;
            model.ShowUploadInfo = true;
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void DocumentMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new DocumentMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
    }
}
