﻿using NSubstitute;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Security.Claims;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Export.Submissions
{
    public class SubmissionExportTests
    {
        private readonly ISubmissionService submissionService;

        public SubmissionExportTests()
        {
            submissionService = Substitute.For<ISubmissionService>();
        }

        [Fact]
        public async Task Export_Submission_ReturnsData()
        {
            string exportcols = "[{\"columnType\":\"resourcesColumn\",\"columnName\":\"Regulatory Lead\"},{\"columnType\":\"datesColumn\",\"columnName\":\"Created By\"},{\"columnType\":\"clientDetailsColumn\",\"columnName\":\"Client Name\"},{\"columnType\":\"generalColumn\",\"columnName\":\"Unique Id\"},{\"columnType\":\"documentColumn\",\"columnName\":\"Location of Source Documents\"}]";
            // Arrange
            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
          {
                 new System.Security.Claims.Claim(ClaimTypes.Name, "testuser"),
                 new System.Security.Claims.Claim(ClaimTypes.Role, "Reader")
          }));
            SubmissionResourceModel submissionResource = new SubmissionResourceModel() { Id = 12, RegulatoryLead = "testlead" };
            IList<SubmissionViewModel> submissionViewModels = new List<SubmissionViewModel>()
            { new SubmissionViewModel(){ Id=1, SubmissionType="sd", ProjectId=3, UniqueId="33", ClientName="testc", CreatedBy="clint", AuthoringDeadline=DateTime.Now, SourceDocumentsLocation="path", ArchivedDocumentsLocation="sts",RegulatoryLead="lead",SubmissionResource=submissionResource } };
            SubmissionFilterModel model = new SubmissionFilterModel()
            { ApplicationNumber = "001", ClientName = "testclient", Description = "TestCountry", LifecycleState = "prodname", SubmissionType = "reg", ExportColumns = exportcols };
            submissionService.GetAllSubmissionData(user, model).Returns(submissionViewModels);

            // Act
            SubmissionExport subExport = new SubmissionExport(submissionService);
            var result = await subExport.Export(user, model);

            Assert.NotNull(result);
            // Assert

        }
        [Fact]
        public async Task Export_Product_Returns_DateTypeRow()
        {
            string exportcols = "[{\"columnType\":\"generalColumn\",\"columnName\":\"Unique Id\"},{\"columnType\":\"generalColumn\",\"columnName\":\"Countries\"},{\"columnType\":\"generalColumn\",\"columnName\":\"Submission Comments\"},{\"columnType\":\"clientDetailsColumn\",\"columnName\":\"Client Name\"},{\"columnType\":\"datesColumn\",\"columnName\":\"Created By\"},{\"columnType\":\"resourcesColumn\",\"columnName\":\"Regulatory Lead\"},{\"columnType\":\"documentColumn\",\"columnName\":\"Location of Source Documents\"}]";
            // Arrange
            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
          {
                 new System.Security.Claims.Claim(ClaimTypes.Name, "testuser"),
                 new System.Security.Claims.Claim(ClaimTypes.Role, "Reader")
          }));
            SubmissionResourceModel submissionResource = new SubmissionResourceModel() { Id = 12, RegulatoryLead = "testlead", Comments = "comm", DisplayPublishers = "s1" };
            IList<SubmissionViewModel> submissionViewModels = new List<SubmissionViewModel>()
            { new SubmissionViewModel(){ Id=1, SubmissionType="sd",DisplayPublishers="s1", ProjectId=3, UniqueId="33", ClientName="testc", CreatedBy="clint", AuthoringDeadline=new DateTime(2023,05,09,9,15,0), SourceDocumentsLocation="path", ArchivedDocumentsLocation="sts",RegulatoryLead="lead",SubmissionResource=submissionResource,CountriesIds=[1,2],DisplayCountries="ind" } };
            SubmissionFilterModel model = new SubmissionFilterModel()
            { ApplicationNumber = "001", DisplayPublishers = "s1", ClientName = "testclient", Description = "TestCountry", LifecycleState = "prodname", SubmissionType = "reg", ExportColumns = exportcols };
            submissionService.GetAllSubmissionData(user, model).Returns(submissionViewModels);

            // Act
            SubmissionExport subExport = new SubmissionExport(submissionService);
            var result = await subExport.Export(user, model);

            Assert.NotNull(result);
            // Assert

        }
        [Fact]
        public async Task Export_Product_Returns_rRow()
        {
            string exportcols = "[{\"columnType\":\"generalColumn\",\"columnName\":\"Unique Id\"},{\"columnType\":\"clientDetailsColumn\",\"columnName\":\"Client Name\"},{\"columnType\":\"datesColumn\",\"columnName\":\"Created Date\"},{\"columnType\":\"resourcesColumn\",\"columnName\":\"Publishing Lead\"},{\"columnType\":\"documentColumn\",\"columnName\":\"Location of Source Documents\"}]";
            // Arrange
            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
          {
                 new System.Security.Claims.Claim(ClaimTypes.Name, "testuser"),
                 new System.Security.Claims.Claim(ClaimTypes.Role, "Reader")
          }));
            SubmissionResourceModel submissionResource = new SubmissionResourceModel() { Id = 12, RegulatoryLead = "testlead", Comments = "comm", DisplayPublishers = "s1" };
            IList<SubmissionViewModel> submissionViewModels = new List<SubmissionViewModel>()
            { new SubmissionViewModel(){ Id=1, SubmissionType="sd",DisplayPublishers="s1", ProjectId=3, UniqueId="33", ClientName="testc", CreatedBy="clint", CreatedDate=new DateTime(2023,05,09,9,15,0), SourceDocumentsLocation="path", ArchivedDocumentsLocation="sts",RegulatoryLead="lead",SubmissionResource=submissionResource,CountriesIds=[1,2],DisplayCountries="ind" } };
            SubmissionFilterModel model = new SubmissionFilterModel()
            { ApplicationNumber = "001", DisplayPublishers = "s1", ClientName = "testclient", Description = "TestCountry", LifecycleState = "prodname", SubmissionType = "reg", ExportColumns = exportcols };
            submissionService.GetAllSubmissionData(user, model).Returns(submissionViewModels);

            // Act
            SubmissionExport subExport = new SubmissionExport(submissionService);
            var result = await subExport.Export(user, model);

            Assert.NotNull(result);
            // Assert

        }
    }
}
