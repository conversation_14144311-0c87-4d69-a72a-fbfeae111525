﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class RemoveAllocatedHours : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AllocatedHours",
                schema: "Audit",
                table: "SubmissionResource_Audit");

            migrationBuilder.DropColumn(
                name: "AllocatedHours",
                table: "SubmissionResource");

            migrationBuilder.SqlFileExec("0009-RemoveAllocatedHours-UpdateSubmissionResourceTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AllocatedHours",
                schema: "Audit",
                table: "SubmissionResource_Audit",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "AllocatedHours",
                table: "SubmissionResource",
                type: "int",
                nullable: true);
        }
    }
}
