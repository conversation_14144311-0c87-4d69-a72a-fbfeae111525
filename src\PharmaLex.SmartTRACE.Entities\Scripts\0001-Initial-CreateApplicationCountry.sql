﻿CREATE TRIGGER [dbo].[ApplicationCountry_Insert] ON [dbo].[ApplicationCountry]
FOR INSERT AS
INSERT INTO [Audit].[ApplicationCountry_Audit]
SELECT 'I', [ApplicationId], [CountryId], [AuthorityRoleId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[ApplicationCountry_Update] ON [dbo].[ApplicationCountry]
FOR UPDATE AS
INSERT INTO [Audit].[ApplicationCountry_Audit]
SELECT 'U', [ApplicationId], [CountryId], [AuthorityRoleId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[ApplicationCountry_Delete] ON [dbo].[ApplicationCountry]
FOR DELETE AS
INSERT INTO [Audit].[ApplicationCountry_Audit]
SELECT 'D', [ApplicationId], [CountryId], [AuthorityRoleId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO