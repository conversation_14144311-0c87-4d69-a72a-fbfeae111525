﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class RemoveDossierName : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
                schema: "Audit",
                table: "Submission_Audit");

            migrationBuilder.DropColumn(
                name: "DossierName",
                table: "Submission");

            migrationBuilder.SqlFileExec("0014-RemoveDossierName-UpdateSubmissionTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
                schema: "Audit",
                table: "Submission_Audit",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Dossier<PERSON><PERSON>",
                table: "Submission",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
