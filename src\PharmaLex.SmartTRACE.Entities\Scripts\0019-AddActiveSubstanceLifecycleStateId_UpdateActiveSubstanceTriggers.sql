﻿ALTER TRIGGER[dbo].[ActiveSubstance_Insert] ON[dbo].[ActiveSubstance]
FOR INSERT AS
INSERT INTO [Audit].[ActiveSubstance_Audit]
SELECT 'I', [Id], [Name], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LifecycleStateId] FROM [Inserted]
GO

ALTER TRIGGER [dbo].[ActiveSubstance_Update] ON[dbo].[ActiveSubstance]
FOR UPDATE AS
INSERT INTO [Audit].[ActiveSubstance_Audit]
SELECT 'U', [Id], [Name], [ClientId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy], [LifecycleStateId] FROM [Inserted]
GO

ALTER TRIGGER [dbo].[ActiveSubstance_Delete] ON[dbo].[ActiveSubstance]
FOR DELETE AS
INSERT INTO [Audit].[ActiveSubstance_Audit]
SELECT 'D', [Id], [Name], [ClientId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()), [LifecycleStateId] FROM [Deleted]
GO