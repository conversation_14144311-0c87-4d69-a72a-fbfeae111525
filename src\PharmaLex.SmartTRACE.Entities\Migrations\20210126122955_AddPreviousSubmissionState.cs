﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class AddPreviousSubmissionState : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PreviousLifecycleStateId",
                schema: "Audit",
                table: "Submission_Audit",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PreviousLifecycleStateId",
                table: "Submission",
                type: "int",
                nullable: true);

            migrationBuilder.SqlFileExec("0005-AddPreviousSubmissionState-UpdateSubmissionTriggers.sql");

            migrationBuilder.SqlFileExec("0005-AddPreviousSubmissionState-PopulatePreviousSubmissionState.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PreviousLifecycleStateId",
                schema: "Audit",
                table: "Submission_Audit");

            migrationBuilder.DropColumn(
                name: "PreviousLifecycleStateId",
                table: "Submission");
        }
    }
}
