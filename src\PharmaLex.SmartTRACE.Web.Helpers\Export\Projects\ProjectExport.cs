﻿using NPOI.SS.UserModel;
using NPOI.SS.Util;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.Office;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public class ProjectExport : ExcelWriter, IProjectExport
    {
        private readonly IDistributedCacheServiceFactory cache;

        public ProjectExport(IDistributedCacheServiceFactory cache)
        {
            this.cache = cache;
        }

        public async Task<byte[]> Export()
        {
            var wb = this.CreateWorkbook();
            ISheet exSheet = wb.Workbook.CreateSheet("Projects");
            int rowIndex = 0;

            var projectCache = this.cache.CreateMappedEntity<Project, ProjectModel>()
                .Configure(o => o
                    .Include(x => x.Client));

            var allProjects = await projectCache.AllAsync();

            List<string> columnNames = new List<string>
            {
                "Name", "Code", "Opportunity Number", "Client Name"
            };
            exSheet.CreateRow(rowIndex++, wb.Styles["header"], columnNames.ToArray());

            foreach (var project in allProjects.OrderBy(x => x.Name))
            {
                List<string> projectProps = new List<string>()
                {
                    project.Name,
                    project.Code,
                    project.OpportunityNumber,
                    project.ClientName
                };
                var row = exSheet.CreateRow(rowIndex++);
                for (int i = 0; i < projectProps.Count; i++)
                {
                    row.CreateCell(i, projectProps[i], wb.Styles["wrapped"]);
                }
            }

            exSheet.AutoSizeColumns(0, columnNames.Count);
            exSheet.SetAutoFilter(new CellRangeAddress(0, 0, 0, columnNames.Count - 1));

            return wb.Workbook.ToByteArray();
        }
    }
}
