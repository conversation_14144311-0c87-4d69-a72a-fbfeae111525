﻿@model EditSubmissionViewModel
@using Newtonsoft.Json
@using Newtonsoft.Json.Serialization
@using PharmaLex.Caching.Data
@using PharmaLex.SmartTRACE.Entities
@using PharmaLex.SmartTRACE.Entities.Enums
@using PharmaLex.SmartTRACE.Web.Helpers.Extensions
@using Microsoft.AspNetCore.Authorization
@using System.Linq;
@using System.Web

@inject IAuthorizationService AuthorizationService
@inject IDistributedCacheServiceFactory cache
@inject AutoMapper.IMapper mapper
@{
    var submissionTypeList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.SubmissionType).OrderBy(x => x.Name);
    var submissionUnitList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.SubmissionUnit).OrderBy(x => x.Name);
    var submissionModeList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.SubmissionMode).OrderBy(x => x.Name);
    var dossierFormatList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.DossierFormat).OrderBy(x => x.Name);
    var deliveryDetailsList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.SubmissionDeliveryDetails).OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString()
            });
    var prioritySelectList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.Priority).OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString()
            });
    var estimatedSizeSelectList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.EstimatedSize).OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString()
            });
    var publishingTeamSelectList = Model.Picklists.Where(x => x.PicklistTypeId == (int)PicklistType.PublishingTeam).OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Name}",
                Value = x.Id.ToString()
            });

    var projectSelectList = Model.Projects.OrderBy(x => x.Name).Select(x => new SelectListItem
            {
                Text = $"{x.Code}",
                Value = x.Id.ToString()
            });

    var applicationData = new List<string>
{
        Model.Application.ApplicationType,
        Model.Application.ApplicationNumber,
        Model.Application.ProcedureType
    };
    var picklistType = mapper.Map<IEnumerable<PicklistTypeList>>(Enum.GetValues(typeof(PicklistType)));
    var euRegion = await cache.CreateMappedEntity<Region, RegionModel>().FirstOrDefaultAsync(x => x.Name == "European Union");
    var adminUser = (await AuthorizationService.AuthorizeAsync(User, "Admin")).Succeeded;
    var editorUser = (await AuthorizationService.AuthorizeAsync(User, "Editor")).Succeeded;
    var externalReaderUser = (await AuthorizationService.AuthorizeAsync(User, "ExternalEditor")).Succeeded;
    var documentType = mapper.Map<IEnumerable<DocumentTypeList>>(Enum.GetValues(typeof(DocumentType)));
    var euCountriesIds = (await cache.CreateMappedEntity<Country, CountryModel>().WhereAsync(x => x.RegionId == euRegion.Id)).Select(x => x.Id);
    var usCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "United States of America");
    var ukCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "United Kingdom");
    var swissCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "Switzerland");
    var auCountry = await cache.CreateMappedEntity<Country, CountryModel>().FirstOrDefaultAsync(x => x.Name == "Australia");
    var isPrivilegedExternalEditorUser = (await AuthorizationService.AuthorizeAsync(User, "PrivilegedExternalEditor")).Succeeded;
}

<div id="submission" class="manage-container" v-cloak>
    <workflow-progress :id="id" :current-step="currentStage" :final-submission-state="finalSubmissionStateId" :last-active-step="lastActiveStep" :show-step="showStep"></workflow-progress>
    <header class="manage-header">
        @if (Model.Submission.Id != 0)
        {
            <a href="/submissions/view/@Model.Submission.Id" class="button secondary icon-button-view">View</a>
        }
        @if (Model.Submission.LifecycleStateId == (int)SubmissionLifeCycleState.QCReview && !(await AuthorizationService.AuthorizeAsync(User, "ExternalEditor")).Succeeded)
        {
            <button class="button icon-button-back" v-on:click.once="moveToPreviousStep()">Move to @(((SubmissionLifeCycleState)(Model.Submission.LifecycleStateId - 1)).GetDescription())</button>
        }
        @if (Model.Submission.LifecycleStateId != (int)SubmissionLifeCycleState.WithdrawnFromHA)
        {
            <button class="button icon-button-next" v-on:click="moveToNextStep()" :key="moveToKey" v-if="!((isUserEditor && isArchiveStage) || isUserExternalEditor)">Move to @(((SubmissionLifeCycleState)(Model.Submission.LifecycleStateId + 1)).GetDescription())</button>
        }
        @if ((await AuthorizationService.AuthorizeAsync(User, "BusinessAdmin")).Succeeded)
        {
            <button class="button secondary icon-button-next" name="proceedToObsolete" v-on:click.once="moveToObsolete()">Move to @(SubmissionLifeCycleState.Obsolete.GetDescription())</button>
        }
    </header>

    <h3 class="submission-content">Submission Details</h3>
    <div class="submission-additional-content">
        <h4>@Model.Product (@Model.Client.Name)</h4>
        <h4>
            @string.Join(", ", applicationData.Where(x => x != null))
        </h4>
    </div>
    <ul v-for="(field, index) of requiredFields" :key="index">
        <li class="field-validation-error">{{ field }}</li>
    </ul>
    <form id="form" method="post">
        @Html.AntiForgeryToken()
        <div class="loader" v-if="uploading"><img src="@Cdn.GetUrl("/images/loaders/spinner-75.svg")" alt="loading" /></div>
        <div :class="['form-col', (isUserExternalEditor ? 'form-col-half' : 'form-col-third') ]">
            <h5>General Info</h5>
            @if (Model.Submission.LifecycleStateId > (int)SubmissionLifeCycleState.Draft)
            {
                <label for="Submission.UniqueId" asp-for="Submission.UniqueId">Unique Id</label>
                <input asp-for="Submission.UniqueId" type="text" readonly />
            }
            <label for="Application.ApplicationNumber" asp-for="Application.ApplicationNumber">Application Number</label>
            <input asp-for="Application.ApplicationNumber" type="text" readonly :aria-disabled="isUserExternalEditor" />
            <label class="notification-email" for="Submission.CountriesIds" asp-for="Submission.CountriesIds">
                Countries
                <i class="icon-info-circled" v-if="memberStateProcedures.includes(application.procedureType)"
                   title="Select submission countries to be removed from the list (press and hold Ctrl button on the keyboard оr drag with the mouse for multi-select)."></i>
            </label>
            <div class="multi-select-wrapper">
                <select id="countriesSelectId" asp-for="Submission.CountriesIds" v-model="selectedCountriesId" :aria-disabled="(!memberStateProcedures.includes(application.procedureType) || isArchiveStage || isUserExternalEditor) || isRemovalOfCountriesDisabled" multiple>
                    <option v-for="(c, index) in countries" :key="index" v-bind:value="c.id">{{ c.name }}</option>
                </select>
                <i name="deleteCountry" class="hidden"></i>
                <i name="proceedToArchive" class="hidden"></i>
                <i name="resetCountries" class="hidden"></i>
            </div>
            <div class="submission-content" v-if="memberStateProcedures.includes(application.procedureType)">
                <a class="button secondary icon-button-cancel" v-on:click="clearSelectedCountries()" :aria-disabled="isArchiveStage" v-if="!isRemovalOfCountriesDisabled">Clear</a>
                <button type="button" class="icon-button-delete" v-on:click.once="deleteCountries()" :key="deleteKey" :aria-disabled="isArchiveStage || isUserExternalEditor" v-if="!isRemovalOfCountriesDisabled">Remove</button>
                <button type="button" class="icon-button-edit" v-on:click="resetCountries()" v-if="!(isRemovalOfCountriesDisabled || isArchiveStage || isUserExternalEditor)">Reset</button>
            </div>
            <label for="Submission.DeliveryDetailsId" asp-for="Submission.DeliveryDetailsId">Submission Delivery Details</label>
            <div class="select-wrapper">
                <select asp-for="Submission.DeliveryDetailsId" asp-items="deliveryDetailsList" v-on:change="selectedDeliveryDetails($event)" :aria-disabled="isArchiveStage || isUserExternalEditor">
                    <option value="">Select delivery details</option>
                </select>
            </div>
            <label for="Submission.SubmissionTypeId" asp-for="Submission.SubmissionTypeId" v-if="submissionTypes.length">Submission Type</label>
            <div v-if="submissionTypes.length" class="select-wrapper">
                <select asp-for="Submission.SubmissionTypeId" v-model="selectedSubmissionTypeId" :aria-disabled="isArchiveStage || isUserExternalEditor">
                    <option value="">Select submission type</option>
                    <option v-for="(s, index) in submissionTypes" :key="index" v-bind:value="s.id" :selected="selectedSubmissionType(s)">{{ s.name }}</option>
                </select>
            </div>
            <label for="Submission.SubmissionUnitId" asp-for="Submission.SubmissionUnitId" v-if="submissionUnits.length">Submission Unit</label>
            <div v-if="submissionUnits.length" class="select-wrapper">
                <select asp-for="Submission.SubmissionUnitId" v-model="selectedSubmissionUnitId" :aria-disabled="isArchiveStage || isUserExternalEditor">
                    <option value="">Select submission unit</option>
                    <option v-for="(s, index) in submissionUnits" :key="index" v-bind:value="s.id" :selected="selectedSubmissionUnit(s)">{{ s.name }}</option>
                </select>
            </div>
            <label for="Submission.SubmissionModeId" asp-for="Submission.SubmissionModeId" v-if="submissionModes.length">Submission Mode</label>
            <div v-if="submissionModes.length" class="select-wrapper">
                <select asp-for="Submission.SubmissionModeId" v-model="selectedSubmissionModeId" :ariadisabled="isArchiveStage || isUserExternalEditor">
                    <option value="">Select submission mode</option>
                    <option v-for="(s, index) in submissionModes" :key="index" v-bind:value="s.id" :selected="selectedSubmissionMode(s)">{{ s.name }}</option>
                </select>
            </div>
            <label for="Submission.DossierFormatId" asp-for="Submission.DossierFormatId">Dossier Format</label>
            <div class="select-wrapper">
                <select asp-for="Submission.DossierFormatId" v-model="selectedDossierFormatId" :change="selectedDossierFormat()" :aria-disabled="disableDossierFormatField() || isArchiveStage || isUserExternalEditor">
                    <option value="">Select dossier format</option>
                    <option v-for="(d, index) in dossierFormats" :key="index" v-bind:value="d.id">{{ d.name }}</option>
                </select>
            </div>
            <label for="Submission.SequenceNumber" asp-for="Submission.SequenceNumber">Sequence Number</label>
            <input asp-for="Submission.SequenceNumber" type="text" :readonly="isArchiveStage || isUserExternalEditor" />
            <label for="Submission.ReferenceNumber" asp-for="Submission.ReferenceNumber">Reference Number</label>
            <input asp-for="Submission.ReferenceNumber" type="text" :readonly="isArchiveStage || isUserExternalEditor" />
            <label for="Submission.RelatedSequenceNumber" asp-for="Submission.RelatedSequenceNumber">Related Sequence Number</label>
            <input asp-for="Submission.RelatedSequenceNumber" type="text" :readonly="isArchiveStage || isUserExternalEditor" />
            <label for="Submission.SerialNumber" asp-for="Submission.SerialNumber" v-if="showSerialNumberField()">Serial Number</label>
            <input asp-for="Submission.SerialNumber" type="text" v-if="showSerialNumberField()" :readonly="isArchiveStage || isUserExternalEditor" />
            <label for="Submission.Description" asp-for="Submission.Description">Description</label>
            <textarea asp-for="Submission.Description" :readonly="isArchiveStage || isUserExternalEditor"></textarea>
            <div id="submissionDescriptionValidationMessage" style="color:red;"></div>
            <label for="Submission.Comments" asp-for="Submission.Comments">Comments</label>
            <textarea asp-for="Submission.Comments" :readonly="isArchiveStage || isUserExternalEditor"></textarea>
            <div id="submissionCommentsValidationMessage" style="color:red;"></div>
        </div>
        <div :class="['form-col', 'form-col', (isUserExternalEditor ? 'form-col-half' : 'form-col-third') ]">
            <h5>Document/Dossier Details</h5>
            @if (Model.Submission.SourceDocumentsLocation != null)
            {
                <label for="Submission.SourceDocumentsLocation" asp-for="Submission.SourceDocumentsLocation">Location of Source Documents</label>
                <input asp-for="Submission.SourceDocumentsLocation" type="text" :readonly="isArchiveStage || isUserExternalEditor" />
            }
            @if (Model.Submission.LifecycleStateId > (int)SubmissionLifeCycleState.Submitted &&
            Model.Submission.ArchivedDocumentsLocation != null)
            {
                <label for="Submission.ArchivedDocumentsLocation" asp-for="Submission.ArchivedDocumentsLocation">Location of Archived Dossier</label>
                <input asp-for="Submission.ArchivedDocumentsLocation" type="text" :readonly="isArchiveStage || isUserExternalEditor" />
            }

            <div class="document-list">
                <div v-for="(document, index) in groupBy(submission.documents, 'documentTypeId')" :key="index">
                    <i v-on:click="expandedDocumenType(index)" :class="['expand-document', { 'icon-right-dir': index != expandedType, 'icon-down-dir': index == expandedType }]">
                        <span style="font-weight:bold;">
                            {{ documentType(index) }}
                        </span>
                    </i>
                    <div v-for="(doc, name) in  groupBy(document, 'name')" :key="name" :class="['document-list', { hidden: index != expandedType }]">
                        <i :class="['expand-document', { 'icon-right-dir': name != expandedDocumentName, 'icon-down-dir': name == expandedDocumentName }]" v-on:click="expandedDocument(name, index)">
                            {{ name }}
                        </i>
                        <document :config="documentConfig" v-bind:document="doc" v-on:selected-document="onSelectedDocument($event)" v-on:download-document="onDownloadDocument($event)" v-on:docshifter-loading="onDocshiftDocument($event)" :class="[{hidden: name != expandedDocumentName}]"></document>
                    </div>
                </div>
            </div>

            <div><label for="uploaddocument" style="font-weight:bold">Upload document</label></div>
            <div :class="['form-col', {'hidden': hideDocumentTypeMessage()}, {'required-field': requiredFields.some(x => x.includes('Please upload'))}]">
                In order to upload a document, please select document type first!
            </div>

            <label for="selectedDocumentTypeId">Document Type</label>
            <div class="select-wrapper">
                <select v-model="selectedDocumentTypeId" v-on:change="selectedDocumentType($event)">
                    <option value="">Select document type</option>
                    <option v-for="(d, index) in displayDocumentTypes" :key="index" v-bind:value="d.id">{{ d.name }}</option>
                </select>
            </div>

            <div :class="['form-col', {'hidden': !showDossierFormatWarning}, 'required-field']">
                In order to upload a submission dossier, please select Dossier Format first!
            </div>

            <div :class="['form-col', {'hidden': !showDropZone}]">
                <label for="dossierUploadId">Upload document (*.zip)</label>
                <upload ref="uploadComponent" id="dossierUploadId" v-on:uploading="fileUploading($event)" v-on:file-added="fileAdded($event)" v-on:latest-version="latestFileVersion($event)" :config="uploadConfig"></upload>
            </div>

            <h5>Dates</h5>
            @if (Model.Submission.LifecycleStateId > (int)SubmissionLifeCycleState.Draft)
            {
                @if (!externalReaderUser)
                {
                    <label for="Submission.CreatedBy" asp-for="Submission.CreatedBy">Created By</label>
                    <input type="text" asp-for="Submission.CreatedBy" readonly />
                }
                <label for="Submission.CreatedDate" asp-for="Submission.CreatedDate">Created Date</label>
                <input type="date" asp-for="Submission.CreatedDate" readonly />
            }
            <label for="Submission.HealthAuthorityDueDate" asp-for="Submission.HealthAuthorityDueDate">Health Authority Due Date</label>
            <input type="date" asp-for="Submission.HealthAuthorityDueDate" :readonly="isArchiveStage || isUserExternalEditor" :aria-disabled="isUserExternalEditor" />
            <div id="healthAuthorityDueDateId" style="color:red;"></div>
            <label for="Submission.AuthoringDeadline" asp-for="Submission.AuthoringDeadline">Deadline for Last Document</label>
            <input type="date" asp-for="Submission.AuthoringDeadline" :readonly="isArchiveStage || isUserExternalEditor" :aria-disabled="isUserExternalEditor" />
            <div id="authoringDeadlineId" style="color:red;"></div>
            <label for="Submission.PlannedDispatchDate" asp-for="Submission.PlannedDispatchDate">Planned Dispatch</label>
            <input type="date" for="Submission.PlannedDispatchDate" asp-for="Submission.PlannedDispatchDate" :readonly="isArchiveStage || isUserExternalEditor" :aria-disabled="isUserExternalEditor" />
            <div id="plannedDispatchDateId" style="color:red;"></div>
            @if (Model.Submission.LifecycleStateId >= (int)SubmissionLifeCycleState.ApprovedForSubmission)
            {
                <label for="Submission.ActualDispatchDate" asp-for="Submission.ActualDispatchDate">Actual Dispatch</label>
                <input type="date" asp-for="Submission.ActualDispatchDate" :max="allowedDate" :readonly="isArchiveStage || isUserExternalEditor" :aria-disabled="isUserExternalEditor" />
                <div id="actualDispatchDateId" style="color:red;"></div>
            }
            <label for="Submission.PlannedSubmissionDate" asp-for="Submission.PlannedSubmissionDate">Planned Submission</label>
            <input type="date" asp-for="Submission.PlannedSubmissionDate" :readonly="isArchiveStage || isUserExternalEditor" :aria-disabled="isUserExternalEditor" />
            <div id="plannedSubmissionDateId" style="color:red;"></div>
            @if (Model.Submission.LifecycleStateId >= (int)SubmissionLifeCycleState.ApprovedForSubmission)
            {
                <label for="Submission.ActualSubmissionDate" asp-for="Submission.ActualSubmissionDate">Actual Submission</label>
                <input type="date" asp-for="Submission.ActualSubmissionDate" :max="allowedDate" :readonly="isArchiveStage || isUserExternalEditor" :aria-disabled="isUserExternalEditor" />
            }
            @if (Model.Submission.LifecycleStateId >= (int)SubmissionLifeCycleState.ReadyForPublishing)
            {
                <label for="Submission.DocubridgeVersionId" asp-for="Submission.DocubridgeVersionId">docuBridge Version Id</label>
                <input asp-for="Submission.DocubridgeVersionId" type="text" :readonly="isArchiveStage || isUserExternalEditor" maxlength="33" :aria-disabled="isUserExternalEditor" />
            }
            @if (Model.Submission.LifecycleStateId >= (int)SubmissionLifeCycleState.ApprovedForSubmission)
            {
                <label for="Submission.CespNumber" asp-for="Submission.CespNumber" v-if="showCespNumberField">Portal/Gateway Reference Number</label>
                <input asp-for="Submission.CespNumber" type="text" v-if="showCespNumberField" :readonly="isArchiveStage || isUserExternalEditor" :aria-disabled="isUserExternalEditor" />
            }
            @if (Model.Submission.LifecycleStateId == (int)SubmissionLifeCycleState.Archived)
            {
                <label for="Submission.WithdrawalDate" asp-for="Submission.WithdrawalDate">Withdrawal date</label>
                <input type="date" asp-for="Submission.WithdrawalDate" :min="allowedDate" :readonly="isArchiveStage || isUserExternalEditor" :aria-disabled="isUserExternalEditor" />
            }
        </div>
        <div :class="['form-col', {'hidden': isUserExternalEditor }, 'form-col-third']">
            <h5>Client Details</h5>
            <label for="Client.Name">Client Name</label>
            <input asp-for="Client.Name" type="text" readonly :aria-disabled="isUserExternalEditor" />
            <label for="Submission.ProjectId">Project Code</label>
            <div class="select-wrapper">
                <select asp-for="Submission.ProjectId" asp-items="projectSelectList" v-on:change="selectedProject($event)" :aria-disabled="isArchiveStage || isUserExternalEditor">
                </select>
            </div>
            <label for="Project.Name">Project Name</label>
            <input asp-for="Project.Name" type="text" v-model="projectName" readonly :aria-disabled="isUserExternalEditor" />
            <label for="Project.OpportunityNumber">Opportunity Number</label>
            <input asp-for="Project.OpportunityNumber" type="text" v-model="opportunityNumber" readonly :aria-disabled="isUserExternalEditor" />
            <label for="Client.ContractOwner">Contract Owner</label>
            <input asp-for="Client.ContractOwner" type="text" readonly :aria-disabled="isUserExternalEditor" />
            <h5>Resources</h5>
            <label for="SubmissionResource.PriorityId" asp-for="SubmissionResource.PriorityId">Priority</label>
            <div class="select-wrapper">
                <select asp-for="SubmissionResource.PriorityId" asp-items="prioritySelectList" :aria-disabled="isArchiveStage || isUserExternalEditor">
                    <option value="">Select priority</option>
                </select>
            </div>
            <label for="SubmissionResource.EstimatedSizeId" asp-for="SubmissionResource.EstimatedSizeId">Estimated Size</label>
            <div class="select-wrapper">
                <select asp-for="SubmissionResource.EstimatedSizeId" asp-items="estimatedSizeSelectList" :aria-disabled="isArchiveStage || isUserExternalEditor">
                    <option value="">Select estimated size</option>
                </select>
            </div>
            <label for="SubmissionResource.EstimatedHours" asp-for="SubmissionResource.EstimatedHours">Estimated Hours</label>
            <i class="icon-info-circled" title="Estimated hours should include pre-publisher & publisher time, check for accuracy before moving to the archived state."></i>
            <input asp-for="SubmissionResource.EstimatedHours" type="number" step="0.01" min="0" :readonly="isArchiveStage || isUserExternalEditor" />
            <label for="SubmissionResource.Comments" asp-for="SubmissionResource.Comments">Resources Comments</label>
            <textarea asp-for="SubmissionResource.Comments" :readonly="isArchiveStage || isUserExternalEditor"></textarea>
            <div id="resourceCommentsValidationMessage" style="color:red;">
            </div>
            <label for="SubmissionResource.PublishingLead" asp-for="SubmissionResource.PublishingLead">Publishing Lead</label>
            <autocomplete :config="config" v-on:selected="selectedLead" v-on:search="onPublishingLeadSearch"></autocomplete>
            <div v-if="selectedPublishingLeadErrorMessage">
                <span class="warning-field"> {{ selectedPublishingLeadErrorMessage }}</span>
            </div>
            <label for="SubmissionResource.RegulatoryLead" asp-for="SubmissionResource.RegulatoryLead">Regulatory Lead</label>
            <autocomplete :config="config" v-on:selected="selectedRegLead" v-on:search="onRegulatoryLeadSearch"></autocomplete>
            <label for="SubmissionResource.PublishingTeamId" asp-for="SubmissionResource.PublishingTeamId">Publishing Team</label>
            <div class="select-wrapper">
                <select asp-for="SubmissionResource.PublishingTeamId" asp-items="publishingTeamSelectList" :aria-disabled="isArchiveStage || isUserExternalEditor">
                    <option value="">Select publishing team</option>
                </select>
            </div>
            <label for="SubmissionResource.Publishers" asp-for="SubmissionResource.Publishers">Publishers</label>
            <autocomplete-list :valid="true" :items="publishers" :added-items="addedPublishers" v-on:change="changePublishers" :config="publisherConfig"></autocomplete-list>
            <div v-if="selectedPublishersErrorMessage">
                <span class="warning-field"> {{ selectedPublishersErrorMessage }}</span>
            </div>
        </div>
        <input type="hidden" asp-for="Project.Code" />
        <input type="hidden" asp-for="Submission.Id" />
        <input type="hidden" asp-for="Submission.ApplicationId" />
        <input type="hidden" asp-for="Submission.UniqueId" />
        <input type="hidden" asp-for="Submission.ProjectId" />
        <input type="hidden" asp-for="SubmissionResource.Id" />
        <input type="hidden" asp-for="SubmissionResource.SubmissionId" />
        <input type="hidden" asp-for="Submission.LifecycleStateId" />
        <input type="hidden" asp-for="Submission.PreviousLifecycleStateId" />
        <input type="hidden" asp-for="Submission.DossierFormatId" v-model="selectedDossierFormatId" />
        <input type="hidden" asp-for="SubmissionResource.RegulatoryLead" v-model="regulatoryLead" />
        <input type="hidden" asp-for="SubmissionResource.PublishingLead" v-model="publishingLead" />
        <input type="hidden" asp-for="Product" value="@Model.Product" />
        <select asp-for="Submission.CountriesIds" class="hidden" v-model="selectedCountries">
            <option v-for="(sc, index) in selectedCountries" :key="index" v-bind:value="sc" v-bind:id="sc"></option>
        </select>
        <template v-for="(item, index) in addedPublishers">
            <input :name="'SubmissionResource.Publishers' + '[' + index + '].GivenName'" type="hidden" :value="item.givenName" />
            <input :name="'SubmissionResource.Publishers' + '[' + index + '].FamilyName'" type="hidden" :value="item.familyName" />
            <input :name="'SubmissionResource.Publishers' + '[' + index + '].Email'" type="hidden" :value="item.email" />
        </template>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/applications/view/@Model.Submission.ApplicationId">Cancel</a>
            <button id="save" class="icon-button-save" v-on:click="onSave($event)" :aria-disabled="isArchiveStage || isUserExternalEditor">Save</button>
            <button id="hiddenSave" class="hide-button"></button>
            <button type="button" name="versionDocument" class="hide-button"></button>
        </div>
    </form>
    <yes-no-dialog ref="yesNoRemoveDocument" :config="removeFileConfig" v-on:button-pressed="onRemoveDocument"></yes-no-dialog>
    <yes-no-dialog :config="deleteCountryConfig" v-on:button-pressed="onDeleteCountries"></yes-no-dialog>
    <yes-no-dialog :config="resetCountriesConfig" v-on:button-pressed="onResetCountries"></yes-no-dialog>
    <yes-no-dialog :config="proceedToArchiveConfig" v-on:button-pressed="onProceedToArchive"></yes-no-dialog>
    <yes-no-dialog :config="uploadVersionDocumentsConfig" v-on:button-pressed="onUploadVersionDocument"></yes-no-dialog>
    <yes-no-dialog :config="obsolеteStateConfig" v-on:button-pressed="onProceedToObsolete"></yes-no-dialog>
</div>

@section Scripts {
    <script src="@Cdn.GetUrl("js/plx.js")"></script>
    <script src="@Cdn.GetUrl("js/toast.js")"></script>
    <script src="@Cdn.GetUrl("lib/uppy/uppy.min.js")"></script>
    <script src="@Cdn.GetUrl("lib/jquery/dist/jquery.min.js")"></script>
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        var pageConfig = {
            appElement: '#submission',
            data() {
                return {
                    allowedDate: new Date().toISOString().substring(0, 10),
                    config: {
                        showSelectedName: true,
                        canAddNew: false,
                        searchUrl: '/manage/users/find?term={term}',
                        placeholder: 'Type and select the e-mail from the list'
                    },
                    deleteCountryConfig: {
                        message: null,
                        noButton: 'Cancel',
                        yesButton: 'Remove',
                        elementName: 'deleteCountry',
                        buttonClass: 'icon-button-delete'
                    },
                    resetCountriesConfig: {
                        message: 'All the countries in this Application will be restored',
                        noButton: 'Cancel',
                        yesButton: 'Reset',
                        elementName: 'resetCountries',
                        buttonClass: 'icon-button-edit'
                    },
                    removeFileConfig: {
                        noButton: 'Cancel',
                        yesButton: 'Remove',
                        elementName: 'removeDossier',
                        buttonClass: 'icon-button-delete'
                    },
                    proceedToArchiveConfig: {
                        message: 'Please check you have uploaded all zip files for Source Documents, Submission Ready Documents, Delivery Receipts and a Submission Dossier. Once in the Archived state, only a Business Admin can remove/upload zips.',
                        noButton: 'Stay on Submitted',
                        yesButton: 'Move to Archived',
                        elementName: 'proceedToArchive',
                        buttonClass: 'icon-button-next',
                        fontSize: 'text-medium-helper'
                    },
                    obsolеteStateConfig: {
                        id: @Model.Submission.Id,
                        message: 'Do you want to move to Obsolete?',
                        noButton: '',
                        yesButton: 'Move to Obsolete',
                        elementName: 'proceedToObsolete',
                        buttonClass: 'icon-button-next',
                        fontSize: 'text-medium-helper'
                    },
                    publisherConfig: {
                        selectable: false,
                        showSelectedName: false,
                        modelProperty: 'SubmissionResource.Publishers',
                        placeholder: 'Type and select the e-mail from the list',
                        emptyMessage: 'No publishers selected',
                        searchUrl: '/manage/users/find?term={term}',
                        active: true,
                    },
                    uploadConfig: {
                        height: '100px',
                        unpacked: false
                    },
                    documentConfig: {
                        viewMode: false
                    },
                    uploadVersionDocumentsConfig: {
                        message: 'A zip file with this name already exists. A new version will be created and saved.',
                        noButton: 'Cancel',
                        yesButton: 'Continue',
                        elementName: 'versionDocument',
                        buttonClass: 'icon-button-upload'
                    },
                    id: @Model.Submission.Id,
                    currentStage: @Model.Submission.LifecycleStateId,
                    lastActiveStep: @Model.Submission.PreviousLifecycleStateId,
                    showStep: @Html.Raw(adminUser.ToString().ToLower()),
                    invalidFields: @Html.Raw(Json.Serialize(Model.InvalidFields)),
                    submissionResource: @Html.Raw(Json.Serialize(Model.SubmissionResource)),
                    submission: @Html.Raw(Model.Submission.ToJson()),
                    application: @Html.Raw(Model.Application.ToJson()),
                    client: @Html.Raw(Model.Client.ToJson()),
                    publishingLead: null,
                    regulatoryLead: null,
                    regulatoryLeadElement: null,
                    publishingLeadElement: null,
                    publishersElement: null,
                    countriesSelectElement: null,
                    requiredFields: [],
                    countries: @Html.Raw(Model.AllCountries.ToJson()),
                    applicationCountries: @Html.Raw(Model.ApplicationCountries.ToJson()),
                    selectedCountries: @Html.Raw(Json.Serialize(Model.Submission.CountriesIds)),
                    selectedCountriesId: [],
                    publishers: [],
                    addedPublishers: @Html.Raw(Json.Serialize(Model.SubmissionResource.Publishers)),
                    submissionTypes: @Html.Raw(Json.Serialize(submissionTypeList)),
                    submissionUnits: @Html.Raw(Json.Serialize(submissionUnitList)),
                    submissionModes: @Html.Raw(Json.Serialize(submissionModeList)),
                    picklistTypes: @Html.Raw(Json.Serialize(picklistType)),
                    deliveryDetails: @Html.Raw(Json.Serialize(deliveryDetailsList)),
                    dossierFormats: @Html.Raw(Json.Serialize(dossierFormatList)),
                    allPicklists: @Html.Raw(Json.Serialize(Model.Picklists)),
                    documentTypes: @Html.Raw(Json.Serialize(documentType)),
                    selectedSubmissionTypeId: @Model.Submission.SubmissionTypeId,
                    selectedSubmissionUnitId: '',
                    selectedSubmissionModeId: '',
                    selectedDocumentTypeId: '',
                    selectedDossierFormatId: '',
                    euRegion: @Html.Raw(Json.Serialize(euRegion)),
                    euCountriesIds: @Html.Raw(Json.Serialize(euCountriesIds)),
                    usCountry: @Html.Raw(Json.Serialize(usCountry)),
                    nonEuUkCountry: @Html.Raw(Json.Serialize(ukCountry)),
                    swissCountry: @Html.Raw(Json.Serialize(swissCountry)),
                    auCountry: @Html.Raw(Json.Serialize(auCountry)),
                    moveToKey: 1,
                    deleteKey: 1,
                    memberStateProcedures: ['Decentralised Procedure', 'Mutual Recognition Procedure', 'ASMF Worksharing Procedure'],
                    showCespNumberField: true,
                    fileName: '',
                    uploading: false,
                    showDropZone: false,
                    showDossierFormatWarning: false,
                    projects: @Html.Raw(Model.Projects.ToJson()),
                    opportunityNumber: null,
                    projectName: null,
                    futureFileVersion: '',
                    dossierDocumentTypeId: @Html.Raw((int)DocumentType.Dossier),
                    sourceDocumentTypeId: @Html.Raw((int)DocumentType.Source),
                    submissionReadyDocumentTypeId: @Html.Raw((int)DocumentType.SubmissionReady),
                    deliveryReceiptDocumentTypeId: @Html.Raw((int)DocumentType.DeliveryReceipt),
                    currentDocument: {},
                    submittedLifecycleState: @((int)SubmissionLifeCycleState.Submitted),
                    archivedLifecycleStage: @((int)SubmissionLifeCycleState.Archived),
                    expandedType: 0,
                    expandedDocumentName: '',
                    baseUploadLink: '',
                    baseUnpackLink: '',
                    baseVersionLink: '',
                    arePublishersSelected: false,
                    selectedPublishingLeadErrorMessage: null,
                    selectedPublishersErrorMessage: null,
                    docubridgeVersionErrorMessage: null,
                    isArchiveStage: false,
                    isRemovalOfCountriesDisabled: false,
                    isUserEditor: @Html.Raw(editorUser.ToString().ToLower()),
                    isUserAdmin: @Html.Raw(adminUser.ToString().ToLower()),
                    isUserExternalEditor: @Html.Raw(externalReaderUser.ToString().ToLower()),
                    isPrivilegedExternalEditorUser: @Html.Raw(Json.Serialize(isPrivilegedExternalEditorUser)),
                    lifeCycleState: '@Html.Raw(((SubmissionLifeCycleState)Model.Submission.LifecycleStateId).GetDescription())',
                    dateError: '',
                }
            },
            computed: {
                finalSubmissionStateId() {
                    return this.showStep ? @Html.Raw((int)SubmissionLifeCycleState.Obsolete) : @Html.Raw((int)SubmissionLifeCycleState.WithdrawnFromHA);
                },
                displayDocumentTypes() {
                    var result = this.documentTypes;

                    if (this.currentStage === this.archivedLifecycleStage && this.isUserEditor && !this.isUserAdmin) {
                        result = result.filter(x => x.id === this.deliveryReceiptDocumentTypeId);
                    } else if (this.isUserExternalEditor && !this.isUserAdmin) {
                        result = result.filter(x => x.id === this.sourceDocumentTypeId);
                    }

                    if (this.submission.documents.map(x => x.documentTypeId).includes(this.dossierDocumentTypeId) && !this.isUserExternalEditor) {
                        result = result.filter(x => x.id !== this.dossierDocumentTypeId);
                    }
                    return result;
                }
            },
            methods: {
                selectedLead(selectedLead) {
                    this.publishingLead = selectedLead.name;
                    this.selectedPublishingLeadErrorMessage = null;
                },
                selectedRegLead(selectedLead) {
                    this.regulatoryLead = selectedLead.name;
                },
                selectedSubmissionType(type) {
                    this.selectedSubmissionTypeId === type.id;
                },
                selectedSubmissionUnit(unit) {
                    this.selectedSubmissionUnitId === unit.id;
                },
                selectedSubmissionMode(mode) {
                    this.selectedSubmissionModeId === mode.id;
                },
                selectedDeliveryDetails(deliveryDetails) {

                    const actualSubmissionElement = document.getElementById('Submission_ActualSubmissionDate');

                    if (deliveryDetails.target.value !== this.deliveryDetails.find(x => x.text === 'Publish & Send to Client')?.value) {
                        this.showCespNumberField = true;

                        setTimeout(() => {

                            if (!actualSubmissionElement.hasAttribute('required')) {
                                actualSubmissionElement.setAttribute('required', 'true');
                                if (!actualSubmissionElement.checkValidity()) {
                                    const label = actualSubmissionElement.labels[0];
                                    label.textContent = `${label.textContent}*`;
                                    label.style.color = 'red';
                                }

                                this.invalidFields.push('Submission.ActualSubmissionDate');
                            }

                            const cespNumberElement = document.getElementById('Submission_CespNumber');
                            if (!cespNumberElement.hasAttribute('required')) {
                                cespNumberElement.setAttribute('required', 'true');
                                if (!cespNumberElement.checkValidity()) {
                                    const label = cespNumberElement.labels[0];
                                    label.textContent = `${label.textContent}*`;
                                    label.style.color = 'red';
                                }
                            }
                        });

                        return;
                    }
                    else {
                        setTimeout(() => {

                            if (actualSubmissionElement.hasAttribute('required')) {
                                actualSubmissionElement.removeAttribute('required');

                                const label = actualSubmissionElement.labels[0];
                                label.textContent = `${label.textContent.slice(0, -1)}`;
                                label.style.color = 'black';

                                this.invalidFields.splice(this.invalidFields.indexOf('Submission.ActualSubmissionDate'), 1);
                            }
                        });
                    }
                    this.showCespNumberField = false;
                },
                selectedProject(project) {
                    let selectedProjectId = project.target.value;
                    let selectedProject = this.projects.find(x => x.id === parseInt(selectedProjectId));
                    if (selectedProject) {
                        this.opportunityNumber = selectedProject.opportunityNumber;
                        this.projectName = selectedProject.name;
                    }
                },
                selectedDocumentType(event) {
                    if (this.uploadConfig && this.baseVersionLink) {
                        this.uploadConfig.versionLink = this.baseVersionLink.replace('{documentTypeId}', this.selectedDocumentTypeId);
                    }
                    if (this.selectedDocumentTypeId === this.dossierDocumentTypeId && this.selectedDossierFormatId) {
                        this.showDossierFormatWarning = true;
                        this.showDropZone = false;
                    }
                },
                selectedDossierFormat() {
                    if ((this.selectedDossierFormatId || this.selectedDocumentTypeId !== this.dossierDocumentTypeId) &&
                        this.selectedDocumentTypeId) {
                        this.showDossierFormatWarning = false;
                        this.showDropZone = true;
                        this.documentConfig.showViewButton = this.showViewDossierButton();
                    } else if (!this.selectedDocumentTypeId) {
                        this.showDossierFormatWarning = false;
                        this.showDropZone = false;
                    } else if (this.selectedDocumentTypeId === this.dossierDocumentTypeId) {
                        this.showDossierFormatWarning = true;
                        this.showDropZone = false;
                    }
                },
                disableDossierFormatField() {
                    return this.submission.documents && this.submission.documents.map(x => x.documentTypeId).includes(this.dossierDocumentTypeId);
                },
                showViewDossierButton() {
                    return (this.submission.countriesIds.some(x => this.euCountriesIds.includes(x)) ||
                        this.submission.countriesIds.includes(this.usCountry.id) ||
                        this.submission.countriesIds.includes(this.nonEuUkCountry.id) ||
                        this.submission.countriesIds.includes(this.swissCountry.id) ||
                        this.submission.countriesIds.includes(this.auCountry.id)) &&
                        this.selectedDossierFormatId === this.dossierFormats.find(x => x.name === 'eCTD')?.id;
                },
                hideDocumentTypeMessage() {
                    return this.showDropZone || this.selectedDocumentTypeId;
                },
                onSave(event) {
                    if (this.addedPublishers.length > 0 && !this.addedPublishers.every(this.checkIfItemIsSelectedFromDropdown)) {
                        this.selectedPublishersErrorMessage = "Please remove your typed name and select your name from the email list shown";
                        event.preventDefault();
                        return;
                    }

                    if (this.hasInvalidDates()) {
                        event.preventDefault();
                        return;
                    }

                    let form = document.getElementById('form');
                    let requiredFields = form.querySelectorAll('[required]');
                    let filteredRequiredFields = Array.from(requiredFields).filter(x => x.id !== 'Submission_PlannedSubmissionDate');
                    for (requiredField of filteredRequiredFields) {
                        requiredField.removeAttribute('required');
                    }
                    this.regulatoryLeadElement.removeAttribute('required');
                    this.publishingLeadElement.removeAttribute('required');
                    this.countriesSelectElement.removeAttribute('disabled');
                },
                moveToNextStep() {
                    this.requiredFields = Vue.ref([]);

                    for (field of this.invalidFields) {
                        var elements = document.getElementsByName(field);
                        if (elements.length == 0) { continue; }
                        var element = elements[0];
                        if (!element) { return; }
                        element.setAttribute('required', 'true');

                        if (!element.checkValidity()) {
                            if (element.previousElementSibling) {
                                if (!element.previousElementSibling.textContent) {
                                    this.requiredFields.push(element.previousElementSibling.previousElementSibling.textContent.includes('*')
                                        ? element.previousElementSibling.previousElementSibling.textContent.slice(0, -1) + ': ' + element.validationMessage
                                        : element.previousElementSibling.previousElementSibling.textContent + ': ' + element.validationMessage);
                                } else {
                                    this.requiredFields.push(element.previousElementSibling.textContent.includes('*')
                                        ? element.previousElementSibling.textContent.slice(0, -1) + ': ' + element.validationMessage
                                        : element.previousElementSibling.textContent + ': ' + element.validationMessage);
                                }
                            }
                            else {
                                this.requiredFields.push(element.parentElement.previousElementSibling.textContent.includes('*')
                                    ? element.parentElement.previousElementSibling.textContent.slice(0, -1) + ': ' + element.validationMessage
                                    : element.parentElement.previousElementSibling.textContent + ': ' + element.validationMessage);
                            }
                        }
                    }

                    this.regulatoryLeadElement.setAttribute('required', 'true');
                    this.publishingLeadElement.setAttribute('required', 'true');


                    let warningMessage = 'Start typing the email address, then select from the list below.';

                    if (!this.publishingLeadElement.checkValidity()) {
                        this.requiredFields.push('Publishing Lead' + ': ' + warningMessage);
                    }

                    if (!this.regulatoryLeadElement.checkValidity()) {
                        this.requiredFields.push('Regulatory Lead' + ': ' + warningMessage);
                    }

                    if (this.addedPublishers.length === 0) {
                        var publisherLabel = this.publishersElement.parentElement.parentElement.previousElementSibling;
                        if (!publisherLabel.textContent.includes('*')) {
                            publisherLabel.textContent = `${publisherLabel.textContent}*`;
                        }
                        this.requiredFields.push('Publishers' + ': Start typing each email address, then select from the list below.');
                        this.setAsteriskToRequiredFields();
                        setTimeout(() => this.moveToKey++, 500);
                        return;
                    }

                    if (this.publishingLead == null) {
                        this.selectedPublishingLeadErrorMessage = 'Please remove your typed name and select your name from the email list shown';

                        if (!this.requiredFields.includes('Publishing Lead' + ': ' + warningMessage)) {
                            this.requiredFields.push('Publishing Lead' + ': ' + warningMessage);
                        }

                        setTimeout(() => this.moveToKey++, 500);
                        return;
                    }

                    if (this.addedPublishers.length > 0 && !this.addedPublishers.every(this.checkIfItemIsSelectedFromDropdown)) {
                        this.selectedPublishersErrorMessage = 'Please remove your typed name and select your name from the email list shown';

                        if (!this.requiredFields.includes('Publishers' + ': Start typing each email address, then select from the list below.')) {
                            this.requiredFields.push('Publishers' + ': Start typing each email address, then select from the list below.');
                            this.setAsteriskToRequiredFields();
                        }

                        setTimeout(() => this.moveToKey++, 500);
                        return;
                    }

                    if ((this.invalidFields.includes('Submission.SourceDocument')) && !this.submission.documents.map(x => x.documentTypeId).includes(this.sourceDocumentTypeId)) {
                        var elements = document.getElementsByName('Submission.SourceDocumentsLocation');
                        var sourceLocationsElement = elements[0];
                        if (!sourceLocationsElement || !sourceLocationsElement.checkValidity()) {
                            this.requiredFields.push('Please upload Source Documents.');
                            setTimeout(() => this.moveToKey++, 500);
                            return;
                        }
                    }

                    if (this.invalidFields.includes('Submission.DossierArchive') && !this.submission.documents.map(x => x.documentTypeId).includes(this.dossierDocumentTypeId)) {
                        this.requiredFields.push('Please upload a Submission Dossier.');
                        setTimeout(() => this.moveToKey++, 500);
                        return;
                    }

                    let form = document.getElementById('form');
                    if (form.checkValidity()) {
                        form.setAttribute('action', `/submissions/next-state/@Model.Submission.Id`);

                        if (this.currentStage == this.submittedLifecycleState) {
                            document.getElementsByName(this.proceedToArchiveConfig.elementName)[0].click();
                        } else {
                            document.getElementById('hiddenSave').click();
                        }

                        this.clearSelectedCountries();
                        this.setAsteriskToRequiredFields();
                        return;
                    }

                    this.setAsteriskToRequiredFields();
                    document.getElementById('hiddenSave').click();
                    setTimeout(() => this.moveToKey++, 500);
                },
                moveToPreviousStep() {
                    let form = document.getElementById('form');
                    form.setAttribute('action', `/submissions/previous-state/@Model.Submission.Id`);
                    this.clearSelectedCountries();
                    document.getElementById('hiddenSave').click();
                },
                setAsteriskToRequiredFields() {
                    for (field of this.invalidFields) {
                        let elements = document.getElementsByName(field);
                        if (elements.length == 0) { continue; }
                        var element = elements[0];
                        if (!element) { return; }
                        element.setAttribute('required', 'true');

                        if (!element.checkValidity()) {
                            let label = document.querySelector('label[for="' + element.name + '"]');
                            if (label) {
                                if (!label.textContent.includes('*')) {
                                    label.textContent = `${label.textContent}*`;
                                    label.style.color = 'red';
                                }
                            }
                        }
                    }

                    this.regulatoryLeadElement.setAttribute('required', 'true');
                    this.publishingLeadElement.setAttribute('required', 'true');

                    if (!this.publishingLeadElement.checkValidity()) {
                        var publishingLeadLabel = this.publishingLeadElement.parentElement.previousElementSibling;
                        if (!publishingLeadLabel.textContent.includes('*')) {
                            publishingLeadLabel.textContent = `${publishingLeadLabel.textContent}*`;
                            publishingLeadLabel.style.color = 'red';
                        }
                    }
                    if (!this.regulatoryLeadElement.checkValidity()) {
                        var regulatoryLeadLabel = this.regulatoryLeadElement.parentElement.previousElementSibling;
                        if (!regulatoryLeadLabel.textContent.includes('*')) {
                            regulatoryLeadLabel.textContent = `${regulatoryLeadLabel.textContent}*`;
                            regulatoryLeadLabel.style.color = 'red';
                        }
                    }

                    if (this.addedPublishers.length === 0) {
                        var publisherLabel = this.publishersElement.parentElement.parentElement.previousElementSibling;
                        if (!publisherLabel.textContent.includes('*')) {
                            publisherLabel.textContent = `${publisherLabel.textContent}*`;
                            publisherLabel.style.color = 'red';
                        }
                    }
                },
                onPublishingLeadSearch(value) {
                    if (!value) {
                        this.publishingLead = value;
                    }
                },
                onRegulatoryLeadSearch(value) {
                    if (!value) {
                        this.regulatoryLead = value;
                    }
                },
                updatePicklistValues() {
                    fetch(`/data/update-picklist`, this.getFetchOptions('POST', JSON.stringify(this.countries)))
                        .then(r => r.json())
                        .then(result => {
                            this.allPicklists = result;
                            this.submissionTypes = this.getPicklistValuesByType('Submission Type');
                            this.submissionUnits = this.getPicklistValuesByType('Submission Unit');
                            this.submissionModes = this.getPicklistValuesByType('Submission Mode');
                        });
                },
                getPicklistValuesByType(type) {
                    let picklistType = this.picklistTypes.find(t => t.name === type);
                    return this.allPicklists.filter(x => x.picklistTypeId === picklistType.id);
                },
                showSerialNumberField() {
                    if (this.usCountry) {
                        return this.selectedCountries.includes(this.usCountry.id);
                    }
                },
                documentType(index) {
                    return this.documentTypes.find(x => x.id === +index).name;
                },
                expandedDocument(name, index) {
                    if (this.expandedDocumentName == name) {
                        this.expandedDocumentName = "";
                    }
                    else {
                        this.expandedDocumentName = name;
                    }
                },
                expandedDocumenType(index) {
                    if (this.expandedType == index) {
                        this.expandedType = 0;
                    }
                    else {
                        this.expandedType = index;
                    }
                },
                onDocshiftDocument(val) {
                    this.uploading = val;
                },
                onDownloadDocument(value) {
                    if (value) {
                        this.downloadDocument();
                    }
                },
                downloadDocument() {
                    fetch(`/submissions/${this.submission.uniqueId}/download/${this.currentDocument.name}?clientId=${this.client.id}&applicationId=${this.application.id}&documentTypeId=${this.currentDocument.documentTypeId}&version=${this.currentDocument.version}`, this.getFetchOptions('GET'))
                        .then(result => result.json())
                        .then(result => {
                            document.location.href = result;
                        })
                        .catch(_ => {
                            plx.toast.show(`Could not download dossier`, 2, 'failed', null, 2000);
                        });
                },
                removeDocument() {
                    this.uploading = true;
                    var url = `/submissions/${this.submission.uniqueId}/clear/${this.currentDocument.name}?clientId=${this.client.id}&applicationId=${this.application.id}&documentTypeId=${this.currentDocument.documentTypeId}&version=${this.currentDocument.version}`
                    if (this.currentDocument.documentTypeId === this.dossierDocumentTypeId) {
                        url = `/submissions/${this.submission.uniqueId}/clear/${this.currentDocument.name}?clientId=${this.client.id}&applicationId=${this.application.id}`
                    }
                    return fetch(url, this.getFetchOptions('DELETE'))
                        .then(result => {
                            if (result.ok) {
                                this.showDropZone = false;
                                this.fileName = null;
                                this.removeDocumentFromDb();
                                this.uploading = false;
                                var index = this.submission.documents.indexOf(this.currentDocument);
                                if (index > -1) {
                                    this.submission.documents.splice(index, 1);
                                }
                            } else plx.toast.show(`Could not remove dossier`, 2, 'failed', null, 2000);
                        });
                },
                onRemoveDocument(yesButtonPressed) {
                    if (yesButtonPressed) {
                        this.removeDocument()
                            .then(result => {
                                var message = ``;
                                if (this.currentDocument.documentTypeId == this.sourceDocumentTypeId || this.currentDocument.documentTypeId == this.submissionReadyDocumentTypeId) {
                                    message = `${this.currentDocument.name}, version ${this.currentDocument.version} has been removed`;
                                } else {
                                    message = `${this.currentDocument.name} has been removed`;
                                }
                                plx.toast.show(message, 2, 'confirm', null, 2000);
                            })
                            .catch(_ => {
                                plx.toast.show(`Could not remove ${this.currentDocument.name}`, 2, 'failed', null, 2000);
                            });
                    }
                },
                onSelectedDocument(document) {
                    this.currentDocument = document;

                    if (this.currentDocument.documentTypeId == this.dossierDocumentTypeId) {
                        this.removeFileConfig.message = `Do you want to remove the submission dossier ${this.currentDocument.name}?`;
                    } else {
                        this.removeFileConfig.message = `Do you want to remove ${this.currentDocument.name}, version ${this.currentDocument.version}?`;
                    }
                },
                fileUploading(value) {
                    this.showDropZone = value;
                    this.uploading = value;
                    plx.toast.close();
                },
                fileAdded(value) {
                    this.fileName = value;
                    this.showDropZone = false;
                    this.documentConfig.showViewButton = this.showViewDossierButton();
                    this.addDocumentToDb();
                    this.updateDossierFormatInDB();
                },
                alertWhenUploadingToDocshifter(event) {
                    return confirm("Sending files to Docshifter, are you sure you want to cancel the task?");
                },
                latestFileVersion({ version: version, fileName: fileName }) {

                    this.futureFileVersion = ++version;

                    if (this.selectedDocumentTypeId === this.dossierDocumentTypeId) {
                        this.uploadConfig.uploadLink = `${this.baseUploadLink}`;
                        this.uploadConfig.unpackLink = `${this.baseUnpackLink}`;
                    } else {
                        this.uploadConfig.uploadLink = `${this.baseUploadLink}&documentTypeId=${this.selectedDocumentTypeId}&version=${this.futureFileVersion}`;
                        this.uploadConfig.unpackLink = `${this.baseUnpackLink}&documentTypeId=${this.selectedDocumentTypeId}&version=${this.futureFileVersion}`;
                    }


                    var isSubmissionReadyVersionUploaded = this.submission.documents.find(x => x.name === fileName &&
                        x.documentTypeId === this.submissionReadyDocumentTypeId &&
                        x.version === version);
                    this.uploadVersionDocumentsConfig.message = 'A zip file with this name already exists. A new version will be created and saved.';
                    if (this.futureFileVersion == 1) {
                        this.$refs.uploadComponent.uploadFile();
                    }
                    else {
                        document.getElementsByName("versionDocument")[0].click();
                        this.uploading = false;
                    }
                },
                addDocumentToDb() {
                    fetch(`/submissions/${this.submission.id}/document`, this.getFetchOptions('POST',
                        JSON.stringify({ name: this.fileName, documentTypeId: this.selectedDocumentTypeId, submissionId: this.submission.id, version: this.futureFileVersion })))
                        .then(result => result.json())
                        .then(result => {
                            console.log(result);
                            if (result.documentTypeId == this.sourceDocumentTypeId || result.documentTypeId == this.submissionReadyDocumentTypeId) {
                                this.removeFileConfig.message = `Do you want to remove ${result.name}, version ${result.version}?`;
                            } else {
                                this.removeFileConfig.message = `Do you want to remove the submission dossier ${result.name}?`;
                            }
                            this.submission.documents = [...this.submission.documents, result];
                            this.showDropZone = false;
                            this.uploading = false;
                            setTimeout(() => this.$refs.yesNoRemoveDocument.reAssign(), 500);
                            this.selectedDocumentTypeId = '';
                        });
                },
                removeDocumentFromDb() {
                    fetch(`/submissions/${this.submission.id}/document/${this.currentDocument.id}`, this.getFetchOptions('DELETE'))
                        .then(result => {
                            console.log(result);
                        });
                },
                updateDossierFormatInDB() {
                    fetch(`/submissions/${this.submission.id}/submission-dossier`, this.getFetchOptions('PATCH', JSON.stringify(this.selectedDossierFormatId.toString())))
                        .then(result => {
                        });
                },
                changePublishers(items) {

                    if (items.length > 0 && items.every(this.checkIfItemIsSelectedFromDropdown)) {
                        this.arePublishersSelected = true;
                        this.selectedPublishersErrorMessage = null;
                    } else if (items.length == 0) {
                        this.selectedPublishersErrorMessage = null;
                    } else {
                        this.arePublishersSelected = false;
                        this.selectedPublishersErrorMessage = "Please remove your typed name and select your name from the email list shown";
                    }

                    this.addedPublishers = items;
                },
                checkIfItemIsSelectedFromDropdown(item) {
                    return item.hasOwnProperty('id');
                },
                deleteCountries() {
                    const options = Array.from(this.countriesSelectElement.children);
                    if (this.selectedCountriesId.length === 0) {
                        plx.toast.show('Please select countries for removing', 5, 'warning', null, 2000);
                        this.deleteKey += 1;
                        return;
                    } else if (this.selectedCountriesId.length === options.length) {
                        plx.toast.show('Cannot remove all countries from a submission', 5, 'warning', null, 2000);
                        this.deleteKey += 1;
                        return;
                    }
                    else if (options.length === 1) {
                        plx.toast.show('At least one country should be associated to a submission', 5, 'warning', null, 2000);
                        this.deleteKey += 1;
                        return;
                    }

                    const labels = this.countries.filter(x => this.selectedCountriesId.includes(x.id)).map(x => x.name);

                    this.deleteCountryConfig.message = `Do you want to remove the ${labels.length === 1 ? "country" : "countries"} ${labels.join(', ')}?`;
                    document.getElementsByName(this.deleteCountryConfig.elementName)[0].click();
                },
                resetCountries() {
                    document.getElementsByName(this.resetCountriesConfig.elementName)[0].click();
                },
                removeCountry() {
                    this.selectedCountriesId.forEach(elementId => {
                        let element = this.countries.find(x => x.id === elementId);
                        const index = this.countries.indexOf(element);
                        this.countries.splice(index, 1);
                        this.selectedCountries = this.countries.map(x => x.id);
                    })
                },
                onDeleteCountries(yesButtonPressed) {
                    if (yesButtonPressed) {
                        this.removeCountry();
                        this.updatePicklistValues();
                    }
                    this.deleteKey += 1;
                },
                onResetCountries(yesButtonPressed) {
                    if (yesButtonPressed) {
                        this.countries = [...this.applicationCountries];
                        this.selectedCountries = this.countries.map(x => x.id);
                        this.selectedCountriesId = Vue.ref([]);
                    }
                },
                onProceedToArchive(yesButtonPressed) {
                    if (yesButtonPressed) {
                        document.getElementById("hiddenSave").click();
                    }

                    this.moveToKey++;
                },
                onProceedToObsolete(yesButtonPressed) {
                    if (yesButtonPressed) {
                        fetch(`/submissions/obsolete-state/${this.submission.id}`, {
                            method: 'POST',
                            credentials: 'same-origin',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': token
                            }
                        }).then(result => {
                            if (result.ok) {
                                document.location.href = '/submissions/view/' + this.submission.id;
                            }
                        });
                    }
                },
                moveToObsolete() {
                    this.obsolеteStateConfig.noButton = 'Stay on ' + this.lifeCycleState;
                },
                clearSelectedCountries() {
                    Array.from(this.countriesSelectElement.children).forEach(child => {
                        child.selected = false;
                    });

                    this.selectedCountriesId = Vue.ref([]);
                },
                onUploadVersionDocument(yesButtonPressed) {
                    if (yesButtonPressed) {
                        this.$refs.uploadComponent.uploadFile();
                        this.uploading = true;
                    }
                    else {
                        this.showDropZone = false;
                        this.uploading = false;
                        this.$refs.uploadComponent.removeFile();
                    }
                },
                getFetchOptions(method, body) {
                    return {
                        method: method,
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': token
                        },
                        body: body
                    };
                },
                groupBy(xs, key) {
                    return xs.reduce(function (rv, x) {
                        (rv[x[key]] = rv[x[key]] || []).push(x);
                        return rv;
                    }, {});
                },
                handleSubmit(event) {
                    const validResourceComments = this.validateInput(event, 'SubmissionResource_Comments', 'resourceCommentsValidationMessage');
                    const validSubmissionComments = this.validateInput(event, 'Submission_Comments', 'submissionCommentsValidationMessage');
                    const validDescription = this.validateInput(event, 'Submission_Description', 'submissionDescriptionValidationMessage');

                    return validDescription || validSubmissionComments || validResourceComments;
                },
                validateInput(event, elementName, validationMessage) {
                    const fieldName = elementName.match(/_(.+)$/)?.[1] || '';
                    var inputValue = $(`#${elementName}`).val();
                    var unmatchedChars = findUnmatchedCharacters(inputValue);

                    if (unmatchedChars._value.length > 0 && fieldName) {
                        event.preventDefault();
                        $(`#${validationMessage}`).text(`${fieldName} contains invalid characters: ` + unmatchedChars._value.join(' '));
                        return false;
                    }

                    $(`#${validationMessage}`).text('');
                    return true;
                },
                validateDate(elementName, elementId) {
                    const fieldName = elementName.match(/_(.+) $ /)?.[1] || '';
                    var dateValue = $(`#${elementName}`).val();

                    if (dateValue) {
                        const date = new Date(dateValue);
                        const year = date.getFullYear();
                        const month = date.getMonth();
                        const day = date.getDate();

                        if (!this.isValidDate(date) || !this.isDateInRange(date, 1900, 9999)) {
                            $(`#${elementId}`).text('Please enter a valid date');
                            return false;
                        }
                    }

                    return true;
                },
                isValidDate(date) {
                    return !isNaN(date.getTime()) && date.getFullYear() >= 1 && date.getFullYear() <= 9999;
                },
                isDateInRange(date, minYear, maxYear) {
                    const year = date.getFullYear();
                    const month = date.getMonth() + 1;
                    const day = date.getDate();

                    return year >= minYear && year <= maxYear
                        && month >= 1 && month <= 12
                        && day >= 1 && day <= this.daysInMonth(date);
                },
                daysInMonth(date) {
                    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
                },
                hasInvalidDates() {
                    var validHealthAuthorityDate = this.validateDate('Submission_HealthAuthorityDueDate', 'healthAuthorityDueDateId');
                    var validAuthoringDeadline = this.validateDate('Submission_AuthoringDeadline', 'authoringDeadlineId');
                    var validPlannedDispatchDate = this.validateDate('Submission_PlannedDispatchDate', 'plannedDispatchDateId');
                    var validPLannedSubmissionDate = this.validateDate('Submission_PlannedSubmissionDate', 'plannedSubmissionDateId');
                    var validActualDispatchDate = this.validateDate('Submission_ActualDispatchDate', 'actualDispatchDateId');
                    var validActualSubmissionDate = this.validateDate('Submission_ActualSubmissionDate', 'actualSubmissionDateId');


                    return !validHealthAuthorityDate || !validAuthoringDeadline ||
                        !validPlannedDispatchDate || !validPLannedSubmissionDate ||
                        !validActualDispatchDate || !validActualSubmissionDate;                   
                }
            },
            created() {
                if (this.submission.submissionUnitId) {
                    this.selectedSubmissionUnitId = this.submission.submissionUnitId;
                }
                if (this.submission.submissionModeId) {
                    this.selectedSubmissionModeId = this.submission.submissionModeId;
                }
                if (this.submission.projectId) {
                    let project = this.projects.find(x => x.id === this.submission.projectId);
                    this.opportunityNumber = project.opportunityNumber;
                    this.projectName = project.name;
                }
                if (this.submission.dossierFormatId) {
                    this.selectedDossierFormatId = this.submission.dossierFormatId;
                }
            },
            mounted() {
                // Add a submit event listener to the form
                document.getElementById('form').addEventListener('submit', this.handleSubmit);
                if (this.submissionResource) {
                    this.regulatoryLead = this.submissionResource.regulatoryLead;
                    this.publishingLead = this.submissionResource.publishingLead;
                    var autocompleteElements = document.getElementsByClassName('autocomplete');
                    var autocompleteListElements = document.getElementsByClassName('autocomplete-list');
                    this.publishersElement = autocompleteListElements[0].nextElementSibling.firstChild;
                    this.publishingLeadElement = autocompleteElements[0].firstChild;
                    this.regulatoryLeadElement = autocompleteElements[1].firstChild;
                    this.publishingLeadElement.value = this.submissionResource.publishingLead;
                    this.regulatoryLeadElement.value = this.submissionResource.regulatoryLead;
                }

                if ((this.submission.lifecycleStateId == this.archivedLifecycleStage &&
                    this.isUserEditor && !this.isUserAdmin) || this.isUserExternalEditor) {
                    this.isArchiveStage = true;
                    this.publishersElement.readOnly = true;
                    this.publishingLeadElement.readOnly = true;
                    this.regulatoryLeadElement.readOnly = true;

                    var autocompleteListElements = document.getElementsByClassName('autocomplete-list')[0];

                    for (var i = 0; i < autocompleteListElements.children.length; i++) {
                        autocompleteListElements.children[i].children[1].style.pointerEvents = "none";
                    }
                }

                if ((this.currentStage >= this.submittedLifecycleState) && this.isUserEditor && !this.isUserAdmin && this.isUserEditor) {
                    this.isRemovalOfCountriesDisabled = true;
                }

                this.countriesSelectElement = document.getElementById("countriesSelectId");

                document.getElementsByName('SubmissionResource.EstimatedHours')[0].value = this.submission.submissionResource.estimatedHours;

                this.showCespNumberField = this.submission.deliveryDetailsId !== +this.deliveryDetails.find(x => x.text === 'Publish & Send to Client')?.value;

                this.setAsteriskToRequiredFields();

                this.baseUploadLink = `/submissions/${this.submission.uniqueId}/link/{fileName}?clientId=${this.client.id}&applicationId=${this.application.id}`;
                this.baseUnpackLink = `/submissions/${this.submission.uniqueId}/unpack/{fileName}?clientId=${this.client.id}&applicationId=${this.application.id}`;
                this.baseVersionLink = `/submissions/${this.submission.id}/{fileName}/version?documentTypeId={documentTypeId}`;

                this.documentConfig.clientId = this.client.id;
                this.documentConfig.applicationId = this.application.id;
                this.documentConfig.submissionUniqueId = this.submission.uniqueId;
                this.documentConfig.dossierDocumentTypeId = this.dossierDocumentTypeId;
                this.documentConfig.sourceDocumentTypeId = this.sourceDocumentTypeId;
                this.documentConfig.submissionId = this.submission.id;
                this.documentConfig.showViewButton = this.showViewDossierButton();
                this.documentConfig.userAdmin = this.isUserAdmin;
                this.documentConfig.userEditor = this.isUserEditor;
                this.documentConfig.userExternalEditor = this.isUserExternalEditor;
                this.documentConfig.isArchiveStage = this.isArchiveStage;
                this.documentConfig.isPrivilegedExternalEditorUser = this.isPrivilegedExternalEditorUser;
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/WorkflowProgress" />
    <partial name="Components/AutoCompleteList" />
    <partial name="Components/YesNoDialog" />
    <partial name="Components/Upload" />
    <partial name="Components/Document" />
}
