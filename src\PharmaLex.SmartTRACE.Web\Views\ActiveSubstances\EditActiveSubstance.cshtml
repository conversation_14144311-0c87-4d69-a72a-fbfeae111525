﻿@model EditActiveSubstanceViewModel
@using PharmaLex.SmartTRACE.Entities.Enums
@inject AutoMapper.IMapper mapper

@{
    var clientSelectList = Model.Clients.OrderBy(x => x.Name).Select(x => new SelectListItem
    {
        Text = $"{x.Name}",
        Value = x.Id.ToString(),
    });

    var activeSubstanceLifecycleStates = mapper.Map<IEnumerable<ActiveSubstanceLifecycleStateList>>(Enum.GetValues(typeof(CommonLifecycleState)));
}

<div id="activeSubstance" class="manage-container">
    <header class="manage-header">
        <h3>@Html.Encode(Model.ActiveSubstance.Name) Active Substance</h3>
        <div class="common-state-container" :stateId="lifecyclestateId">
            <span class="common-state">{{ lifecycleState }}</span>
        </div>
        @if (Model.ActiveSubstance.Id != 0)
        {
            <a class="button icon-button-delete" href="/active-substances/delete/@Model.ActiveSubstance.Id">Delete</a>
        }
        <a href="/active-substances" class="button secondary icon-button-back">Back to Active substance list</a>
        <button class="button secondary icon-button-next" id="obsoleteState" v-show="displayObsoleteButton" name="moveToObsolete">Move to @(CommonLifecycleState.Obsolete.GetDescription())</button>
    </header>
    <form id="activesubstance-form" method="post">
        @Html.AntiForgeryToken()
        <div class="form-col">
            <label for="ActiveSubstance.Name">Name*</label>
            <input asp-for="ActiveSubstance.Name" type="text" v-bind:class="{'validation-error' : hasDuplicate}" required :aria-disabled="isObsolete" />
            <div id="validationMessage" style="color:red;">
            </div>
            @if (Model.ActiveSubstance.HasDuplicate)
            {
                <span class="field-duplication-error">@Model.ActiveSubstance.ErrorMessage</span>
            }
            <label for="ActiveSubstance.ClientId">Client*</label>
            <div class="select-wrapper">
                <select asp-for="ActiveSubstance.ClientId" asp-items="clientSelectList" required :aria-disabled="isObsolete">
                    <option value="">Select client</option>
                </select>
            </div>
        </div>
        <div class="buttons">
            <a class="button secondary icon-button-cancel" href="/active-substances">Cancel</a>
            <button class="icon-button-save" :aria-disabled="isObsolete">Save</button>
        </div>
        <input type="hidden" asp-for="ActiveSubstance.Id" />
        <input type="hidden" asp-for="ActiveSubstance.LifecycleStateId" />
        <yes-no-dialog :config="moveToConfig" v-on:button-pressed="onMoveTo"></yes-no-dialog>
    </form>
</div>

@section Scripts {
    <script src="@Cdn.GetUrl("lib/jquery/dist/jquery.min.js")"></script>
    <script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0]?.value;
        var pageConfig = {
            appElement: '#activeSubstance',
            data() {
                return {
                    hasDuplicate: @Model.ActiveSubstance.HasDuplicate.ToString().ToLower(),
                    activeSubstanceId: @Model.ActiveSubstance.Id,
                    obsoleteStateId: @Html.Raw((int)CommonLifecycleState.Obsolete),
                    lifecycleState: null,
                    lifecyclestateId: @Html.Raw(Model.ActiveSubstance.LifecycleStateId),
                    activeStateId: @Html.Raw((int)CommonLifecycleState.Active),
                    isProductAssigned: @Html.Raw(Model.IsProductAssigned.ToString().ToLower()),
                    activeSubstanceId: @Model.ActiveSubstance.Id,
                    activeSubstanceLifecycleStates: @Html.Raw(Json.Serialize(activeSubstanceLifecycleStates)),
                    moveToConfig: {
                        message: 'Do you want to move to Obsolete?',
                        noButton: 'Cancel',
                        yesButton: 'Continue',
                        elementName: 'moveToObsolete',
                        buttonClass: 'icon-button-next'
                    },
                    isObsolete: false,
                };
            },
            methods: {
                handleSubmit(event) {
                    var inputValue = $('#ActiveSubstance_Name').val();
                    var unmatchedChars = findUnmatchedCharacters(inputValue);
                    if (unmatchedChars._value.length > 0) {
                        event.preventDefault();
                        $('#validationMessage').text("Name contains invalid characters: " + unmatchedChars._value.join(' '));
                        $('#validationMessage').css('color', 'red');
                        return false;
                    }
                    $('#validationMessage').text('');
                    return true;
                },
                handleChange(event) {
                    $('#ActiveSubstance_Name').change(function () {
                        $('#validationMessage').text('');
                    })
                },
                onMoveTo(yesButtonPressed) {
                    if (yesButtonPressed) {
                        fetch(`/active-substances/state/${this.activeSubstanceId}`, this.getFetchOptions('POST', JSON.stringify(this.obsoleteStateId.toString())))
                            .then(r => r.json())
                            .then(stateId => {
                                this.lifecyclestateId = stateId;
                                this.lifecycleState = this.activeSubstanceLifecycleStates.find(x => x.id === this.lifecyclestateId)?.name;
                                if (this.lifecyclestateId == this.obsoleteStateId) {
                                    this.isObsolete = true;
                                }
                            });
                    }
                },
                getFetchOptions(method, body) {
                    return {
                        method: method,
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            'RequestVerificationToken': token
                        },
                        body: body
                    };
                },
            },
            computed: {
                displayObsoleteButton() {
                    return !this.isProductAssigned && (this.lifecyclestateId == this.activeStateId);
                }
            },
            created() {
                this.lifecycleState = this.activeSubstanceLifecycleStates.find(x => x.id === this.lifecyclestateId)?.name;
            },
            mounted() {
                document.getElementById('activesubstance-form').addEventListener('submit', this.handleSubmit);
                document.getElementById('ActiveSubstance_Name').addEventListener('change', this.handleChange);
                this.isObsolete = this.lifecyclestateId == this.obsoleteStateId;
            }
        };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/YesNoDialog" />
}
