﻿using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class EditApplicationViewModel
    {
        public EditApplicationViewModel()
        {
            this.Picklists = new List<PicklistDataModel>();
            this.Products = new List<ProductModel>();
            this.Clients = new List<ClientModel>();
            this.AllCountries = new List<CountryModel>();
        }

        public ApplicationModel Application { get; set; }
        public bool ConcernedMemberStatesRequired { get; set; }

        public IList<PicklistDataModel> Picklists { get; set; }
        public IList<ProductModel> Products { get; set; }
        public IList<ClientModel> Clients { get; set; }
        public IList<CountryModel> AllCountries { get; set; }
        public IList<ActiveSubstanceModel> ActiveSubstances { get; set; }
    }
}
