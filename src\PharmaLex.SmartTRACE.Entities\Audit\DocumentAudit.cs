﻿using PharmaLex.DataAccess;

namespace PharmaLex.SmartTRACE.Entities.Audit
{
    public partial class DocumentAudit : AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }

        public int Id { get; set; }
        public string Name { get; set; }
        public int DocumentTypeId { get; set; }
        public int Version { get; set; }
        public int SubmissionId { get; set; }
    }
}
