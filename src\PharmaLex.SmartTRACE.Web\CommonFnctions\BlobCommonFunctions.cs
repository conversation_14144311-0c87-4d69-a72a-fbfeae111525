﻿using PharmaLex.SmartTRACE.Web.DataFactory;

namespace PharmaLex.SmartTRACE.Web.CommonFnctions
{
    public static class BlobCommonFunctions
    {
        public static string GetUploadLink(IAzureDataFactoryManagementClient adfClient, string fileName, string clientId, string applicationId, string submissionUniqueId, string documentTypeId, string version)
        {
            string uploadFilePath;

            if (!string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(applicationId) && !string.IsNullOrEmpty(submissionUniqueId) &&
                !string.IsNullOrEmpty(documentTypeId) && !string.IsNullOrEmpty(version))
            {
                uploadFilePath = adfClient.GetUploadPath(fileName, clientId, applicationId, submissionUniqueId, documentTypeId, version);
            }
            else
            {
                uploadFilePath = adfClient.GetUploadPath(fileName, clientId, applicationId, submissionUniqueId);
            }

            return uploadFilePath;
        }

    }
}
