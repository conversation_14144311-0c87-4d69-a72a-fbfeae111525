﻿using PharmaLex.SmartTRACE.Entities.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

#nullable enable

namespace PharmaLex.SmartTRACE.Web.Helpers.Builders
{
    public static class ExpressionBuilder<T> where T : class
    {
        public static Func<IQueryable<T>, IOrderedQueryable<T>> BuildSortExpression<TKey>(string? sort, 
            Dictionary<string, Expression<Func<T, object>>> sortExpressions, Expression<Func<T, TKey>> orderBy)
        {
            if (string.IsNullOrWhiteSpace(sort))
            {
                return sub => sub.OrderByDescending(orderBy);
            }

            var orderSegments = sort.Split("=>");
            var propName = orderSegments[0];
            var order = orderSegments[1];
            var isAsc = order.Equals(OrderType.asc.ToString(), StringComparison.OrdinalIgnoreCase);

            if (sortExpressions.TryGetValue(propName.ToLowerInvariant(), out var func))
            {
                return CompareAndOrderBy(func);
            }

            return sub => sub.OrderBy(orderBy);

            Func<IQueryable<T>, IOrderedQueryable<T>> CompareAndOrderBy<TKey>(
                Expression<Func<T, TKey>> expression) => isAsc ?
                    sub => sub.OrderBy(expression) :
                    sub => sub.OrderByDescending(expression);
        }
    }
}
