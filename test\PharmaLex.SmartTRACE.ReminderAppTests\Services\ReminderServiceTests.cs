﻿using Grpc.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.RemindersApp;
using PharmaLex.SmartTRACE.RemindersApp.Models;
using PharmaLex.SmartTRACE.RemindersApp.Services;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.WebTests;

namespace PharmaLex.SmartTRACE.ReminderAppTests.Services
{
    public class ReminderServiceTests
    {
        private IRepositoryFactory _repoFactory;
        private readonly IEmailService _emailService;
        private readonly IPickListReminderHelper _pickListReminderHelper;
        private readonly IConfiguration _configuration;
        private readonly ILogger _logger;
      
        public ReminderServiceTests()
        {
            _emailService = Substitute.For<IEmailService>();
            _pickListReminderHelper = Substitute.For<IPickListReminderHelper>();
            _configuration = Substitute.For<IConfiguration>();
            _logger = Substitute.For<ILogger>();
         
            
        }
        [Fact]
        public async Task SendPlannedSubmissionDateReminders_Task_Throws_InvalidOperationException()
        {
            //Arrange
            IRepositoryFactory _repoFactory = Substitute.For<IRepositoryFactory>();
            var ReminderSernice = new ReminderService(_repoFactory, _emailService, _pickListReminderHelper, _configuration);

            //Act    & assert    
            var ex = await Assert.ThrowsAsync<InvalidOperationException>(() => ReminderSernice.SendPlannedSubmissionDateReminders(_logger));

        }
        [Fact]
        public async Task SendPlannedSubmissionDateReminders_Task_Completes()
        {
            IntialiseDB();
            //Arrange
           _configuration.GetValue<string>("AppSettings:PlannedSubmissionReminderNotificationTemplateId").Returns("34251");
            await _emailService.SendAsync(Arg.Any<NotificationModel>(), Arg.Any<string>());

            var ReminderSernice = new ReminderService(_repoFactory, _emailService, _pickListReminderHelper, _configuration);

            //Act
            await ReminderSernice.SendPlannedSubmissionDateReminders(_logger);

        }
       
        [Fact]
        public async Task SendPlannedDispatchDateReminders_Task_Completes()
        {
            IntialiseDB();
            //Arrange
            _configuration.GetValue<string>("AppSettings:PlannedSubmissionReminderNotificationTemplateId").Returns("34251");
            await _emailService.SendAsync(Arg.Any<NotificationModel>(), Arg.Any<string>());

            var ReminderSernice = new ReminderService(_repoFactory, _emailService, _pickListReminderHelper, _configuration);

            //Act
            await ReminderSernice.SendPlannedDispatchDateReminders(_logger);

        }
        [Fact]
        public async Task SendPlannedDispatchDateReminders_Task_Throws_InvalidOperationException()
        {
            //Arrange
            IRepositoryFactory _repoFactory = Substitute.For<IRepositoryFactory>();
            var ReminderSernice = new ReminderService(_repoFactory, _emailService, _pickListReminderHelper, _configuration);
           
            //Act    & assert    
            var ex = await Assert.ThrowsAsync<InvalidOperationException>(() => ReminderSernice.SendPlannedDispatchDateReminders(_logger));

          


        }
        private  void IntialiseDB()
        {
               List<Submission> submissions = new List<Submission>
        {
            new (){ PlannedDispatchDate=DateTime.Today.AddDays(+1), UniqueId ="001", Project = new Project {  Id=22, CreatedBy = "test", LastUpdatedBy = "test" ,Client = new Client { Id = 1,CreatedBy="test",LastUpdatedBy="test"  } }, LifecycleStateId = (int)SubmissionLifeCycleState.Draft ,SubmissionTypeId=1,PlannedSubmissionDate=DateTime.Today.AddDays(+30),ApplicationId=1,CreatedBy="test",LastUpdatedBy="test",SubmissionResource= new SubmissionResource() {Id=1, PublishingLead = "test{group{test{structT\r\n(email33)\r\n", CreatedBy = "test",RegulatoryLead="test{group{test{structT\r\n(email)\r\n", LastUpdatedBy = "test" } },
            new () {PlannedDispatchDate=DateTime.Today.AddDays(+2), UniqueId="002",Project = new Project {Id=33,  CreatedBy = "test", LastUpdatedBy = "test" ,Client = new Client { Id = 2 ,CreatedBy="test",LastUpdatedBy="test" }}, LifecycleStateId = (int)SubmissionLifeCycleState.ReadyForPublishing ,SubmissionTypeId=2,PlannedSubmissionDate=DateTime.Today.AddDays(+30),ApplicationId=2,CreatedBy="test",LastUpdatedBy="test",SubmissionResource=new SubmissionResource() {Id=2, PublishingLead = "test{structT\r\n(email232)\r\n", CreatedBy = "test",RegulatoryLead="test{structT\r\n(email)\r\n", LastUpdatedBy = "test" } },
            new () {PlannedDispatchDate=DateTime.Today.AddDays(+3), UniqueId="003",Project = new Project {Id=44, CreatedBy = "test", LastUpdatedBy = "test" ,Client = new Client { Id = 3,CreatedBy="test",LastUpdatedBy="test"  }   }, LifecycleStateId = (int)SubmissionLifeCycleState.ReadyForPublishing ,SubmissionTypeId=3,PlannedSubmissionDate=DateTime.Today.AddDays(+60),ApplicationId=3,CreatedBy="test",LastUpdatedBy="test" ,SubmissionResource=new SubmissionResource() {Id=3, PublishingLead = "testmail233 (<EMAIL>)", CreatedBy = "test",RegulatoryLead="regleademail", LastUpdatedBy = "test" }},
             };
         List<Application> app = new List<Application>() { new() { Id = 1, ApplicationNumber = "110", CreatedBy = "test", LastUpdatedBy = "test" }, new() { Id = 3, ApplicationNumber = "111", CreatedBy = "test", LastUpdatedBy = "test" }, new() { Id = 2, ApplicationNumber = "112", CreatedBy = "test", LastUpdatedBy = "test" } };

        var dbctxReslover = TestHelpers.GetPlxDbContextResolver();

            var dbCtx = ((SmartTRACEContext)dbctxReslover.Context);

            if (!dbCtx.PicklistData.Any(x => x.Name == "IND Safety Reports" || x.Name == "Annual Report"))
            {
                dbCtx.PicklistData.Add(new PicklistData { Id = 1, Name = "IND Safety Reports", CreatedBy = "test", LastUpdatedBy = "test" });
                dbCtx.PicklistData.Add(new PicklistData { Id = 2, Name = "Annual Report", CreatedBy = "test", LastUpdatedBy = "test" });
            }
            if (!dbCtx.Submission.Any(x => x.UniqueId == "001" || x.UniqueId == "002" || x.UniqueId == "003"))
            {
                dbCtx.Submission.AddRange(submissions);
            }
            if (!dbCtx.Application.Any(x => x.Id == 1 || x.Id == 2 || x.Id == 3))
            {
                dbCtx.Application.AddRange(app);
            }
            if (!dbCtx.SubmissionPublisher.Any(x => x.Id == 1 || x.Id == 2 || x.Id == 3))
            {
                dbCtx.SubmissionPublisher.Add(new SubmissionPublisher { Id = 1, PublisherId = 1, CreatedBy = "test", LastUpdatedBy = "test", SubmissionResourceId = 1, Publisher = new User() { Id = 23, FamilyName = "test45", UserTypeId = 3, CreatedBy = "test", LastUpdatedBy = "test2", Email = "<EMAIL>" } });
                dbCtx.SubmissionPublisher.Add(new SubmissionPublisher { Id = 2, PublisherId = 2, CreatedBy = "test", LastUpdatedBy = "test", SubmissionResourceId = 2, Publisher = new User() { Id = 24, FamilyName = "test23", UserTypeId = 4, CreatedBy = "test", LastUpdatedBy = "test", Email = "<EMAIL>" } });
                dbCtx.SubmissionPublisher.Add(new SubmissionPublisher { Id = 3, PublisherId = 3, CreatedBy = "test", LastUpdatedBy = "test", SubmissionResourceId = 3, Publisher = new User() { Id = 25, FamilyName = "test23", UserTypeId = 5, CreatedBy = "test", LastUpdatedBy = "test", Email = "<EMAIL>" } });

            }
            dbCtx.SaveChanges();
            _repoFactory = new RepositoryFactory(dbctxReslover, new SubmissionContext());
        }
    }
}


