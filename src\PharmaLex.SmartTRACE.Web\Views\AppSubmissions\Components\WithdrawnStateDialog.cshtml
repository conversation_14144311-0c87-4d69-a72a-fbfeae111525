﻿<script type="text/x-template" id="withdrawn-state-template">
    <div class="dialog-surface" v-if="!closed" v-on:click="close">
        <div class="dialog-container" v-on:click.stop style="display: block; width: 500px; height: 330px;">
            <i class="icon-cancel-circled dialog-closer" v-on:click="close"></i>
            <div class="dialog-content">
                <h5>Additional info</h5>
                 <ul v-if="!validDate">
                    <li class="field-validation-error">Withdrawal date: Please fill out this field.</li>
                 </ul>
                <label for="subject">Withdrawal date</label>
                <input id="withdrawalDate"
                       v-model="withdrawalDate"
                       type="date"
                       :min="minDate"
                       required />
                <footer>
                    <div style="display: flex; margin-top:20px;">
                        <button v-on:click="close" class="button secondary icon-button-cancel">Cancel</button>
                        <button v-on:click="submit" class="icon-button-save">Submit</button>
                    </div>
                </footer>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
    vueApp.component('withdrawn-state', {
        template: '#withdrawn-state-template',
        data() {
            return {
                closed: true,
                withdrawalDateValid: false,
                minDate: new Date().toISOString().substring(0, 10),
                withdrawalDate: Date.now(),
                validDate: true
            };
        },
        props: {
            config: {
                type: Object,
                default: {}
            }
        },
        methods: {
            submit() {
                this.checkValidity() && fetch(`/submissions/final-state/${this.config.id}`, {
                    method: 'POST',
                    credentials: 'same-origin',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': token
                    },
                    body: JSON.stringify(this.withdrawalDate)
                }).then(result => {
                    if (result.ok) {
                        this.close();
                        plx.toast.show('Submission is updated', 2, 'confirm', null, 2500);
                        setTimeout(() => {
                            location.reload();
                        }, 3000);
                    }
                });
            },
            close() {
                this.closed = true;
                this.withdrawalDate = this.config.withdrawalDate || null;
            },
            open() {
                this.closed = false;
            },
            checkValidity() {
                this.validDate = document.getElementById('withdrawalDate').validity.valid;
                return this.validDate;
            }
        },
        mounted() {
            let link = document.getElementById('nextState');

            if (link) {
                link.addEventListener('click', this.open);
            }
        },
        unmounted() {
            let link = document.getElementById('nextState');

            if (link) {
                link.removeEventListener('click', this.open);
            }
        }
    });
</script>
