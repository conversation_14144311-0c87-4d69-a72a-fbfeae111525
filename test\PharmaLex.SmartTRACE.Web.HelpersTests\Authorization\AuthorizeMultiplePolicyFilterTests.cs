﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using PharmaLex.SmartTRACE.Web.Helpers.Authorization;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Authorization
{
    public class AuthorizeMultiplePolicyFilterTests
    {
        private readonly IAuthorizationService _authorizationService;
        public string _policies;
        public bool _all;
        AuthorizeMultiplePolicyFilter _authorizemultiplepolicyfilter;

        public AuthorizeMultiplePolicyFilterTests()
        {
            this._authorizationService = Substitute.For<IAuthorizationService>();
            _authorizemultiplepolicyfilter = new AuthorizeMultiplePolicyFilter(_policies, _all, _authorizationService);
        }
        [Theory]
        [InlineData(true,"system")]
        [InlineData(true, "")]
        [InlineData(false,"admin")]
        [InlineData(false, "")]
        public void OnAuthorization_WithValidLicense_ShouldNotSetResult(bool all, string _policies)
        {
            var context = new DefaultHttpContext();
            var actionContext = new ActionContext(context, new RouteData(), new ActionDescriptor());
            AuthorizeMultiplePolicyFilter _authorizemultiplepolicyfilter = new AuthorizeMultiplePolicyFilter(_policies, all, _authorizationService);

            var authorizationContext = new AuthorizationFilterContext(actionContext, new List<IFilterMetadata>());
            if (all)
            {
                if (_policies == "system")
                {
                    _authorizationService.AuthorizeAsync(actionContext.HttpContext.User, _policies).Returns(Task.FromResult(AuthorizationResult.Failed()));
                }
                else
                {
                    _authorizationService.AuthorizeAsync(actionContext.HttpContext.User, _policies).Returns(Task.FromResult(AuthorizationResult.Success()));
                }
            }
            else 
            {
                if (_policies == "admin")
                {
                    _authorizationService.AuthorizeAsync(actionContext.HttpContext.User, _policies).Returns(Task.FromResult(AuthorizationResult.Success()));
                }
                else
                {
                    _authorizationService.AuthorizeAsync(actionContext.HttpContext.User, _policies).Returns(Task.FromResult(AuthorizationResult.Failed()));
                }
                
            }
            var res = _authorizemultiplepolicyfilter.OnAuthorizationAsync(authorizationContext);
            Assert.NotNull(res);

        }

    }

}
