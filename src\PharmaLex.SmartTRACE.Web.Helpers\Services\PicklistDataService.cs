﻿using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Helpers.Services;

public class PicklistDataService(IDistributedCacheServiceFactory cache) : IPicklistDataService
{
    public async Task<bool> IsPicklistNameDuplicate(string picklistName)
    {
        var picklistDataCache = cache.CreateEntity<PicklistData>();
        var picklistResult = await picklistDataCache.WhereAsync(p => p.Name == picklistName);
        return picklistResult.Count != 0;
    }
}
