﻿using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class ApplicationViewModel: IModel
    {
        public ApplicationViewModel()
        {
            this.Submissions = new List<SubmissionModel>();
        }
        public int Id { get; set; }
        public string Region { get; set; }
        public string Country { get; set; }
        public string ClientName { get; set; }
        public int ClientId { get; set; }
        public string Product { get; set; }
        public string ProcedureType { get; set; }
        public int ProcedureTypeId { get; set; }
        public string ApplicationNumber { get; set; }
        public string ProcedureNumber { get; set; }
        public string MedicinalProductDomain { get; set; }
        public string MedicinalProductType { get; set; }
        public string ApplicationType { get; set; }
        public string ActiveSubstances { get; set; }
        public string ReferenceMemberState { get; set; }
        public string ConcernedMemberState { get; set; }
        public int ProductId { get; set; }
        public string Comments { get; set; }
        public int LifecycleStateId { get; set; }
        public string LifecycleState { get; set; }
        public IList<SubmissionModel> Submissions { get; set; }
    }
}
