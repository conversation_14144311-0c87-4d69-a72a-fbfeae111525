﻿using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.Web.HelpersTests
{
    public class PicklistHelperTests
    {
        private IDistributedCacheServiceFactory _cache;
        private string expectedName = "client1";
        private IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel> _picklistCache;
        public PicklistHelperTests()
        {
            _cache = Substitute.For<IDistributedCacheServiceFactory>();
            _picklistCache= Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
        }
        PicklistDataModel picklistDataModel = new()
        {
            Id = 1,
            Name = "client1"
        };
        List<PicklistDataModel> picklistdatamodel = new List<PicklistDataModel>
                 {
                     new PicklistDataModel {Id = 1,Name = "Dosage Form1" },
                     new PicklistDataModel {Id = 2,Name = "Dosage Form2" },
                 };
        [Fact]
        public async Task ExtractPickList_Retuns_Not_Null()
        {
            // Arrange
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);
            _picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).ReturnsForAnyArgs(Task.FromResult(picklistDataModel));
            // Act
            var result = await PicklistHelper.ExtractPicklist(_cache, 2);
            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedName, result.Name);
        }
        [Fact]
        public async Task GetPicklistByName_Retuns_Not_Null()
        {
            // Arrange
           _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);
            _picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(picklistDataModel));
            // Act
            var result = await PicklistHelper.GetPicklistByName(_cache,"Test", 2);
            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedName, result.Name);
        }
        [Fact]
        public async Task GetPicklistsByPartialName_Retuns_Not_Null()
        {
            // Arrange
            var expectedresult = "Dosage Form1";
            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);
            _picklistCache.WhereAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(picklistdatamodel);
            // Act
            var result = await PicklistHelper.GetPicklistsByPartialName(_cache, "Test");
            var actualresult = result[0].Name;
            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedresult, actualresult);
        }
        [Fact]
        public async Task GetPicklistDataBasedOnCountry_Retuns_Not_Null()
        {
            // Arrange
            IList<CountryModel> countryModels = new List<CountryModel>()
            {
                new CountryModel { Name = "Test", ErrorMessage = "Error", Id = 1, HasError = false, RegionId = 123 }

            };
            List<PicklistDataCountry> picklistDataCountry = new()
            {
            new PicklistDataCountry(){ Id = 1, CountryId=1},
            new PicklistDataCountry(){ Id = 2, CountryId=1}
            };
            var expectedresult = "Dosage Form1";
            var PicklistDataCountryCache = Substitute.For<IEntityCacheServiceProxy<PicklistDataCountry>>();
            _cache.CreateEntity<PicklistDataCountry>().Returns(PicklistDataCountryCache);
            PicklistDataCountryCache.AllAsync().Returns(Task.FromResult(picklistDataCountry));

            _cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);
            _picklistCache.WhereAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(picklistdatamodel);


            // Act
            var result = await PicklistHelper.GetPicklistDataBasedOnCountry(_cache, countryModels);
            var actualresult = result[0].Name;
            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedresult, actualresult);
        }
    }
}
