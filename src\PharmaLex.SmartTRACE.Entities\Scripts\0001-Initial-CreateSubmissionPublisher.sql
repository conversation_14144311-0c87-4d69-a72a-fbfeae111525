﻿CREATE TRIGGER [dbo].[SubmissionPublisher_Insert] ON [dbo].[SubmissionPublisher]
FOR INSERT AS
INSERT INTO [Audit].[SubmissionPublisher_Audit]
SELECT 'I', [SubmissionResourceId], [PublisherId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[SubmissionPublisher_Update] ON [dbo].[SubmissionPublisher]
FOR UPDATE AS
INSERT INTO [Audit].[SubmissionPublisher_Audit]
SELECT 'U', [SubmissionResourceId], [PublisherId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[SubmissionPublisher_Delete] ON [dbo].[SubmissionPublisher]
FOR DELETE AS
INSERT INTO [Audit].[SubmissionPublisher_Audit]
SELECT 'D', [SubmissionResourceId], [PublisherId], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO