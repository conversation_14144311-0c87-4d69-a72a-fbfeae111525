﻿using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities.Audit
{
    public partial class UserAudit: AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }

        public int? Id { get; set; }
        public string Email { get; set; }
        public string GivenName { get; set; }
        public string FamilyName { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public int UserTypeId { get; set; }
        public bool AutoAccessClients { get; set; }
        public string InvitationEmailLink { get; set; }
    }
}
