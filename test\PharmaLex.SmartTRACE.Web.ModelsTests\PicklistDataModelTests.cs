﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class PicklistDataModelTests
    {
        [Fact]
        public void PicklistDataModel_Get_SetValue()
        {
            //Arrange
            var model = new PicklistDataModel();
            model.Id = 1;
            model.Name = "Test";
            model.PicklistTypeId = 1;
            model.CountriesIds=new List<int>();
            model.Country = "Test";
            model.Selected = true;
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void PicklistDataMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new PicklistDataMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
    }
}
