﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class EditSubmissionRequestFormViewModelTests
    {
        [Fact]
        public void EditSubmissionRequestFormViewModel_Get_SetValue()
        {
            //Arrange
            var model = new EditSubmissionRequestFormViewModel();
            model.SubmissionRequestForm = new SubmissionRequestFormModel();
            model.ActiveSubstances = new List<ActiveSubstanceModel>();
            model.Picklists = new List<PicklistDataModel>();
            model.Clients = new List<ClientModel>();
            model.Projects = new List<ProjectModel>();
            model.Countries = new List<CountryModel>();
            //Assert
            Assert.NotNull(model);
        }
    }
}
