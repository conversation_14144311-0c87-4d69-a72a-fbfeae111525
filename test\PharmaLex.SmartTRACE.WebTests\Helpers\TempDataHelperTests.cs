﻿using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Newtonsoft.Json;
using NSubstitute;
using PharmaLex.SmartTRACE.Web.Helpers;

namespace PharmaLex.SmartTRACE.WebTests.Helpers
{
    public class TempDataHelperTests
    {
        [Fact]
        public void Set_SerializesAndStoresValue()
        {
            // Arrange
            var tempData = new TempDataDictionary(new Microsoft.AspNetCore.Http.DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            var key = "testKey";
            var value = new TestObject { Id = 1, Name = "Test" };

            // Act
            TempDataHelper.Set(tempData, key, value);

            // Assert
            Assert.True(tempData.ContainsKey(key));
            var storedValue = tempData[key];
            Assert.NotNull(storedValue);
            var deserializedValue = JsonConvert.DeserializeObject<TestObject>(storedValue?.ToString() ?? string.Empty);

            Assert.NotNull(deserializedValue);
            Assert.Equal(value.Id, deserializedValue.Id);
            Assert.Equal(value.Name, deserializedValue.Name);
        }
        [Fact]
        public void Get_ExistingKey_ReturnsDeserializedObject()
        {
            // Arrange
            var td = Substitute.For<ITempDataDictionary>();
            var expectedObject = new TestObject { Id =1, Name = "Test" };
            var serializedObject = JsonConvert.SerializeObject(expectedObject);
            td.TryGetValue("TestKey", out object? storedValue).Returns(callInfo =>
            {
                callInfo[1] = serializedObject;
                return true;
            });

            // Act
            var result = TempDataHelper.Get<TestObject>(td, "TestKey");

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedObject.Id, result.Id);
            Assert.Equal(expectedObject.Name, result.Name);
        }
        [Fact]
        public void Get_NonExistingKey_ReturnsNull()
        {
            // Arrange
            var td = Substitute.For<ITempDataDictionary>();
            td.TryGetValue("NonExistingKey", out object? storedValue).Returns(false);

            // Act
            var result = TempDataHelper.Get<TestObject>(td, "NonExistingKey");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void TryGet_ReturnsTrue_WhenValueExistsAndIsDeserialized()
        {
            // Arrange
            var td = Substitute.For<ITempDataDictionary>();
            var serializedObject = JsonConvert.SerializeObject(new TestObject());
            td.TryGetValue("TestKey", out object? storedValue).Returns(callInfo =>
            {
                callInfo[1] = serializedObject;
                return true;
            });

            // Act
            var result = td.TryGet<TestObject>("TestKey", out TestObject value);

            // Assert
            Assert.True(result);
            Assert.NotNull(value);
           
        }
        [Fact]
        public void TryGet_ReturnsFalse_WhenValueDoesNotExist()
        {
            // Arrange
            var td = Substitute.For<ITempDataDictionary>();
            td.TryGetValue("NonExistentKey", out object? storedValue).Returns(false);

            // Act
            var result = td.TryGet<TestObject>("NonExistentKey", out TestObject value);

            // Assert
            Assert.False(result);
            Assert.Null(value);
        }

        private class TestObject
        {
            public int Id { get; set; }
            public string? Name { get; set; }
        }
    }
}
