﻿using Microsoft.Extensions.Configuration;
using NSubstitute;
using PharmaLex.SmartTRACE.Web.Helpers;

namespace PharmaLex.SmartTRACE.WebTests.Helpers;

public class VersionCdnHelperTests
{
    private readonly IConfiguration mockConfiguration;

    public VersionCdnHelperTests()
    {
        mockConfiguration = Substitute.For<IConfiguration>();
    }

    [Fact]
    public void Constructor_InitializesPropertiesCorrectly()
    {
        // Arrange
        var configuration = TestHelpers.GetConfiguration();

        // Act
        var helper = new VersionCdnHelper(configuration);

        // Assert
        Assert.Equal("https://test-endpoint.test.net", helper.Root);
        Assert.Equal("https://test-endpoint.test.net/content/v1.35.0", helper.Host);
    }

    [Fact]
    public void GetUrl_ReturnsVersionedUrl_WhenVersionedIsTrue()
    {
        // Arrange
        var configuration = TestHelpers.GetConfiguration();
        var helper = new VersionCdnHelper(configuration);

        // Act
        var result = helper.GetUrl("test/path", true);

        // Assert
        Assert.Equal("https://test-endpoint.test.net/content/v1.35.0/test/path?v=local", result);
    }

    [Fact]
    public void GetUrl_ReturnsNonVersionedUrl_WhenVersionedIsFalse()
    {
        // Arrange
        var configuration = TestHelpers.GetConfiguration();
        var helper = new VersionCdnHelper(configuration);

        // Act
        var result = helper.GetUrl("test/path", false);

        // Assert
        Assert.Equal("https://test-endpoint.test.net/content/v1.35.0/test/path", result);
    }

    [Fact]
    public void GetUrl_HandlesPathsWithLeadingAndTrailingSlashes()
    {
        // Arrange
        var configuration = TestHelpers.GetConfiguration();
        var helper = new VersionCdnHelper(configuration);

        // Act
        var result = helper.GetUrl("/test/path/", true);

        // Assert
        Assert.Equal("https://test-endpoint.test.net/content/v1.35.0/test/path?v=local", result);
    }
}
