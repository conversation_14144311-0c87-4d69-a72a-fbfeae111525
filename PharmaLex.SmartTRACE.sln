﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32112.339
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{E8885032-5F59-4A35-876C-BB8239C2F892}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "config", "config", "{F92EB87A-6F11-4B1B-BB39-B35CDFF89162}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		.gitmodules = .gitmodules
		azure-pipelines.yml = azure-pipelines.yml
		README.md = README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{0ED884C8-6A38-4521-98DF-E0AFEBDEF269}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartTRACE.Data", "src\PharmaLex.SmartTRACE.Data\PharmaLex.SmartTRACE.Data.csproj", "{A8153157-A1AA-435C-AED7-A727D139A16A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartTRACE.Entities", "src\PharmaLex.SmartTRACE.Entities\PharmaLex.SmartTRACE.Entities.csproj", "{AF70482B-04D7-42CC-83EE-1E928FC5E0A9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartTRACE.RemindersApp", "src\PharmaLex.SmartTRACE.RemindersApp\PharmaLex.SmartTRACE.RemindersApp.csproj", "{772B8954-574E-431E-8006-76481C6DEDCA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartTRACE.Web", "src\PharmaLex.SmartTRACE.Web\PharmaLex.SmartTRACE.Web.csproj", "{B46CF7CA-1B33-484E-AE43-013968CDFFE4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartTRACE.Web.Helpers", "src\PharmaLex.SmartTRACE.Web.Helpers\PharmaLex.SmartTRACE.Web.Helpers.csproj", "{CCE16B29-5A9D-44B8-B033-66E401A71079}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartTRACE.Web.Models", "src\PharmaLex.SmartTRACE.Web.Models\PharmaLex.SmartTRACE.Web.Models.csproj", "{3462246B-94A6-420E-93EA-815BA0852090}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartTRACE.Web.HelpersTests", "test\PharmaLex.SmartTRACE.Web.HelpersTests\PharmaLex.SmartTRACE.Web.HelpersTests.csproj", "{4CC78CC6-9ED5-442E-94D4-2FF779DECA77}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartTRACE.Web.ModelsTests", "test\PharmaLex.SmartTRACE.Web.ModelsTests\PharmaLex.SmartTRACE.Web.ModelsTests.csproj", "{51F68059-42DF-44A6-8F72-960B6DC1D51D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartTRACE.WebTests", "test\PharmaLex.SmartTRACE.WebTests\PharmaLex.SmartTRACE.WebTests.csproj", "{FA1CBF69-3A1B-48B9-8BB2-C9D88A376526}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.SmartTRACE.ReminderAppTests", "test\PharmaLex.SmartTRACE.ReminderAppTests\PharmaLex.SmartTRACE.ReminderAppTests.csproj", "{760A2D23-187B-4013-9483-D7FC19278A00}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A8153157-A1AA-435C-AED7-A727D139A16A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A8153157-A1AA-435C-AED7-A727D139A16A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A8153157-A1AA-435C-AED7-A727D139A16A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A8153157-A1AA-435C-AED7-A727D139A16A}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF70482B-04D7-42CC-83EE-1E928FC5E0A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF70482B-04D7-42CC-83EE-1E928FC5E0A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF70482B-04D7-42CC-83EE-1E928FC5E0A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF70482B-04D7-42CC-83EE-1E928FC5E0A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{772B8954-574E-431E-8006-76481C6DEDCA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{772B8954-574E-431E-8006-76481C6DEDCA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{772B8954-574E-431E-8006-76481C6DEDCA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{772B8954-574E-431E-8006-76481C6DEDCA}.Release|Any CPU.Build.0 = Release|Any CPU
		{B46CF7CA-1B33-484E-AE43-013968CDFFE4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B46CF7CA-1B33-484E-AE43-013968CDFFE4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B46CF7CA-1B33-484E-AE43-013968CDFFE4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B46CF7CA-1B33-484E-AE43-013968CDFFE4}.Release|Any CPU.Build.0 = Release|Any CPU
		{CCE16B29-5A9D-44B8-B033-66E401A71079}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CCE16B29-5A9D-44B8-B033-66E401A71079}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CCE16B29-5A9D-44B8-B033-66E401A71079}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CCE16B29-5A9D-44B8-B033-66E401A71079}.Release|Any CPU.Build.0 = Release|Any CPU
		{3462246B-94A6-420E-93EA-815BA0852090}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3462246B-94A6-420E-93EA-815BA0852090}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3462246B-94A6-420E-93EA-815BA0852090}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3462246B-94A6-420E-93EA-815BA0852090}.Release|Any CPU.Build.0 = Release|Any CPU
		{4CC78CC6-9ED5-442E-94D4-2FF779DECA77}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4CC78CC6-9ED5-442E-94D4-2FF779DECA77}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4CC78CC6-9ED5-442E-94D4-2FF779DECA77}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4CC78CC6-9ED5-442E-94D4-2FF779DECA77}.Release|Any CPU.Build.0 = Release|Any CPU
		{51F68059-42DF-44A6-8F72-960B6DC1D51D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{51F68059-42DF-44A6-8F72-960B6DC1D51D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{51F68059-42DF-44A6-8F72-960B6DC1D51D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{51F68059-42DF-44A6-8F72-960B6DC1D51D}.Release|Any CPU.Build.0 = Release|Any CPU
		{FA1CBF69-3A1B-48B9-8BB2-C9D88A376526}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FA1CBF69-3A1B-48B9-8BB2-C9D88A376526}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FA1CBF69-3A1B-48B9-8BB2-C9D88A376526}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FA1CBF69-3A1B-48B9-8BB2-C9D88A376526}.Release|Any CPU.Build.0 = Release|Any CPU
		{760A2D23-187B-4013-9483-D7FC19278A00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{760A2D23-187B-4013-9483-D7FC19278A00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{760A2D23-187B-4013-9483-D7FC19278A00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{760A2D23-187B-4013-9483-D7FC19278A00}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A8153157-A1AA-435C-AED7-A727D139A16A} = {E8885032-5F59-4A35-876C-BB8239C2F892}
		{AF70482B-04D7-42CC-83EE-1E928FC5E0A9} = {E8885032-5F59-4A35-876C-BB8239C2F892}
		{772B8954-574E-431E-8006-76481C6DEDCA} = {E8885032-5F59-4A35-876C-BB8239C2F892}
		{B46CF7CA-1B33-484E-AE43-013968CDFFE4} = {E8885032-5F59-4A35-876C-BB8239C2F892}
		{CCE16B29-5A9D-44B8-B033-66E401A71079} = {E8885032-5F59-4A35-876C-BB8239C2F892}
		{3462246B-94A6-420E-93EA-815BA0852090} = {E8885032-5F59-4A35-876C-BB8239C2F892}
		{4CC78CC6-9ED5-442E-94D4-2FF779DECA77} = {0ED884C8-6A38-4521-98DF-E0AFEBDEF269}
		{51F68059-42DF-44A6-8F72-960B6DC1D51D} = {0ED884C8-6A38-4521-98DF-E0AFEBDEF269}
		{FA1CBF69-3A1B-48B9-8BB2-C9D88A376526} = {0ED884C8-6A38-4521-98DF-E0AFEBDEF269}
		{760A2D23-187B-4013-9483-D7FC19278A00} = {0ED884C8-6A38-4521-98DF-E0AFEBDEF269}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {69747315-3ACC-4AD0-9BD5-40781B60028B}
	EndGlobalSection
EndGlobal
