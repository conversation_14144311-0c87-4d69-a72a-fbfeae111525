﻿/*!
  plx.map
*/

/// <reference path="../lib/d3/d3.v4.js" />

plx.map = {
    width: 0,
    height: 0,
    container: null,
    svg: null,
    group: null,
    projection: null,
    path: null,
    zoom: null,
    maxScaleExtent: 10,
    showZoomReset: true,
    highlightCountryOnSelect: true,
    showTooltips: true,
    countries: null,
    onMapInitialised: null,
    onMapRendered: null,
    onMapZoomed: null,
    getCountryTooltipContent: null,
    selectedCountry: null,
    onCountrySelected: null,
    init: function (containerId, onMapInitialised, onMapRendered, onMapZoomed, onCountrySelected, getCountryTooltipContent) {
        plx.map.onMapInitialised = onMapInitialised;
        plx.map.onMapRendered = onMapRendered;
        plx.map.onMapZoomed = onMapZoomed;
        plx.map.onCountrySelected = onCountrySelected;
        plx.map.getCountryTooltipContent = getCountryTooltipContent;
        plx.map.container = d3.select(`#${containerId}`);
    },
    render: function (id) {
        plx.map.container.html(null);
        plx.map.setBounds();
        plx.map.svg = plx.map.container.append('svg').classed('svg-map', true);
        plx.map.group = plx.map.svg.append('g');
        plx.map.projection = d3.geoMercator().scale((plx.map.width - 1) / 2 / Math.PI).translate([plx.map.width / 2, plx.map.height / 2 + 75]);
        plx.map.path = d3.geoPath().projection(plx.map.projection);
        plx.map.zoom = new d3.zoom().scaleExtent([1, plx.map.maxScaleExtent]).translateExtent([[0, 0], [plx.map.width, plx.map.height]]).on('zoom', plx.map.zoomed);
        plx.map.zoom.current = { x: 0, y: 0, k: 1 };
        plx.map.svg.call(plx.map.zoom);
        if (!plx.map.countries) {
            plx.map.countries = topojson.feature(plx.countries, plx.countries.objects.countries).features;
            plx.map.renderCountries();
            if (plx.map.onMapInitialised instanceof Function) {
                plx.map.onMapInitialised();
            }
            if (plx.map.onMapRendered instanceof Function) {
                plx.map.onMapRendered();
            }
        } else {
            plx.map.renderCountries();
            if (plx.map.onMapRendered instanceof Function) {
                plx.map.onMapRendered();
            }
        }
        plx.map.renderZoomReset();
    },
    setBounds: function () {
        let cb = plx.map.container.node().getBoundingClientRect();
        plx.map.width = cb.width;
        plx.map.height = cb.height;
    },
    renderCountries: function () {
        let cp = plx.map.group.selectAll('x').data(plx.map.countries).enter().append('g').append('path').attr('d', plx.map.path).attr('id', (c) => `c-${c.id}-path`).attr('class', 'country-path');

        let centroid = plx.map.group.selectAll('circle').data(plx.map.countries).enter().append('circle');
        centroid.attr('id', (c) => `c-${c.id}-centroid`).attr('class', 'country-centroid').attr('r', 3).attr('cx', (c) => plx.map.projection(c.properties.pin.slice().reverse())[0]).attr('cy', (c) => plx.map.projection(c.properties.pin.slice().reverse())[1]);

        if (plx.map.selectedCountry) {
            plx.map.countrySelected(plx.map.selectedCountry);
        }

        if (plx.map.showTooltips) {
            cp.on('mouseenter', (f) => plx.map.showCountryTooltip(f));
            cp.on('mouseleave', (f) => plx.map.hideTooltip(f));
        }
        cp.on('click', plx.map.countrySelected);
    },
    renderZoomReset: function () {
        if (plx.map.showZoomReset) {
            let r = plx.map.container.append('div').attr('id', 'reset-zoom-button').classed('map-button reset-zoom-button', true);
            r.append('i').classed('icon-zoom-out', true);
            r.append('span').text('Reset');
            r.on('click', plx.map.resetZoomClicked);  
        }
    },
    zoomed: function () {
        plx.map.zoom.current = { x: d3.event.transform.x, y: d3.event.transform.y, k: d3.event.transform.k };
        if (plx.map.zoom.isTransition === true) {
            plx.map.group.transition().duration(750).attr('transform', `translate(${d3.event.transform.x},${d3.event.transform.y})scale(${d3.event.transform.k})`);
            plx.map.zoom.isTransition = false;
        } else {
            plx.map.group.attr('transform', `translate(${d3.event.transform.x},${d3.event.transform.y})scale(${d3.event.transform.k})`);
        }
        d3.selectAll('.country-centroid').attr('r', (d3.event.transform.k < 3.3 ? 3 : d3.event.transform.k < 6.6 ? 4 : 5) / d3.event.transform.k);
        d3.selectAll('.country-path').classed('country-path-zoomed', d3.event.transform.k >= 5).classed('country-path-max-zoomed', d3.event.transform.k >= 20);
        if (plx.map.onMapZoomed instanceof Function) {
            plx.map.onMapZoomed();
        }
    },
    highlightCountry: function(id) {
        d3.select(`#c-${id}-path`).classed('country-highlight', true);
    },
    fillCountry: function (id, color) {
        d3.select(`#c-${id}-path`).style('fill', color);
    },
    resetCountryFill: function (id) {
        if (id === undefined) {
            plx.map.countries.forEach((c) => {
                d3.select(`#c-${c.id}-path`).style('fill', null).classed('country-path', true);
            });
        } else {
            d3.select(`#c-${id}-path`).style('fill', null).classed('country-path', true);
        }
    },
    resetZoomClicked: function () {
        d3.event.stopPropagation();
        plx.map.resetting = true;
        plx.map.resetZoom();
        plx.map.resetting = false;
    },
    resetZoom: function () {
        plx.map.zoom.isTransition = true;
        plx.map.svg.call(plx.map.zoom.transform, d3.zoomIdentity.translate(0, 0).scale(1));
    },
    showTooltip: function (anchor, title, content) {
        let tt = plx.map.container.insert('div', ':first-child').classed('map-tooltip', true);
        if (typeof title === 'string' && title.length > 0) {
            tt.append('h3').html(title);
        }
        if (typeof content === 'string' && content.length > 0) {
            tt.append('div').html(content);
        }
        let ttb = tt.node().getBoundingClientRect();
        let ab = anchor.node().getBoundingClientRect();
        let conb = plx.map.container.node().getBoundingClientRect();
        let top = ab.top - ttb.height - conb.top - 10;
        if (top <= 0) {
            top = ab.bottom - conb.top + 10;
            if (top <= 7) {
                top = 8;
            }
            tt.classed('map-tooltip-below', true);
        } else if (top + ttb.height + conb.top + 7 > conb.bottom) {
            top = conb.height - ttb.height - 10;
        }
        let left = ab.left - conb.left - ttb.width / 10 * 4 - 4;
        if (left < 0 - conb.left) {
            left = 5 - conb.left;
        } else if (left + conb.left + 20 > conb.right - ttb.width) {
            left = conb.right - conb.left - ttb.width - 5;
        }
        tt.style('top', `${top}px`).style('left', `${left}px`);
    },
    hideTooltip: function (f) {
        d3.select('.map-tooltip').remove();
    },
    showCountryTooltip: function (c) {
        let content = plx.map.getCountryTooltipContent instanceof Function ? plx.map.getCountryTooltipContent(c.id) : null;
        plx.map.showTooltip(d3.select(`#c-${c.id}-centroid`), c.properties.name, content)
    },
    countrySelected: function (c) {
        plx.map.clearSelection();
        if (plx.map.highlightCountryOnSelect) {
            plx.map.hideTooltip(c);
            d3.select(`#c-${c.id}-path`).classed('country-selected', true);
            let centroid = d3.select(`#c-${c.id}-centroid`);
            plx.map.zoom.isTransition = true;
            let s = 4; // Magic number for zoomed scale
            plx.map.svg.call(plx.map.zoom.transform, d3.zoomIdentity.translate(plx.map.width / 2 - centroid.attr('cx') * s, plx.map.height / 2 - centroid.attr('cy') * s).scale(s));
        }
        if (plx.map.onCountrySelected instanceof Function) {
            plx.map.onCountrySelected(c.id);
        }
        plx.map.selectedCountry = c;
    },
    zoomToCountry: function (id) {
        let c = plx.map.countries.find(x => x.id == id);
        let centroid = d3.select(`#c-${c.id}-centroid`);
        let b = plx.map.path.bounds(c),
            dx = b[1][0] - b[0][0],
            dy = b[1][1] - b[0][1],
            s = Math.max(.9 / Math.max(dx / plx.map.width, dy / plx.map.height), 4);
        plx.map.svg.call(plx.map.zoom.transform, d3.zoomIdentity.translate(plx.map.width / 2 - centroid.attr('cx') * s, plx.map.height / 2 - centroid.attr('cy') * s).scale(s));
    },
    clearSelection: function () {
        d3.selectAll('.country-path').classed('country-selected', false);
        d3.select('.country-centroid-highlight');
    },
    getCountryPinCoords: function (id) {
        return plx.map.projection(plx.map.countries.find(x => x.id === id).properties.pin.slice().reverse());
    },
    addLogoPin: function (lat, long, r, cssclass, invert) {
        let t = plx.map.projection([long, lat]);
        t[0] -= r / 2;
        t[1] += r / 2;
        let g = plx.map.group.append('g').attr('transform', `translate(${t}) scale(${r/200}, -${r/200})`);
        if (typeof cssclass === 'string') {
            g.classed(cssclass, true);
        }
        g.append('circle').attr('fill', invert !== true ? '#ffffff' : '#009aa8').attr('r', 100).attr('cx', 100).attr('cy', 100);
        g.append('path').attr('d', 'M71 129 c7 -14 7 -29 0 -50 l-10 -29 51 26 c27 14 48 27 46 29 -2 2 -25 13 -51 24 l-47 20 11 -20z').attr('fill', invert !== true ? '#009aa8' : '#ffffff');
        return g;
    }
};