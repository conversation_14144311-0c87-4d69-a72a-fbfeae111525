﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Models.ViewModels
{
    /// <summary>
    /// Represents a single page of data from a paged collection of API data. Along with additional paging information
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class ApiPagedListResult<T>
    {
        /// <summary>
        /// An collection of <see cref="T"/>.
        /// </summary>
        /// <value>
        /// A list of type <see cref="T"/>
        /// </value>
        public IList<T> Data { get; }

        /// <summary>
        /// Paging information regarding the current page and total set  
        /// </summary>
        public PagingInfo Paging { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ApiPagedListResult{T}"/> class.
        /// </summary>
        /// <param name="items">Collection of items.</param>
        /// <param name="offset">Paging Offset.</param>
        /// <param name="limit">Page Size.</param>
        /// <param name="totalItemCount">The total number of items in result set.</param>
        /// <param name="filteredCount">The filtered number of items in result set.</param>
        public ApiPagedListResult(IEnumerable<T> items, int offset, int limit, long totalItemCount, long filteredCount)
        {
            Data = new List<T>(items);
            Paging = new PagingInfo
            {
                Offset = offset,
                Limit = limit,
                TotalItemCount = totalItemCount,
                FilteredCount = filteredCount
            };
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ApiPagedListResult{T}"/> class (used in serialization).
        /// </summary>
        /// <param name="data">Collection of items.</param>
        /// <param name="paging">Paging information model.</param>
        [JsonConstructor]
        public ApiPagedListResult(IList<T> data, PagingInfo paging)
        {
            Data = data;
            Paging = paging;
        }
    }

    public class PagingInfo
    {
        /// <summary>
        /// The offset from 0 where the paged set returns from.
        /// </summary>
        public int Offset { get; set; }

        /// <summary>
        /// The number of items returned.
        /// </summary>
        public int Limit { get; set; }

        /// <summary>
        /// Total number of items in the result set
        /// </summary>
        public long TotalItemCount { get; set; }

        /// <summary>
        /// Filtered number of items in the result set
        /// </summary>
        public long FilteredCount { get; set; }
    }
}