﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class Application : EntityBase
    {
        public Application()
        {
            Submission = new HashSet<Submission>();
            ApplicationCountry = new HashSet<ApplicationCountry>();
            ApplicationProduct = new HashSet<ApplicationProduct>();
        }
        public int Id { get; set; }
        public string ApplicationNumber { get; set; }
        public int? MedicinalProductDomainId { get; set; }
        public int? MedicinalProductTypeId { get; set; }
        public int ApplicationTypeId { get; set; }
        public int? ProcedureTypeId { get; set; }
        public string Comments { get; set; }
        public int LifecycleStateId { get; set; }

        public virtual ICollection<ApplicationProduct> ApplicationProduct { get; set; }
        public virtual ICollection<ApplicationCountry> ApplicationCountry { get; set; }
        public virtual ICollection<Submission> Submission { get; set; }
    }
}
