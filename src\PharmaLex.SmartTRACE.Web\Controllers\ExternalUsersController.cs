﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    public class ExternalUsersController : BaseController
    {
        private readonly IMapper mapper;
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IAzureAdB2CGraphService graphService;
        private readonly IUserService userService;

        public ExternalUsersController(
          IDistributedCacheServiceFactory cache,
          IMapper mapper,
          IAzureAdB2CGraphService graphService,
          IUserService userService)
        {
            this.cache = cache;
            this.mapper = mapper;
            this.graphService = graphService;
            this.userService = userService;
        }

        [HttpGet("manage/external-user/find")]
        public async Task<IActionResult> ExternalUserFind(string term)
       {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            IList<User> dbUsers = await this.cache.CreateEntity<User>()
                .Configure(o => o.Include(x => x.UserClaim))
                .WhereAsync(x => x.UserClaim != null);

            List<Microsoft.Graph.Models.User> graphUsers = await this.graphService.FindUsers(term);
            List<UserFindResultModel> users = mapper.Map<List<UserFindResultModel>>(graphUsers);
            foreach (var um in users)
            {
                var dbUser = dbUsers.FirstOrDefault(x => x.Email.ToLower() == um.Email?.ToLower());
                if (dbUser != null)
                {
                    var m = mapper.Map<UserFindResultModel>(dbUser);
                    um.Name = m.Name;
                    um.Id = dbUser.Id;
                    um.GivenName = dbUser.GivenName;
                    um.FamilyName = dbUser.FamilyName;
                }
            }

            return Json(users.OrderBy(x => x.Name));
        }

        [HttpGet, Route("/manage/external-users/new")]
        public async Task<IActionResult> NewUser()
        {
            var clients = await cache.CreateMappedEntity<Client, ClientModel>().AllAsync();

            var userClients = new UserClientsModel
            {
                Clients = clients.OrderBy(x => x.Name).ToList(),
                User = new UserModel()
            };
            return View("EditExternalUser", userClients);
        }

        [HttpPost("/manage/external-users/new"), ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveNewUser(UserClientsModel userModel)
        {
            if (this.ModelState.IsValid)
            {
                bool success = await this.userService.ProcessExternalUser(userModel);
                if (!success)
                    return this.BadRequest();

                return this.Redirect($"/manage/users");
            }

            var clients = await cache.CreateMappedEntity<Client, ClientModel>().AllAsync();
            userModel.Clients = clients;
            return this.View("EditExternalUser", userModel);
        }

        [HttpPost("manage/users/available")]
        public async Task<IActionResult> AvailableUser(string email)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            if (string.IsNullOrWhiteSpace(email))
            {
                return null;
            }

            User dbUser = await this.cache.CreateEntity<User>()
                .FirstOrDefaultAsync(x => x.Email.ToLower() == email.ToLower());

            return Json(dbUser);
        }

        [HttpPost("manage/users/resendinvitation"), Authorize(Policy = "UserAdmin")]
        public async Task<IActionResult> ResendInvitationEmail([FromBody] UserModel model)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var invitationLink = await this.userService.ProcessSignupInvitationResend(model);

            return Json(invitationLink);
        }
    }
}
