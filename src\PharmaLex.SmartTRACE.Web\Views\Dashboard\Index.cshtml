﻿@model DashboardViewModel
@using Microsoft.Extensions.Configuration;

@inject IConfiguration configuration;
@{
    var systemAccessLink = configuration.GetValue<string>("DashboardLinks:SystemAccessLink");
    var traceEmailLink = configuration.GetValue<string>("DashboardLinks:TraceEmailLink");
}

<div class="manage-container">
    <div class="home-page">
        <section>
            @{
                await Html.RenderPartialAsync("_WelcomeDashboardPartial", @Model);
            }

            <div class="app-content">
                Smart<strong>TRACE</strong> is a regulatory information and process management system to manage regulatory submissions
                from planning through to submission and archive.
                <div>
                    Your current permissions give you access to the eCTD Viewing tool, for reviewing and viewing single or multiple eCTD sequences for EU and US Applications. 
                    If you would like to change your permissions so that you are able to view and/or edit submission planning and tracking records,
                    please complete the
                    <a href="@systemAccessLink" style="text-decoration: underline">
                        System Access Request Form
                    </a>.
                </div>
                <div>
                    To use the eCTD Viewing tool click on
                    <span class="dashboard-text">
                        <strong>
                            eCTD VIEWER
                        </strong>
                    </span> in the Smart<strong>TRACE</strong> menu above.  eCTD Viewer training can be booked through Rexx.
                </div>
                <div>
                    For all other queries about Smart<strong>TRACE</strong> please contact the team via e-mail  (<a href="@traceEmailLink" style="text-decoration: underline"><EMAIL></a>).
                </div>
            </div>

        </section>

        <div class="users-logo-content">
            @{
                await Html.RenderPartialAsync("_DashboardBAPartial", @Model);
            }
        </div>


    </div>
</div>