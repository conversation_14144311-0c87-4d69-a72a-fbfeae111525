﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.HTMLTags;
using PharmaLex.SmartTRACE.Web.Models;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    [Authorize("BusinessAdmin")]
    public class RegionsController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper _mapper;

        public RegionsController(IDistributedCacheServiceFactory cache, IMapper mapper)
        {
            this.cache = cache;
            this._mapper = mapper;
        }

        [HttpGet, Route("/regions")]
        public async Task<IActionResult> Index()
        {
            var regions = cache.CreateMappedEntity<Region, RegionModel>();
            return View(await regions.AllAsync());
        }

        [HttpGet, Route("/regions/new")]
        public IActionResult New()
        {
            return View("EditRegion", new RegionModel());
        }

        [HttpPost, Route("/regions/new"), ValidateAntiForgeryToken]
        public async Task<IActionResult> New([Bind("Id,Name,Abbreviation")]RegionModel model)
        {
            if (!this.ModelState.IsValid || (HtmlTags.CheckHTMLTags(model.Name)))
            {
                return View("EditRegion", model);
            }

            var regionCache = cache.CreateTrackedEntity<Region>();
            var region = _mapper.Map<RegionModel, Region>(model);
            regionCache.Add(region);
            await regionCache.SaveChangesAsync();
            this.AddConfirmationNotification($"<em>{region.Name}</em> created");
            return Redirect("/regions");
        }

        [HttpGet, Route("/regions/edit/{id}")]
        public async Task<IActionResult> Edit(int id)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var regionCache = cache.CreateMappedEntity<Region, RegionModel>();
            var countryCache = cache.CreateMappedEntity<Country, CountryModel>();
            var currentRegion = await regionCache.FirstOrDefaultAsync(x => x.Id == id);
            var regionCountries = await countryCache.WhereAsync(x => x.RegionId == id);
            currentRegion.Countries = regionCountries;
            return View("EditRegion", currentRegion);
        }

        [HttpPost, Route("/regions/edit/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, RegionModel model)
        {
            if (!this.ModelState.IsValid || HtmlTags.CheckHTMLTags(model.Name))
            {
                model.Countries = await cache.CreateMappedEntity<Country, CountryModel>().WhereAsync(x => x.RegionId == id);
                return this.BadRequest(this.ModelState);
            }
            var regionCache = cache.CreateTrackedEntity<Region>();
            var region = await regionCache.FirstOrDefaultAsync(x => x.Id == id);
            _mapper.Map(model, region);
            await regionCache.SaveChangesAsync();
            this.AddConfirmationNotification($"<em>{region.Name}</em> updated");
            return Redirect("/regions");
        }
    }
}
