﻿@model ApiPagedListResult<ApplicationViewModel>
@using PharmaLex.Caching.Data
@using PharmaLex.SmartTRACE.Entities
@using PharmaLex.SmartTRACE.Entities.Enums
@using Microsoft.AspNetCore.Authorization
@using PharmaLex.SmartTRACE.Web.Models.ViewModels
@inject IDistributedCacheServiceFactory cache
@inject IAuthorizationService AuthorizationService
@inject AutoMapper.IMapper mapper

@{
    var medicinalProductDomains = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                                  .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.MedicinalProductDomain);
    var applicationTypes = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                                .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.ApplicationType);
    var procedureTypes = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                               .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.ProcedureType);
    var lifecycleStates = mapper.Map<IEnumerable<ApplicationLifecycleStateList>>(Enum.GetValues(typeof(CommonLifecycleState)));
    var isExternalEditorUser = (await AuthorizationService.AuthorizeAsync(User, "ExternalEditor")).Succeeded;
}

<div id="applications" class="manage-container">
    <header class="manage-header">
        <h2>Applications</h2>
        @if(!isExternalEditorUser)
        {
            <a id="exportButton" class="button icon-button-download">Export</a>
        }
        @if ((await AuthorizationService.AuthorizeAsync(User, "BusinessAdmin")).Succeeded)
        {
            <a class="button icon-button-add" href="/applications/new">Add application</a>
        }
    </header>
    <filtered-table :items="applications" 
                    :columns="columns" 
                    :filters="filters" 
                    :link="link"
                    :total-items-count="totalItemCount"
                    :filtered-count="filteredCount"
                    @@filter="onFilter"
                    @@sort="onSort"
                    v-on:on-page-index-change="onPageIndexChange"
                    v-on:on-page-size-change="onPageSizeChange"
                    v-on:on-load-state="onLoadState"
                    :resources="resources"></filtered-table>
    <form id="exportForm" style="display: none;" method="post" action="/applications/export">
        @Html.AntiForgeryToken()
        <input type="hidden" id="applicationNumber" name="applicationNumber" />
        <input type="hidden" id="applicationType" name="applicationType" />
        <input type="hidden" id="clientName" name="clientName" />
        <input type="hidden" id="country" name="country" />
        <input type="hidden" id="medicinalProductDomain" name="medicinalProductDomain" />
        <input type="hidden" id="procedureType" name="procedureType" />
        <input type="hidden" id="product" name="product" />
        <input type="hidden" id="region" name="region" />
        <input type="hidden" id="lifecycleState" name="lifecycleState" />
    </form>
</div>

@section Scripts {
<script type="text/javascript">

    var pageConfig = {
        appElement: '#applications',
        data() {
            return {
                link: '/applications/view/',
                applications: [],
                isExternalEditorUser: @Html.Raw(Json.Serialize(isExternalEditorUser)),
                columns: {
                    idKey: 'id',
                    styleKey: 'lifecycleState',
                    config: [
                        {
                            dataKey: 'region',
                            header: 'Region',
                            type: 'text'
                        },
                        {
                            dataKey: 'country',
                            header: 'Country',
                            type: 'text'
                        },
                        {
                            dataKey: 'clientName',
                            header: 'Client',
                            type: 'text'
                        },
                        {
                            dataKey: 'medicinalProductDomain',
                            header: 'Medicinal Product Domain',
                            type: 'text'
                        },
                        {
                            dataKey: 'product',
                            header: 'Product name, Dosage Form, Strength',
                            type: 'text'
                        },
                        {
                            dataKey: 'procedureType',
                            header: 'Procedure Type',
                            type: 'text'
                        },
                        {
                            dataKey: 'applicationType',
                            header: 'Application Type',
                            type: 'text'
                        },
                        {
                            dataKey: 'applicationNumber',
                            sortKey: 'applicationNumber',
                            header: 'Application Number',
                            type: 'text'
                        },
                        {
                            dataKey: 'lifecycleState',
                            header: 'Lifecycle State',
                            type: 'text'
                        },
                    ]
                },
                totalItemCount: 0,
                filteredCount: 0,
                offset: 0,
                pageSize: 25,
                resources: {
                    sortByFormat: 'Sort by {}',
                },
                filters: [
                    {
                        key: 'region',
                        options: [],
                        type: 'search',
                        header: 'Search Region',
                        fn: v => p => p.region.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'country',
                        options: [],
                        type: 'search',
                        header: 'Search Country',
                        fn: v => p => p.country.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'clientName',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.clientName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'product',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.product.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'applicationNumber',
                        options: [],
                        type: 'search',
                        header: 'Search Number',
                        fn: v => p => p.applicationNumber.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'medicinalProductDomain',
                        options: @Html.Raw(medicinalProductDomains.ToJson()),
                        filterCollection: 'medicinalProductDomain',
                        type: 'select-multiple',
                        header: 'Filter By Type',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.medicinalProductDomain),
                        convert: v => v
                    },
                    {
                        key: 'applicationType',
                        options: @Html.Raw(applicationTypes.ToJson()),
                        filterCollection: 'applicationType',
                        type: 'select-multiple',
                        header: 'Filter By Type',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.applicationType),
                        convert: v => v
                    },
                    {
                        key: 'procedureType',
                        options: @Html.Raw(procedureTypes.ToJson()),
                        filterCollection: 'procedureType',
                        type: 'select-multiple',
                        header: 'Filter By Type',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.procedureType),
                        convert: v => v
                    },
                    {
                        key: 'lifecycleState',
                        options: @Html.Raw(lifecycleStates.ToJson()),
                        filterCollection: 'lifecycleState',
                        type: 'select-multiple',
                        header: 'Filter By State',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.lifecycleState),
                        convert: v => v
                    }
                ],
                storageKey: `${window.location.href}(${this.$attrs.id || ''})`
            };
        },
        created() {
            if(this.isExternalEditorUser) {
                this.columns.config = this.columns.config.filter(c => c.dataKey != 'clientName');
                this.filters = this.filters.filter(c => c.key != 'clientName');
            }
        },
        mounted() {
            document.getElementById('exportButton').addEventListener('click', (e) => {
                e.preventDefault();
                Object.keys(localStorage).forEach(key => {
                    if (key.endsWith(`${window.location.pathname}()`)) {
                        let appSettings = localStorage.getItem(key);
                        if (appSettings.includes('filterModel')) {
                            let filterModel = JSON.parse(appSettings).filterModel;
                            Object.keys(filterModel).forEach(filterKey => {
                                if (Array.isArray(filterModel[filterKey])) {
                                    document.getElementById(filterKey).value = filterModel[filterKey].filter(f => f.selected).map(f => f.value).join(',');
                                } else {
                                    document.getElementById(filterKey).value = filterModel[filterKey];
                                }
                            })
                        }
                    }
                })
                document.getElementById('exportForm').submit();
            });
        },
        methods: {
                onLoadState() {
                    this.pageSize = this.getPageSize();
                    const filters = this.getFilters();
                    const sort = this.getSort();
                    const pageIndex = this.getPageIndex();
                    this.getData(pageIndex * this.pageSize, this.pageSize, filters, sort);
                },
                onSort(sortModel, pageIndex) {
                    this.pageSize = this.getPageSize();
                    this.getData(pageIndex * this.pageSize, this.pageSize, this.getFilters(), this.toSortValues(Object.entries(sortModel)));
                },
                onFilter(filters) {
                    const mapFilters = filters ? this.toKeyValueFilter(filters) : {};
                    if (this.timeout) {
                        clearTimeout(this.timeout);
                    }
                    this.timeout = setTimeout(() => {
                        this.getData(0, this.pageSize, mapFilters, this.getSort());
                    }, 700)
                },
                onPageIndexChange(pageIndex) {
                    this.pageSize = this.getPageSize();
                    this.getData(pageIndex * this.pageSize, this.pageSize, this.getFilters(), this.getSort());
                },
                onPageSizeChange(pageSize) {
                    this.pageSize = pageSize;
                    const filters = this.getFilters();
                    const sort = this.getSort();
                    this.getData(0, this.pageSize, filters, sort);
                },
                getData(skip, take, filters, sort) {
                    const requestOptions = {
                        method: "GET",
                        credentials: 'same-origin'
                    };

                    var tableFilters = Object.keys(filters).length !== 0 ? "&filters=" + filters.join('&filters=') : '';

                    fetch(`/paged-applications?skip=${skip}&take=${take}${tableFilters}&sort=${sort}`, requestOptions)
                        .then(r => r.json())
                        .then(body => {
                            this.applications = body.data;
                            this.filteredCount = body.paging.filteredCount;
                            this.totalItemCount = body.paging.totalItemCount;
                            this.offset = body.paging.offset;
                            this.pageSize = body.paging.limit;
                        });
                },
                getSort() {
                    let state = this.getState();
                    return state.sortModel ? this.toSortValues(state.sortModel) : "";
                },
                toSortValues(sortModel) {
                    let selectedProperty = sortModel.find(e => e[1] !== 0);

                    if (selectedProperty) {
                        if (selectedProperty[1] === 1) {
                            return `${selectedProperty[0]}=>asc`;
                        }
                        else if (selectedProperty[1] === -1) {
                            return `${selectedProperty[0]}=>desc`;
                        }
                    }

                    return "";
                },
                getFilters() {
                    let state = this.getState();
                    return state.filterModel ? this.toKeyValueFilter(state.filterModel) : {};
                },
                toKeyValueFilter(filters) {
                    return Object.keys(filters).map(function (key) {

                        if ((key == "medicinalProductDomain"
                            || key == "procedureType"
                            || key == "applicationType"
                            || key == "lifecycleState")
                            && filters[key] != undefined
                            && filters[key].length > 0) {
                            var filterValue = filters[key].filter(x => x.selected).map(y => y.value);
                            return `${key}=>${filterValue}`;
                        }
                        return filters[key] != undefined ? `${key}=>${filters[key]}` : `${key}=>`;
                    });
                },
                getPageSize() {
                    const state = this.getState();
                    return state.pageSize || this.pageSize;
                },
                getPageIndex() {
                    const state = this.getState();
                    return state.pageIndex || 0;
                },
                getState() {
                    let stateString = localStorage.getItem(this.storageKey);
                    return stateString ? JSON.parse(stateString) : {};
                },
        }
    };
</script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTableV3" />
}