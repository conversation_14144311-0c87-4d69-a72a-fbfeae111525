﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class ProjectModelTests
    {
        [Fact]
        public void PicklistDataModel_Get_SetValue()
        {
            //Arrange
            var model = new ProjectModel();
            model.Id = 1;
            model.Name = "Test";
            model.Code = "abc";
            model.OpportunityNumber = "1";
            model.ClientId = 1;
            model.ClientName = "Test";
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void ProjectMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new ProjectMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
    }
}
