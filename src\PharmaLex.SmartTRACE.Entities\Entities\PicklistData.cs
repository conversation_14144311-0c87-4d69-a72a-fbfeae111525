﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class PicklistData: EntityBase
    {
        public PicklistData()
        {
            this.PicklistDataCountry = new HashSet<PicklistDataCountry>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public int PicklistTypeId { get; set; }

        public virtual ICollection<PicklistDataCountry> PicklistDataCountry { get; set; }
    }
}
