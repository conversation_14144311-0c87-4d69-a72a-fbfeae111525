﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.EntityFrameworkCore;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Controllers;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Helpers.Constants;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.WebTests.Controllers
{
    public class PicklistDataControllerTests
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IMapper mapper;
        private readonly IPicklistDataService picklistDataService;
        private readonly PicklistDataController controller;
        public PicklistDataControllerTests()
        {
            cache = Substitute.For<IDistributedCacheServiceFactory>();
            mapper = Substitute.For<IMapper>();
            picklistDataService = Substitute.For<IPicklistDataService>();
            controller = new PicklistDataController(cache, mapper, picklistDataService);
        }
        #region static methods
        private static List<CountryModel> GetCountryModel()
        {
            return new List<CountryModel>
            {
                new CountryModel { Id=1,RegionId=1, Name="country1"},
                new CountryModel { Id=2,RegionId=2, Name="country2"},
                new CountryModel { Id=3,RegionId=3, Name="country3"}
            };
        }
        #endregion
        [Fact]
        public async Task PickListData_Returns_InvalidObject()
        {
            // Arrange
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            //Act
            var result = await controller.PicklistData(1) as BadRequestObjectResult;

            //Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        [Fact]
        public async Task PickListData_Returns_Value()
        {
            int picklistTypeId = 1;
            List<PicklistDataCountry> picklistDataCountries = new List<PicklistDataCountry>()
            {
                new PicklistDataCountry{Country=new Country(){ Name="Belgium"},PicklistData=new PicklistData{Id=1,Name="Test",PicklistTypeId=001 } },
            };

            var picklistdatamodel = new List<PicklistDataModel>
                 {
                     new PicklistDataModel {Id = 1,Name = "Dosage Form1" },
                     new PicklistDataModel {Id = 2,Name = "Dosage Form2" },
                 };

            var picklistDataCountrycache = Substitute.For<IEntityCacheServiceProxy<PicklistDataCountry>>();

            cache.CreateEntity<PicklistDataCountry>()
                                          .Configure(Arg.Any<Func<IIncludable<PicklistDataCountry>, IIncludable<PicklistDataCountry>>>())
                                      .Returns(picklistDataCountrycache);

            picklistDataCountrycache.WhereAsync(Arg.Any<Expression<Func<PicklistDataCountry, bool>>>()).Returns(picklistDataCountries);

            var _picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);


            _picklistCache.WhereAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(picklistdatamodel);

            //Act
            var result = await controller.PicklistData(picklistTypeId);
            //Assert
            Assert.NotNull(result);
        }
        [Fact]
        public async Task New_Returns_InvalidObject()
        {
            // Arrange
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            //Act
            var result = await controller.New(1) as BadRequestObjectResult;

            //Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        [Fact]
        public async Task New_Returns_View()
        {
            //Arrange
            var Countries = GetCountryModel();
            cache.CreateMappedEntity<Country, CountryModel>().AllAsync().Returns(Countries);
            //Act
            var result = await controller.New(1) as ViewResult;
            //Assert
            Assert.NotNull(result);
            Assert.Equal("EditPicklistData", result?.ViewName);
        }
        [Fact]
        public async Task New_Returns_RedirectResult()
        {
            //Arrange
            EditPicklistDataViewModel model = new EditPicklistDataViewModel()
            {
                Picklist = new PicklistDataModel { Country = "Belgium", Name = "Test", CountriesIds = [123, 111] }
            };
            PicklistData picklistData = new PicklistData() { PicklistTypeId = 1 };
            var picklistdata = Substitute.For<ITrackedEntityCacheServiceProxy<PicklistData>>();
            cache.CreateTrackedEntity<PicklistData>().Returns(picklistdata);
            mapper.Map<PicklistDataModel, PicklistData>(model.Picklist).Returns(picklistData);

            var picklistdatacountry = Substitute.For<ITrackedEntityCacheServiceProxy<PicklistDataCountry>>();
            cache.CreateTrackedEntity<PicklistDataCountry>().Returns(picklistdatacountry);

            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            controller.TempData = tempData;

            //Act
            var result = await controller.New(1, model) as RedirectResult;
            //Assert
            Assert.NotNull(result);
            Assert.Equal("/data/1", result.Url);
        }
        [Fact]
        public async Task New_Returns_EditPicklistData_View()
        {
            // Arrange
            EditPicklistDataViewModel model = new EditPicklistDataViewModel()
            {
                Picklist = new PicklistDataModel { Country = "Belgium", Name = "Test", CountriesIds = [123, 111] }
            };
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            //Act
            var result = await controller.New(1, model) as ViewResult;

            //Assert
            Assert.NotNull(result);
            Assert.Equal("EditPicklistData", result?.ViewName);
        }
        [Fact]
        public async Task New_WhenPicklistNameIsDuplicate_ReturnsViewWithError()
        {
            // Arrange
            var model = new EditPicklistDataViewModel
            {
                Picklist = new PicklistDataModel { Name = "DuplicateName" }
            };
            var countries = new List<CountryModel>();
            cache.CreateMappedEntity<Country, CountryModel>().AllAsync().Returns(countries);
            picklistDataService.IsPicklistNameDuplicate("DuplicateName").Returns(true);

            // Act
            var result = await controller.New(1, model);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal("EditPicklistData", viewResult.ViewName);
            Assert.Same(model, viewResult.Model);
            Assert.True(model.HasError);
            Assert.Equal(ErrorMessages.UniqueErrorMessage, model.ErrorMessage);
            Assert.Same(countries, model.AllCountries);
        }
        [Fact]
        public async Task Edit_Returns_View()
        {
            //Arrange
            EditPicklistDataViewModel model = new EditPicklistDataViewModel()
            {
                Picklist = new PicklistDataModel { Country = "Belgium", Name = "Test", CountriesIds = [123, 111] }
            };

            List<PicklistDataCountry> picklistDataCountries = new List<PicklistDataCountry>()
            {
                new PicklistDataCountry{Country=new Country(){ Name="Belgium"},PicklistData=new PicklistData{Id=1,Name="Test",PicklistTypeId=001 } },
            };


            var _picklistDataCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, EditPicklistDataViewModel>>();
            cache.CreateMappedEntity<PicklistData, EditPicklistDataViewModel>().Returns(_picklistDataCache);
            _picklistDataCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(model);

            var _picklistCountryCache = Substitute.For<ITrackedEntityCacheServiceProxy<PicklistDataCountry>>();
            cache.CreateEntity<PicklistDataCountry>().Returns(_picklistCountryCache);
            _picklistCountryCache.WhereAsync(Arg.Any<Expression<Func<PicklistDataCountry, bool>>>()).Returns(picklistDataCountries);

            var Countries = GetCountryModel();
            cache.CreateMappedEntity<Country, CountryModel>().AllAsync().Returns(Countries);

            //Act
            var result = await controller.Edit(1) as ViewResult;
            //Assert
            Assert.NotNull(result);
            Assert.Equal("EditPicklistData", result?.ViewName);
        }
        [Fact]
        public async Task Edit_Returns_InvalidObject()
        {
            // Arrange
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            //Act
            var result = await controller.Edit(1) as BadRequestObjectResult;

            //Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        [Fact]
        public async Task Edit_Returns_RedirectResult()
        {
            //Arrange
            EditPicklistDataViewModel model = new EditPicklistDataViewModel()
            {
                Picklist = new PicklistDataModel { Country = "Belgium", Name = "Test", CountriesIds = [123, 111] }
            };

            List<PicklistDataCountry> picklistDataCountries = new List<PicklistDataCountry>()
            {
                new PicklistDataCountry{Country=new Country(){ Name="Belgium"},PicklistData=new PicklistData{Id=1,Name="Test",PicklistTypeId=001 } },
            };
            PicklistData picklistData = new PicklistData() { PicklistTypeId = 1 };
            var _picklistDataCache = Substitute.For<ITrackedEntityCacheServiceProxy<PicklistData>>();
            cache.CreateTrackedEntity<PicklistData>().Returns(_picklistDataCache);
            _picklistDataCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(picklistData);

            mapper.Map<PicklistDataModel, PicklistData>(model.Picklist).Returns(picklistData);


            var _picklistCountryCache = Substitute.For<ITrackedEntityCacheServiceProxy<PicklistDataCountry>>();
            cache.CreateTrackedEntity<PicklistDataCountry>().Returns(_picklistCountryCache);
            _picklistCountryCache.WhereAsync(Arg.Any<Expression<Func<PicklistDataCountry, bool>>>()).Returns(picklistDataCountries);

            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            controller.TempData = tempData;

            //Act
            var result = await controller.Edit(1, model) as RedirectResult;
            //Assert
            Assert.NotNull(result);
            Assert.Equal("/data/1", result.Url);
        }
        [Fact]
        public async Task Edit_Returns_EditPicklistData_View()
        {
            // Arrange
            var Countries = GetCountryModel();
            cache.CreateMappedEntity<Country, CountryModel>().AllAsync().Returns(Countries);

            EditPicklistDataViewModel model = new EditPicklistDataViewModel()
            {
                Picklist = new PicklistDataModel { Country = "Belgium", Name = "Test", CountriesIds = [123, 111] }
            };
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            //Act
            var result = await controller.Edit(1, model) as ViewResult;

            //Assert
            Assert.NotNull(result);
            Assert.Equal("EditPicklistData", result?.ViewName);
        }
        [Fact]
        public async Task Edit_WhenPicklistNameIsDuplicate_ReturnsViewWithError_()
        {
            // Arrange
            var model = new EditPicklistDataViewModel
            {
                Picklist = new PicklistDataModel { Name = "DuplicateName" }
            };
            var countries = new List<CountryModel>();
            cache.CreateMappedEntity<Country, CountryModel>().AllAsync().Returns(countries);
            picklistDataService.IsPicklistNameDuplicate("DuplicateName").Returns(true);

            // Act
            var result = await controller.Edit(1, model);

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal("EditPicklistData", viewResult.ViewName);
            Assert.Same(model, viewResult.Model);
            Assert.True(model.HasError);
            Assert.Equal(ErrorMessages.UniqueErrorMessage, model.ErrorMessage);
            Assert.Same(countries, model.AllCountries);
        }
        [Fact]
        public async Task Delete_Returns_View()
        {
            var picklistdatamodel = new List<PicklistDataModel>
                 {
                     new PicklistDataModel {Id = 1,Name = "Dosage Form1" },
                     new PicklistDataModel {Id = 2,Name = "Dosage Form2" },
                 };
            var _picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(_picklistCache);

            _picklistCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(picklistdatamodel[0]);
            var result = await controller.Delete(1) as ViewResult;
            Assert.NotNull(result);
            Assert.Equal("DeletePicklistData", result.ViewName);
        }
        [Fact]
        public async Task Delete_Returns_InvalidObject()
        {
            // Arrange
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            //Act
            var result = await controller.Delete(1) as BadRequestObjectResult;

            //Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        [Fact]
        public async Task ConfirmDelete_Returns_RedirectResult()
        {
            PicklistData picklistData = new PicklistData() { PicklistTypeId = 1 };
            var _picklistDataCache = Substitute.For<ITrackedEntityCacheServiceProxy<PicklistData>>();
            cache.CreateTrackedEntity<PicklistData>().Returns(_picklistDataCache);
            _picklistDataCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(picklistData);

            var tempData = new TempDataDictionary(new DefaultHttpContext(), Substitute.For<ITempDataProvider>());
            tempData.TryGetValue("UserNotification", out var userNotificationModel);
            tempData.Set("td", "td");
            controller.TempData = tempData;

            //Act
            var result = await controller.ConfirmedDelete(1) as RedirectResult;
            //Assert
            Assert.NotNull(result);
            Assert.Equal("/data/1", result.Url);
        }
        [Fact]
        public async Task ConfirmedDelete_Returns_InvalidObject()
        {
            // Arrange
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            //Act
            var result = await controller.ConfirmedDelete(1) as BadRequestObjectResult;

            //Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.StatusCode);
        }
        [Fact]
        public async Task UpdatePicklistData_Returns_Value()
        {
            IList<CountryModel> countryModels = new List<CountryModel>()
            {
                new CountryModel { Name = "Test", ErrorMessage = "Error", Id = 1, HasError = false, RegionId = 123 }

            };
            List<PicklistDataCountry> picklistDataCountry = new()
            {
            new PicklistDataCountry(){ Id = 1, CountryId=1},
            new PicklistDataCountry(){ Id = 2, CountryId=1}
            };
            PicklistDataModel pkDatamodel = new()
            {
                Id = 11,
                Name = "prod1",
                CountriesIds = [1],
                PicklistTypeId = 11
            };
            List<PicklistDataModel> PicklistDataModel = new()
            {   new PicklistDataModel(){Id =1,Name= "prod1",CountriesIds=[1],PicklistTypeId=1, Selected=true, Country="country1"  },
                new PicklistDataModel(){Id =2,Name= "prod2",CountriesIds=[2],PicklistTypeId=1, Selected=true, Country="country2"}
            };

            var PicklistDataCountryCache = Substitute.For<IEntityCacheServiceProxy<PicklistDataCountry>>();
            cache.CreateEntity<PicklistDataCountry>().Returns(PicklistDataCountryCache);
            PicklistDataCountryCache.AllAsync().Returns(Task.FromResult(picklistDataCountry));

            var picklistCache = Substitute.For<IMappedEntityCacheServiceProxy<PicklistData, PicklistDataModel>>();
            cache.CreateMappedEntity<PicklistData, PicklistDataModel>().Returns(picklistCache);
            picklistCache.WhereAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(Task.FromResult(PicklistDataModel));

            var result = await controller.UpdatePicklistData(countryModels);
            Assert.NotNull(result);

        }
        [Fact]
        public async Task UpdatePicklistData_Returns_InvalidObject()
        {
            IList<CountryModel> countryModels = new List<CountryModel>()
            {
                new CountryModel { Name = "Test", ErrorMessage = "Error", Id = 1, HasError = false, RegionId = 123 }

            };
            // Arrange
            controller.ModelState.AddModelError("Key", "ErrorMessage");

            //Act
            var result = await controller.UpdatePicklistData(countryModels);

            //Assert
            Assert.NotNull(result);
        }
        [Fact]
        public async Task GetCountriesPerProcedureType_Returns_List()
        {
            PicklistData picklistData = new PicklistData() { PicklistTypeId = 1 };
            var _procedureTypeCache = Substitute.For<IEntityCacheServiceProxy<PicklistData>>();
            cache.CreateEntity<PicklistData>()
                                             .Configure(Arg.Any<Func<IIncludable<PicklistData>, IIncludable<PicklistData>>>())
                                      .Returns(_procedureTypeCache);
            _procedureTypeCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PicklistData, bool>>>()).Returns(picklistData);
            var result = await controller.GetCountriesPerProcedureType(1);
            Assert.NotNull(result);
        }
        [Fact]
        public async Task GetCountriesPerProcedureType_Returns_InvalidObject()
        {
            controller.ModelState.AddModelError("Key", "ErrorMessage");
            await Assert.ThrowsAsync<InvalidCastException>(async () => await controller.GetCountriesPerProcedureType(1));
        }
    }
}
