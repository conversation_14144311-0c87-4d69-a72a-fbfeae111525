﻿CREATE TRIGGER [dbo].[User_Insert] ON [dbo].[User]
FOR INSERT AS
INSERT INTO [Audit].[User_Audit]
SELECT 'I', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[User_Update] ON [dbo].[User]
FOR UPDATE AS
INSERT INTO [Audit].[User_Audit]
SELECT 'U', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] FROM [Inserted]
GO

CREATE TRIGGER [dbo].[User_Delete] ON [dbo].[User]
FOR DELETE AS
INSERT INTO [Audit].[User_Audit]
SELECT 'D', [Id], [Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], GETDATE(), COALESCE(RTRIM(CONVERT(VARCHAR(128), CONTEXT_INFO())), SUSER_NAME()) FROM [Deleted]
GO

INSERT INTO [dbo].[User]
SELECT '<EMAIL>', 'Rob', 'Williams', NULL, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[User]
SELECT '<EMAIL>', 'Velin', 'Angelov', NULL, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[User]
SELECT '<EMAIL>', 'Tanya', 'Lisseva', NULL, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[User] 
SELECT '<EMAIL>', 'Irina', 'Dimitrova', NULL, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[User] 
SELECT '<EMAIL>', 'Harry', 'Rowland', NULL, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[User]
SELECT '<EMAIL>', 'Tanya', 'Lisseva(Tester)', NULL, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[User] 
SELECT '<EMAIL>', 'Tanya', 'Lisseva(Validator)', NULL, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[User] 
SELECT '<EMAIL>', 'Velin', 'Angelov(Tester)', NULL, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[User] 
SELECT '<EMAIL>', 'Rob', 'Williams(Tester)', NULL, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[User] 
SELECT '<EMAIL>', 'Velin', 'Angelov(Admin)', NULL, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[User] 
SELECT '<EMAIL>', 'Jennie', 'May', NULL, GETDATE(), 'update script', GETDATE(), 'update script'
INSERT INTO [dbo].[User] 
SELECT '<EMAIL>', 'Jill', 'Elliott', NULL, GETDATE(), 'update script', GETDATE(), 'update script'