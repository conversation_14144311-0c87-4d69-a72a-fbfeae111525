﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class SubmissionModelTests
    {
        [Fact]
        public void SubmissionModel_Get_SetValue()
        {
            //Arrange
            var model = new SubmissionModel();
            model.Id = 1;
            model.UniqueId = "Test";
            model.DeliveryDetailsId = 1;
            model.SubmissionDeliveryDetails = "Test";
            model.Description = "Test";
            model.RelatedSequenceNumber = "Test123";
            model.DossierFormatId = 123;
            model.DossierFormat = "Test";
            model.DossierFormat = "Test";
            model.LifecycleState = "Test";
            model.SequenceNumber = "001";
            model.SubmissionMode = "Test";
            model.SubmissionType = "Test";
            model.SubmissionUnit = "Test";
            model.SerialNumber = "Test";
            model.ReferenceNumber = "Test";
            model.SubmissionTypeId = 123;
            model.SubmissionUnitId = 123;
            model.SubmissionModeId = 123;
            model.Comments = "Test";
            model.LifecycleStateId = 1;
            model.PreviousLifecycleStateId = 1;
            model.HealthAuthorityDueDate = DateTime.Now;
            model.AuthoringDeadline = DateTime.Now;
            model.PlannedDispatchDate = DateTime.Now;
            model.ActualDispatchDate = DateTime.Now;
            model.PlannedSubmissionDate = DateTime.Now;
            model.ActualSubmissionDate = DateTime.Now;
            model.CreatedBy = "nitish";
            model.CreatedDate = DateTime.Now;
            model.CespNumber = "1";
            model.WithdrawalDate = DateTime.Now;
            model.DisplayRegulatoryLead = "test";
            model.DisplayPublishingLead = "test";
            model.DisplayCountries = "Test";
            model.SourceDocumentsLocation = "Test";
            model.ArchivedDocumentsLocation = "Test";
            model.DossierName = "Test";
            model.DocubridgeVersionId = "Test";
            model.ApplicationId = 123;
            model.ProjectId = 123;
            model.CountriesIds = new List<int>();
            model.RegionId = 123;
            model.SubmissionResource = new SubmissionResourceModel();
            model.Documents = new List<DocumentModel>();
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void SubmissionMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new SubmissionMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void SubmissionLifecycleStateList_Get_SetValue()
        {
            //Arrange
            var model = new SubmissionLifecycleStateList();
            model.Selected = true;
            //Assert
            Assert.NotNull(model);
        }
    }
}
