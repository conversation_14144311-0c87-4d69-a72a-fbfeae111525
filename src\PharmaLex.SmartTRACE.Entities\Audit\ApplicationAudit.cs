﻿using PharmaLex.DataAccess;

namespace PharmaLex.SmartTRACE.Entities.Audit
{
    public partial class ApplicationAudit: AuditEntityBase
    {
        public override int AuditId { get; set; }
        public override string AuditAction { get; set; }

        public int? Id { get; set; }
        public string ApplicationNumber { get; set; }
        public int? ApplicationTypeId { get; set; }
        public int? MedicinalProductDomainId { get; set; }
        public int? MedicinalProductTypeId { get; set; }
        public int? ProcedureTypeId { get; set; }
        public string Comments { get; set; }
        public int LifecycleStateId { get; set; }
    }
}
