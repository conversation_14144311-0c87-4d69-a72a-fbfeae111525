﻿@model ApiPagedListResult<SubmissionTableViewModel>
@using PharmaLex.Caching.Data
@using PharmaLex.SmartTRACE.Entities
@using PharmaLex.SmartTRACE.Entities.Enums
@using Microsoft.AspNetCore.Authorization
@using PharmaLex.SmartTRACE.Web.Models.ViewModels
@inject IDistributedCacheServiceFactory cache
@inject IAuthorizationService AuthorizationService
@inject AutoMapper.IMapper mapper

@{
    var dossierFormats = (await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                              .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.DossierFormat)).OrderBy(d => d.Name);
    var submissionUnits = (await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                         .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.SubmissionUnit)).OrderBy(s => s.Name);
    var submissionModes = (await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                         .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.SubmissionMode)).OrderBy(s => s.Name);
    var lifecycleStates = mapper.Map<IEnumerable<SubmissionLifecycleStateList>>(Enum.GetValues(typeof(SubmissionLifeCycleState))).OrderBy(l => l.Name);
    var isExternalEditorUser = (await AuthorizationService.AuthorizeAsync(User, "ExternalEditor")).Succeeded;

}

<div id="submissions" class="manage-container">
    <header class="manage-header">
        <h2>Submissions</h2>
        @if(!isExternalEditorUser)
        {
            <a id="exportButton" class="button icon-button-download">Export</a>
        }
    </header>
    <filtered-table :items="submissions" 
                    :columns="columns" 
                    :filters="filters" 
                    :link="link"
                    :total-items-count="totalItemCount"
                    :filtered-count="filteredCount"
                    @@filter="onFilter"
                    @@sort="onSort"
                    v-on:on-page-index-change="onPageIndexChange"
                    v-on:on-page-size-change="onPageSizeChange"
                    v-on:on-load-state="onLoadState"
                    :resources="resources"></filtered-table>
    <form id="exportForm" style="display: none;" method="post" action="/submissions/export">
        @Html.AntiForgeryToken()
        <input type="hidden" id="clientName" name="clientName" />
        <input type="hidden" id="applicationNumber" name="applicationNumber" />
        <input type="hidden" id="description" name="description" />
        <input type="hidden" id="displayPublishers" name="displayPublishers" />
        <input type="hidden" id="regulatoryLead" name="regulatoryLead" />
        <input type="hidden" id="dossierFormat" name="dossierFormat" />
        <input type="hidden" id="lifecycleState" name="lifecycleState" />
        <input type="hidden" id="sequenceNumber" name="sequenceNumber" />
        <input type="hidden" id="submissionMode" name="submissionMode" />
        <input type="hidden" id="submissionType" name="submissionType" />
        <input type="hidden" id="submissionUnit" name="submissionUnit" />
        <input type="hidden" id="exportColumns" name="exportColumns" />
    </form>
    <export-submissions :config="exportConfig" v-on:changed-column="onChangedColumn" v-on:button-pressed="onExportSubmissions" v-on:select-all="onSelectAllColumns"></export-submissions>
</div>

@section Scripts {
<script type="text/javascript">

    var pageConfig = {
        appElement: '#submissions',
        data() {
            return {
                link: '/submissions/view/',
                submissions: [],
                generalTabColumns: ["Unique Id", "Application Number",
                    "Countries", "Submission Delivery Details", "Submission Type",
                    "Submission Unit", "Submission Mode", "Dossier Format",
                    "Serial Number", "Sequence Number", "Related Sequence Number",
                    "Description", "Submission Comments", "Lifecycle State"],
                clientDetailsTabColumns: ["Client Name", "Project Name", "Project Code", "Opportunity Number", "Contract Owner"],
                datesTabColumns: ["Created By", "Created Date", "Health Authority Due Date", "Deadline for Last Document",
                    "Planned Submission", "Planned Dispatch", "Actual Dispatch", "Actual Submission", "docuBridge Version Id", "Portal/Gateway Reference Number"],
                resourcesTabColumns: ["Priority", "Estimated Size", "Estimated Hours", "Publishing Lead", "Regulatory Lead",
                    "Publishing Team", "Publishers", "Draft Submission Form Sent To", "Submission Resource Comments"],
                dossierTabColumns: ["Location of Source Documents", "Location of Archived Dossier", "Submission Dossier"],
                exportConfig: {
                    message: 'Submission export',
                    errorMessage: 'Please select at least one submission column',
                    noButton: 'Cancel',
                    yesButton: 'Export',
                    elementId: 'exportButton',
                    buttonClass: 'icon-button-upload'
                },
                totalItemCount: 0,
                filteredCount: 0,
                offset: 0,
                pageSize: 25,
                resources: {
                        sortByFormat: 'Sort by {}',
                    },
                selectedColumns: [],
                exportColumns: null,
                storageKey: 'submissionExportColumns',
                isExternalUser: @Html.Raw(Json.Serialize(isExternalEditorUser)),
                columns: {
                    idKey: 'id',
                    styleKey: 'lifecycleState',
                    config: [
                        {
                            dataKey: 'clientName',
                            sortKey: 'clientName',
                            header: 'Client Name',
                            type: 'text'
                        },
                        {
                            dataKey: 'applicationNumber',
                            sortKey: 'applicationNumber',
                            header: 'Application Number',
                            type: 'text'
                        },
                        {
                            dataKey: 'sequenceNumber',
                            sortKey: 'sequenceNumber',
                            header: 'Sequence Number',
                            type: 'text'
                        },
                        {
                            dataKey: 'dossierFormat',
                            header: 'Dossier Format',
                            type: 'text'
                        },
                        {
                            dataKey: 'submissionType',
                            header: 'Submission Type',
                            type: 'text'
                        },
                        {
                            dataKey: 'submissionUnit',
                            header: 'Submission Unit',
                            type: 'text'
                        },
                        {
                            dataKey: 'submissionMode',
                            header: 'Submission Mode',
                            type: 'text'
                        },
                        {
                            dataKey: 'description',
                            sortKey: 'description',
                            header: 'Submission Description',
                            type: 'text'
                        },
                        {
                            dataKey: 'lifecycleState',
                            header: 'Lifecycle State',
                            type: 'text'
                        },
                        {
                            dataKey: 'regulatoryLead',
                            sortKey: 'regulatoryLead',
                            header: 'Regulatory Lead',
                            type: 'text'
                        },
                        {
                            dataKey: 'displayPublishers',
                            header: 'Publishers',
                            type: 'text'
                        }
                    ]
                },
                filters: [
                    {
                        key: 'clientName',
                        options: [],
                        type: 'search',
                        header: 'Search Client',
                        fn: v => p => p.clientName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'applicationNumber',
                        options: [],
                        type: 'search',
                        header: 'Search Application Number',
                        fn: v => p => p.applicationNumber.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'sequenceNumber',
                        options: [],
                        type: 'search',
                        header: 'Search Sequence Number',
                        fn: v => p => (p.sequenceNumber || '').toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'dossierFormat',
                        options: @Html.Raw(dossierFormats.ToJson()),
                        filterCollection: 'dossierFormat',
                        type: 'select-multiple',
                        header: 'Filter By Dossier Format',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.dossierFormat),
                        convert: v => v
                    },
                    {
                        key: 'submissionType',
                        options: [],
                        type: 'search',
                        header: 'Search Submission Type',
                        fn: v => p => (p.submissionType || '').toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'submissionUnit',
                        options: @Html.Raw(submissionUnits.ToJson()),
                        filterCollection: 'submissionUnit',
                        type: 'select-multiple',
                        header: 'Filter By Unit',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.submissionUnit),
                        convert: v => v
                    },
                    {
                        key: 'submissionMode',
                        options: @Html.Raw(submissionModes.ToJson()),
                        filterCollection: 'submissionMode',
                        type: 'select-multiple',
                        header: 'Filter By Mode',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.submissionMode),
                        convert: v => v
                    },
                    {
                        key: 'lifecycleState',
                        options: @Html.Raw(lifecycleStates.ToJson()),
                        filterCollection: 'lifecycleState',
                        type: 'select-multiple',
                        header: 'Filter By Multiple States',
                        fn: valuesCollection => p => valuesCollection.every(v => !v.selected) ? true : valuesCollection.filter(v => v.selected).map(v => v.value).includes(p.lifecycleState),
                        convert: v => v
                    },
                    {
                        key: 'description',
                        options: [],
                        type: 'search',
                        header: 'Search Description',
                        fn: v => p => (p.description || '').toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'regulatoryLead',
                        options: [],
                        type: 'search',
                        header: 'Search Regulatory Lead',
                        fn: v => p => (p.regulatoryLead || '').toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    }
                ],
                storageKey: `${window.location.href}(${this.$attrs.id || ''})`
            };
               
        },
        methods: {
                onLoadState() {
                    this.pageSize = this.getPageSize();
                    const filters = this.getFilters();
                    const sort = this.getSort();
                    const pageIndex = this.getPageIndex();
                    this.getData(pageIndex * this.pageSize, this.pageSize, filters, sort);
                },
                onSort(sortModel, pageIndex) {
                    this.pageSize = this.getPageSize();
                    this.getData(pageIndex * this.pageSize, this.pageSize, this.getFilters(), this.toSortValues(Object.entries(sortModel)));
                },
                onFilter(filters) {
                    const mapFilters = filters ? this.toKeyValueFilter(filters) : {};
                    if (this.timeout) {
                        clearTimeout(this.timeout);
                    }
                    this.timeout = setTimeout(() => {
                        this.getData(0, this.pageSize, mapFilters, this.getSort());
                    }, 700)
                },
                onPageIndexChange(pageIndex) {
                    this.pageSize = this.getPageSize();
                    this.getData(pageIndex * this.pageSize, this.pageSize, this.getFilters(), this.getSort());
                },
                onPageSizeChange(pageSize) {
                    this.pageSize = pageSize;
                    const filters = this.getFilters();
                    const sort = this.getSort();
                    this.getData(0, this.pageSize, filters, sort);
                },
                getData(skip, take, filters, sort) {
                    const requestOptions = {
                        method: "GET",
                        credentials: 'same-origin'
                    };

                    var tableFilters = Object.keys(filters).length !== 0 ? "&filters=" + filters.join('&filters=') : '';

                    fetch(`/paged-app-submissions?skip=${skip}&take=${take}${tableFilters}&sort=${sort}`, requestOptions)
                        .then(r => r.json())
                        .then(body => {
                            this.submissions = body.data;
                            this.filteredCount = body.paging.filteredCount;
                            this.totalItemCount = body.paging.totalItemCount;
                            this.offset = body.paging.offset;
                            this.pageSize = body.paging.limit;
                        });
                },
                getSort() {
                    let state = this.getState();
                    return state.sortModel ? this.toSortValues(state.sortModel) : "";
                },
                toSortValues(sortModel) {
                    let selectedProperty = sortModel.find(e => e[1] !== 0);

                    if (selectedProperty) {
                        if (selectedProperty[1] === 1) {
                            return `${selectedProperty[0]}=>asc`;
                        }
                        else if (selectedProperty[1] === -1) {
                            return `${selectedProperty[0]}=>desc`;
                        }
                    }

                    return "";
                },
                getFilters() {
                    let state = this.getState();
                    return state.filterModel ? this.toKeyValueFilter(state.filterModel) : {};
                },
                toKeyValueFilter(filters) {
                    return Object.keys(filters).map(function (key) {

                        if ((key == "dossierFormat"
                            || key == "submissionUnit"
                            || key == "submissionMode"
                            || key == "lifecycleState")
                            && filters[key] != undefined
                            && filters[key].length > 0) {
                            var filterValue = filters[key].filter(x => x.selected).map(y => y.value);
                            return `${key}=>${filterValue}`;
                        }
                        return filters[key] != undefined ? `${key}=>${filters[key]}` : `${key}=>`;
                    });
                },
                getPageSize() {
                    const state = this.getState();
                    return state.pageSize || this.pageSize;
                },
                getPageIndex() {
                    const state = this.getState();
                    return state.pageIndex || 0;
                },
                getState() {
                    let stateString = localStorage.getItem(this.storageKey);
                    return stateString ? JSON.parse(stateString) : {};
                },
            onChangedColumn(event) {
                if (event) {
                    const column = this.selectedColumns.find(col => col.columnType == event.columnType && col.columnName == event.columnName);
                    if (column) {
                        const index = this.selectedColumns.indexOf(column);
                        this.selectedColumns.splice(index, 1);
                    }
                    else {
                        this.selectedColumns.push(event);
                    }

                    localStorage.setItem(this.storageKey, JSON.stringify(this.selectedColumns));
                    this.exportConfig.selectedColumns = this.selectedColumns;
                }
            },
            onExportSubmissions(exportButtonClicked) {
                if (exportButtonClicked && this.selectedColumns.length !== 0) {
                    Object.keys(localStorage).forEach(key => {
                        if (key.endsWith(`${window.location.pathname}()`)) {
                            let appSettings = localStorage.getItem(key);
                            if (appSettings.includes('filterModel')) {
                                let filterModel = JSON.parse(appSettings).filterModel;
                                Object.keys(filterModel).forEach(filterKey => {
                                    if (Array.isArray(filterModel[filterKey])) {
                                        document.getElementById(filterKey).value = filterModel[filterKey].filter(f => f.selected).map(f => f.value).join(',');
                                    } else {
                                        document.getElementById(filterKey).value = filterModel[filterKey];
                                    }
                                })
                            }
                        }
                    })
                    document.getElementById('exportColumns').value = JSON.stringify(this.selectedColumns);
                    document.getElementById('exportForm').submit();
                    this.initializeSelectedColumns();
                }
            },
            onSelectAllColumns(event) {
                if (event.currentTarget.checked) {
                    this.selectedColumns =
                        [...this.generalTabColumns.map(function (columnName) {
                            return { columnType: 'generalColumn', columnName: columnName }
                        }),
                        ...this.clientDetailsTabColumns.map(function (columnName) {
                            return { columnType: 'clientDetailsColumn', columnName: columnName }
                        }),
                        ...this.datesTabColumns.map(function (columnName) {
                            return { columnType: 'datesColumn', columnName: columnName }
                        }),
                        ...this.resourcesTabColumns.map(function (columnName) {
                            return { columnType: 'resourcesColumn', columnName: columnName }
                        }),
                        ...this.dossierTabColumns.map(function (columnName) {
                            return { columnType: 'documentColumn', columnName: columnName }
                        })];
                }
                else {
                    this.selectedColumns = Vue.ref([]);
                }

                localStorage.setItem(this.storageKey, JSON.stringify(this.selectedColumns));
                this.exportConfig.selectedColumns = this.selectedColumns;
            },
            initializeSelectedColumns() {
                let initialColumns = localStorage.getItem(this.storageKey);
                if (initialColumns) {
                    this.selectedColumns = JSON.parse(initialColumns);
                    this.exportConfig.selectedColumns = this.selectedColumns;
                }
            }
        },
        created() {
            if(this.isExternalUser) {
                this.columns.config = this.columns.config.filter(c => c.dataKey != 'clientName');
                this.columns.config = this.columns.config.filter(c => c.dataKey != 'regulatoryLead');
                this.columns.config = this.columns.config.filter(c => c.dataKey != 'displayPublishers');
                this.filters = this.filters.filter(c => c.key != 'clientName');
                this.filters = this.filters.filter(c => c.key != 'regulatoryLead');
                this.filters = this.filters.filter(c => c.key != 'displayPublishers');
                
            }
        },
        mounted() {
            this.exportConfig.generalTabColumns = this.generalTabColumns;
            this.exportConfig.clientDetailsTabColumns = this.clientDetailsTabColumns;
            this.exportConfig.datesTabColumns = this.datesTabColumns;
            this.exportConfig.resourcesTabColumns = this.resourcesTabColumns;
            this.exportConfig.dossierTabColumns = this.dossierTabColumns;
            this.initializeSelectedColumns();
        }
    };
</script>
}
@section VueComponentScripts {
    <partial name="Components/FilteredTableV3" />
    <partial name="Components/ExportSubmissions" />
}
