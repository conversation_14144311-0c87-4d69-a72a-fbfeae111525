using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using System;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Middleware
{
    public class SessionTimeoutMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly int _timeoutMinutes;

        public SessionTimeoutMiddleware(RequestDelegate next, int timeoutMinutes = 20)
        {
            _next = next;
            _timeoutMinutes = timeoutMinutes;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Check if user is authenticated
            if (context.User.Identity.IsAuthenticated)
            {
                // Get the authentication result
                var authResult = await context.AuthenticateAsync();
                
                if (authResult.Succeeded)
                {
                    var issuedUtc = authResult.Properties.IssuedUtc;
                    var expiresUtc = authResult.Properties.ExpiresUtc;
                    
                    // Check if we need to update the expiration
                    if (issuedUtc.HasValue)
                    {
                        var now = DateTimeOffset.UtcNow;
                        var timeElapsed = now - issuedUtc.Value;
                        
                        // If more than half the timeout has passed, refresh the cookie
                        if (timeElapsed.TotalMinutes > (_timeoutMinutes / 2))
                        {
                            authResult.Properties.ExpiresUtc = now.AddMinutes(_timeoutMinutes);
                            authResult.Properties.IssuedUtc = now;
                            
                            // Re-sign the user in with updated properties
                            await context.SignInAsync(authResult.Principal, authResult.Properties);
                        }
                    }
                    else
                    {
                        // Set expiration if not set
                        authResult.Properties.ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(_timeoutMinutes);
                        authResult.Properties.IssuedUtc = DateTimeOffset.UtcNow;
                        await context.SignInAsync(authResult.Principal, authResult.Properties);
                    }
                }
            }

            await _next(context);
        }
    }
}
