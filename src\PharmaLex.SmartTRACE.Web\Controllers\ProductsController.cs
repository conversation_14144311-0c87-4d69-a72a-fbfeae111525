﻿using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.DataAccess;
using System.Linq;
using System.Collections.Generic;
using PharmaLex.SmartTRACE.Web.Helpers;
using Microsoft.AspNetCore.Authorization;
using PharmaLex.SmartTRACE.Entities.Enums;
using System.Web;
using System.Text.RegularExpressions;
using PharmaLex.SmartTRACE.Web.HTMLTags;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    [Authorize("BusinessAdmin")]
    public class ProductsController : BaseController
    {
        private readonly IDistributedCacheServiceFactory _cache;
        private readonly IMapper _mapper;
        private readonly IProductExport _productExport;
        private const string UniqueErrorMessage = "Name, Dosage Form, Strength and Client must be a unique combination";

        public ProductsController(IDistributedCacheServiceFactory cache, IMapper mapper, IProductExport productExport)
        {
            this._cache = cache;
            this._mapper = mapper;
            this._productExport = productExport;
        }

        [HttpGet, Route("/products")]
        public async Task<IActionResult> Index()
        {
            var productCache = _cache.CreateMappedEntity<Product, ProductModel>()
                .Configure(o => o
                    .Include(x => x.Client));
            var products = await productCache.AllAsync();
            foreach (var product in products)
            {
                product.DosageForm = (await PicklistHelper.ExtractPicklist(_cache, product.DosageFormId)).Name;
                product.LifecycleState = ((CommonLifecycleState)product.LifecycleStateId).ToString();
            }
            return View(products);
        }

        [HttpGet, Route("/products/new")]
        public async Task<IActionResult> New()
        {
            (IList<ActiveSubstanceModel> allActiveSubstances, IList<ClientModel> allClients, IList<PicklistDataModel> picklists) = await PrepareInitialData();

            if (!(allClients?.Any() ?? false))
            {
                return View("GoToClient", new EditProductViewModel());
            }

            return View("EditProduct", new EditProductViewModel()
            {
                Product = new ProductModel(),
                ActiveSubstances = allActiveSubstances,
                AllClients = allClients,
                Picklists = picklists
            });
        }

        [HttpPost, Route("/products/new"), ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveNew(EditProductViewModel model)
        {
            try
            {
                (IList<ActiveSubstanceModel> allActiveSubstances, IList<ClientModel> allClients, IList<PicklistDataModel> picklists) = await PrepareInitialData();
                model.ActiveSubstances = allActiveSubstances;
                model.AllClients = allClients;
                model.Picklists = picklists;
                if (!ModelState.IsValid || HtmlTags.CheckHTMLTags(model.Product.Name))
                {
                    return this.BadRequest(this.ModelState);
                }
                var productCache = _cache.CreateTrackedEntity<Product>();
                var product = _mapper.Map<Product>(model.Product);
                product.LifecycleStateId = (int)CommonLifecycleState.Active;
                productCache.Add(product);
                await productCache.SaveChangesAsync();

                var substanceProductCache = _cache.CreateTrackedEntity<ActiveSubstanceProduct>();

                foreach (var activeSubstance in model.Product.ActiveSubstances)
                {
                    substanceProductCache.Add(new ActiveSubstanceProduct
                    {
                        ActiveSubstanceId = activeSubstance.Id,
                        ProductId = product.Id
                    });
                }
                await substanceProductCache.SaveChangesAsync();
                this.AddConfirmationNotification($"<em>{product.Name}</em> created");

                return Redirect("/products");
            }
            catch (DbUpdateException)
            {
                model.Product.HasError = true;
                model.Product.ErrorMessage = UniqueErrorMessage;
                return View("EditProduct", model);
            }
        }

        [HttpGet, Route("/products/edit/{id}")]
        public async Task<IActionResult> Edit(int id)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var product = await _cache.CreateTrackedEntity<Product>()
                                       .Configure(o => o
                                            .Include(x => x.ApplicationProduct)
                                            .Include(x => x.ActiveSubstanceProduct)
                                                .ThenInclude(x => x.ActiveSubstance))
                                       .FirstOrDefaultAsync(x => x.Id == id);
            (IList<ActiveSubstanceModel> allActiveSubstances, IList<ClientModel> allClients, IList<PicklistDataModel> picklists) = await PrepareInitialData();
            var productModel = _mapper.Map<EditProductViewModel>(product);
            productModel.IsApplicationAssigned = product.ApplicationProduct.Any();
            productModel.ActiveSubstances = allActiveSubstances;
            productModel.AllClients = allClients;
            productModel.Picklists = picklists;
            return View("EditProduct", productModel);
        }

        [HttpPost, Route("/products/edit/{id}"), ValidateAntiForgeryToken]
        public async Task<IActionResult> SaveEdit(int id, EditProductViewModel model)
        {
            try
            {
                (IList<ActiveSubstanceModel> allActiveSubstances, IList<ClientModel> allClients, IList<PicklistDataModel> picklists) = await PrepareInitialData();
                model.ActiveSubstances = allActiveSubstances;
                model.AllClients = allClients;
                model.Picklists = picklists;
                if (!ModelState.IsValid || HtmlTags.CheckHTMLTags(model.Product.Name) || HtmlTags.CheckHTMLTags(model.Product.Strength))
                {
                    return View("EditProduct", model);
                }

                var productCache = _cache.CreateTrackedEntity<Product>();
                var product = await productCache.FirstOrDefaultAsync(x => x.Id == id);
                _mapper.Map(model.Product, product);
                await productCache.SaveChangesAsync();

                var activeSubstanceCache = _cache.CreateTrackedEntity<ActiveSubstance>();
                var activeSubstanceProduct = _cache.CreateTrackedEntity<ActiveSubstanceProduct>();
                var substances = (await activeSubstanceProduct.WhereAsync(x => x.ProductId == id)).ToList();
                var missingActiveSubstances = model.Product.ActiveSubstances.Where(x => !substances.Select(y => y.ActiveSubstanceId).Contains(x.Id)).ToList();

                foreach (var missingActSubId in missingActiveSubstances.Select(ms => ms.Id))
                {
                    var actSubProducts = substances.Where(x => x.ActiveSubstanceId == missingActSubId);
                    if (!actSubProducts.Any())
                    {
                        var activeSubstance = await activeSubstanceCache.FirstOrDefaultAsync(x => x.Id == missingActSubId);
                        activeSubstance.ClientId = model.Product.ClientId;
                        await activeSubstanceCache.SaveChangesAsync();
                    }
                    activeSubstanceProduct.Add(new ActiveSubstanceProduct
                    {
                        ActiveSubstanceId = missingActSubId,
                        ProductId = id
                    });
                }

                var deletedActiveSubstanceProducts = substances.Where(x => !model.Product.ActiveSubstances.Select(y => y.Id).Contains(x.ActiveSubstanceId)).ToList();

                foreach (var deletedActSubProduct in deletedActiveSubstanceProducts)
                {
                    activeSubstanceProduct.Remove(deletedActSubProduct);
                }

                await activeSubstanceProduct.SaveChangesAsync();
                this.AddConfirmationNotification($"<em>{product.Name}</em> updated");
                return Redirect("/products");
            }
            catch (DbUpdateException)
            {
                model.Product.HasError = true;
                model.Product.ErrorMessage = UniqueErrorMessage;
                return View("EditProduct", model);
            }
        }

        [HttpPost("/products/state/{id:int}"), Authorize(Policy = "BusinessAdmin"), ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangeState(int id, [FromBody] string stateId)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState);
            }
            var productCache = _cache.CreateTrackedEntity<Product>();
            var product = await productCache.FirstOrDefaultAsync(x => x.Id == id);
            product.LifecycleStateId = int.Parse(stateId);
            await productCache.SaveChangesAsync();
            return Ok(product.LifecycleStateId);
        }

        [HttpPost("/products/export"), Authorize(Policy = "Reader"), ValidateAntiForgeryToken]
        public async Task<IActionResult> Export()
        {
            return File(await this._productExport.Export(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"Products_{System.DateTime.Now.ToString("yyyy-MM-dd")}.xlsx");
        }
        private async Task<(IList<ActiveSubstanceModel>, IList<ClientModel>, IList<PicklistDataModel> picklists)> PrepareInitialData()
        {
            var asCache = _cache.CreateMappedEntity<ActiveSubstance, ActiveSubstanceModel>();
            var clientCache = _cache.CreateMappedEntity<Client, ClientModel>();
            var picklistCache = _cache.CreateMappedEntity<PicklistData, PicklistDataModel>();
            var allActiveSubstances = await asCache.WhereAsync(x => x.LifecycleStateId != (int)CommonLifecycleState.Obsolete);
            var allClients = await clientCache.AllAsync();
            var picklists = await picklistCache.AllAsync();

            return (allActiveSubstances, allClients, picklists);
        }
    }
}
