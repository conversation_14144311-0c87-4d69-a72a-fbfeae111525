﻿using Microsoft.AspNetCore.Mvc;
using PharmaLex.Authentication.B2C;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    public class BaseController : Controller
    {
        protected int CurrentUserId => 122;//this.User.GetClaimValue<int>("plx:userid");

        protected void AddConfirmationNotification(string html)
        {
            this.TempData.Set<UserNotificationModel>("UserNotification", new UserNotificationModel(html, type: UserNotificationType.Confirm));
        }
    }
}
