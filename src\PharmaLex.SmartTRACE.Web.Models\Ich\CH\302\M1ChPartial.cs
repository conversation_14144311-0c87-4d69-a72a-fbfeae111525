﻿using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.CH32
{
    public partial class chbackbone : CtdSectionBase, IEctdModule1
    {
        public List<EctdModule1Data> Data => new List<EctdModule1Data>() {
            this.chenvelope?.envelope as EctdModule1Data ?? new EctdModule1Data()
        };

        public string DtdVersion => this.dtdversion;

        public string Region => "Europe (Non-EU)";

        public List<CtdSectionBase> Content => this.ChildNodes ?? new List<CtdSectionBase>();

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>();

                if (this.m1ch != null)
                    nodes.AddRange(this.m1ch);

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class envelope : EctdModule1Data
    {
        
    }

    public partial class chenvelope : CtdSectionBase
    {

    }

    public partial class galenicform : CtdSectionBase
    {

    }

    public partial class galenicname : CtdSectionBase
    {

    }

    public partial class application : CtdSectionBase
    {

    }

    public partial class nodeextension : CtdSectionBase
    {

    }

    public partial class leaf : CtdSectionBase, Ileaf
    {
        public string text
        {
            get
            {
                return this.title;
            }
        }

        public string op
        {
            get
            {
                var op = this.operation.ToString();
                return $"{char.ToUpper(op[0])}{op.Substring(1)}";
            }
        }

        public string Submission { get ; set; }
        public string Path { get; set; }
    }

    public partial class linktext : CtdSectionBase
    {

    }

    public partial class xref : CtdSectionBase
    {

    }

    public partial class m1ch : CtdSectionBase
    {
        
    }

    public partial class m1galenicform : CtdSectionBase
    {
        public override string CtdName => $"Galenic Form: { this.name }";
        public override string CtdSection => ""; 

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    this.m10cover,
                    this.m12applvar,
                    this.m13pi,
                    this.m14expert,
                    this.m15bioavailability,
                    this.m16environrisk,
                    this.m17decisionsauthorities,
                    this.m18pharmacovigilance,
                    this.m19fasttrackdecision,
                    this.m110paediatrics,
                    this.m111orphandrug,
                    this.m112art14sec1letabisquater,
                    this.m1swissresponses,
                    this.m1additionalinfo
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m10cover : CtdSectionBase
    {
        public override string CtdName => "Cover Letter";        
    }

    public partial class m12applvar : CtdSectionBase
    {
        public override string CtdName => "Application for Marketing Authorisation and Variation";

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>()
                {
                    m121foapplvar,
                    m122formadd,
                    m123quality,
                    m124manufacturing,
                    m125others
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m121foapplvar : CtdSectionBase
    {
        public override string CtdName => "Form - Application";
    }

    public partial class m122formadd : CtdSectionBase
    {
        public override string CtdName => "Form - Additional";

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>()
                {
                    this.m1221formfulldeclaration,
                    this.m1222formmanufacturerinformation,
                    this.m1223formstatusmarketingauthorisationsabroad,
                    this.m1224formvariationrequiringnotification,
                    this.m1225formqualityvariationrequiringapproval,
                    this.m1226formapplicationforextensionofauthorisation,
                    this.m1227formhumanbloodcomponents,
                    this.m1228formsubstancesofanimalorhumanorigin,
                    this.m1229formpharmaceuticalinformationforparenteralpreparations,
                    this.m12210formcomarketingconfirmation,
                    this.m12211formimportaccordingtoparagraph14section2tpa,
                    this.m12212formsafetychangestoproductinformation,
                    this.m12213formchangeofmarketingauthorisationholder,
                    this.m12214clformalcontrol,
                    this.m12215clformalcontrol13,
                    this.m12216formpsurforhumanmedicines,
                    this.m12217formdeclarationradiopharmaceuticals,
                    this.m12218formconfirmationsubstancesfromgmo,
                    this.m12219formdmf,
                    this.m12220forminformationapplicationsart13tpa,
                    this.m12221formnotificationsamplepackages,
                    this.m12222formnotificationofnomarketingorinterruptiontodistribution,
                    this.m12223formapplicationforrecognitionoforphandrugstatus,
                    this.m12224applicationforrecognitionoffasttrackstatus,
                    this.m12225formpip,
                    this.m12226gcpinspections,
                    this.m12299otherforms
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m1221formfulldeclaration : CtdSectionBase
    {
        public override string CtdName => "Form Full Declaration";
    }

    public partial class m1222formmanufacturerinformation : CtdSectionBase
    {
        public override string CtdName => "Form Manufacturer Information";
    }

    public partial class m1223formstatusmarketingauthorisationsabroad : CtdSectionBase
    {
        public override string CtdName => "Form Status Marketing Authorisations Abroad";
    }

    public partial class m1224formvariationrequiringnotification : CtdSectionBase
    {

    }

    public partial class m1225formqualityvariationrequiringapproval : CtdSectionBase
    {

    }

    public partial class m1226formapplicationforextensionofauthorisation : CtdSectionBase
    {

    }

    public partial class m1227formhumanbloodcomponents : CtdSectionBase
    {

    }

    public partial class m1228formsubstancesofanimalorhumanorigin : CtdSectionBase
    {
        public override string CtdName => "Form Substances of Animal or Human Origin";
    }

    public partial class m1229formpharmaceuticalinformationforparenteralpreparations : CtdSectionBase
    {

    }

    public partial class m12210formcomarketingconfirmation : CtdSectionBase
    {

    }

    public partial class m12211formimportaccordingtoparagraph14section2tpa : CtdSectionBase
    {

    }

    public partial class m12212formsafetychangestoproductinformation : CtdSectionBase
    {

    }

    public partial class m12213formchangeofmarketingauthorisationholder : CtdSectionBase
    {
        public override string CtdName => "Form Change of Marketing Authorisation Holder";
    }

    public partial class m12214clformalcontrol : CtdSectionBase
    {

    }

    public partial class m12215clformalcontrol13 : CtdSectionBase
    {

    }

    public partial class m12216formpsurforhumanmedicines : CtdSectionBase
    {
        public override string CtdName => "Form PSUR/PBRER for Human Medicines";
    }

    public partial class m12217formdeclarationradiopharmaceuticals : CtdSectionBase
    {
        public override string CtdName => "Form Declaration Radiopharmaceuticals";
    }

    public partial class m12218formconfirmationsubstancesfromgmo : CtdSectionBase
    {
        public override string CtdName => "Form Confirmation Regarding Substances from GMO";
    }

    public partial class m12219formdmf : CtdSectionBase
    {
        public override string CtdName => "Form DMF";
    }

    public partial class m12220forminformationapplicationsart13tpa : CtdSectionBase
    {
        public override string CtdName => "Form Information Relating to Applications under Art. 13 TPA";
    }

    public partial class m12221formnotificationsamplepackages : CtdSectionBase
    {

    }

    public partial class m12222formnotificationofnomarketingorinterruptiontodistribution : CtdSectionBase
    {

    }

    public partial class m12223formapplicationforrecognitionoforphandrugstatus : CtdSectionBase
    {
        public override string CtdName => "Form Application for Recognition of Orphan Drug Status";
    }

    public partial class m12224applicationforrecognitionoffasttrackstatus : CtdSectionBase
    {

    }

    public partial class m12225formpip : CtdSectionBase
    {
        public override string CtdName => "Form PIP";
    }

    public partial class m12226gcpinspections : CtdSectionBase
    {
        public override string CtdName => "GCP Inspections";
    }

    public partial class m12299otherforms : CtdSectionBase
    {
        public override string CtdName => "Other Forms [extensional sections allowed]";
    }

    public partial class m123quality : CtdSectionBase
    {
        public override string CtdName => "Annexes - Documents on Drug Quality";

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>()
                {
                    m1231dmfletterofaccess,
                    m1232certificateofsuitabilityforactivesubstance,
                    m1233certificateofsuitabilityfortse,
                    m1234emacertificateforplasmamasterfilepmf,
                    m1235emacertificateforvaccineantigenmasterfilevamf
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m1231dmfletterofaccess : CtdSectionBase
    {
        public override string CtdName => "DMF Letter of Access";
    }

    public partial class m1232certificateofsuitabilityforactivesubstance : CtdSectionBase
    {
        public override string CtdName => "Ph. Eur. Certificate of Suitability for Active Substance";
    }

    public partial class m1233certificateofsuitabilityfortse : CtdSectionBase
    {
        public override string CtdName => "Ph. Eur. Certificate of Suitability for TSE";
    }

    public partial class m1234emacertificateforplasmamasterfilepmf : CtdSectionBase
    {
        public override string CtdName => "EMA Certificate for Plasma Master File (PMF)";
    }

    public partial class m1235emacertificateforvaccineantigenmasterfilevamf : CtdSectionBase
    {
        public override string CtdName => "EMA Certificate for Vaccine Antigen Master File (VAMF)";
    }

    public partial class m124manufacturing : CtdSectionBase
    {
        public override string CtdName => "Annexes - Manufacturing"; 

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>()
                {
                   m1241gmpcertificateorothergmpdocuments,
                   m1242manufacturingauthorisation,
                   m1243completemanufacturinginformationwithflowchart,
                   m1244confirmationongmpconformity
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m1241gmpcertificateorothergmpdocuments : CtdSectionBase
    {
        public override string CtdName => "GMP Certificate or Other GMP Documents";
    }

    public partial class m1242manufacturingauthorisation : CtdSectionBase
    {
        public override string CtdName => "Documentation Concerning Manufacturing Authorisation";
    }

    public partial class m1243completemanufacturinginformationwithflowchart : CtdSectionBase
    {
        public override string CtdName => "Complete Manufacturing Information with Flow Chart";
    }

    public partial class m1244confirmationongmpconformity : CtdSectionBase
    {
        public override string CtdName => "Confirmation on GMP Conformity";
    }

    public partial class m125others : CtdSectionBase
    {
        public override string CtdName => "Annexes – Others";

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    m1251comparisonofapprovedproductinformation,
                    m1252companycoredatasheet
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m1251comparisonofapprovedproductinformation : CtdSectionBase
    {
        public override string CtdName => "Comparison of Approved Information for Professionals with EU SmPC (for PSURs)";
    }

    public partial class m1252companycoredatasheet : CtdSectionBase
    {
        public override string CtdName => "Company Core Data Sheet (for PSURs)";
    }

    public partial class m13pi : CtdSectionBase
    {
        public override string CtdName => "Product Information and Packaging Material";

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    m131professionals,
                    m132patient,
                    m133packaging,
                    m134professionalsothercountries
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m131professionals : CtdSectionBase
    {
        public override string CtdName => "Information for Professionals";
    }

    public partial class m132patient : CtdSectionBase
    {
        public override string CtdName => "Patient Information";
    }

    public partial class m133packaging : CtdSectionBase
    {
        public override string CtdName => "Packaging Information";
    }

    public partial class m134professionalsothercountries : CtdSectionBase
    {
        public override string CtdName => "Information for Professionals from Other Countries";
    }

    public partial class m14expert : CtdSectionBase
    {
        public override string CtdName => "Information about the Expert";

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    m141quality,
                    m142nonclinical,
                    m143clinical
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m141quality : CtdSectionBase
    {
        
    }

    public partial class m142nonclinical : CtdSectionBase
    {

    }

    public partial class m143clinical : CtdSectionBase
    {

    }

    public partial class m15bioavailability : CtdSectionBase
    {
        public override string CtdName => "Data of Bioavailability Studies (Known Active Substance without Innovation)";

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    m151infoaccordappivguidelinebioequivalence,
                    m152referenceproduct,
                    m153confirmationidentitybioequivalence,
                    m154art14tabcompare
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m151infoaccordappivguidelinebioequivalence : CtdSectionBase
    {
        public override string CtdName => "Information according to Appendix IV of the Guideline on the Investigation on Bioequivalence";
    }

    public partial class m152referenceproduct : CtdSectionBase
    {
        public override string CtdName => "Documents on the Reference Product";
    }

    public partial class m153confirmationidentitybioequivalence : CtdSectionBase
    {

    }

    public partial class m154art14tabcompare : CtdSectionBase
    {

    }

    public partial class m16environrisk : CtdSectionBase
    {
        public override string CtdName => "Environmental Risk Assessment";
    }

    public partial class m161nongmo : CtdSectionBase
    {
        public override string CtdName => "Non-GMO";
    }

    public partial class m162gmo : CtdSectionBase
    {
        public override string CtdName => "GMO";
    }

    public partial class m17decisionsauthorities : CtdSectionBase
    {
        public override string CtdName => "Decisions of Foreign Authorities";

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>
                {
                    m171responses,
                    m172assessment,
                    m173eudecisions,
                    m174fdadecision,
                    m175foreigndecisions,
                    m176article13adddoc
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m171responses : CtdSectionBase
    {
        public override string CtdName => "Responses to LoQ";
    }

    public partial class m172assessment : CtdSectionBase
    {
        public override string CtdName => "Assessment Report";
    }

    public partial class m173eudecisions : CtdSectionBase
    {
        public override string CtdName => "EU Decision";
    }

    public partial class m174fdadecision : CtdSectionBase
    {
        public override string CtdName => "FDA Decision";
    }

    public partial class m175foreigndecisions : CtdSectionBase
    {
        public override string CtdName => "Decisions of Other Foreign Authorities";
    }

    public partial class m176article13adddoc : CtdSectionBase
    {
        public override string CtdName => "Article 13 TPA Additional Documentation";
    }

    public partial class m18pharmacovigilance : CtdSectionBase
    {
        public override string CtdName => "Information relating to Pharmacovigilance";

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>()
                {
                    m181pharmacovigilancesystem,
                    m182riskmanagementsystem
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m181pharmacovigilancesystem : CtdSectionBase
    {
        public override string CtdName => "Pharmacovigilance System";
    }

    public partial class m182riskmanagementsystem : CtdSectionBase
    {
        public override string CtdName => "Risk-Management System";
    }

    public partial class m19fasttrackdecision : CtdSectionBase
    {
        public override string CtdName => "Fast Track Status Decision";
    }

    public partial class m110paediatrics : CtdSectionBase
    {
        public override string CtdName => "Information Relating to Paediatrics";
    }

    public partial class m111orphandrug : CtdSectionBase
    {
        public override string CtdName => "Orphan Drug Status Decision";
    }

    public partial class m112art14sec1letabisquater : CtdSectionBase
    {
        public override string CtdName => "Art 14 Sec 1 Let abis-quater TPA Documents";

        public override List<CtdSectionBase> ChildNodes
        {
            get
            {
                var nodes = new List<CtdSectionBase>()
                {
                    m1121eueftaauthorisation,
                    m1122eueftadocreference,
                    m1123overallmedicaluse,
                    m1124cantonalauthorisation
                };

                return nodes.Where(x => x != null).ToList();
            }
        }
    }

    public partial class m1121eueftaauthorisation : CtdSectionBase
    {
        public override string CtdName => "Proof of 10 Years EU/EFTA Authorisation";
    }

    public partial class m1122eueftadocreference : CtdSectionBase
    {

    }

    public partial class m1123overallmedicaluse : CtdSectionBase
    {
        public override string CtdName => "Proof of 30 Years Overall Medical Use - 15 Years Medical Use EU/EFTA";
    }

    public partial class m1124cantonalauthorisation : CtdSectionBase
    {
        public override string CtdName => "Proof of 15 Years Cantonal Authorisation";
    }

    public partial class m1swissresponses : CtdSectionBase
    {
        public override string CtdName => "Responses to Swissmedic LoQ";
        public override string CtdSection => string.Empty;
    }

    public partial class m1additionalinfo : CtdSectionBase
    {
        public override string CtdName => "Additional Information";
        public override string CtdSection => string.Empty; 
    }
}
