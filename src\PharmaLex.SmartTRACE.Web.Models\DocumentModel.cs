﻿using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using System;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class DocumentModel: IModel
    {
        public int Id { get; set; }
        public string Name { get; set; }

        public int DocumentTypeId { get; set; }
        public string DocumentType { get; set; }

        public int Version { get; set; }

        public string CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int FilesCount { get; set; }
        public int SubmissionId { get; set; }
        public bool ShowUploadInfo { get; set; } = true;
    }

    public class DocumentTypeList : NamedEntityModel
    {
    }

    public class DocumentMappingProfile : Profile
    {
        public DocumentMappingProfile()
        {
            this.CreateMap<Document, DocumentModel>()
                 .ForMember(d => d.DocumentType, s => s.MapFrom(x => (DocumentType)x.DocumentTypeId));
            this.CreateMap<DocumentModel, Document>();
            this.CreateMap<DocumentType, DocumentTypeList>()
                 .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
                 .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));
        }
    }
}
