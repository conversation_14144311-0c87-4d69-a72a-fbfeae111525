﻿using NSubstitute;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Security.Claims;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Export.Applications
{
    public class ApplicationExportTests
    {
        private readonly IApplicationService applicationService;

        public ApplicationExportTests()
        {
            applicationService = Substitute.For<IApplicationService>();
        }
        [Fact]
        public async Task Export_NoApp_ReturnsOnlyHeaderRow()
        {
            // Arrange
            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
          {
                 new System.Security.Claims.Claim(ClaimTypes.Name, "testuser"),
                 new System.Security.Claims.Claim(ClaimTypes.Role, "Reader")
          }));
            ApplicationFilterModel model = new ApplicationFilterModel()
            { ApplicationNumber = "001", ClientName = "testclient", Country = "TestCountry", Product = "prodname", Region = "reg" };
            ApplicationExport appExport = new Helpers.ApplicationExport(applicationService);
            IList<ApplicationViewModel> viewModel = new List<ApplicationViewModel>();
            applicationService.ListApplications(user, model).Returns(viewModel);
            // Act
            var result = await appExport.Export(user, model);

            Assert.NotNull(result);
            // Assert

        }
        [Fact]
        public async Task Export_App_ReturnsRows()
        {
            // Arrange
            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
          {
                 new System.Security.Claims.Claim(ClaimTypes.Name, "testuser"),
                 new System.Security.Claims.Claim(ClaimTypes.Role, "Reader")
          }));
            ApplicationFilterModel model = new ApplicationFilterModel()
            { ApplicationNumber = "001", ClientName = "testclient", Country = "TestCountry", Product = "prodname", Region = "reg" };
            ApplicationExport appExport = new Helpers.ApplicationExport(applicationService);
            IList<ApplicationViewModel> viewModel = new List<ApplicationViewModel>()
            {  new ApplicationViewModel(){  ApplicationNumber="001", Product="prodname" , Region="reg",
                ClientName = "testclient", Country = "TestCountry",} };
            applicationService.ListApplications(user, model).Returns(viewModel);
            // Act
            var result = await appExport.Export(user, model);

            Assert.NotNull(result);
            // Assert

        }
    }
}
