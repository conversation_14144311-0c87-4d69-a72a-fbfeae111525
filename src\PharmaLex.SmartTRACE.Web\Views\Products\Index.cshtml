﻿@model IEnumerable<ProductModel>
@using PharmaLex.Caching.Data
@using PharmaLex.SmartTRACE.Entities
@using PharmaLex.SmartTRACE.Entities.Enums
@inject IDistributedCacheServiceFactory cache
@inject AutoMapper.IMapper mapper

@{
    var dosageForms = await this.cache.CreateMappedEntity<PicklistData, PicklistDataModel>()
                                                     .WhereAsync(x => x.PicklistTypeId == (int)PicklistType.DosageForm);
    var lifecycleStates = mapper.Map<IEnumerable<ApplicationLifecycleStateList>>(Enum.GetValues(typeof(CommonLifecycleState)));
}

<div id="products" class="manage-container">
    <header class="manage-header">
        <h2>Manage Products</h2>
        <a id="exportButton" class="button icon-button-download">Export</a>
        <a class="button icon-button-add" href="/products/new">Add product</a>
    </header>
    <filtered-table :items="products" :columns="columns" :filters="filters" :link="link"></filtered-table>
    <form id="exportForm" style="display: none;" method="post" action="/products/export">
        @Html.AntiForgeryToken()
    </form>
</div>

@section Scripts {
    <script type="text/javascript">

    var pageConfig = {
        appElement: '#products',
        data() {
            return {
                link: '/products/edit/',
                products: @Html.Raw(Model.ToJson()),
                columns: {
                    idKey: 'id',
                    styleKey: 'lifecycleState',
                    config: [
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Name',
                            type: 'text'
                        },
                        {
                            dataKey: 'clientName',
                            sortKey: 'clientName',
                            header: 'Client Name',
                            type: 'text'
                        },
                        {
                            dataKey: 'dosageForm',
                            sortKey: 'dosageForm',
                            header: 'Dosage Form',
                            type: 'text'
                        },
                        {
                            dataKey: 'strength',
                            sortKey: 'strength',
                            header: 'Strength',
                            type: 'text'
                        },
                        {
                            dataKey: 'lifecycleState',
                            sortKey: 'lifecycleState',
                            header: 'Lifecycle State',
                            type: 'text'
                        },
                    ]
                },
                filters: [
                    {
                        key: 'name',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'clientName',
                        options: [],
                        type: 'search',
                        header: 'Search Name',
                        fn: v => p => p.clientName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'dosageForm',
                        options: @Html.Raw(dosageForms.ToJson()),
                        filterCollection: 'dosageForm',
                        display: 'id',
                        type: 'select',
                        header: 'Filter By Dosage Form',
                        fn: v => p => p.dosageForm === v,
                        convert: v => v
                    },
                    {
                        key: 'strength',
                        options: [],
                        type: 'search',
                        header: 'Search Dosage Form',
                        fn: v => p => p.strength.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'lifecycleState',
                        options: @Html.Raw(lifecycleStates.ToJson()),
                        filterCollection: 'lifecycleState',
                        display: 'id',
                        type: 'select',
                        header: 'Filter By State',
                        fn: v => p => p.lifecycleState === v,
                        convert: v => v
                    }
                ]
            };
        },
        mounted() {
            document.getElementById('exportButton').addEventListener('click', (e) => {
                e.preventDefault();
                document.getElementById('exportForm').submit();
            });
        }
    };
    </script>
}

@section VueComponentScripts {
    <partial name="Components/FilteredTable" />
}