﻿using PharmaLex.SmartTRACE.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.WebTests.Models
{
    public class UserNotificationModelTests
    {
        [Fact]
        public void GetUserNotificationModel()
        {
            //Arrange
            UserNotificationModel userNotificationModel = new UserNotificationModel("html", UserNotificationPosition.BottomLeft, UserNotificationType.Info,2500,45)
            {
                Type = "",
                Duration = 34,
                Html = "html",
                Number = 3,
                Position = 22
            };

            //Assert
            Assert.NotNull(userNotificationModel);
        }
    }
}
