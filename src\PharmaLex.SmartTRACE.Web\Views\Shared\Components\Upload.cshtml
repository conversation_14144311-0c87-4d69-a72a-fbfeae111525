﻿<script type="text/x-template" id="upload-template">
    <div>
        <div id="drop-zone"></div>
        <div id="upload-progress"></div>
    </div>
</script>

<script type="text/javascript">
    vueApp.component('upload', {
        template: '#upload-template',
        data() {
            return {
                uploadLink: '',
                unpackLink: '',
                versionLink: '',
                file: {},
                headers: [],
            }
        },
        props: {
            config: {
                type: Object,
                required: false
            }
        },
        methods: {
            removeFile() {
                uppy.removeFile(this.file.id);
            },
            initialize() {
                let uppy = Uppy.Core({
                    debug: true,
                    autoProceed: false,
                    restrictions: {
                        maxNumberOfFiles: 1,
                        maxFileSize: 4096000000,
                        allowedFileTypes: ['application/zip', 'application/x-zip-compressed']
                    }
                });
                uppy.use(Uppy.DragDrop, {
                    target: '#drop-zone',
                    height: this.config.height,
                    note: ' - single Zip archive (.zip) file'
                })
                    .use(Uppy.ProgressBar, {
                        target: '#upload-progress',
                        hideAfterFinish: false
                    });

                uppy.on('file-added', (file) => {
                    this.$emit('uploading', true);
                    
                    let headers = {
                        'x-ms-blob-type': 'BlockBlob',
                        'x-ms-version': '2019-12-12',
                    };
                    if (this.config && this.config.unpacked !== undefined && this.config.unpacked !== null) {
                        headers['x-ms-meta-unpacked'] = this.config.unpacked;
                    }                 

                    this.file = file;
                    this.headers = headers;

                    if (this.config && this.config.versionLink) {
                        this.versionLink = this.config.versionLink.replace('{fileName}', file.name);
                        fetch(this.versionLink, {
                            method: 'GET',
                            credentials: 'same-origin',
                            headers: {
                                "Content-Type": "application/json"
                            }
                        }).then(r => r.json())
                          .then(version =>
                          {
                              this.$emit('latest-version', { version: version, fileName: file.name });
                          })
                          .catch(error => {
                              console.log(error);
                          });
                    } else {
                        this.uploadFile();
                    }              
                });
            },
            uploadFile() {
                this.uploadLink = this.config.uploadLink.replace('{fileName}', this.file.name);

                fetch(this.uploadLink, {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        "Content-Type": "application/json"
                    }
                }).then(r => r.json())
                    .then(link => {
                        uppy.use(Uppy.XHRUpload, {
                            endpoint: link,
                            method: 'PUT',
                            formData: false,
                            fieldName: 'file',
                            timeout: 0,
                            headers: this.headers
                        });

                        uppy.upload().then(result => {
                            uppy.removeFile(this.file.id);
                            const plugin = uppy.getPlugin('XHRUpload');
                            if (plugin) uppy.removePlugin(plugin);

                            this.unpackLink = this.config.unpackLink.replace('{fileName}', this.file.name);

                            if (result.successful.length) {
                                fetch(this.unpackLink, {
                                    method: 'POST',
                                    credentials: 'same-origin',
                                    headers: {
                                        "Content-Type": "application/json",
                                        'RequestVerificationToken': token
                                    }
                                }).then(r => r.json())
                                    .then(success => {
                                        if (success) {
                                            this.$emit('file-added', this.file.name);
                                        } else plx.toast.show(`Could not unpack file`, 2, 'failed');
                                    })
                                    .catch(_ => {
                                        plx.toast.show(`Could not unpack file`, 2, 'failed');
                                    });

                                return;
                            }

                            plx.toast.show(`Sequence upload failed`, 2, 'failed');
                        });
                    })
                    .catch(error => {
                        console.log(error);
                    });
            }
        },
        mounted() {
            this.initialize();
        }
    });
</script>