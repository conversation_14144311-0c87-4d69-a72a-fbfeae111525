﻿using NPOI.SS.UserModel;
using PharmaLex.Office;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using NPOI.SS.Util;
using System.Security.Claims;
using PharmaLex.SmartTRACE.Web.Models;
using System;
using PharmaLex.SmartTRACE.Web.Helpers.Constants;

namespace PharmaLex.SmartTRACE.Web.Helpers;

public class ApplicationExport : ExcelWriter, IApplicationExport
{
    private readonly IApplicationService applicationService;

    public ApplicationExport(IApplicationService applicationService)
    {
        this.applicationService = applicationService;
    }

    public async Task<byte[]> Export(ClaimsPrincipal user, ApplicationFilterModel model)
    {
        // Create a new workbook
        var wb = this.CreateWorkbook();
        var exSheet = wb.Workbook.CreateSheet("Applications");

        // Get all applications
        var allApplications = await this.applicationService.ListApplications(user, model);

        // Apply filters
        allApplications = ApplyFilters(allApplications, model);

        // Define column names
        var columnNames = new[]
        {
            ApplicationsExportConstants.REGION, ApplicationsExportConstants.COUNTRY, ApplicationsExportConstants.CLIENT,
            ApplicationsExportConstants.MEDICINAL_PRODUCT_DOMAIN, ApplicationsExportConstants.PRODUCT_NAME_DOSAGE_FORM_STRENGTH,
            ApplicationsExportConstants.PROCEDURE_TYPE, ApplicationsExportConstants.APPLICATION_TYPE, ApplicationsExportConstants.APPLICATION_NUMBER,
            ApplicationsExportConstants.COMMENTS, ApplicationsExportConstants.LIFECYCLE_STATE
        };

        // Create header row
        exSheet.CreateRow(0, wb.Styles["header"], columnNames);

        // Create rows for applications
        var rowIndex = 1;
        foreach (var app in allApplications)
        {
            var applicationProps = GetApplicationProperties(app);
            var row = exSheet.CreateRow(rowIndex++);
            for (int i = 0; i < applicationProps.Length; i++)
            {
                row.CreateCell(i, applicationProps[i], wb.Styles["wrapped"]);
            }
        }

        // Format the sheet
        FormatSheet(exSheet, columnNames);

        return wb.Workbook.ToByteArray();
    }

    private static List<ApplicationViewModel> ApplyFilters(IEnumerable<ApplicationViewModel> applications, ApplicationFilterModel model)
    {
        var filteredApplications = applications;

        if (!string.IsNullOrEmpty(model.Product))
        {
            filteredApplications = filteredApplications.Where(x => x.Product.Contains(model.Product));
        }

        if (!string.IsNullOrEmpty(model.Country))
        {
            filteredApplications = filteredApplications.Where(x => x.Country.Contains(model.Country, StringComparison.InvariantCultureIgnoreCase));
        }

        if (!string.IsNullOrEmpty(model.Region))
        {
            filteredApplications = filteredApplications.Where(x => x.Region.Contains(model.Region, StringComparison.InvariantCultureIgnoreCase));
        }

        return filteredApplications.ToList();
    }

    private static string[] GetApplicationProperties(ApplicationViewModel app)
    {
        return
        [
            app.Region,
            app.Country,
            app.ClientName,
            app.MedicinalProductDomain,
            app.Product,
            app.ProcedureType,
            app.ApplicationType,
            app.ApplicationNumber,
            app.Comments,
            app.LifecycleState
        ];
    }

    private static void FormatSheet(ISheet sheet, string[] columnNames)
    {
        sheet.AutoSizeColumns(0, columnNames.Length);
        sheet.SetAutoFilter(new CellRangeAddress(0, 0, 0, columnNames.Length - 1));
        sheet.SetColumnWidth(Array.IndexOf(columnNames, "Comments"), 20 * 1000);
    }


}
