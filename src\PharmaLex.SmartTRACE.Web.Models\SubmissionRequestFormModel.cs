﻿using AutoMapper;
using PharmaLex.SmartTRACE.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.SmartTRACE.Web.Models
{
    public class SubmissionRequestFormModel: IModel
    {
        public int? DeliveryDetailsId { get; set; }
        public string ClientName { get; set; }
        [Required]
        public DateTime PlannedSubmissionDate { get; set; }
        [Required]
        public DateTime PlannedDispatchDate { get; set; }
        public DateTime? HealthAuthorityDueDate { get; set; }
        public IList<int> CountryIds { get; set; }
        public string ApplicationType { get; set; }
        [Required]
        public int SubmissionTypeId { get; set; }
        public int? SubmissionUnitId { get; set; }
        public int? SubmissionModeId { get; set; }
        [Required]
        public string RegulatoryLead { get; set; }
        public string PublishingLead { get; set; }
        public string Publisher { get; set; }
        public string Description { get; set; }
        public string Comments { get; set; }
        public string ApplicationNumber { get; set; }
        public string OpportunityNumber { get; set; }
        [MaxLength(10)]
        public string SerialNumber { get; set; }
        [MaxLength(10)]
        public string SequenceNumber { get; set; }
        public int? PriorityId { get; set; }
        public int? EstimatedSizeId { get; set; }
        public string EstimatedSize { get; set; }
        public string ProcedureType { get; set; }
        public int ApplicationId { get; set; }
        public int ProjectId { get; set; }
        public bool SendNotification { get; set; } = true;
        public string InitialSentToEmail { get; set; }
    }

    public class SubmissionRequestMappingProfile : Profile
    {
        public SubmissionRequestMappingProfile()
        {
            this.CreateMap<SubmissionRequestFormModel, Submission>().ReverseMap();
        }
    }
}
