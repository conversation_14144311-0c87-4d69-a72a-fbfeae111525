﻿using System;
using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.eCTD32.US32
{
    [Serializable]
    public partial class leaf
    {
        public leaf()
        {
            this.type = "simple";
        }
        public title title { get; set; }
        [XmlElement("link-text")]
        public linktext linktext { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        [XmlAttribute("application-version")]
        public string applicationversion { get; set; }
        [XmlAttribute()]
        public string version { get; set; }
        [XmlAttribute("font-library")]
        public string fontlibrary { get; set; }
        [XmlAttribute()]
        public leafOperation operation { get; set; }
        [XmlAttribute("modified-file")]
        public string modifiedfile { get; set; }
        [XmlAttribute()]
        public string checksum { get; set; }
        [XmlAttribute("checksum-type")]
        public string checksumtype { get; set; }
        [XmlAttribute()]
        public string keywords { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string type { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string role { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string href { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefShow show { get; set; }
        [XmlIgnore]
        public bool showSpecified { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefActuate actuate { get; set; }
        [XmlIgnore]
        public bool actuateSpecified { get; set; }
        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class title
    {
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        [XmlText]
        public string Value { get; set; }
    }

    [Serializable]
    [XmlRoot("link-text", Namespace = "", IsNullable = false)]
    public partial class linktext
    {
        [XmlElement("xref")]
        public xref[] Items { get; set; }
        [XmlText]
        public string[] Text { get; set; }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class xref
    {
        public xref()
        {
            type = "simple";
        }
        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string type { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string role { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string title { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public string href { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefShow show { get; set; }
        [XmlIgnore]
        public bool showSpecified { get; set; }
        [XmlAttribute(Namespace = "http://www.w3c.org/1999/xlink")]
        public xrefActuate actuate { get; set; }
        [XmlIgnore]
        public bool actuateSpecified { get; set; }
    }

    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3c.org/1999/xlink")]
    public enum xrefShow
    {
        @new,
        replace,
        embed,
        other,
        none,
    }

    [Serializable]
    [XmlType(AnonymousType = true, Namespace = "http://www.w3c.org/1999/xlink")]
    public enum xrefActuate
    {
        onLoad,
        onRequest,
        other,
        none,
    }

    [Serializable]
    public enum leafOperation
    {
        @new,
        append,
        replace,
        delete,
    }

    [Serializable]
    [XmlRoot("node-extension", Namespace = "", IsNullable = false)]
    public partial class nodeextension
    {
        public title title { get; set; }

        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class admin
    {
        [XmlElement("applicant-info")]
        public applicantinfo applicantinfo { get; set; }

        [XmlElement("product-description")]
        public productdescription productdescription { get; set; }

        [XmlElement("application-information")]
        public applicationinformation applicationinformation { get; set; }
    }

    [Serializable]
    [XmlRoot("applicant-info", Namespace = "", IsNullable = false)]
    public partial class applicantinfo
    {
        [XmlElement("company-name")]
        public string companyname { get; set; }

        [XmlElement("date-of-submission")]
        public dateofsubmission dateofsubmission { get; set; }
    }

    [Serializable]
    [XmlRoot("date-of-submission", Namespace = "", IsNullable = false)]
    public partial class dateofsubmission
    {
        public date date { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class date
    {
        [XmlAttribute()]
        public dateFormat format { get; set; }

        [XmlText]
        public string Value { get; set; }
    }

    [Serializable]
    public enum dateFormat
    {
        yyyymmdd,
    }

    [Serializable]
    [XmlRoot("product-description", Namespace = "", IsNullable = false)]
    public partial class productdescription
    {
        [XmlElement("application-number")]
        public string applicationnumber { get; set; }

        [XmlElement("prod-name")]
        public prodname[] prodname { get; set; }
    }

    [Serializable]
    [XmlRoot("prod-name", Namespace = "", IsNullable = false)]
    public partial class prodname
    {
        [XmlAttribute()]
        public prodnameType type { get; set; }

        [XmlText]
        public string Value { get; set; }
    }

    [Serializable]
    public enum prodnameType
    {
        established,
        proprietary,
        chemical,
        code,
    }

    [Serializable]
    [XmlRoot("application-information", Namespace = "", IsNullable = false)]
    public partial class applicationinformation
    {
        public submission submission { get; set; }

        [XmlAttribute("application-type")]
        public applicationinformationApplicationtype applicationtype { get; set; }
    }

    [Serializable]
    [XmlRoot(Namespace = "", IsNullable = false)]
    public partial class submission
    {
        [XmlElement("sequence-number")]
        public string sequencenumber { get; set; }

        [XmlElement("related-sequence-number")]
        public string[] relatedsequencenumber { get; set; }

        [XmlAttribute("submission-type")]
        public submissionSubmissiontype submissiontype { get; set; }
    }

    [Serializable]
    public enum submissionSubmissiontype
    {
        [XmlEnum("original-application")]
        originalapplication,
        amendment,
        resubmission,
        presubmission,
        [XmlEnum("annual-report")]
        annualreport,
        [XmlEnum("establishment-description-supplement")]
        establishmentdescriptionsupplement,
        [XmlEnum("efficacy-supplement")]
        efficacysupplement,
        [XmlEnum("labeling-supplement")]
        labelingsupplement,
        [XmlEnum("chemistry-manufacturing-controls-supplement")]
        chemistrymanufacturingcontrolssupplement,
        other,
    }

    [Serializable]
    public enum applicationinformationApplicationtype
    {
        nda,
        anda,
        bla,
        dmf,
        ind,
        [XmlEnum("master-file")]
        masterfile,
    }

    [Serializable]
    [XmlRoot("m1-regional", Namespace = "", IsNullable = false)]
    public partial class m1regional
    {
        [XmlElement("m1-1-forms")]
        public m11forms m11forms { get; set; }

        [XmlElement("m1-2-cover-letters")]
        public m12coverletters m12coverletters { get; set; }

        [XmlElement("m1-3-administrative-information")]
        public m13administrativeinformation m13administrativeinformation { get; set; }

        [XmlElement("m1-4-references")]
        public m14references m14references { get; set; }

        [XmlElement("m1-5-application-status")]
        public m15applicationstatus m15applicationstatus { get; set; }

        [XmlElement("m1-6-meetings")]
        public m16meetings m16meetings { get; set; }

        [XmlElement("m1-7-fast-track")]
        public m17fasttrack m17fasttrack { get; set; }

        [XmlElement("m1-8-special-protocol-assessment-request")]
        public m18specialprotocolassessmentrequest m18specialprotocolassessmentrequest { get; set; }

        [XmlElement("m1-9-pediatric-administrative-information")]
        public m19pediatricadministrativeinformation m19pediatricadministrativeinformation { get; set; }

        [XmlElement("m1-10-dispute-resolution")]
        public m110disputeresolution m110disputeresolution { get; set; }

        [XmlElement("m1-11-information-amendment")]
        public m111informationamendment m111informationamendment { get; set; }

        [XmlElement("m1-12-other-correspondence")]
        public m112othercorrespondence m112othercorrespondence { get; set; }

        [XmlElement("m1-13-annual-report")]
        public m113annualreport m113annualreport { get; set; }

        [XmlElement("m1-14-labeling")]
        public m114labeling m114labeling { get; set; }

        [XmlElement("m1-15-promotional-material")]
        public m115promotionalmaterial m115promotionalmaterial { get; set; }

        [XmlElement("m1-16-risk-management-plans")]
        public m116riskmanagementplans m116riskmanagementplans { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-1-forms", Namespace = "", IsNullable = false)]
    public partial class m11forms
    {
        [XmlElement("m1-1-1-fda-form-1571")]
        public m111fdaform1571 m111fdaform1571 { get; set; }

        [XmlElement("m1-1-2-fda-form-356h")]
        public m112fdaform356h m112fdaform356h { get; set; }

        [XmlElement("m1-1-3-fda-form-3397")]
        public m113fdaform3397 m113fdaform3397 { get; set; }

        [XmlElement("m1-1-4-fda-form-2252")]
        public m114fdaform2252 m114fdaform2252 { get; set; }

        [XmlElement("m1-1-5-fda-form-2253")]
        public m115fdaform2253 m115fdaform2253 { get; set; }

        [XmlElement("m1-1-6-fda-form-2567")]
        public m116fdaform2567 m116fdaform2567 { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-1-1-fda-form-1571", Namespace = "", IsNullable = false)]
    public partial class m111fdaform1571
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-1-2-fda-form-356h", Namespace = "", IsNullable = false)]
    public partial class m112fdaform356h
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-1-3-fda-form-3397", Namespace = "", IsNullable = false)]
    public partial class m113fdaform3397
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-1-4-fda-form-2252", Namespace = "", IsNullable = false)]
    public partial class m114fdaform2252
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-1-5-fda-form-2253", Namespace = "", IsNullable = false)]
    public partial class m115fdaform2253
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-1-6-fda-form-2567", Namespace = "", IsNullable = false)]
    public partial class m116fdaform2567
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-2-cover-letters", Namespace = "", IsNullable = false)]
    public partial class m12coverletters
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-administrative-information", Namespace = "", IsNullable = false)]
    public partial class m13administrativeinformation
    {
        [XmlElement("m1-3-1-applicant-information")]
        public m131applicantinformation[] m131applicantinformation { get; set; }

        [XmlElement("m1-3-2-field-copy-certification")]
        public m132fieldcopycertification[] m132fieldcopycertification { get; set; }

        [XmlElement("m1-3-3-debarment-certification")]
        public m133debarmentcertification[] m133debarmentcertification { get; set; }

        [XmlElement("m1-3-4-financial-certification-disclosure")]
        public m134financialcertificationdisclosure[] m134financialcertificationdisclosure { get; set; }

        [XmlElement("m1-3-5-patent-exclusivity")]
        public m135patentexclusivity[] m135patentexclusivity { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-applicant-information", Namespace = "", IsNullable = false)]
    public partial class m131applicantinformation
    {
        [XmlElement("m1-3-1-1-change-of-address-or-corporate-name")]
        public m1311changeofaddressorcorporatename[] m1311changeofaddressorcorporatename { get; set; }

        [XmlElement("m1-3-1-2-change-contact-agent")]
        public m1312changecontactagent[] m1312changecontactagent { get; set; }

        [XmlElement("m1-3-1-3-change-in-sponsor")]
        public m1313changeinsponsor[] m1313changeinsponsor { get; set; }

        [XmlElement("m1-3-1-4-transfer-obligation")]
        public m1314transferobligation[] m1314transferobligation { get; set; }

        [XmlElement("m1-3-1-5-change-application-ownership")]
        public m1315changeapplicationownership[] m1315changeapplicationownership { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-1-change-of-address-or-corporate-name", Namespace = "", IsNullable = false)]
    public partial class m1311changeofaddressorcorporatename
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-2-change-contact-agent", Namespace = "", IsNullable = false)]
    public partial class m1312changecontactagent
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

    }

    [Serializable]
    [XmlRoot("m1-3-1-3-change-in-sponsor", Namespace = "", IsNullable = false)]
    public partial class m1313changeinsponsor
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-4-transfer-obligation", Namespace = "", IsNullable = false)]
    public partial class m1314transferobligation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-1-5-change-application-ownership", Namespace = "", IsNullable = false)]
    public partial class m1315changeapplicationownership
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-2-field-copy-certification", Namespace = "", IsNullable = false)]
    public partial class m132fieldcopycertification
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-3-debarment-certification", Namespace = "", IsNullable = false)]
    public partial class m133debarmentcertification
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-4-financial-certification-disclosure", Namespace = "", IsNullable = false)]
    public partial class m134financialcertificationdisclosure
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-5-patent-exclusivity", Namespace = "", IsNullable = false)]
    public partial class m135patentexclusivity
    {
        [XmlElement("m1-3-5-1-patent-information")]
        public m1351patentinformation[] m1351patentinformation { get; set; }

        [XmlElement("m1-3-5-2-patent-certification")]
        public m1352patentcertification[] m1352patentcertification { get; set; }

        [XmlElement("m1-3-5-3-exclusivity-request")]
        public m1353exclusivityrequest[] m1353exclusivityrequest { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-5-1-patent-information", Namespace = "", IsNullable = false)]
    public partial class m1351patentinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-5-2-patent-certification", Namespace = "", IsNullable = false)]
    public partial class m1352patentcertification
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-3-5-3-exclusivity-request", Namespace = "", IsNullable = false)]
    public partial class m1353exclusivityrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-references", Namespace = "", IsNullable = false)]
    public partial class m14references
    {
        [XmlElement("m1-4-1-letter-authorization")]
        public m141letterauthorization[] m141letterauthorization { get; set; }

        [XmlElement("m1-4-2-statement-right-reference")]
        public m142statementrightreference[] m142statementrightreference { get; set; }

        [XmlElement("m1-4-3-list-of-authorized-persons-to-incorporate-by-reference")]
        public m143listofauthorizedpersonstoincorporatebyreference[] m143listofauthorizedpersonstoincorporatebyreference { get; set; }

        [XmlElement("m1-4-4-cross-reference-other-applications")]
        public m144crossreferenceotherapplications[] m144crossreferenceotherapplications { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-1-letter-authorization", Namespace = "", IsNullable = false)]
    public partial class m141letterauthorization
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-2-statement-right-reference", Namespace = "", IsNullable = false)]
    public partial class m142statementrightreference
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-3-list-of-authorized-persons-to-incorporate-by-reference", Namespace = "", IsNullable = false)]
    public partial class m143listofauthorizedpersonstoincorporatebyreference
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-4-4-cross-reference-other-applications", Namespace = "", IsNullable = false)]
    public partial class m144crossreferenceotherapplications
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-application-status", Namespace = "", IsNullable = false)]
    public partial class m15applicationstatus
    {
        [XmlElement("m1-5-1-withdrawal-request")]
        public m151withdrawalrequest[] m151withdrawalrequest { get; set; }

        [XmlElement("m1-5-2-inactivation-request")]
        public m152inactivationrequest[] m152inactivationrequest { get; set; }

        [XmlElement("m1-5-3-reactivation-request")]
        public m153reactivationrequest[] m153reactivationrequest { get; set; }

        [XmlElement("m1-5-4-reinstatement-request")]
        public m154reinstatementrequest[] m154reinstatementrequest { get; set; }

        [XmlElement("m1-5-5-withdrawal-unapproved-nda")]
        public m155withdrawalunapprovednda[] m155withdrawalunapprovednda { get; set; }

        [XmlElement("m1-5-6-withdrawal-of-listed-drug")]
        public m156withdrawaloflisteddrug[] m156withdrawaloflisteddrug { get; set; }

        [XmlElement("m1-5-7-request-withdrawal-application-approval")]
        public m157requestwithdrawalapplicationapproval[] m157requestwithdrawalapplicationapproval { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-1-withdrawal-request", Namespace = "", IsNullable = false)]
    public partial class m151withdrawalrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-2-inactivation-request", Namespace = "", IsNullable = false)]
    public partial class m152inactivationrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-3-reactivation-request", Namespace = "", IsNullable = false)]
    public partial class m153reactivationrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-4-reinstatement-request", Namespace = "", IsNullable = false)]
    public partial class m154reinstatementrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-5-withdrawal-unapproved-nda", Namespace = "", IsNullable = false)]
    public partial class m155withdrawalunapprovednda
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-6-withdrawal-of-listed-drug", Namespace = "", IsNullable = false)]
    public partial class m156withdrawaloflisteddrug
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-5-7-request-withdrawal-application-approval", Namespace = "", IsNullable = false)]
    public partial class m157requestwithdrawalapplicationapproval
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-meetings", Namespace = "", IsNullable = false)]
    public partial class m16meetings
    {
        [XmlElement("m1-6-1-meeting-request")]
        public m161meetingrequest[] m161meetingrequest { get; set; }

        [XmlElement("m1-6-2-meeting-background-materials")]
        public m162meetingbackgroundmaterials[] m162meetingbackgroundmaterials { get; set; }

        [XmlElement("m1-6-3-correspondence-regarding-meetings")]
        public m163correspondenceregardingmeetings[] m163correspondenceregardingmeetings { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-1-meeting-request", Namespace = "", IsNullable = false)]
    public partial class m161meetingrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-6-2-meeting-background-materials", Namespace = "", IsNullable = false)]
    public partial class m162meetingbackgroundmaterials
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

    }

    [Serializable]
    [XmlRoot("m1-6-3-correspondence-regarding-meetings", Namespace = "", IsNullable = false)]
    public partial class m163correspondenceregardingmeetings
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

    }

    [Serializable]
    [XmlRoot("m1-7-fast-track", Namespace = "", IsNullable = false)]
    public partial class m17fasttrack
    {
        [XmlElement("m1-7-1-fast-track-designation-request")]
        public m171fasttrackdesignationrequest[] m171fasttrackdesignationrequest { get; set; }

        [XmlElement("m1-7-2-fast-track-designation-withdrawal-request")]
        public m172fasttrackdesignationwithdrawalrequest[] m172fasttrackdesignationwithdrawalrequest { get; set; }

        [XmlElement("m1-7-3-rolling-review-request")]
        public m173rollingreviewrequest[] m173rollingreviewrequest { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-1-fast-track-designation-request", Namespace = "", IsNullable = false)]
    public partial class m171fasttrackdesignationrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-2-fast-track-designation-withdrawal-request", Namespace = "", IsNullable = false)]
    public partial class m172fasttrackdesignationwithdrawalrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-7-3-rolling-review-request", Namespace = "", IsNullable = false)]
    public partial class m173rollingreviewrequest
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-special-protocol-assessment-request", Namespace = "", IsNullable = false)]
    public partial class m18specialprotocolassessmentrequest
    {
        [XmlElement("m1-8-1-clinical-study")]
        public m181clinicalstudy[] m181clinicalstudy { get; set; }

        [XmlElement("m1-8-2-carcinogenicity-study")]
        public m182carcinogenicitystudy[] m182carcinogenicitystudy { get; set; }

        [XmlElement("m1-8-3-stability-study")]
        public m183stabilitystudy[] m183stabilitystudy { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-1-clinical-study", Namespace = "", IsNullable = false)]
    public partial class m181clinicalstudy
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-2-carcinogenicity-study", Namespace = "", IsNullable = false)]
    public partial class m182carcinogenicitystudy
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-8-3-stability-study", Namespace = "", IsNullable = false)]
    public partial class m183stabilitystudy
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-pediatric-administrative-information", Namespace = "", IsNullable = false)]
    public partial class m19pediatricadministrativeinformation
    {
        [XmlElement("m1-9-1-request-waiver-pediatric-studies")]
        public m191requestwaiverpediatricstudies[] m191requestwaiverpediatricstudies { get; set; }

        [XmlElement("m1-9-2-request-deferral-pediatric-studies")]
        public m192requestdeferralpediatricstudies[] m192requestdeferralpediatricstudies { get; set; }

        [XmlElement("m1-9-3-request-pediatric-exclusivity-determination")]
        public m193requestpediatricexclusivitydetermination[] m193requestpediatricexclusivitydetermination { get; set; }

        [XmlElement("m1-9-4-proposed-pediatric-study-request-amendments")]
        public m194proposedpediatricstudyrequestamendments[] m194proposedpediatricstudyrequestamendments { get; set; }

        [XmlElement("m1-9-5-proposal-written-agreement")]
        public m195proposalwrittenagreement[] m195proposalwrittenagreement { get; set; }

        [XmlElement("m1-9-6-other-correspondence-regarding-pediatric-exclusivity-study-plans")]
        public m196othercorrespondenceregardingpediatricexclusivitystudyplans[] m196othercorrespondenceregardingpediatricexclusivitystudyplans { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-1-request-waiver-pediatric-studies", Namespace = "", IsNullable = false)]
    public partial class m191requestwaiverpediatricstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-2-request-deferral-pediatric-studies", Namespace = "", IsNullable = false)]
    public partial class m192requestdeferralpediatricstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-3-request-pediatric-exclusivity-determination", Namespace = "", IsNullable = false)]
    public partial class m193requestpediatricexclusivitydetermination
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-4-proposed-pediatric-study-request-amendments", Namespace = "", IsNullable = false)]
    public partial class m194proposedpediatricstudyrequestamendments
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-5-proposal-written-agreement", Namespace = "", IsNullable = false)]
    public partial class m195proposalwrittenagreement
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-9-6-other-correspondence-regarding-pediatric-exclusivity-study-plans", Namespace = "", IsNullable = false)]
    public partial class m196othercorrespondenceregardingpediatricexclusivitystudyplans
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-10-dispute-resolution", Namespace = "", IsNullable = false)]
    public partial class m110disputeresolution
    {
        [XmlElement("m1-10-1-request-for-dispute-resolution")]
        public m1101requestfordisputeresolution[] m1101requestfordisputeresolution { get; set; }

        [XmlElement("m1-10-2-correspondence-related-to-dispute-resolution")]
        public m1102correspondencerelatedtodisputeresolution[] m1102correspondencerelatedtodisputeresolution { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-10-1-request-for-dispute-resolution", Namespace = "", IsNullable = false)]
    public partial class m1101requestfordisputeresolution
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-10-2-correspondence-related-to-dispute-resolution", Namespace = "", IsNullable = false)]
    public partial class m1102correspondencerelatedtodisputeresolution
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-information-amendment", Namespace = "", IsNullable = false)]
    public partial class m111informationamendment
    {
        [XmlElement("m1-11-1-quality-information-amendment")]
        public m1111qualityinformationamendment[] m1111qualityinformationamendment { get; set; }

        [XmlElement("m1-11-2-safety-information-amendment")]
        public m1112safetyinformationamendment[] m1112safetyinformationamendment { get; set; }

        [XmlElement("m1-11-3-efficacy-information-amendment")]
        public m1113efficacyinformationamendment[] m1113efficacyinformationamendment { get; set; }

        [XmlElement("m1-11-4-multiple-module-information-amendments")]
        public m1114multiplemoduleinformationamendments[] m1114multiplemoduleinformationamendments { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-1-quality-information-amendment", Namespace = "", IsNullable = false)]
    public partial class m1111qualityinformationamendment
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-2-safety-information-amendment", Namespace = "", IsNullable = false)]
    public partial class m1112safetyinformationamendment
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-3-efficacy-information-amendment", Namespace = "", IsNullable = false)]
    public partial class m1113efficacyinformationamendment
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-11-4-multiple-module-information-amendments", Namespace = "", IsNullable = false)]
    public partial class m1114multiplemoduleinformationamendments
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-other-correspondence", Namespace = "", IsNullable = false)]
    public partial class m112othercorrespondence
    {
        [XmlElement("m1-12-1-pre-ind-correspondence")]
        public m1121preindcorrespondence[] m1121preindcorrespondence { get; set; }

        [XmlElement("m1-12-2-request-charge")]
        public m1122requestcharge[] m1122requestcharge { get; set; }

        [XmlElement("m1-12-3-notification-charging-under-treatment-ind")]
        public m1123notificationchargingundertreatmentind[] m1123notificationchargingundertreatmentind { get; set; }

        [XmlElement("m1-12-4-request-comments-advice-ind")]
        public m1124requestcommentsadviceind[] m1124requestcommentsadviceind { get; set; }

        [XmlElement("m1-12-5-request-waiver")]
        public m1125requestwaiver[] m1125requestwaiver { get; set; }

        [XmlElement("m1-12-6-exemption-informed-consent-emergency-research")]
        public m1126exemptioninformedconsentemergencyresearch[] m1126exemptioninformedconsentemergencyresearch { get; set; }

        [XmlElement("m1-12-7-public-disclosure-statement-emergency-care-research")]
        public m1127publicdisclosurestatementemergencycareresearch[] m1127publicdisclosurestatementemergencycareresearch { get; set; }

        [XmlElement("m1-12-8-correspondence-regarding-emergency-care-research")]
        public m1128correspondenceregardingemergencycareresearch[] m1128correspondenceregardingemergencycareresearch { get; set; }

        [XmlElement("m1-12-9-notification-discontinuation-clinical-trial")]
        public m1129notificationdiscontinuationclinicaltrial[] m1129notificationdiscontinuationclinicaltrial { get; set; }

        [XmlElement("m1-12-10-generic-drug-enforcement-act-statement")]
        public m11210genericdrugenforcementactstatement[] m11210genericdrugenforcementactstatement { get; set; }

        [XmlElement("m1-12-11-basis-submission-statement")]
        public m11211basissubmissionstatement[] m11211basissubmissionstatement { get; set; }

        [XmlElement("m1-12-12-comparison-generic-drug-reference-listed-drug")]
        public m11212comparisongenericdrugreferencelisteddrug[] m11212comparisongenericdrugreferencelisteddrug { get; set; }

        [XmlElement("m1-12-13-request-waiver-in-vivo-studies")]
        public m11213requestwaiverinvivostudies[] m11213requestwaiverinvivostudies { get; set; }

        [XmlElement("m1-12-14-environmental-analysis")]
        public m11214environmentalanalysis[] m11214environmentalanalysis { get; set; }

        [XmlElement("m1-12-15-request-waiver-in-vivo-bioavailability-studies")]
        public m11215requestwaiverinvivobioavailabilitystudies[] m11215requestwaiverinvivobioavailabilitystudies { get; set; }

        [XmlElement("m1-12-16-field-alert-reports")]
        public m11216fieldalertreports[] m11216fieldalertreports { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-1-pre-ind-correspondence", Namespace = "", IsNullable = false)]
    public partial class m1121preindcorrespondence
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-2-request-charge", Namespace = "", IsNullable = false)]
    public partial class m1122requestcharge
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-3-notification-charging-under-treatment-ind", Namespace = "", IsNullable = false)]
    public partial class m1123notificationchargingundertreatmentind
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-4-request-comments-advice-ind", Namespace = "", IsNullable = false)]
    public partial class m1124requestcommentsadviceind
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-5-request-waiver", Namespace = "", IsNullable = false)]
    public partial class m1125requestwaiver
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-6-exemption-informed-consent-emergency-research", Namespace = "", IsNullable = false)]
    public partial class m1126exemptioninformedconsentemergencyresearch
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-7-public-disclosure-statement-emergency-care-research", Namespace = "", IsNullable = false)]
    public partial class m1127publicdisclosurestatementemergencycareresearch
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-8-correspondence-regarding-emergency-care-research", Namespace = "", IsNullable = false)]
    public partial class m1128correspondenceregardingemergencycareresearch
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-9-notification-discontinuation-clinical-trial", Namespace = "", IsNullable = false)]
    public partial class m1129notificationdiscontinuationclinicaltrial
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-10-generic-drug-enforcement-act-statement", Namespace = "", IsNullable = false)]
    public partial class m11210genericdrugenforcementactstatement
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-11-basis-submission-statement", Namespace = "", IsNullable = false)]
    public partial class m11211basissubmissionstatement
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-12-comparison-generic-drug-reference-listed-drug", Namespace = "", IsNullable = false)]
    public partial class m11212comparisongenericdrugreferencelisteddrug
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-13-request-waiver-in-vivo-studies", Namespace = "", IsNullable = false)]
    public partial class m11213requestwaiverinvivostudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-14-environmental-analysis", Namespace = "", IsNullable = false)]
    public partial class m11214environmentalanalysis
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-15-request-waiver-in-vivo-bioavailability-studies", Namespace = "", IsNullable = false)]
    public partial class m11215requestwaiverinvivobioavailabilitystudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-12-16-field-alert-reports", Namespace = "", IsNullable = false)]
    public partial class m11216fieldalertreports
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-annual-report", Namespace = "", IsNullable = false)]
    public partial class m113annualreport
    {
        [XmlElement("m1-13-1-summary-nonclinical-studies")]
        public m1131summarynonclinicalstudies[] m1131summarynonclinicalstudies { get; set; }

        [XmlElement("m1-13-2-summary-clinical-pharmacology-information")]
        public m1132summaryclinicalpharmacologyinformation[] m1132summaryclinicalpharmacologyinformation { get; set; }

        [XmlElement("m1-13-3-summary-safety-information")]
        public m1133summarysafetyinformation[] m1133summarysafetyinformation { get; set; }

        [XmlElement("m1-13-4-summary-labeling-changes")]
        public m1134summarylabelingchanges[] m1134summarylabelingchanges { get; set; }

        [XmlElement("m1-13-5-summary-manufacturing-changes")]
        public m1135summarymanufacturingchanges[] m1135summarymanufacturingchanges { get; set; }

        [XmlElement("m1-13-6-summary-microbiological-changes")]
        public m1136summarymicrobiologicalchanges[] m1136summarymicrobiologicalchanges { get; set; }

        [XmlElement("m1-13-7-summary-other-significant-new-information")]
        public m1137summaryothersignificantnewinformation[] m1137summaryothersignificantnewinformation { get; set; }

        [XmlElement("m1-13-8-individual-study-information")]
        public m1138individualstudyinformation[] m1138individualstudyinformation { get; set; }

        [XmlElement("m1-13-9-general-investigational-plan")]
        public m1139generalinvestigationalplan[] m1139generalinvestigationalplan { get; set; }

        [XmlElement("m1-13-10-foreign-marketing-history")]
        public m11310foreignmarketinghistory[] m11310foreignmarketinghistory { get; set; }

        [XmlElement("m1-13-11-distribution-data")]
        public m11311distributiondata[] m11311distributiondata { get; set; }

        [XmlElement("m1-13-12-status-postmarketing-study-commitments")]
        public m11312statuspostmarketingstudycommitments[] m11312statuspostmarketingstudycommitments { get; set; }

        [XmlElement("m1-13-13-status-other-postmarketing-studies")]
        public m11313statusotherpostmarketingstudies[] m11313statusotherpostmarketingstudies { get; set; }

        [XmlElement("m1-13-14-log-outstanding-regulatory-business")]
        public m11314logoutstandingregulatorybusiness[] m11314logoutstandingregulatorybusiness { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-1-summary-nonclinical-studies", Namespace = "", IsNullable = false)]
    public partial class m1131summarynonclinicalstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-2-summary-clinical-pharmacology-information", Namespace = "", IsNullable = false)]
    public partial class m1132summaryclinicalpharmacologyinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-3-summary-safety-information", Namespace = "", IsNullable = false)]
    public partial class m1133summarysafetyinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-4-summary-labeling-changes", Namespace = "", IsNullable = false)]
    public partial class m1134summarylabelingchanges
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-5-summary-manufacturing-changes", Namespace = "", IsNullable = false)]
    public partial class m1135summarymanufacturingchanges
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-6-summary-microbiological-changes", Namespace = "", IsNullable = false)]
    public partial class m1136summarymicrobiologicalchanges
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-7-summary-other-significant-new-information", Namespace = "", IsNullable = false)]
    public partial class m1137summaryothersignificantnewinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-8-individual-study-information", Namespace = "", IsNullable = false)]
    public partial class m1138individualstudyinformation
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-9-general-investigational-plan", Namespace = "", IsNullable = false)]
    public partial class m1139generalinvestigationalplan
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-10-foreign-marketing-history", Namespace = "", IsNullable = false)]
    public partial class m11310foreignmarketinghistory
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-11-distribution-data", Namespace = "", IsNullable = false)]
    public partial class m11311distributiondata
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-12-status-postmarketing-study-commitments", Namespace = "", IsNullable = false)]
    public partial class m11312statuspostmarketingstudycommitments
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-13-status-other-postmarketing-studies", Namespace = "", IsNullable = false)]
    public partial class m11313statusotherpostmarketingstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-13-14-log-outstanding-regulatory-business", Namespace = "", IsNullable = false)]
    public partial class m11314logoutstandingregulatorybusiness
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-labeling", Namespace = "", IsNullable = false)]
    public partial class m114labeling
    {
        [XmlElement("m1-14-1-draft-labeling")]
        public m1141draftlabeling[] m1141draftlabeling { get; set; }

        [XmlElement("m1-14-2-final-labeling")]
        public m1142finallabeling[] m1142finallabeling { get; set; }

        [XmlElement("m1-14-3-listed-drug-labeling")]
        public m1143listeddruglabeling[] m1143listeddruglabeling { get; set; }

        [XmlElement("m1-14-4-investigational-drug-labeling")]
        public m1144investigationaldruglabeling[] m1144investigationaldruglabeling { get; set; }

        [XmlElement("m1-14-5-foreign-labeling")]
        public m1145foreignlabeling[] m1145foreignlabeling { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-draft-labeling", Namespace = "", IsNullable = false)]
    public partial class m1141draftlabeling
    {
        [XmlElement("m1-14-1-1-draft-carton-container-labels")]
        public m11411draftcartoncontainerlabels[] m11411draftcartoncontainerlabels { get; set; }

        [XmlElement("m1-14-1-2-annotated-draft-labeling-text")]
        public m11412annotateddraftlabelingtext[] m11412annotateddraftlabelingtext { get; set; }

        [XmlElement("m1-14-1-3-draft-labeling-text")]
        public m11413draftlabelingtext[] m11413draftlabelingtext { get; set; }

        [XmlElement("m1-14-1-4-label-comprehension-studies")]
        public m11414labelcomprehensionstudies[] m11414labelcomprehensionstudies { get; set; }

        [XmlElement("m1-14-1-5-labeling-history")]
        public m11415labelinghistory[] m11415labelinghistory { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-1-draft-carton-container-labels", Namespace = "", IsNullable = false)]
    public partial class m11411draftcartoncontainerlabels
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-2-annotated-draft-labeling-text", Namespace = "", IsNullable = false)]
    public partial class m11412annotateddraftlabelingtext
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-3-draft-labeling-text", Namespace = "", IsNullable = false)]
    public partial class m11413draftlabelingtext
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-4-label-comprehension-studies", Namespace = "", IsNullable = false)]
    public partial class m11414labelcomprehensionstudies
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-1-5-labeling-history", Namespace = "", IsNullable = false)]
    public partial class m11415labelinghistory
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-2-final-labeling", Namespace = "", IsNullable = false)]
    public partial class m1142finallabeling
    {
        [XmlElement("m1-14-2-1-final-carton-container-labels")]
        public m11421finalcartoncontainerlabels[] m11421finalcartoncontainerlabels { get; set; }

        [XmlElement("m1-14-2-2-final-package-insert-package-inserts")]
        public m11422finalpackageinsertpackageinserts[] m11422finalpackageinsertpackageinserts { get; set; }

        [XmlElement("m1-14-2-3-final-labeling-text")]
        public m11423finallabelingtext[] m11423finallabelingtext { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-2-1-final-carton-container-labels", Namespace = "", IsNullable = false)]
    public partial class m11421finalcartoncontainerlabels
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-2-2-final-package-insert-package-inserts", Namespace = "", IsNullable = false)]
    public partial class m11422finalpackageinsertpackageinserts
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-2-3-final-labeling-text", Namespace = "", IsNullable = false)]
    public partial class m11423finallabelingtext
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-3-listed-drug-labeling", Namespace = "", IsNullable = false)]
    public partial class m1143listeddruglabeling
    {
        [XmlElement("m1-14-3-1-annotated-comparison-listed-drug")]
        public m11431annotatedcomparisonlisteddrug[] m11431annotatedcomparisonlisteddrug { get; set; }

        [XmlElement("m1-14-3-2-approved-labeling-text-listed-drug")]
        public m11432approvedlabelingtextlisteddrug[] m11432approvedlabelingtextlisteddrug { get; set; }

        [XmlElement("m1-14-3-3-labeling-text-reference-listed-drug")]
        public m11433labelingtextreferencelisteddrug[] m11433labelingtextreferencelisteddrug { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-3-1-annotated-comparison-listed-drug", Namespace = "", IsNullable = false)]
    public partial class m11431annotatedcomparisonlisteddrug
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-3-2-approved-labeling-text-listed-drug", Namespace = "", IsNullable = false)]
    public partial class m11432approvedlabelingtextlisteddrug
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-3-3-labeling-text-reference-listed-drug", Namespace = "", IsNullable = false)]
    public partial class m11433labelingtextreferencelisteddrug
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-4-investigational-drug-labeling", Namespace = "", IsNullable = false)]
    public partial class m1144investigationaldruglabeling
    {
        [XmlElement("m1-14-4-1-investigational-brochure")]
        public m11441investigationalbrochure[] m11441investigationalbrochure { get; set; }

        [XmlElement("m1-14-4-2-investigational-drug-label")]
        public m11442investigationaldruglabel[] m11442investigationaldruglabel { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-4-1-investigational-brochure", Namespace = "", IsNullable = false)]
    public partial class m11441investigationalbrochure
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-4-2-investigational-drug-label", Namespace = "", IsNullable = false)]
    public partial class m11442investigationaldruglabel
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-14-5-foreign-labeling", Namespace = "", IsNullable = false)]
    public partial class m1145foreignlabeling
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-15-promotional-material", Namespace = "", IsNullable = false)]
    public partial class m115promotionalmaterial
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("m1-16-risk-management-plans", Namespace = "", IsNullable = false)]
    public partial class m116riskmanagementplans
    {
        [XmlElement("leaf", typeof(leaf))]
        [XmlElement("node-extension", typeof(nodeextension))]
        public object[] Items { get; set; }

        [XmlAttribute(DataType = "ID")]
        public string ID { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }
    }

    [Serializable]
    [XmlRoot("fda-regional", Namespace = "http://www.ich.org/fda", IsNullable = false)]
    public partial class fdaregional
    {
        public fdaregional()
        {
            this.dtdversion = "2.01";
        }
        [XmlElement(Namespace = "")]
        public admin admin { get; set; }

        [XmlElement("m1-regional", Namespace = "")]
        public m1regional m1regional { get; set; }

        [XmlAttribute(Namespace = "http://www.w3.org/XML/1998/namespace")]
        public string lang { get; set; }

        [XmlAttribute("dtd-version")]
        public string dtdversion { get; set; }
    }
}
