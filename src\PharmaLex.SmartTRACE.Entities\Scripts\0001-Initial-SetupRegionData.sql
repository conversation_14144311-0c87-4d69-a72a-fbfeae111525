﻿insert into [Region] ([Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select 'Africa',  'AE',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Region] ([Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select 'AsiaPac',  'APAC',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Region] ([Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select 'Central America',  'CA',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Region] ([Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select 'Core',  'C',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Region] ([Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select 'Europe (Non-EU)',  'EU',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Region] ([Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select 'European Union',  'EU',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Region] ([Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select 'Gulf Cooperation Council',  'GCC',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Region] ([Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select 'Middle East (Non-GCC)',  'ME',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Region] ([Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select 'North America',  'NA',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Region] ([Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select 'RCIS',  'RCIS',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
insert into [Region] ([Name], [Abbreviation], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) select 'South America',  'SA',  getdate(), '<EMAIL>', getdate(), '<EMAIL>'
