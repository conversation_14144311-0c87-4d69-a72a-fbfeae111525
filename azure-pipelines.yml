name: "$(Date:yyyyMMdd).$(Rev:r)"

pool:
  vmImage: 'windows-latest'

parameters:
  - name: Analyse_Packages
    displayName: Analyse Packages
    type: boolean
    default: false
 
trigger:
  branches:
    include:
      - develop
      - main 
# pr trigger specifies which branches cause a pull request build to run.
pr:
  - master
  - develop      

variables:
  - name: Source_Branch
    value: $[replace(variables['Build.SourceBranch'],'refs/heads/','')]
  - name: Version_Number
    value: "2.2.0"
  - name: Build_Number
    value: $[counter(variables['Version_Number'], 0)]
  - name: Build_Configuration
    value: "Release"
  - group: Nuget 
  - name: NuGet_Source
    value: $[variables.Source]
  - name: Long_Lived_Branch
    ${{ if or(eq(variables['Build.SourceBranchName'], 'develop'), eq(variables['Build.SourceBranchName'], 'main')) }}:
      value : true
    ${{ else }}:
      value: false


resources:
  repositories:
    - repository: templates
      name: Phlex.Core/Dev.Pipelines.Templates
      type: git

stages:
  - stage: Prerequisites
    jobs:
      - job: CalculateVersion
        displayName: "Calculate Version"
        steps:
          - template: General/calculate-version.yml@templates
            parameters:
              VersionNumber: "$(Version_Number)"
              BuildNumber: "$(Build_Number)"
              BranchName: "$(Source_Branch)"
              IgnorePostFixTag: true
  
  - stage: BuildAndTest
    dependsOn:
      - Prerequisites
    jobs:
      - job: BuildAndTest
        displayName: "Build and test"
        steps:
          - template: Build/dotnet/build-test-analyse.yml@templates
            parameters:
              NugetSource: '$(NuGet_Source)'
              BuildConfiguration: '$(Build_Configuration)'
              SolutionName: "PharmaLex.SmartTRACE.sln"
              VersionNumber: '$(Version_Number)'
              TestProjects: "test/**/*Tests.csproj"
              TestOptions: '/p:Include="[PharmaLex.SmartTRACE.*]*"'
              SonarProjectKey: 'Phlexglobal_SmartTrace'
              SonarProjectName: 'SmartTrace'
              AnalysePackages: "${{ parameters.Analyse_Packages }}"
              LongLivedBranch: '${{ variables.Long_Lived_Branch }}'
              CheckmarxTeam: "Code_Busters"
              CheckmarxProjectName: "SmartTrace" 

  - ${{ if eq(variables['Long_Lived_Branch'], true) }}:
    - stage: BuildArtifacts
      dependsOn:
        - BuildAndTest
      jobs:
        - job: BuildArtifacts
          displayName: "Build Artifacts"
          steps:
            - checkout: self
              persistCredentials: true
              submodules: true
              fetchDepth: 1
            - task: UseDotNet@2
              displayName: 'Install Dotnet Core 8'
              inputs:
                version: '8.0.x'
            - task: DotNetCoreCLI@2
              displayName: 'dotnet restore'
              inputs:
                command: 'restore'
                projects: '**/*.csproj'
                feedsToUse: 'select'
                vstsFeed: Phlexglobal
            - task: DotNetCoreCLI@2
              displayName: 'dotnet build'
              inputs:
                command: 'build'
                projects: '**/*.csproj'
                arguments: '--configuration $(Build_Configuration) --no-restore'
            - task: DotNetCoreCLI@2
              displayName: 'dotnet publish web'
              inputs:
                command: publish
                publishWebProjects: true
                arguments: '--configuration $(Build_Configuration) --output $(build.artifactstagingdirectory)'
            - task: DotNetCoreCLI@2
              displayName: 'dotnet publish Reminders function'
              inputs:
                command: publish
                publishWebProjects: false
                projects: 'src\PharmaLex.SmartTRACE.RemindersApp\PharmaLex.SmartTRACE.RemindersApp.csproj'
                arguments: '--configuration $(Build_Configuration) --output $(build.artifactstagingdirectory)\import'
                zipAfterPublish: false
                modifyOutputPath: false
            - task: ArchiveFiles@2
              inputs:
                rootFolderOrFile: '$(Build.ArtifactStagingDirectory)\import'
                includeRootFolder: false
                archiveType: 'zip'
                archiveFile: '$(Build.ArtifactStagingDirectory)\PharmaLex.SmartTRACE.RemindersApp.zip'
                replaceExistingArchive: true
            - task: DotNetCoreCLI@2
              displayName: Install EF Tool
              inputs:
                command: custom
                custom: 'tool'
                arguments: 'install --global dotnet-ef --version 8.0.2'
            - task: DotNetCoreCLI@2
              displayName: Create SQL Scripts
              inputs:
                command: custom
                custom: 'ef '
                arguments: migrations  script --project "$(Build.SourcesDirectory)\src\PharmaLex.SmartTRACE.Entities\PharmaLex.SmartTRACE.Entities.csproj" --startup-project "$(Build.SourcesDirectory)\src\PharmaLex.SmartTRACE.Web\PharmaLex.SmartTRACE.Web.csproj" --output $(Build.artifactstagingdirectory)\Migrations\migration.sql --idempotent --verbose --no-build --configuration $(Build_Configuration)
            - task: PublishBuildArtifacts@1
              displayName: 'Publish Artifact: drop'
              inputs:
                PathtoPublish: '$(build.artifactstagingdirectory)'
                ArtifactName: drop
              condition: succeededOrFailed()

