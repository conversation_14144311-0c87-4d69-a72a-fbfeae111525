﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class AddActiveSubstanceLifecycleId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.AddColumn<int>(
                name: "LifecycleStateId",
                schema: "Audit",
                table: "ActiveSubstance_Audit",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.AddColumn<int>(
                name: "LifecycleStateId",
                table: "ActiveSubstance",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.SqlFileExec("0019-AddActiveSubstanceLifecycleStateId_UpdateActiveSubstanceTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LifecycleStateId",
                schema: "Audit",
                table: "ActiveSubstance_Audit");

            migrationBuilder.DropColumn(
                name: "LifecycleStateId",
                table: "ActiveSubstance");
        }
    }
}
