﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using NSubstitute;
using NuGet.Protocol;
using PharmaLex.Caching;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Data.Persistance.Repository;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.WebTests;
using System.Collections.Concurrent;
using System.Linq.Expressions;
using System.Security.Claims;
using System.Text;


namespace PharmaLex.SmartTRACE.Web.HelpersTests.Services
{
    public class ApplicationServiceTests
    {

        private readonly IAuthorizationService _authorizationService;
        private readonly IApplicationRepository _applicationRepository;
        private readonly ApplicationService applicationService;
        public ApplicationServiceTests()
        {
            _authorizationService = Substitute.For<IAuthorizationService>();

            #region Fake Distributed Cache Service and DbContextReslover
            var config = TestHelpers.GetConfiguration();
            var dbctxReslover = TestHelpers.GetPlxDbContextResolver();
            var dbCtx = ((SmartTRACEContext)dbctxReslover.Context);
            _applicationRepository = new ApplicationRepository(dbCtx, Substitute.For<IUserContext>());
            IRepositoryFactory repoFactory = new RepositoryFactory(dbctxReslover, Substitute.For<IUserContext>());
            var configuration = new MapperConfiguration(cfg => cfg.AddProfile<PicklistDataMappingProfile>());
            var mapper = configuration.CreateMapper();
            var cacheKey = config.GetSection("Static:App").Value + "|" + config.GetSection("Static:Env").Value + "|CacheDependencyMap";
            var ds = new ConcurrentDictionary<string, List<string>>(
              new List<KeyValuePair<string, List<string>>> { new KeyValuePair<string, List<string>>("", new List<string> { "" }) }).ToJson();
            var cache = Substitute.For<IDistributedCache>();
            cache.Get(cacheKey).Returns(Encoding.UTF8.GetBytes(ds));
            var cacheService = new DistributedCacheService(cache, config);
            var cacheServiceFactory = new DistributedCacheServiceFactory(repoFactory, cacheService, mapper, config);
            #endregion

            #region Test Data
            if (!dbCtx.PicklistData.Any(x => x.PicklistTypeId == 4 || x.PicklistTypeId == 5 || x.PicklistTypeId == 1))
            {
                dbCtx.PicklistData.AddRange(new List<PicklistData> {
                new PicklistData { Name = "Human Use", PicklistTypeId = 1, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData { Name = "Clinical", PicklistTypeId = 5, CreatedBy = "test", LastUpdatedBy="test" },
                new PicklistData { Name = "Normal", PicklistTypeId = 4, CreatedBy = "test", LastUpdatedBy="test" }
            });
            }
            if (!dbCtx.Application.Any(x => x.Id == 23 || x.Id == 24))
            {
                dbCtx.Application.Add(new PharmaLex.SmartTRACE.Entities.Application { Id = 23, ApplicationNumber = "AP-20210113-1 APPLICATION TEST", MedicinalProductDomainId = 1, MedicinalProductTypeId = 5, ApplicationTypeId = 3, ProcedureTypeId = 2, Comments = null, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.Application.Add(new PharmaLex.SmartTRACE.Entities.Application { Id = 24, ApplicationNumber = "AP-RSUB/REG LEAD", MedicinalProductDomainId = 1, MedicinalProductTypeId = 5, ApplicationTypeId = 3, ProcedureTypeId = 2, Comments = null, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            }
            if (!dbCtx.Product.Any(x => x.Id == 5 || x.Id == 6 || x.Id == 7))
            {
                dbCtx.Product.Add(new PharmaLex.SmartTRACE.Entities.Product { Id = 5, Name = "Fusce velit dolor 100 mg", DosageFormId = 3, Strength = "100 mg", ClientId = 1, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.Product.Add(new PharmaLex.SmartTRACE.Entities.Product { Id = 6, Name = "Fusce velit dolor 150 mg", DosageFormId = 3, Strength = "150 mg", ClientId = 1, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.Product.Add(new PharmaLex.SmartTRACE.Entities.Product { Id = 7, Name = "Fusce velit dolor 200 mg", DosageFormId = 3, Strength = "200 mg", ClientId = 1, LifecycleStateId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            }
            if (!dbCtx.Region.Any(x => x.Id == 6))
            {
                dbCtx.Region.Add(new PharmaLex.SmartTRACE.Entities.Region { Id = 6, Name = "European Union", Abbreviation = "EU", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            }
            if (!dbCtx.Country.Any(x => x.Id == 1 || x.Id == 2))
            {
                dbCtx.Country.Add(new PharmaLex.SmartTRACE.Entities.Country { Id = 1, Name = "Austria", RegionId = 6, TwoLetterCode = "AT", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.Country.Add(new PharmaLex.SmartTRACE.Entities.Country { Id = 2, Name = "Belgium", RegionId = 6, TwoLetterCode = "BE", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            }
            if (!dbCtx.ApplicationProduct.Any(x => x.Id == 29 || x.Id == 28 || x.Id == 32 || x.Id == 31 || x.Id == 30))
            {
                dbCtx.ApplicationProduct.Add(new PharmaLex.SmartTRACE.Entities.ApplicationProduct { Id = 29, ApplicationId = 23, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationProduct.Add(new PharmaLex.SmartTRACE.Entities.ApplicationProduct { Id = 28, ApplicationId = 23, ProductId = 6, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationProduct.Add(new PharmaLex.SmartTRACE.Entities.ApplicationProduct { Id = 32, ApplicationId = 24, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationProduct.Add(new PharmaLex.SmartTRACE.Entities.ApplicationProduct { Id = 31, ApplicationId = 24, ProductId = 6, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationProduct.Add(new PharmaLex.SmartTRACE.Entities.ApplicationProduct { Id = 30, ApplicationId = 24, ProductId = 7, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            }
            if (!dbCtx.ApplicationCountry.Any(x => x.Id == 95 || x.Id == 87 || x.Id == 88 || x.Id == 89
                || x.Id == 90 || x.Id == 91 || x.Id == 92 || x.Id == 93 || x.Id == 102 || x.Id == 96 || x.Id == 97
                || x.Id == 98 || x.Id == 99 || x.Id == 100 || x.Id == 101))
            {
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 95, ApplicationId = 23, CountryId = 1, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 87, ApplicationId = 23, CountryId = 2, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 88, ApplicationId = 23, CountryId = 3, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 89, ApplicationId = 23, CountryId = 4, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 90, ApplicationId = 23, CountryId = 5, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 91, ApplicationId = 23, CountryId = 6, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 92, ApplicationId = 23, CountryId = 7, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 93, ApplicationId = 23, CountryId = 8, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });

                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 102, ApplicationId = 24, CountryId = 1, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 96, ApplicationId = 24, CountryId = 2, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 97, ApplicationId = 24, CountryId = 3, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 98, ApplicationId = 24, CountryId = 4, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 99, ApplicationId = 24, CountryId = 5, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 100, ApplicationId = 24, CountryId = 6, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.ApplicationCountry.Add(new PharmaLex.SmartTRACE.Entities.ApplicationCountry { Id = 101, ApplicationId = 24, CountryId = 7, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });

            }
            if (!dbCtx.Client.Any(x => x.Id == 1))
            {
                dbCtx.Client.Add(new PharmaLex.SmartTRACE.Entities.Client { Id = 1, Name = "Astra", ContractOwnerId = 152, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            }
            if (!dbCtx.User.Any(x => x.Id == 3))
            {
                dbCtx.User.Add(new PharmaLex.SmartTRACE.Entities.User { Id = 3, Email = "<EMAIL>", GivenName = "Tanya", FamilyName = "Lisseva", CreatedBy = "update script", LastUpdatedBy = "<EMAIL>", UserTypeId = 1 });

            }
            if (!dbCtx.UserClient.Any(x => x.ClientId == 1 || x.ClientId == 2))
            {
                dbCtx.UserClient.Add(new PharmaLex.SmartTRACE.Entities.UserClient { ClientId = 1, UserId = 3, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
                dbCtx.UserClient.Add(new PharmaLex.SmartTRACE.Entities.UserClient { ClientId = 2, UserId = 3, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            }
            if (!dbCtx.Claim.Any(x => x.Id == 5))
            {
                dbCtx.Claim.Add(new PharmaLex.SmartTRACE.Entities.Claim { Id = 5, Name = "Reader", ClaimType = "application", CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" });
            }
            dbCtx.SaveChanges();
            #endregion

            applicationService = new ApplicationService(cacheServiceFactory, mapper, _authorizationService, _applicationRepository);
        }
        readonly ApplicationFilterModel model = new ApplicationFilterModel()
        {
            ApplicationNumber = "AP-20210113-1 APPLICATION TEST",
            ClientName = "Astra",
            Country = "Belgium",
            Product = "Fusce velit dolor 100 mg",
            Region = "European Union",
            MedicinalProductDomain = "Human Use",
            ProcedureType = "Clinical",
            LifecycleState = "Active",
            ApplicationType = "Normal"
        };
        [Fact]
        public async Task ListApplications_ShouldReturnCorrectViewModels()
        {
            // Arrange
            var userid = 3;
            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
                {
                     new System.Security.Claims.Claim(ClaimTypes.Name, "testuser"),
                     new System.Security.Claims.Claim(ClaimTypes.Role, "Reader"),
                     new System.Security.Claims.Claim("plx:userid",userid.ToString())
                }));
            _authorizationService.AuthorizeAsync(user, "BusinessAdmin").Returns(Task.FromResult(AuthorizationResult.Failed()));

            // Act
            var result = await applicationService.ListApplications(user, model);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Astra", result[0].ClientName);
        }
        [Fact]
        public async Task ListApplications_BusinessAdmin_ShouldReturnCorrectViewModels()
        {
            // Arrange
            var userid = 3;
            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
                {
                     new System.Security.Claims.Claim(ClaimTypes.Name, "testuser"),
                     new System.Security.Claims.Claim(ClaimTypes.Role, "Reader"),
                     new System.Security.Claims.Claim("plx:userid",userid.ToString())
                }));
            _authorizationService.AuthorizeAsync(user, "BusinessAdmin").Returns(Task.FromResult(AuthorizationResult.Success()));

            // Act
            var result = await applicationService.ListApplications(user, model);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Astra", result[0].ClientName);
        }
        [Fact]
        public async Task GetPagedApplicationsAsync_UserInBusinessAdminRole_ReturnsApplications()
        {
            // Arrange
            var userid = 3;
            int skip = 0;
            int take = 5;
            var user = new ClaimsPrincipal(new ClaimsIdentity(new System.Security.Claims.Claim[]
                {
                     new System.Security.Claims.Claim(ClaimTypes.Name, "Reader"),
                     new System.Security.Claims.Claim(ClaimTypes.Role, "application"),
                     new System.Security.Claims.Claim("plx:userid",userid.ToString())
                }));
            string? sort = null;
            _authorizationService.AuthorizeAsync(user, "BusinessAdmin").Returns(Task.FromResult(AuthorizationResult.Success()));

            // Act
            var result = await applicationService.GetPagedApplicationsAsync(user, skip, take, model, sort);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Astra", result.Data[0].ClientName);
        }
        [Fact]
        public async Task GetApplication_ReturnsExpectedApplicationModel()
        {
            // Arrange
            var applicationId = 1;

            var appModel = new ApplicationModel()
            {
                Id = 1,
                ApplicationNumber = "ap11",
                ProcedureTypeId = 11,
                ApplicationTypeId = 110,
                ClientId = 1100,
                UniqueId = "uid1",
            };
            var casheService = Substitute.For<IDistributedCacheServiceFactory>();
            var mapper = Substitute.For<IMapper>();
            var appMappedCache = Substitute.For<IMappedEntityCacheServiceProxy<PharmaLex.SmartTRACE.Entities.Application, ApplicationModel>>();
            casheService.CreateMappedEntity<PharmaLex.SmartTRACE.Entities.Application, ApplicationModel>().Returns(appMappedCache);
            appMappedCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<PharmaLex.SmartTRACE.Entities.Application, bool>>>()).Returns(appModel);
            var applicationService = new ApplicationService(casheService, mapper, _authorizationService, _applicationRepository);

            // Act
            var result = await applicationService.GetApplication(applicationId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(applicationId, result.Id);
        }

        [Fact]
        public async Task InsertApplication_Should_Insert_New_Application()
        {
            // Arrange
            var editModel = new EditApplicationViewModel
            {
                Application = new ApplicationModel
                {
                    ApplicationNumber = "AP-20210113-1 APPLICATION TEST1",
                    MedicinalProductDomainId = 1,
                    MedicinalProductTypeId = 5,
                    ApplicationTypeId = 3,
                    ProcedureTypeId = 64,
                    Comments = null,
                    LifecycleStateId = 1,
                    ClientId = 1,
                    CountriesIds = [1, 2, 3],
                    CountryId = 1,
                    ProductsIds = [5, 6, 7]
                },

                ConcernedMemberStatesRequired = true
            };
            List<PicklistDataModel> picklistDataModel = new List<PicklistDataModel>()
            {
                new PicklistDataModel
                {
                    Id = 1,
                    Name = "Human Use",
                    PicklistTypeId = 1,
                    CountriesIds = [1, 2, 3],
                    Country = "Austria",
                    Selected = false
                },
                new PicklistDataModel
                {
                    Id = 64,
                    Name = "Decentralised Procedure",
                    PicklistTypeId = 5,
                    CountriesIds = [1, 2, 3],
                    Country = "Belgium",
                    Selected = false
                }
            };
            var applications = new Application
            {
                Id = 23,
                ApplicationNumber = "AP-20210113-1 APPLICATION TEST",
                MedicinalProductDomainId = 1,
                MedicinalProductTypeId = 5,
                ApplicationTypeId = 3,
                ProcedureTypeId = 2,
                Comments = null,
                LifecycleStateId = 1,
                CreatedBy = "<EMAIL>",
                LastUpdatedBy = "<EMAIL>"

            };
            var casheService = Substitute.For<IDistributedCacheServiceFactory>();
            var appCache = Substitute.For<ITrackedEntityCacheServiceProxy<Application>>();
            casheService.CreateTrackedEntity<Application>().Returns(appCache);
            var mapper = Substitute.For<IMapper>();
            mapper.Map<ApplicationModel, Application>(editModel.Application).Returns(applications);
            var service = new ApplicationService(casheService, mapper, _authorizationService, _applicationRepository);

            // Act
            var result = await service.InsertApplication(editModel, picklistDataModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("AP-20210113-1 APPLICATION TEST", result.ApplicationNumber);
        }
        [Fact]
        public async Task UpdateApplication_Should_Insert_New_Application()
        {
            // Arrange
            var editModel = new EditApplicationViewModel
            {
                Application = new ApplicationModel
                {
                    Id = 23,
                    ApplicationNumber = "AP-20210113-1 APPLICATION TEST1",
                    MedicinalProductDomainId = 1,
                    MedicinalProductTypeId = 5,
                    ApplicationTypeId = 3,
                    ProcedureTypeId = 64,
                    Comments = null,
                    LifecycleStateId = 1,
                    ClientId = 1,
                    CountriesIds = [1, 2, 3, 10, 11, 12],
                    CountryId = 1,
                    ProductsIds = [5, 6, 7]
                },

                ConcernedMemberStatesRequired = true
            };

            List<PicklistDataModel> picklistDataModel = new List<PicklistDataModel>()
            {
                 new PicklistDataModel
                 {
                     Id = 1,
                     Name = "Human Use",
                     PicklistTypeId = 1,
                     CountriesIds = [1, 2, 3],
                     Country = "Austria",
                     Selected = false
                 },
                 new PicklistDataModel
                 {
                     Id = 64,
                     Name = "Decentralised Procedure",
                     PicklistTypeId = 5,
                     CountriesIds = [1, 2, 3],
                     Country = "Belgium",
                     Selected = false
                 }
            };
            List<ApplicationCountry> applicationCountry = new List<ApplicationCountry>()
            {
                new ApplicationCountry { Id = 95, ApplicationId = 23, CountryId = 1, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 87, ApplicationId = 23, CountryId = 2, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 88, ApplicationId = 23, CountryId = 3, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 89, ApplicationId = 23, CountryId = 4, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 90, ApplicationId = 23, CountryId = 5, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 91, ApplicationId = 23, CountryId = 6, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 92, ApplicationId = 23, CountryId = 7, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 93, ApplicationId = 23, CountryId = 8, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 102, ApplicationId = 24, CountryId = 1, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 96, ApplicationId = 24, CountryId = 2, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 97, ApplicationId = 24, CountryId = 3, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 98, ApplicationId = 24, CountryId = 4, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 99, ApplicationId = 24, CountryId = 5, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 100, ApplicationId = 24, CountryId = 6, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 101, ApplicationId = 24, CountryId = 7, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" }

            };
            List<ApplicationProduct> applicationProduct = new List<ApplicationProduct>()
            {
                new ApplicationProduct { Id = 29, ApplicationId = 23, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 28, ApplicationId = 23, ProductId = 6, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 32, ApplicationId = 24, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 31, ApplicationId = 24, ProductId = 6, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 30, ApplicationId = 24, ProductId = 7, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },

            };
            var applications = new Application
            {
                Id = 23,
                ApplicationNumber = "AP-20210113-1 APPLICATION TEST",
                MedicinalProductDomainId = 1,
                MedicinalProductTypeId = 5,
                ApplicationTypeId = 3,
                ProcedureTypeId = 64,
                Comments = null,
                LifecycleStateId = 1,
                CreatedBy = "<EMAIL>",
                LastUpdatedBy = "<EMAIL>"

            };
            var casheService = Substitute.For<IDistributedCacheServiceFactory>();
            var appCache = Substitute.For<ITrackedEntityCacheServiceProxy<Application>>();
            casheService.CreateTrackedEntity<Application>().Returns(appCache);
            appCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Application, bool>>>()).Returns(applications);
            var appCountryCache = Substitute.For<ITrackedEntityCacheServiceProxy<ApplicationCountry>>();
            casheService.CreateTrackedEntity<ApplicationCountry>().Returns(appCountryCache);
            appCountryCache.WhereAsync(Arg.Any<Expression<Func<ApplicationCountry, bool>>>()).Returns(applicationCountry);
            var appProductCache = Substitute.For<ITrackedEntityCacheServiceProxy<ApplicationProduct>>();
            casheService.CreateTrackedEntity<ApplicationProduct>().Returns(appProductCache);
            appProductCache.WhereAsync(Arg.Any<Expression<Func<ApplicationProduct, bool>>>()).Returns(applicationProduct);
            var mapper = Substitute.For<IMapper>();
            var service = new ApplicationService(casheService, mapper, _authorizationService, _applicationRepository);

            // Act
            var result = await service.UpdateApplication(editModel, picklistDataModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("AP-20210113-1 APPLICATION TEST", result.ApplicationNumber);
        }
        [Fact]
        public async Task UpdateApplication_MissingCountry_Should_Insert_New_Application()
        {
            // Arrange
            var editModel = new EditApplicationViewModel
            {
                Application = new ApplicationModel
                {
                    Id = 23,
                    ApplicationNumber = "AP-20210113-1 APPLICATION TEST1",
                    MedicinalProductDomainId = 1,
                    MedicinalProductTypeId = 5,
                    ApplicationTypeId = 3,
                    ProcedureTypeId = 64,
                    Comments = null,
                    LifecycleStateId = 1,
                    ClientId = 1,
                    CountriesIds = [1, 2],
                    CountryId = 1,
                    ProductsIds = [5, 6, 7, 12]
                },
                ConcernedMemberStatesRequired = true
            };
            List<PicklistDataModel> picklistDataModel = new List<PicklistDataModel>()
            {
                 new PicklistDataModel
                 {
                     Id = 1,
                     Name = "Human Use",
                     PicklistTypeId = 1,
                     CountriesIds = [1, 2, 3],
                     Country = "Austria",
                     Selected = false
                 },
                 new PicklistDataModel
                 {
                     Id = 64,
                     Name = "Decentralised Procedure",
                     PicklistTypeId = 5,
                     CountriesIds = [1, 2, 3],
                     Country = "Belgium",
                     Selected = false
                 },
                 new PicklistDataModel
                 {
                     Id = 62,
                     Name = "Mutual Recognition Procedure",
                     PicklistTypeId = 5,
                     CountriesIds = [1, 2, 3],
                     Country = "Belgium",
                     Selected = false
                 },
                 new PicklistDataModel
                 {
                     Id = 162,
                     Name = "Active Substance Master File",
                     PicklistTypeId = 5,
                     CountriesIds = [1, 2, 3],
                     Country = "Belgium",
                     Selected = false
                 }
            };
            List<ApplicationCountry> applicationCountry = new List<ApplicationCountry>()
            {
                new ApplicationCountry { Id = 95, ApplicationId = 23, CountryId = 1, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 87, ApplicationId = 23, CountryId = 2, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 88, ApplicationId = 23, CountryId = 3, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 89, ApplicationId = 23, CountryId = 4, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 90, ApplicationId = 23, CountryId = 5, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 91, ApplicationId = 23, CountryId = 6, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 92, ApplicationId = 23, CountryId = 7, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 93, ApplicationId = 23, CountryId = 8, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 102, ApplicationId = 24, CountryId = 1, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 96, ApplicationId = 24, CountryId = 2, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 97, ApplicationId = 24, CountryId = 3, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 98, ApplicationId = 24, CountryId = 4, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 99, ApplicationId = 24, CountryId = 5, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 100, ApplicationId = 24, CountryId = 6, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 101, ApplicationId = 24, CountryId = 7, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" }

            };
            List<ApplicationProduct> applicationProduct = new List<ApplicationProduct>()
            {
                new ApplicationProduct { Id = 29, ApplicationId = 23, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 28, ApplicationId = 23, ProductId = 6, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 32, ApplicationId = 24, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 31, ApplicationId = 24, ProductId = 6, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 30, ApplicationId = 24, ProductId = 7, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },

            };
            var applications = new Application
            {
                Id = 23,
                ApplicationNumber = "AP-20210113-1 APPLICATION TEST",
                MedicinalProductDomainId = 1,
                MedicinalProductTypeId = 5,
                ApplicationTypeId = 3,
                ProcedureTypeId = 64,
                Comments = null,
                LifecycleStateId = 1,
                CreatedBy = "<EMAIL>",
                LastUpdatedBy = "<EMAIL>"

            };
            var casheService = Substitute.For<IDistributedCacheServiceFactory>();
            var appCache = Substitute.For<ITrackedEntityCacheServiceProxy<Application>>();
            casheService.CreateTrackedEntity<Application>().Returns(appCache);
            appCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Application, bool>>>()).Returns(applications);

            var appCountryCache = Substitute.For<ITrackedEntityCacheServiceProxy<ApplicationCountry>>();
            casheService.CreateTrackedEntity<ApplicationCountry>().Returns(appCountryCache);
            appCountryCache.WhereAsync(Arg.Any<Expression<Func<ApplicationCountry, bool>>>()).Returns(applicationCountry);

            var appProductCache = Substitute.For<ITrackedEntityCacheServiceProxy<ApplicationProduct>>();
            casheService.CreateTrackedEntity<ApplicationProduct>().Returns(appProductCache);
            appProductCache.WhereAsync(Arg.Any<Expression<Func<ApplicationProduct, bool>>>()).Returns(applicationProduct);
            var mapper = Substitute.For<IMapper>();
            var service = new ApplicationService(casheService, mapper, _authorizationService, _applicationRepository);

            // Act
            var result = await service.UpdateApplication(editModel, picklistDataModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("AP-20210113-1 APPLICATION TEST", result.ApplicationNumber);
        }
        [Fact]
        public async Task UpdateApplication_CentralProcedure_Should_Insert_New_Application()
        {
            // Arrange
            var editModel = new EditApplicationViewModel
            {
                Application = new ApplicationModel
                {
                    Id = 23,
                    ApplicationNumber = "AP-20210113-1 APPLICATION TEST1",
                    MedicinalProductDomainId = 1,
                    MedicinalProductTypeId = 5,
                    ApplicationTypeId = 3,
                    ProcedureTypeId = 64,
                    Comments = null,
                    LifecycleStateId = 1,
                    ClientId = 1,
                    CountriesIds = [1, 2],
                    CountryId = 1,
                    ProductsIds = [5, 6]
                },

                ConcernedMemberStatesRequired = true
            };

            List<PicklistDataModel> picklistDataModel = new List<PicklistDataModel>()
            {
                 new PicklistDataModel
                 {
                     Id = 1,
                     Name = "Human Use",
                     PicklistTypeId = 1,
                     CountriesIds = [1, 2, 3],
                     Country = "Austria",
                     Selected = false
                 },
                 new PicklistDataModel
                 {
                     Id = 64,
                     Name = "Centralised Procedure",
                     PicklistTypeId = 5,
                     CountriesIds = [1, 2, 3],
                     Country = "Belgium",
                     Selected = false
                 }
            };
            List<ApplicationCountry> applicationCountry = new List<ApplicationCountry>()
            {
                new ApplicationCountry { Id = 95, ApplicationId = 23, CountryId = 1, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 87, ApplicationId = 23, CountryId = 2, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 88, ApplicationId = 23, CountryId = 3, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 89, ApplicationId = 23, CountryId = 4, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 90, ApplicationId = 23, CountryId = 5, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 91, ApplicationId = 23, CountryId = 6, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 92, ApplicationId = 23, CountryId = 7, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 93, ApplicationId = 23, CountryId = 8, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 102, ApplicationId = 24, CountryId = 1, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 96, ApplicationId = 24, CountryId = 2, AuthorityRoleId = 1, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 97, ApplicationId = 24, CountryId = 3, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 98, ApplicationId = 24, CountryId = 4, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 99, ApplicationId = 24, CountryId = 5, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 100, ApplicationId = 24, CountryId = 6, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationCountry { Id = 101, ApplicationId = 24, CountryId = 7, AuthorityRoleId = 2, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" }

            };
            List<ApplicationProduct> applicationProduct = new List<ApplicationProduct>()
            {
                new ApplicationProduct { Id = 29, ApplicationId = 23, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 28, ApplicationId = 23, ProductId = 6, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 32, ApplicationId = 24, ProductId = 5, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 31, ApplicationId = 24, ProductId = 6, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
                new ApplicationProduct { Id = 30, ApplicationId = 24, ProductId = 7, CreatedBy = "<EMAIL>", LastUpdatedBy = "<EMAIL>" },
            };
            var applications = new Application
            {
                Id = 23,
                ApplicationNumber = "AP-20210113-1 APPLICATION TEST",
                MedicinalProductDomainId = 1,
                MedicinalProductTypeId = 5,
                ApplicationTypeId = 3,
                ProcedureTypeId = 64,
                Comments = null,
                LifecycleStateId = 1,
                CreatedBy = "<EMAIL>",
                LastUpdatedBy = "<EMAIL>"

            };
            var casheService = Substitute.For<IDistributedCacheServiceFactory>();
            var appCache = Substitute.For<ITrackedEntityCacheServiceProxy<Application>>();
            casheService.CreateTrackedEntity<Application>().Returns(appCache);
            appCache.FirstOrDefaultAsync(Arg.Any<Expression<Func<Application, bool>>>()).Returns(applications);

            var appCountryCache = Substitute.For<ITrackedEntityCacheServiceProxy<ApplicationCountry>>();
            casheService.CreateTrackedEntity<ApplicationCountry>().Returns(appCountryCache);
            appCountryCache.WhereAsync(Arg.Any<Expression<Func<ApplicationCountry, bool>>>()).Returns(applicationCountry);

            var appProductCache = Substitute.For<ITrackedEntityCacheServiceProxy<ApplicationProduct>>();
            casheService.CreateTrackedEntity<ApplicationProduct>().Returns(appProductCache);
            appProductCache.WhereAsync(Arg.Any<Expression<Func<ApplicationProduct, bool>>>()).Returns(applicationProduct);

            var mapper = Substitute.For<IMapper>();
            //mapper.Map<ApplicationModel, Application>(model.Application).Returns(applications);
            var service = new ApplicationService(casheService, mapper, _authorizationService, _applicationRepository);

            // Act
            var result = await service.UpdateApplication(editModel, picklistDataModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("AP-20210113-1 APPLICATION TEST", result.ApplicationNumber);
        }
    }
}
