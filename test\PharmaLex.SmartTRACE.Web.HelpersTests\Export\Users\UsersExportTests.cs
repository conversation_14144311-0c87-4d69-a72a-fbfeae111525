﻿using NSubstitute;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers;
using PharmaLex.SmartTRACE.Web.Models;
using System.Linq.Expressions;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Export.Products
{
    public class UserExportTests
    {
        private readonly IDistributedCacheServiceFactory cache;

        public UserExportTests()
        {
            cache = Substitute.For<IDistributedCacheServiceFactory>();
        }

        [Fact]
        public async Task Export_user_ReturnsData()
        {
            // Arrange
            List<UserModel> userModels = new List<UserModel>() { new UserModel() { Id = 8, DisplayFullName = "name",  GivenName = "abc",DisplayUserType="2",Email="email",DisplayClaimsText="claim",DisplayClientsText="r" , UserTypeId = 2,AutoAccessClients=true} };

            var ac = Substitute.For<IMappedEntityCacheServiceProxy<User, UserModel>>();
           cache.CreateMappedEntity<User, UserModel>()
              .Configure(Arg.Any<Func<IIncludable<User>, IIncludable<User>>>()).Returns(ac);
            ac.AllAsync().Returns(Task.FromResult(userModels));

            // Act
            UserExport userExport = new UserExport(cache);
            var result = await userExport.Export();

            Assert.NotNull(result);
            // Assert

        }
        [Fact]
        public async Task Export_NoProduct_Returns_HeaderRow()
        {
            // Arrange
            List<ProjectModel> projects = new List<ProjectModel>() {  };

            var ac = Substitute.For<IMappedEntityCacheServiceProxy<Project, ProjectModel>>();
            cache.CreateMappedEntity<Project, ProjectModel>()
               .Configure(Arg.Any<Func<IIncludable<Project>, IIncludable<Project>>>()).Returns(ac);
            ac.AllAsync().Returns(Task.FromResult(projects));

            // Act
            ProjectExport projExport = new ProjectExport(cache);
            var result = await projExport.Export();

            Assert.NotNull(result);
            // Assert

        }
    }
}
