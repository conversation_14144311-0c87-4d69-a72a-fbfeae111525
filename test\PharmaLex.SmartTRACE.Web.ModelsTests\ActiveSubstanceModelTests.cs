﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class ActiveSubstanceModelTests
    {
        [Fact]
        public void ActiveSubstanceModel_Get_SetValue()
        {
            //Arrange
            var model = new ActiveSubstanceModel();
            model.Id = 1;
            model.Name = "test";
            model.ClientId = 1;
            model.ClientName = "test";
            model.LifecycleStateId = 1;
            model.HasDuplicate=true;
            model.ErrorMessage = "test";
            model.LifecycleState = "initial";
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void ActiveSubstanceMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new ActiveSubstanceMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
    }
}
