﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers
{
    public class DashboardController : BaseController
    {
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IAuthorizationService authorizationService;

        public DashboardController(IDistributedCacheServiceFactory cache, IAuthorizationService authorizationService)
        {
            this.cache = cache;
            this.authorizationService = authorizationService;
        }

        [HttpGet("/dashboard")]
        public async Task<IActionResult> Index()
        {
            var model = new DashboardViewModel()
            {
                CurrentUserName = this.User.Claims.FirstOrDefault(x => x.Type.StartsWith("name"))?.Value
            };

            if ((await authorizationService.AuthorizeAsync(this.User, "Reader")).Succeeded)
            {
                return await this.SubmissionDashboard(model);
            }

            return this.View(model);
        }

        private async Task<IActionResult> SubmissionDashboard(DashboardViewModel dashboardModel)
        {
            var subCache = cache.CreateMappedEntity<Submission, SubmissionModel>()
                                .Configure(o => o
                                    .Include(x => x.Project)
                                        .ThenInclude(x => x.Client));

            var clientsId = (await this.cache.CreateEntity<UserClient>().WhereAsync(x => x.UserId == this.CurrentUserId)).Select(x => x.ClientId).ToList();

            IList<SubmissionModel> submissions;
            if ((await this.authorizationService.AuthorizeAsync(this.User, "Admin")).Succeeded)
            {
                submissions = await subCache.AllAsync();
            }
            else
            {
                submissions = await subCache.WhereAsync(x => clientsId.Contains(x.Project.Client.Id) && x.LifecycleStateId != (int)SubmissionLifeCycleState.Obsolete);
            }
           

            return this.View("Submission", new DashboardSubmissionViewModel()
            {
                DraftSubmissionsCount = submissions.Distinct().Count(x => x.LifecycleStateId == (int)SubmissionLifeCycleState.Draft),
                PlannedSubmissionsCount = submissions.Distinct().Count(x => x.LifecycleStateId == (int)SubmissionLifeCycleState.Planned),
                InProgresstSubmissionsCount = submissions.Distinct().Count(x => x.LifecycleStateId == (int)SubmissionLifeCycleState.InProgress),
                ReadyForPublishingSubmissionsCount = submissions.Distinct().Count(x => x.LifecycleStateId == (int)SubmissionLifeCycleState.ReadyForPublishing),
                QCReviewSubmissionsCount = submissions.Distinct().Count(x => x.LifecycleStateId == (int)SubmissionLifeCycleState.QCReview),
                ApprovedForSubmissionsCount = submissions.Distinct().Count(x => x.LifecycleStateId == (int)SubmissionLifeCycleState.ApprovedForSubmission),
                SubmittedSubmissionsCount = submissions.Distinct().Count(x => x.LifecycleStateId == (int)SubmissionLifeCycleState.Submitted),
                ArchivedSubmissionsCount = submissions.Distinct().Count(x => x.LifecycleStateId == (int)SubmissionLifeCycleState.Archived),
                WithdrawnSubmissionsCount = submissions.Distinct().Count(x => x.LifecycleStateId == (int)SubmissionLifeCycleState.WithdrawnFromHA),
                AllSubmissionsCount = submissions.Distinct().Count(),
                DashboardModel = dashboardModel
            });
        }
    }
}
