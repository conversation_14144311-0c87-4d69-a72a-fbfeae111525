﻿using PharmaLex.SmartTRACE.Web.Helpers;

namespace PharmaLex.SmartTRACE.Web.HelpersTests.Authorization
{
    public class ClaimsHierarchyTests
    {
        AuthorizeMultipleAttribute authorizeMultipleAttribute;
        public string _policies;
        public bool _all;
        public ClaimsHierarchyTests()
        {
                authorizeMultipleAttribute=new AuthorizeMultipleAttribute(_policies, _all);
        }
        [Fact]
        public void ClaimsHierarchy_GetAllAdmins_Retunnotnull() 
        {
            var result = ClaimsHierarchy.GetAllAdmins();
            Assert.NotNull(result);
        }
        [Fact]
        public void ClaimsHierarchy_GetAllEditors_Retunnotnull()
        {
            var result = ClaimsHierarchy.GetAllEditors();
            Assert.NotNull(result);
        }
        [Fact]
        public void ClaimsHierarchy_GetAllReaders_Retunnotnull()
        {
            var result = ClaimsHierarchy.GetAllReaders();
            Assert.NotNull(result);
        }
        [Fact]
        public void ClaimsHierarchy_GetAllExternalEditors_Retunnotnull()
        {
            var result = ClaimsHierarchy.GetAllExternalEditors();
            Assert.NotNull(result);
        }
        [Fact]
        public void ClaimsHierarchy_GetAllPrivilegedExternalEditor_Retunnotnull()
        {
            var result = ClaimsHierarchy.GetAllPrivilegedExternalEditor();
            Assert.NotNull(result);
        }
    }
}
