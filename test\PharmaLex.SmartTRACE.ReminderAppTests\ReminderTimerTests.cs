using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using NSubstitute;
using PharmaLex.SmartTRACE.RemindersApp;
using PharmaLex.SmartTRACE.RemindersApp.Services;

namespace PharmaLex.SmartTRACE.ReminderAppTests
{
    public class ReminderTimerTests
    {
        private readonly IReminderService _reminderService;
        private static readonly FunctionContext _context = Substitute.For<FunctionContext>();
        private static readonly ILogger _logger = Substitute.For<ILogger>();
        private static readonly TimerInfo _timerInfo = Substitute.For<TimerInfo>();
        #region Constructor 
        public ReminderTimerTests()
        {
            _reminderService = Substitute.For<IReminderService>();

        }
        #endregion
        [Fact]
        public void RunReminder_Returns_Status()
        {   
            //Arrange
            var logFactory = Substitute.For<ILoggerFactory>();
            logFactory.CreateLogger(Arg.Any<string>()).Returns(_logger);
            var services = Substitute.For<IServiceProvider>();
            services.GetService(Arg.Any<Type>()).Returns(logFactory);
            _context.InstanceServices.Returns(services);
            _reminderService.SendPlannedSubmissionDateReminders(_logger);
            _reminderService.SendPlannedDispatchDateReminders(_logger);
            var Reminderfunction = new RemindersTimer(_reminderService);

            //Act
            var functionResponse = Reminderfunction.Run(_timerInfo, _context);

            //Assert
            Assert.NotNull(functionResponse);
            Assert.Equal("RanToCompletion", functionResponse.Status.ToString());
        }


    }
}