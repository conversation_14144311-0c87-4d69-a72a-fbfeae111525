﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32
{
    public interface Inodeextension { }

    public interface Ileaf
    {
        string text { get; }
        string version { get; }
        string ID { get; }
        string op { get; }
        string href { get; }
        string Submission { get; set; }
        string Path { get; set; }
        string modifiedfile { get; set; }
    }

    public abstract class CtdSectionBase
    {
        public CtdSectionBase()
        {
            XmlRootAttribute element = this.GetType().GetCustomAttributes(typeof(XmlRootAttribute), false).FirstOrDefault() as XmlRootAttribute;

            if (element != null)
            {
                this.CtdElementName = element.ElementName;

                string[] parts = this.CtdElementName.Split("-");

                this.CtdModule = parts[0].ToUpper();

                this.CtdSection = string.Empty;
                int i = 1;
                for (; i < parts.Length; i++)
                {
                    if (parts[i].Length == 1 || int.TryParse(parts[i], out _))
                    {
                        this.CtdSection += $"{parts[i]}.";
                        continue;
                    }
                    break;
                }
                this.CtdSection = this.CtdSection.TrimEnd('.').ToUpper();
                if (string.IsNullOrWhiteSpace(this.CtdSection))
                    this.CtdSection = this.CtdModule;
                else
                    this.CtdSection = $"{this.CtdModule[1]}.{this.CtdSection}";

                this.CtdName = string.Empty;
                for (; i < parts.Length; i++)
                {
                    this.CtdName += $"{parts[i]} ";
                }

                if (!string.IsNullOrWhiteSpace(this.CtdName))
                {
                    this.CtdName = this.CtdName?.Trim();
                    this.CtdName = $"{ char.ToUpper(this.CtdName[0]) }{ this.CtdName.Substring(1) }";
                }
            }
        }

        public virtual string CtdModule { get; }

        public virtual string CtdSection { get; }

        public virtual string CtdName { get; }

        public string CtdElementName { get; }
        private readonly IConfiguration configuration;
        public int CtdSectionNumber
        {
            get
            {
                int res;
                var maxTime = this.configuration.GetValue<double>("AppSettings:MaxTime");
                TimeSpan RegexTimeout = TimeSpan.FromMilliseconds(maxTime);
                string[] parts = Regex.Split(this.CtdSection, @"\d+", RegexOptions.None, RegexTimeout);
                string joinedString = string.Join("0",parts);
                return int.TryParse(joinedString, out res) ? res : int.MaxValue;
            }
        }

        public virtual List<CtdSectionBase> ChildNodes => this.Documents.Where(x => x is Inodeextension).ToList();
        public List<CtdSectionBase> Documents
        {
            get
            {
                PropertyInfo prop = this.GetType().GetProperty("Items");

                if (prop != null)
                {
                    var propVal = prop.GetValue(this);

                    if (propVal != null)
                    {
                        return new List<CtdSectionBase>(((object[])propVal).Select(x => 
                        {
                            var item = x as CtdSectionBase;
                            item.Parent = this;

                            return item;
                        }));
                    }
                }

                return new List<CtdSectionBase>();
            }
        }

        public CtdSectionBase Parent { get; set; }
        public virtual string CtdSectionMetadata { get; } = string.Empty;

        public T GetParent<T>() where T: CtdSectionBase
        {
            CtdSectionBase parent = this.Parent;

            while (parent != null && parent.GetType() != typeof(T))
                parent = parent.Parent;

            return parent as T;
        }
    }

    public class CtdSectionBaseMappingProfile : Profile
    {
        public CtdSectionBaseMappingProfile()
        {
            this.CreateMap<CtdSectionBase, CtdNodeModel>()
                .ForMember(d => d.Name, s => s.MapFrom(x => $"{x.CtdSection} {x.CtdName} {x.CtdSectionMetadata}"))
                .ForMember(d => d.SortOrder, s => s.MapFrom(x => x.CtdSectionNumber));

        }
    }
}
