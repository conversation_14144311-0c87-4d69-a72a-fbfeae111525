﻿using PharmaLex.SmartTRACE.Entities;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;

namespace PharmaLex.SmartTRACE.Web.Helpers.Extensions
{
    public static class ExpressionExtensions
    {
        public static Expression<Func<T, bool>> AndAlso<T>(this Expression<Func<T, bool>> left, Expression<Func<T, bool>> right)
            {
                Expression<Func<T, bool>> combined = Expression.Lambda<Func<T, bool>>(
                    Expression.AndAlso(
                        left.Body,
                        new ExpressionParameterReplacer(right.Parameters, left.Parameters).Visit(right.Body)
                        ), left.Parameters);

                return combined;
            }
    }
}
