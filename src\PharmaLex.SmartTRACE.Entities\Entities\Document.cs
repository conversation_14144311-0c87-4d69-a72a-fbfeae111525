﻿using PharmaLex.DataAccess;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class Document: EntityBase
    {
        public Document()
        {
            this.DocshifterDocumentFile = new HashSet<DocshifterDocumentFile>();
        }
        public int Id { get; set; }
        public string Name { get; set; }
        public int DocumentTypeId { get; set; }
        public int Version { get; set; }
        public int SubmissionId { get; set; }

        public virtual Submission Submission { get; set; }
        public virtual ICollection<DocshifterDocumentFile> DocshifterDocumentFile { get; set; }
    }
}
