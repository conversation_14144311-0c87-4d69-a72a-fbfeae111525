﻿using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using NSubstitute;
using PharmaLex.SmartTRACE.Web.DataFactory;
using PharmaLex.SmartTRACE.Web.Storage;

namespace PharmaLex.SmartTRACE.WebTests.Storage
{
    public class SubmissionsPubBlobStorageTests
    {
        readonly IAzureDataFactoryManagementClient adfClient;
        readonly IDataFactoryAuthenticationPovider dataFactoryAuthenticationPovider;
        readonly IOptions<DataFactoryPipelineSettings> settings;
        readonly ISubmissionPubBlobStorage submissionPubBlobStorage;
        readonly SubmissionPubBlobContainer submissionPubBlobContainer;
        readonly BlobContainerClient azBlobContainerClient;
        readonly BlobClient azBlobClient;
        readonly IConfiguration _configuration;
        public SubmissionsPubBlobStorageTests()
        {
            #region Fake storagesettings and blobServiceClient
            IHttpContextAccessor httpContextAccessor = new HttpContextAccessor();
            _configuration=Substitute.For<IConfiguration>();    
            StorageSettings storageSettings = new StorageSettings();
            storageSettings.TenantId = Guid.NewGuid().ToString();
            storageSettings.Url = "url";
            storageSettings.Account = "account";
            storageSettings.ConnectionString = "ConnectionString";
            storageSettings.Container = "Container";
            var settingStorage = Substitute.For<IOptions<StorageSettings>>();
            settingStorage.Value.Returns(storageSettings);
            UserDelegationKey userDelegationKey = Substitute.For<UserDelegationKey>();
            AsyncPageable<BlobHierarchyItem> blobHierarchyItem = Substitute.For<AsyncPageable<BlobHierarchyItem>>();
            Response<UserDelegationKey> response = Substitute.For<Response<UserDelegationKey>>();
            response.Value.Returns(userDelegationKey);
            BlobServiceClient blobServiceClient = Substitute.For<BlobServiceClient>();
            azBlobContainerClient = Substitute.For<BlobContainerClient>();
            azBlobClient = Substitute.For<BlobClient>();
            azBlobClient.BlobContainerName.Returns("Contname");
            azBlobClient.Name.Returns("Contname");
            azBlobClient.Uri.Returns(new Uri("http://amee/test/"));
            blobServiceClient.Uri.Returns(new Uri("http://amee/test/"));
            blobServiceClient.GetUserDelegationKey(Arg.Any<DateTimeOffset?>(), Arg.Any<DateTimeOffset>()).ReturnsForAnyArgs(response);
            azBlobClient.GetParentBlobContainerClient().GetParentBlobServiceClient().Returns(blobServiceClient);
            submissionPubBlobStorage = Substitute.For<ISubmissionPubBlobStorage>();
            #endregion
            #region Settings-Pipeline
            settings = Substitute.For<IOptions<DataFactoryPipelineSettings>>();
            settings.Value.Returns(new DataFactoryPipelineSettings
            {
                AllSequencesPath = "/all",
                DocumentsPath = "/documents",
                FactoryName = "test",
                Instance = "test",
                ResourceGroupName = "test",
                Subscription = "72c56c64-39f3-4fdd-86c7-2c323bfa8679",
                UnpackPath = "test",
                UnpackPipelineName = "test",
                UploadPath = "test"
            });
            dataFactoryAuthenticationPovider = Substitute.For<IDataFactoryAuthenticationPovider>();
            dataFactoryAuthenticationPovider.CreateClientCrdential().Returns(new Azure.Identity.DefaultAzureCredential());
            #endregion

            azBlobContainerClient.GetBlobClient(Arg.Any<string>()).ReturnsForAnyArgs(azBlobClient);
            submissionPubBlobStorage.GetContainerAsync().ReturnsForAnyArgs(azBlobContainerClient);
            (azBlobContainerClient).GetBlobsByHierarchyAsync(BlobTraits.None, BlobStates.None, "", "", default).ReturnsForAnyArgs(blobHierarchyItem);
            adfClient = Substitute.For<IAzureDataFactoryManagementClient>();
            submissionPubBlobContainer = new SubmissionPubBlobContainer(submissionPubBlobStorage, adfClient, settings, httpContextAccessor, _configuration);

        }
        [Theory]
        [InlineData("cid", "11", "231", "2", "0.1")]
        [InlineData("", "11", "231", "2", "0.1")]
        [InlineData("cid", "", "231", "2", "0.1")]
        [InlineData("cid", "11", "", "2", "0.1")]
        [InlineData("cid", "11", "231", "", "0.1")]
        [InlineData("cid", "11", "231", "", "")]
        public void GetSequenceUploadLinkAsync_CompletesAction(
        string clientId,
        string applicationId,
        string submissionUniqueId,
        string documentTypeId,
        string version)
        {

            adfClient.GetUploadPath("file.zip", clientId, applicationId, submissionUniqueId, documentTypeId, version).Returns("pathfile");
            adfClient.GetUploadPath("file.zip", clientId, applicationId, submissionUniqueId).Returns("uploadpathfile");

            var result = submissionPubBlobContainer.Pub_GetSequenceUploadLinkAsync("file.zip", clientId, applicationId, submissionUniqueId, documentTypeId, version);
            Assert.Equal("Faulted", result.Status.ToString());
        }
    }
}
