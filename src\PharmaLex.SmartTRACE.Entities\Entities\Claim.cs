﻿using PharmaLex.DataAccess;
using System;
using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Entities
{
    public partial class Claim : EntityBase
    {
        public Claim()
        {
            UserClaim = new HashSet<UserClaim>();
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string ClaimType { get; set; }

        public virtual ICollection<UserClaim> UserClaim { get; set; }
    }
}
