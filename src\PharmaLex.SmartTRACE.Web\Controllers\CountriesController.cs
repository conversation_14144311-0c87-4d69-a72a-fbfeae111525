﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PharmaLex.Caching.Data;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Web.Helpers.Constants;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using PharmaLex.SmartTRACE.Web.HTMLTags;
using PharmaLex.SmartTRACE.Web.Models;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Controllers;

[Authorize("BusinessAdmin")]
public class CountriesController(IDistributedCacheServiceFactory cache, IMapper mapper, ICountryService countryService) : BaseController
{
    [HttpGet, Route("/countries")]
    public async Task<IActionResult> Index()
    {
        var allCountries = await cache.CreateMappedEntity<Country, CountryModel>().AllAsync();
        return View(allCountries);
    }

    [HttpGet, Route("/countries/new/{regionId}")]
    public IActionResult New(int regionId)
    {
        if (!this.ModelState.IsValid)
        {
            return this.BadRequest(this.ModelState);
        }
        return View("EditCountry", new CountryModel()
        {
            RegionId = regionId
        });
    }

    [HttpPost, Route("/countries/new/{regionId}"), ValidateAntiForgeryToken]
    public async Task<IActionResult> SaveNew(int regionId,[Bind("Id,Name,TwoLetterCode,RegionId")] CountryModel model)
    {
        if (!this.ModelState.IsValid || HtmlTags.CheckHTMLTags(model.Name) || HtmlTags.CheckHTMLTags(model.TwoLetterCode))
        {
            return this.BadRequest(this.ModelState);
        }

        if (await countryService.IsCountryNameDuplicate(model.Name))
        {
            model.HasError = true;
            model.ErrorMessage = ErrorMessages.UniqueErrorMessage;
            return View("EditCountry", model);
        }

        var countryCache = cache.CreateTrackedEntity<Country>();
        var country = mapper.Map<Country>(model);
        countryCache.Add(country);
        await countryCache.SaveChangesAsync();
        this.AddConfirmationNotification($"<em>{country.Name}</em> created");
        return Redirect($"/regions/edit/{regionId}");
    }

    [HttpGet, Route("/countries/edit/{id}")]
    public async Task<IActionResult> Edit(int id)
    {
        if (!this.ModelState.IsValid)
        {
            return this.BadRequest(this.ModelState);
        }
        var countryCache = cache.CreateEntity<Country>();
        var currentCountry = await countryCache.FirstOrDefaultAsync(x => x.Id == id);
        var model = mapper.Map<CountryModel>(currentCountry);
        return View("EditCountry", model);
    }

    [HttpPost, Route("/countries/edit/{id}"), ValidateAntiForgeryToken]
    public async Task<IActionResult> SaveEdit(int id, CountryModel model)
    {
        if (!ModelState.IsValid || HtmlTags.CheckHTMLTags(model.Name) || HtmlTags.CheckHTMLTags(model.TwoLetterCode))
        {
            return View("EditCountry", model);
        }

        if (await countryService.IsCountryNameDuplicate(model.Name))
        {
            model.HasError = true;
            model.ErrorMessage = ErrorMessages.UniqueErrorMessage;
            return View("EditCountry", model);
        }
        var countryCache = cache.CreateTrackedEntity<Country>();
        var country = await countryCache.FirstOrDefaultAsync(x => x.Id == model.Id);
        mapper.Map(model, country);
        await countryCache.SaveChangesAsync();
        this.AddConfirmationNotification($"<em>{country.Name}</em> updated");
        return Redirect($"/regions/edit/{model.RegionId}");
    }

    [HttpGet("/countries/delete/{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        if (!this.ModelState.IsValid)
        {
            return this.BadRequest(this.ModelState);
        }
        var countryCache = cache.CreateMappedEntity<Country, CountryModel>();
        var country = await countryCache.FirstOrDefaultAsync(x => x.Id == id);
        return View("DeleteCountry", country);
    }

    [HttpPost("/countries/delete/{id}"), ValidateAntiForgeryToken]
    public async Task<IActionResult> ConfirmedDelete(int id)
    {
        if (!this.ModelState.IsValid)
        {
            return this.BadRequest(this.ModelState);
        }
        var countryCache = cache.CreateTrackedEntity<Country>();
        var country = await countryCache.FirstOrDefaultAsync(x => x.Id == id);
        countryCache.Remove(country);
        await countryCache.SaveChangesAsync();
        this.AddConfirmationNotification($"<em>{country.Name}</em> deleted");
        return Redirect($"/regions/edit/{country.RegionId}");
    }
}

