﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests
{
    public class ClientModelTests
    {
        [Fact]
        public void ClientModel_Get_SetValue()
        {
            //Arrange
            var model = new ClientModel();
            model.Id = 1;
            model.Name = "test";
            model.ContractOwnerId = 123;
            model.ContractOwner = "test";
            model.HasError = false;
            model.ErrorMessage = "test";
            //Assert
            Assert.NotNull(model);
        }
        [Fact]
        public void ClientMappingProfile_Get_SetValue()
        {
            //Arrange
            var model = new ClientMappingProfile();
            //Assert
            Assert.NotNull(model);
        }
    }
}
