﻿using AutoMapper;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Models;
using PharmaLex.SmartTRACE.Web.Models.Ich.eCtd32;
using PharmaLex.SmartTRACE.Web.Models.Ich.StudyReport;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.CTD
{
    public interface IEctdStudyReportProcessor
    {
        Task<List<CtdNodeModel>> Process(IEctdModule module, CtdSectionBase ctd, string sequenceLocation, string submission, string path, string schema);
    }

    public class EctdStudyReportProcessor : IEctdStudyReportProcessor
    {
        private readonly IEctdStudyReportFileReader ectdStudyReportFileReader;
        private readonly IMapper mapper;

        public EctdStudyReportProcessor(IEctdStudyReportFileReader ectdStudyReportFileReader, IMapper mapper)
        {
            this.ectdStudyReportFileReader = ectdStudyReportFileReader;
            this.mapper = mapper;
        }

        public async Task<List<CtdNodeModel>> Process(IEctdModule module, CtdSectionBase ctd, string sequenceLocation, string submission, string path, string schema)
        {
            if (!CanProcess(schema))
                return new List<CtdNodeModel>();

            var studyReportDocuments = ctd.Documents
                .Where(x => x is Ileaf)
                .Cast<Ileaf>()
                .Where(x => x.href != null && x.href.EndsWith("xml"))
                .Select(x =>
                {
                    x.Submission = submission;
                    x.Path = path;

                    return x;
                });

            var studyReportLeafs = this.mapper.Map<List<LeafModel>>(studyReportDocuments);
            var moduleDocuments = LoadModuleDocuments(module);

            return await ProcessStudyReportsTree(studyReportLeafs, moduleDocuments, sequenceLocation);
        }

        private bool CanProcess(string schemaName)
        {
            if (schemaName == "ectd")
                return true;

            return false;
        }

        private async Task<List<CtdNodeModel>> ProcessStudyReportsTree(List<LeafModel> leafs, IDictionary<string, Ileaf> moduleDocuments, string sequenceLocation)
        {
            var result = new List<CtdNodeModel>();

            foreach (var document in leafs)
            {
                if (document.href.EndsWith(".xml"))
                {
                    var studyTagLocation = $"{sequenceLocation}/{document.href}";
                    var studyReportFile = await this.ectdStudyReportFileReader.ReadFile(studyTagLocation);

                    if (studyReportFile != null)
                    {
                        var studyReportNode = new CtdNodeModel() { Name = "Study Report" };
                        var studyDocuments = studyReportFile.studydocument.Items.OfType<CtdSectionBase>();

                        ProcessDocContentDocuments(studyReportNode, studyDocuments, moduleDocuments);

                        if (studyDocuments.Any(sd => sd is contentblock))
                            ProcessContendBlockDocuments(studyReportNode, studyDocuments.OfType<contentblock>(), moduleDocuments);

                        var newNode = new CtdNodeModel()
                        {
                            Name = $"{document.text} (Study ID: {studyReportFile.studyidentifier.studyid}{BuildStudyTagCategoryValue(studyReportFile)})",
                            ChildNodes = new List<CtdNodeModel>() { studyReportNode }
                        };
                        result.Add(newNode);
                    }
                }
            }

            return result;
        }

        private string BuildStudyTagCategoryValue(study studyReportFile)
        {
            if (studyReportFile.studyidentifier.category == null || studyReportFile.studyidentifier.category.Length == 0)
                return string.Empty;

            return $", Category: {string.Join(", ", studyReportFile.studyidentifier.category.Select(s => s.ToString()))}";
        }

        private void ProcessContendBlockDocuments(CtdNodeModel contentNode, IEnumerable<contentblock> contentblocks, IDictionary<string, Ileaf> moduleDocuments)
        {
            foreach (contentblock content in contentblocks)
            {
                var contentBlockNode = new CtdNodeModel() { Name = content.blocktitle };

                if (content.Items.Any(i => i is contentblock))
                    ProcessContendBlockDocuments(contentBlockNode, content.Items.OfType<contentblock>(), moduleDocuments);

                ProcessDocContentDocuments(contentBlockNode, content.Items.OfType<CtdSectionBase>(), moduleDocuments);

                contentNode.ChildNodes.Add(contentBlockNode);
            }
        }

        private void ProcessDocContentDocuments(CtdNodeModel node, IEnumerable<CtdSectionBase> contentDocuments, IDictionary<string, Ileaf> moduleDocuments)
        {
            var contentBlockDocumentNodes = ReadStudyReportDocumentLeafs(contentDocuments, moduleDocuments);
            var contentBlockDocumentLeafs = mapper.Map<List<LeafModel>>(contentBlockDocumentNodes);

            node.Nodes = contentBlockDocumentLeafs;
        }

        private IEnumerable<LeafModel> ReadStudyReportDocumentLeafs(IEnumerable<CtdSectionBase> studyDocuments, IDictionary<string, Ileaf> moduleDocuments)
        {
            var documentLeafs = studyDocuments
                .OfType<doccontent>()
                .Select(doc => GetStudyReportLeafDocument(doc, moduleDocuments))
                .Where(f => f != null);

            var leafModels = mapper.Map<List<LeafModel>>(documentLeafs);
            return leafModels;
        }

        private Ileaf GetStudyReportLeafDocument(doccontent studydocument, IDictionary<string, Ileaf> moduleDocuments)
        {
            var documentId = studydocument.href.Substring(studydocument.href.IndexOf("#") + 1);
            if (moduleDocuments.ContainsKey(documentId))
            {
                return moduleDocuments[documentId];
            }
            return null;
        }

        private IDictionary<string, Ileaf> LoadModuleDocuments(IEctdModule module)
        {
            var moduleDocuments = module.Content
                .Flatten(c => c.ChildNodes)
                .SelectMany(s => s.Documents)
                .OfType<Ileaf>()
                .ToDictionary(s => s.ID, v => v);

            return moduleDocuments;
        }
    }
}
