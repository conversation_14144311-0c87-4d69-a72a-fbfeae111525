﻿using PharmaLex.SmartTRACE.Web.Models;

namespace PharmaLex.SmartTRACE.Web.ModelsTests.ViewModels
{
    public class EditSubmissionViewModelTests
    {
        [Fact]
        public void EditSubmissionViewModel_Get_SetValue()
        {
            //Arrange
            var model = new EditSubmissionViewModel();
            model.Submission = new SubmissionModel();
            model.SubmissionResource = new SubmissionResourceModel();
            model.Project = new ProjectModel();
            model.Application = new ApplicationModel();
            model.Product = "test";
            model.Client = new ClientModel();
            model.InvalidFields = new List<string>();
            model.Picklists = new List<PicklistDataModel>();
            model.AllCountries = new List<CountryModel>();
            model.Project = new ProjectModel();
            model.ApplicationCountries = new List<CountryModel>();
            //Assert
            Assert.NotNull(model);
        }
    }
}
