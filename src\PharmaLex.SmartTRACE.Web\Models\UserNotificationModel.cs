﻿namespace PharmaLex.SmartTRACE.Web.Models
{
    public class UserNotificationModel
    {
        public string Html { get; set; }
        public int Position { get; set; }
        public string Type { get; set; }
        public int? Number { get; set; }
        public int Duration { get; set; }

        public UserNotificationModel(string html, UserNotificationPosition position = UserNotificationPosition.TopCenter, UserNotificationType type = UserNotificationType.Info, int duration = 2500, int? number = null)
        {
            this.Html = html;
            this.Position = (int)position;
            this.Type = type.ToString().ToLower();
            this.Duration = duration;
            this.Number = number;
        }
    }

    public enum UserNotificationPosition
    {
        TopLeft = 1,
        TopCenter = 2,
        TopRight = 3,
        MiddleLeft = 4,
        MiddleCenter = 5,
        MiddleRight = 6,
        BottomLeft = 7,
        BottomCenter = 8,
        BottomRight = 9
    }

    public enum UserNotificationType
    {
        Info,
        Confirm,
        Failed,
        Warning,
        Number
    }
}
