﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.Helpers;
using PharmaLex.SmartTRACE.Entities;
using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.Helpers.Builders;
using PharmaLex.SmartTRACE.Web.Helpers.Constants;
using PharmaLex.SmartTRACE.Web.Helpers.Extensions;
using PharmaLex.SmartTRACE.Web.Helpers.Services.Interfaces;
using PharmaLex.SmartTRACE.Web.Models;
using PharmaLex.SmartTRACE.Web.Models.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Claims;
using System.Threading.Tasks;

#nullable enable

namespace PharmaLex.SmartTRACE.Web.Helpers
{
    public class SubmissionService : ISubmissionService
    {
        private readonly IAppSubmissionsRepository submissionsRepository;
        private readonly IDistributedCacheServiceFactory cache;
        private readonly IAuthorizationService authorizationService;

        private static Dictionary<string, Expression<Func<Submission, object>>> sortExpressions =
        new()
        {
                    { TableFilterConstants.ApplicationNumber, x => x.Application.ApplicationNumber},
                    { TableFilterConstants.ClientName, x => x.Project.Client.Name },
                    { TableFilterConstants.Description, x => x.Description },
                    { TableFilterConstants.RegulatoryLead, x => x.SubmissionResource.RegulatoryLead },
                    { TableFilterConstants.SequenceNumber, x => x.SequenceNumber }
        };

        public SubmissionService(
            IDistributedCacheServiceFactory cache,
            IAuthorizationService authorizationService,
            IAppSubmissionsRepository submissionsRepository)
        {
            this.cache = cache;
            this.authorizationService = authorizationService;
            this.submissionsRepository = submissionsRepository;
        }

        public async Task<IList<SubmissionViewModel>> GetAllSubmissionData(ClaimsPrincipal user, SubmissionFilterModel? model = null)
        {
            var currentUserId = user.GetClaimValue<int>("plx:userid");
            var clientsId = (await this.cache.CreateEntity<UserClient>().WhereAsync(x => x.UserId == currentUserId)).Select(x => x.ClientId).ToList();

            IList<ApplicationProduct> appProductClients = await GetAppProductClients(clientsId);

            IList<Submission> allSubmissions = await GetAllSubmissions(user, model, appProductClients.Select(x => x.ApplicationId).Distinct());

            IList<SubmissionPublisher> subPublishers = await GetSubmissionPublishers(allSubmissions.Select(x => x.Id).ToList());

            IList<SubmissionCountry> subCountries = await GetSubmissionCountries(allSubmissions.Select(x => x.Id).Distinct());

            List<SubmissionViewModel> submissions = new List<SubmissionViewModel>();

            foreach (var sub in allSubmissions.OrderBy(x => x.UniqueId))
            {
                var submissionPublishers = subPublishers.Where(x => x.SubmissionResourceId == sub.SubmissionResource.Id).Select(x => x.Publisher);
                var countries = subCountries.Where(x => x.SubmissionId == sub.Id).Select(x => x.Country);

                submissions.Add(new SubmissionViewModel
                {
                    Id = sub.Id,
                    UniqueId = sub.UniqueId,
                    ApplicationNumber = sub.Application.ApplicationNumber,
                    DisplayCountries = string.Join(", ", countries.Select(x => x.Name).Distinct().OrderBy(x => x).ToList()),
                    SubmissionDeliveryDetails = (await PicklistHelper.ExtractPicklist(cache, sub.DeliveryDetailsId))?.Name,
                    SubmissionType = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionTypeId))?.Name,
                    SubmissionUnit = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionUnitId))?.Name,
                    SubmissionMode = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionModeId))?.Name,
                    DossierFormat = (await PicklistHelper.ExtractPicklist(cache, sub.DossierFormatId))?.Name,
                    SerialNumber = sub.SerialNumber,
                    SequenceNumber = sub.SequenceNumber,
                    RelatedSequenceNumber = sub.RelatedSequenceNumber,
                    Description = sub.Description,
                    Comments = sub.Comments,
                    LifecycleState = ((SubmissionLifeCycleState)sub.LifecycleStateId).GetDescription(),
                    ClientName = sub.Project.Client.Name,
                    ProjectName = sub.Project.Name,
                    ProjectCode = sub.Project.Code,
                    ProjectOpportunityNumber = sub.Project.OpportunityNumber,
                    ContractOwner = (await PicklistHelper.ExtractPicklist(cache, sub.Project.Client.ContractOwnerId))?.Name,
                    CreatedBy = sub.CreatedBy,
                    CreatedDate = sub.CreatedDate,
                    HealthAuthorityDueDate = sub.HealthAuthorityDueDate,
                    AuthoringDeadline = sub.AuthoringDeadline,
                    PlannedSubmissionDate = sub.PlannedSubmissionDate,
                    PlannedDispatchDate = sub.PlannedDispatchDate,
                    ActualDispatchDate = sub.ActualDispatchDate,
                    ActualSubmissionDate = sub.ActualSubmissionDate,
                    DocubridgeVersionId = sub.DocubridgeVersionId,
                    CespNumber = sub.CespNumber,
                    RegulatoryLead = sub.SubmissionResource.RegulatoryLead,
                    DisplayPublishers = string.Join(", ", submissionPublishers.Select(x => $"{x.GivenName} {x.FamilyName} ({x.Email})").Distinct().OrderBy(x => x).ToList()),
                    SubmissionResource = new SubmissionResourceModel
                    {
                        Priority = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionResource.PriorityId))?.Name,
                        EstimatedSize = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionResource.EstimatedSizeId))?.Name,
                        EstimatedHours = sub.SubmissionResource.EstimatedHours,
                        PublishingLead = sub.SubmissionResource.PublishingLead,
                        RegulatoryLead = sub.SubmissionResource.RegulatoryLead,
                        PublishingTeam = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionResource.PublishingTeamId))?.Name,
                        InitialSentToEmail = sub.SubmissionResource.InitialSentToEmail,
                        Comments = sub.SubmissionResource.Comments
                    },
                    SourceDocumentsLocation = sub.SourceDocumentsLocation,
                    ArchivedDocumentsLocation = sub.ArchivedDocumentsLocation,
                    DossierName = sub.Document.FirstOrDefault(d => d.DocumentTypeId == (int)DocumentType.Dossier)?.Name
                });
            }

            return submissions;
        }

        public async Task<Submission> GetSubmission(int submissionId)
        {
            var submissionCache = cache.CreateEntity<Submission>()
                        .Configure(o => o
                            .Include(x => x.Application)
                             .ThenInclude(x => x.ApplicationProduct)
                                .ThenInclude(x => x.Product)
                                    .ThenInclude(x => x.Client));


            return await submissionCache.FirstOrDefaultAsync(x => x.Id == submissionId);
        }

        public async Task<ApiPagedListResult<SubmissionTableViewModel>> GetPagedSubmissionsAsync(ClaimsPrincipal user, int skip, int take, SubmissionFilterModel model, string? sort)
        {
            var currentUserId = 122; // user.GetClaimValue<int>("plx:userid");
            var clientsId = (await this.cache.CreateEntity<UserClient>().WhereAsync(x => x.UserId == currentUserId)).Select(x => x.ClientId).ToList();

            IList<ApplicationProduct> appProductClients = await GetAppProductClients(clientsId);

            var isUserInBusinessAdminRole = (await authorizationService.AuthorizeAsync(user, "BusinessAdmin")).Succeeded;
            Expression<Func<Submission, bool>> filterByRoleExpression = x => (!isUserInBusinessAdminRole && x.LifecycleStateId != (int)SubmissionLifeCycleState.Obsolete) || isUserInBusinessAdminRole;

            var expression = GetFilterExpression(model, filterByRoleExpression.AndAlso(x => appProductClients.Select(x => x.ApplicationId).Distinct().Contains(x.ApplicationId)));

            var predicate = ExpressionBuilder<Submission>.BuildSortExpression(sort, sortExpressions, x => x.UniqueId);

            var query = submissionsRepository.GetQueryableItems(x => x.Include(y => y.Application)
                                                                    .Include(y => y.SubmissionResource)
                                                                    .Include(y => y.Project)
                                                                        .ThenInclude(z => z.Client));

            var entities = await query.FilterItems(expression, predicate, skip, take).ToListAsync();

            IList<SubmissionPublisher> subPublishers = await GetSubmissionPublishers(entities.Select(x => x.Id).ToList());

            var submissions = new List<SubmissionTableViewModel>();

            foreach (var sub in entities)
            {
                var submissionPublishers = subPublishers.Where(x => x.SubmissionResourceId == sub.SubmissionResource.Id).Select(x => x.Publisher);

                submissions.Add(new SubmissionTableViewModel
                {
                    Id = sub.Id,
                    ApplicationNumber = sub.Application.ApplicationNumber,
                    SubmissionType = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionTypeId))?.Name,
                    SubmissionUnit = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionUnitId))?.Name,
                    SubmissionMode = (await PicklistHelper.ExtractPicklist(cache, sub.SubmissionModeId))?.Name,
                    DossierFormat = (await PicklistHelper.ExtractPicklist(cache, sub.DossierFormatId))?.Name,
                    SequenceNumber = sub.SequenceNumber,
                    Description = sub.Description,
                    LifecycleState = ((SubmissionLifeCycleState)sub.LifecycleStateId).GetDescription(),
                    ClientName = sub.Project.Client.Name,
                    RegulatoryLead = sub.SubmissionResource.RegulatoryLead,
                    DisplayPublishers = string.Join(", ", submissionPublishers.Select(x => $"{x.GivenName} {x.FamilyName} ({x.Email})").Distinct().OrderBy(x => x).ToList()),
                });
            }

            return new ApiPagedListResult<SubmissionTableViewModel>(
               submissions,
               new()
               {
                   TotalItemCount = await query.CountAsync(filterByRoleExpression),
                   FilteredCount = await query.CountAsync(expression),
                   Offset = skip,
                   Limit = take,
               });
        }

        private Expression<Func<Submission, bool>> GetFilterExpression(SubmissionFilterModel model, Expression<Func<Submission, bool>> expression)
        {
            if (!string.IsNullOrEmpty(model.ClientName))
            {
                expression = expression.AndAlso(i => i.Project.Client.Name.ToLower().Contains(model.ClientName.ToLower()));
            }

            if (!string.IsNullOrEmpty(model.ApplicationNumber))
            {
                expression = expression.AndAlso(i => i.Application.ApplicationNumber.ToLower().Contains(model.ApplicationNumber.ToLower()));
            }

            if (!string.IsNullOrEmpty(model.Description))
            {
                expression = expression.AndAlso(i => i.Description.ToLower().Contains(model.Description.ToLower()));
            }

            if (!string.IsNullOrEmpty(model.DossierFormat))
            {
                var dossierFormats = model.DossierFormat.Split(',');
                var dossierFormatIds = new List<int?>();

                foreach (var dossierFormat in dossierFormats)
                {
                    var dossierFormatId = PicklistHelper.GetPicklistByName(cache, dossierFormat, (int)PicklistType.DossierFormat).GetAwaiter().GetResult()?.Id;
                    if (dossierFormatId != null)
                    {
                        dossierFormatIds.Add(dossierFormatId);
                    }
                }

                expression = expression.AndAlso(i => dossierFormatIds.Contains(i.DossierFormatId));
            }

            if (!string.IsNullOrEmpty(model.LifecycleState))
            {
                var lifecycleStates = model.LifecycleState.Split(',');
                var lifecycleStateIds = new List<int>();

                foreach (var lifecycleState in lifecycleStates)
                {
                    var lifecycleStateId = (int)Extensions.EnumExtensions.GetValueFromDescription<SubmissionLifeCycleState>(lifecycleState);
                    lifecycleStateIds.Add(lifecycleStateId);
                }

                expression = expression.AndAlso(i => lifecycleStateIds.Contains(i.LifecycleStateId));
            }

            if (!string.IsNullOrEmpty(model.SequenceNumber))
            {
                expression = expression.AndAlso(i => i.SequenceNumber.ToLower().Contains(model.SequenceNumber.ToLower()));
            }

            if (!string.IsNullOrEmpty(model.SubmissionMode))
            {
                var submissionModes = model.SubmissionMode.Split(',');
                var submissionModeIds = new List<int?>();

                foreach (var submissionsMode in submissionModes)
                {
                    var submissionModeId = PicklistHelper.GetPicklistByName(cache, submissionsMode, (int)PicklistType.SubmissionMode).GetAwaiter().GetResult()?.Id;
                    if (submissionModeId != null)
                    {
                        submissionModeIds.Add(submissionModeId);
                    }
                }

                expression = expression.AndAlso(i => submissionModeIds.Contains(i.SubmissionModeId));
            }

            if (!string.IsNullOrEmpty(model.SubmissionType))
            {
                var submissionTypeIds = PicklistHelper.GetPicklistsByPartialName(cache, model.SubmissionType).GetAwaiter().GetResult().Select(x => x.Id).ToList();
                expression = expression.AndAlso(i => submissionTypeIds.Contains(i.SubmissionTypeId));
            }

            if (!string.IsNullOrEmpty(model.SubmissionUnit))
            {
                var submissionUnits = model.SubmissionUnit.Split(',');
                var submissionUnitIds = new List<int?>();

                foreach (var submissionUnit in submissionUnits)
                {
                    var submissionUnitId = PicklistHelper.GetPicklistByName(cache, submissionUnit, (int)PicklistType.SubmissionUnit).GetAwaiter().GetResult()?.Id;
                    if (submissionUnitId != null)
                    {
                        submissionUnitIds.Add(submissionUnitId);
                    }
                }

                expression = expression.AndAlso(i => submissionUnitIds.Contains(i.SubmissionUnitId));
            }

            if (!string.IsNullOrEmpty(model.RegulatoryLead))
            {
                expression = expression.AndAlso(i => i.SubmissionResource.RegulatoryLead.ToLower().Contains(model.RegulatoryLead.ToLower()));
            }

            return expression;
        }

        private async Task<IList<SubmissionPublisher>> GetSubmissionPublishers(List<int> submissionIds)
        {
            var subPublisherCache = this.cache.CreateEntity<SubmissionPublisher>()
                    .Configure(o => o
                        .Include(x => x.Publisher)
                        .Include(x => x.SubmissionResource));

            var subPublishers = await subPublisherCache.WhereAsync(x => submissionIds.Contains(x.SubmissionResource.SubmissionId));
            return subPublishers;
        }

        private async Task<IList<Submission>> GetAllSubmissions(ClaimsPrincipal user, SubmissionFilterModel? model, IEnumerable<int> appIds)
        {
            var submissionCache = this.cache.CreateEntity<Submission>()
                               .Configure(o => o
                                       .Include(x => x.Application)
                                       .Include(x => x.SubmissionResource)
                                       .Include(x => x.Document)
                                       .Include(x => x.Project)
                                        .ThenInclude(x => x.Client));

            Expression<Func<Submission, bool>> expression = x => appIds.Contains(x.ApplicationId);

            if (model != null)
            {
                expression = GetFilterExpression(model, expression);
            }

            var allSubmissions = await submissionCache.WhereAsync(expression);

            if (!(await authorizationService.AuthorizeAsync(user, "BusinessAdmin")).Succeeded)
            {
                allSubmissions = allSubmissions.Where(x => x.LifecycleStateId != (int)SubmissionLifeCycleState.Obsolete).ToList();
            }

            return allSubmissions;
        }

        private async Task<IList<ApplicationProduct>> GetAppProductClients(List<int> clientsId)
        {
            var appProductClientCache = this.cache.CreateEntity<ApplicationProduct>()
                                            .Configure(o => o
                                                .Include(x => x.Product));

            var appProductClients = (await appProductClientCache.WhereAsync(x => clientsId.Contains(x.Product.ClientId)));
            return appProductClients;
        }

        private async Task<IList<SubmissionCountry>> GetSubmissionCountries(IEnumerable<int> submissionIds)
        {
            var subCountryCache = this.cache.CreateEntity<SubmissionCountry>()
                                            .Configure(o => o
                                                .Include(x => x.Country));

            var subCountries = (await subCountryCache.WhereAsync(x => submissionIds.Contains(x.SubmissionId)));
            return subCountries;
        }
    }
}
