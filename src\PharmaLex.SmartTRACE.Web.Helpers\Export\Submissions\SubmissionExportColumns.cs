﻿using System.Collections.Generic;

namespace PharmaLex.SmartTRACE.Web.Helpers.Export.Submissions
{
    public static class SubmissionExportColumns
    {
        public static Dictionary<string, string> ColumnsValueDict
        {
            get
            {
                return new Dictionary<string, string>()
                {
                    { "Unique Id", "UniqueId" },
                    { "Application Number", "ApplicationNumber" },
                    { "Countries", "DisplayCountries" },
                    { "Submission Delivery Details", "SubmissionDeliveryDetails" },
                    { "Submission Type", "SubmissionType" },
                    { "Submission Unit", "SubmissionUnit" },
                    { "Submission Mode", "SubmissionMode" },
                    { "Dossier Format", "DossierFormat" },
                    { "Serial Number", "SerialNumber" },
                    { "Sequence Number", "SequenceNumber" },
                    { "Related Sequence Number", "RelatedSequenceNumber" },
                    { "Description", "Description" },
                    { "Submission Comments", "Comments" },
                    { "Lifecycle State", "LifecycleState" },
                    { "Client Name", "ClientName" },
                    { "Project Name", "ProjectName" },
                    { "Project Code", "ProjectCode" },
                    { "Opportunity Number", "ProjectOpportunityNumber" },
                    { "Contract Owner", "ContractOwner" },
                    { "Created By", "CreatedBy" },
                    { "Created Date", "CreatedDate" },
                    { "Health Authority Due Date", "HealthAuthorityDueDate" },
                    { "Deadline for Last Document", "AuthoringDeadline" },
                    { "Planned Submission", "PlannedSubmissionDate" },
                    { "Planned Dispatch", "PlannedDispatchDate" },
                    { "Actual Dispatch", "ActualDispatchDate" },
                    { "Actual Submission", "ActualSubmissionDate" },
                    { "docuBridge Version Id", "DocubridgeVersionId" },
                    { "Portal/Gateway Reference Number", "CespNumber" },
                    { "Priority", "Priority" },
                    { "Estimated Size", "EstimatedSize" },
                    { "Estimated Hours", "EstimatedHours" },
                    { "Publishing Lead", "PublishingLead" },
                    { "Regulatory Lead", "RegulatoryLead" },
                    { "Publishing Team", "PublishingTeam" },
                    { "Publishers", "DisplayPublishers" },
                    { "Draft Submission Form Sent To", "InitialSentToEmail" },
                    { "Submission Resource Comments", "Comments" },
                    { "Location of Source Documents", "SourceDocumentsLocation" },
                    { "Location of Archived Dossier", "ArchivedDocumentsLocation" },
                    { "Submission Dossier", "DossierName" }
                };
            }
        }
    }
}
