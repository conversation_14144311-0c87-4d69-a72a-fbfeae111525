﻿SET IDENTITY_INSERT [dbo].[Country] ON 

INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (1, N'Austria', N'AT', 6, CAST(N'2020-11-27T11:04:52.573' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.573' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (2, N'Belgium', N'BE', 6, CAST(N'2020-11-27T11:04:52.597' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:44:05.997' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (3, N'Bulgaria', N'BG', 6, CAST(N'2020-11-27T11:04:52.597' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.597' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (4, N'Croatia', N'HR', 6, CAST(N'2020-11-27T11:04:52.597' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.597' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (5, N'Cyprus', N'CY', 6, CAST(N'2020-11-27T11:04:52.597' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:43:57.120' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (6, N'Czech Republic', N'CZ', 6, CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (7, N'Denmark', N'DK', 6, CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (8, N'Estonia', N'EE', 6, CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (9, N'Finland', N'FI', 6, CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (10, N'France', N'FR', 6, CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (11, N'Germany', N'DE', 6, CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (12, N'Greece', N'GR', 6, CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (13, N'Hungary', N'HU', 6, CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.600' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (14, N'Iceland', N'IS', 6, CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (15, N'Ireland', N'IE', 6, CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (16, N'Italy', N'IT', 6, CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (17, N'Latvia', N'LV', 6, CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (18, N'Lithuania', N'LT', 6, CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (19, N'Luxembourg', N'LU', 6, CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (20, N'Malta', N'MT', 6, CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (21, N'Netherlands', N'NL', 6, CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (22, N'Norway', N'NO', 6, CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.610' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (23, N'Poland', N'PL', 6, CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (24, N'Portugal', N'PT', 6, CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (25, N'Romania', N'RO', 6, CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (26, N'Slovakia', N'SK', 6, CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (27, N'Slovenia', N'SI', 6, CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (28, N'Spain', N'ES', 6, CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (29, N'Sweden', N'SE', 6, CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>', CAST(N'2020-11-27T11:04:52.613' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (30, N'United Kingdom (EU)', N'UK', 6, CAST(N'2020-12-09T10:52:58.147' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T12:06:13.530' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (37, N'South Africa', N'ZA', 1, CAST(N'2020-12-11T13:38:48.613' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:38:48.613' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (38, N'Australia', N'AU', 2, CAST(N'2020-12-11T13:39:13.757' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:39:13.757' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (39, N'New Zealand', N'NZ', 2, CAST(N'2020-12-11T13:39:22.577' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:39:22.577' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (40, N'China', N'CN', 2, CAST(N'2020-12-11T13:39:31.783' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:39:31.783' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (41, N'Singapore', N'SG', 2, CAST(N'2020-12-11T13:39:38.897' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:39:38.897' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (42, N'Bosnia and Herzegovina', N'BA', 5, CAST(N'2020-12-11T13:40:12.337' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:40:12.337' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (43, N'Great Britain', N'GB', 5, CAST(N'2020-12-11T13:40:21.850' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:40:21.850' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (44, N'Macedonia', N'MK', 5, CAST(N'2020-12-11T13:40:34.187' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:40:34.187' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (45, N'Russia', N'RU', 5, CAST(N'2020-12-11T13:40:43.210' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:40:43.210' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (46, N'Serbia', N'RS', 5, CAST(N'2020-12-11T13:40:54.170' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:40:59.907' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (47, N'Switzerland', N'CH', 5, CAST(N'2020-12-11T13:41:13.380' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:41:13.380' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (48, N'Turkey', N'TR', 5, CAST(N'2020-12-11T13:41:23.617' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:41:23.617' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (49, N'United Kingdom', N'UK', 5, CAST(N'2020-12-11T13:41:33.847' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:41:33.847' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (50, N'Liechtenstein', N'LI', 6, CAST(N'2020-12-11T13:43:47.970' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:43:47.970' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (51, N'Bahrain', N'BH', 7, CAST(N'2020-12-11T13:44:31.647' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:44:31.647' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (52, N'Kuwait', N'KW', 7, CAST(N'2020-12-11T13:44:40.843' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:44:40.843' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (53, N'Oman', N'OM', 7, CAST(N'2020-12-11T13:44:49.157' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:44:49.157' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (54, N'Qatar', N'QA', 7, CAST(N'2020-12-11T13:44:57.573' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:44:57.573' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (55, N'Saudi Arabia', N'SA', 7, CAST(N'2020-12-11T13:45:05.810' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:45:05.810' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (56, N'United Arab Emirates', N'AE', 7, CAST(N'2020-12-11T13:45:14.150' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:45:14.150' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (57, N'Yemen', N'YE', 7, CAST(N'2020-12-11T13:45:21.763' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:45:21.763' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (58, N'Jordan', N'JO', 8, CAST(N'2020-12-11T13:45:40.780' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:45:40.780' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (59, N'United States of America', N'US', 9, CAST(N'2020-12-11T13:45:59.467' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:46:07.773' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (60, N'Canada', N'CA', 9, CAST(N'2020-12-11T13:46:23.077' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:46:23.077' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (61, N'Armenia', N'AM', 10, CAST(N'2020-12-11T13:46:45.597' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:46:45.597' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (62, N'Belarus', N'BY', 10, CAST(N'2020-12-11T13:46:55.350' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:46:55.350' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (63, N'Georgia', N'GE', 10, CAST(N'2020-12-11T13:47:03.680' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:47:03.680' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (64, N'Kazakhstan', N'KZ', 10, CAST(N'2020-12-11T13:47:17.313' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:47:17.313' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (65, N'Kyrgyzstan', N'KG', 10, CAST(N'2020-12-11T13:47:26.867' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:47:26.867' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (66, N'Moldova', N'MD', 10, CAST(N'2020-12-11T13:47:38.563' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:47:38.563' AS DateTime), N'<EMAIL>')
INSERT [dbo].[Country] ([Id], [Name], [TwoLetterCode], [RegionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy]) VALUES (67, N'Ukraine', N'UA', 10, CAST(N'2020-12-11T13:47:47.670' AS DateTime), N'<EMAIL>', CAST(N'2020-12-11T13:47:47.670' AS DateTime), N'<EMAIL>')
SET IDENTITY_INSERT [dbo].[Country] OFF
GO
