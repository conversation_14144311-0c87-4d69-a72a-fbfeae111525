﻿using PharmaLex.SmartTRACE.Entities.Enums;
using PharmaLex.SmartTRACE.Web.Helpers;

namespace PharmaLex.SmartTRACE.Web.HelpersTests
{
    public class RequiredFieldsHelperTests
    {
        [Fact]
        public void GetRequiredFields_Retuns_Not_Null()
        {   
            //Arrange
            var state= SubmissionLifeCycleState.Draft;
            //Act
            var result = RequiredFieldsHelper.GetRequiredFields(state);
            //Assert
            Assert.NotNull(result);
        }
    }
}
