﻿using PharmaLex.SmartTRACE.Web.Models.Ich.eCTD.Interfaces;
using PharmaLex.SmartTRACE.Web.Storage;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.Xml.Serialization;

namespace PharmaLex.SmartTRACE.Web.CTD
{
    public interface IEctdFileReader
    {
        string SchemaName { get; }
        string DtdVersion { get; }
        Type DeserializeToType { get; }
        Task<IEctdModule> ReadFile(string fullFilePath);
    }

    public class AzureBlobStorageEctdFileReader : EctdSequenceReaderBase
    {
        private readonly ISubmissionBlobContainer container;
        public AzureBlobStorageEctdFileReader(ISubmissionBlobContainer blobContainer)
        {
            this.container = blobContainer;
        }
        protected override async Task<Stream> GetFileStream(string fullFilePath)
        {
            var file = await this.container.GetBlobClientAsync(fullFilePath);

            return await file.OpenReadAsync();
        }
    }

    public abstract class EctdSequenceReaderBase : IEctdFileReader
    {
        public async Task<IEctdModule> ReadFile(string fullFilePath)
        {
            using (Stream stream = await this.GetFileStream(fullFilePath))
            {
                XDocument doc = await XDocument.LoadAsync(stream, LoadOptions.None, new System.Threading.CancellationToken());

                this.DtdVersion = doc.Root.Attributes().First(x => x.Name == "dtd-version" || x.Name == "schema-version").Value;
                this.SchemaName = doc.Root.Name.LocalName;

                this.DeserializeToType = this.GetOutputType();
                XmlSerializer serializer = new XmlSerializer(this.DeserializeToType);

                object output = serializer.Deserialize(doc.CreateReader());
                return (IEctdModule)output;
            }
        }

        public string SchemaName { get; private set; }
        public string DtdVersion { get; private set; }
        public Type DeserializeToType { get; private set; }

        protected abstract Task<Stream> GetFileStream(string fullFilePath);

        private Type GetOutputType()
        {
            if (this.SchemaName == "ectd" && this.DtdVersion == "3.2")
                return typeof(Models.Ich.eCtd32.ectd);

            if (this.SchemaName == "eu-backbone" && (this.DtdVersion is "3.0.1" or "2.0" or "3.1"))
                return typeof(Models.Ich.Eu301.eubackbone);

            if (this.SchemaName == "fda-regional" && this.DtdVersion == "3.3")
                return typeof(Models.eCTD32.US33.fdaregional);

            if (this.SchemaName == "fda-regional" && this.DtdVersion == "2.01")
                return typeof(Models.eCTD32.US32.fdaregional);

            if (this.SchemaName == "ch-backbone" && (this.DtdVersion is "1.5" or "1.4" or "1.3"))
                return typeof(Models.Ich.CH32.chbackbone);

            if (this.SchemaName == "tga_ectd" && (this.DtdVersion is "3.1" or "3.0"))
                return typeof(Models.Ich.AU.tga_ectd);

            throw new NotSupportedException($"Not supported schema \"{this.SchemaName}\" with version \"{this.DtdVersion}\".");
        }
    }
}
