﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PharmaLex.SmartTRACE.Entities.Migrations
{
    public partial class AddDocubridgeVersionId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "DocubridgeVersionId",
                schema: "Audit",
                table: "Submission_Audit",
                type: "nvarchar(35)",
                maxLength: 35,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DocubridgeVersionId",
                table: "Submission",
                type: "nvarchar(35)",
                maxLength: 35,
                nullable: true);

            migrationBuilder.SqlFileExec("0021-AddDocubridgeVersionId-CreateTriggers.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DocubridgeVersionId",
                schema: "Audit",
                table: "Submission_Audit");

            migrationBuilder.DropColumn(
                name: "DocubridgeVersionId",
                table: "Submission");
        }
    }
}
